{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        
        {
            "type": "chrome",
            "request": "launch",
            "name": "Launch Chrome against localhost",
            "url": "http://localhost:8080",
            "webRoot": "${workspaceFolder}"
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Run Script: dev",
            "runtimeExecutable": "npm",
            "runtimeArgs": ["run", "server"],
            "cwd": "${workspaceFolder}",
            "console": "integratedTerminal",
            "env": {
                "NODE_ENV": "development"
            },
            "restart": true,
            "protocol": "inspector",
            "skipFiles": [
                "<node_internals>/**"
            ]
        },
        {
            "type": "node",
            "request": "launch",
            "name": "Debug Server Directly",
            "program": "${workspaceFolder}/server/index.js",
            "cwd": "${workspaceFolder}/server",
            "console": "integratedTerminal",
            "env": {
                "NODE_ENV": "development"
            },
            "restart": true,
            "protocol": "inspector",
            "skipFiles": [
                "<node_internals>/**"
            ]
        }
    ]
}