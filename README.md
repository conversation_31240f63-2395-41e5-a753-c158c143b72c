# AI Form Builder Chatbot

A dynamic form builder chatbot application that allows users to create, manage, and fill out forms through a conversational interface.

## Features

- Create dynamic forms with unique names
- Configure API endpoints, methods, headers, and authorization
- Define input fields with validation rules
- Store form data in MongoDB
- Chat interface to interact with forms
- Local AI model integration (llama3.2.2 via Ollama)
- Responsive UI design

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (local instance or MongoDB Compass)
- Ollama with llama3.2 model installed

## Installation

1. Clone the repository:
```
git clone <repository-url>
cd ai-form-builder
```

2. Install dependencies for the root, client, and server:
```
npm run install-all
```

3. Configure environment variables:
   - Create a `.env` file in the server directory with the following variables:
   ```
   NODE_ENV=development
   PORT=5000
   MONGO_URI=mongodb://localhost:27017/form_builder
   OLLAMA_API_URL=http://localhost:11434/api
   ```

4. Make sure MongoDB is running:
   - Start your local MongoDB instance or connect to MongoDB Compass

5. Make sure <PERSON><PERSON><PERSON> is running with the llama3.2 model:
   - Install Ollama from https://ollama.ai/
   - Run `ollama pull llama3.2` to download the model
   - Start Ollama service

## Running the Application

1. Start both the client and server:
```
npm start
```

2. Access the application:
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:5000

## Usage

### Creating Forms

1. Navigate to the Forms page
2. Click "Create New Form"
3. Fill in the form details:
   - Form name (must be unique)
   - Description
   - API configuration (method, endpoint, headers, auth)
   - Form fields (with validation rules)
4. Save the form

### Using the Chatbot

1. Navigate to the Chat page
2. Ask the chatbot about a form by name
3. The chatbot will display the form if it exists
4. Fill out the form and submit
5. The chatbot will show the API response

## Project Structure

- `/client` - React frontend
  - `/src/components` - UI components
  - `/src/context` - React context providers
  - `/src/pages` - Main application pages
  - `/src/utils` - Utility functions

- `/server` - Node.js backend
  - `/config` - Configuration files
  - `/controllers` - API controllers
  - `/models` - MongoDB models
  - `/routes` - API routes
  - `/services` - Business logic

## Technologies Used

- **Frontend**: React, Tailwind CSS, Axios
- **Backend**: Node.js, Express, MongoDB, Mongoose
- **AI**: Ollama (llama3.2 model)
- **Other**: RESTful API, JWT Authentication