// Simple Node.js script to check forms in MongoDB
// Run this with: node check-forms-in-db.js

const mongoose = require('mongoose');

// Connect to MongoDB (adjust the connection string if needed)
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGO_URI || 'mongodb://localhost:27017/botnexus');
    console.log(`✅ MongoDB Connected: ${conn.connection.host}`);
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Define Form schema (should match your actual schema)
const formSchema = new mongoose.Schema({
  name: { type: String, required: true },
  formTitle: { type: String },
  fields: [
    {
      name: { type: String, required: true },
      label: { type: String, required: true },
      type: { type: String, required: true },
      required: { type: Boolean, default: false },
      options: [String],
      placeholder: String,
      validation: {
        pattern: String,
        message: String
      }
    }
  ],
  apiConfig: {
    endpoint: String,
    method: { type: String, default: 'POST' },
    headers: Object,
    requiresFile: { type: Boolean, default: false }
  },
  active: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});

const Form = mongoose.model('Form', formSchema);

async function checkForms() {
  try {
    await connectDB();
    
    console.log('🔍 Checking all forms in database...\n');
    
    // Get all forms
    const forms = await Form.find({});
    
    console.log(`📋 Found ${forms.length} forms in database:\n`);
    
    forms.forEach((form, index) => {
      console.log(`--- Form ${index + 1} ---`);
      console.log(`ID: ${form._id}`);
      console.log(`Name: ${form.name}`);
      console.log(`Form Title: ${form.formTitle || 'Not set'}`);
      console.log(`Fields Count: ${form.fields ? form.fields.length : 0}`);
      
      if (form.fields && form.fields.length > 0) {
        console.log(`Fields: ${form.fields.map(f => f.name).join(', ')}`);
      }
      
      console.log(`Active: ${form.active}`);
      console.log(`Created: ${form.createdAt}`);
      console.log('');
    });
    
    // Look specifically for regularization forms
    const regularizationForms = forms.filter(form => {
      const nameMatch = form.name && form.name.toLowerCase().includes('regular');
      const titleMatch = form.formTitle && form.formTitle.toLowerCase().includes('regular');
      return nameMatch || titleMatch;
    });
    
    console.log(`🎯 Regularization-related forms (${regularizationForms.length}):`);
    regularizationForms.forEach(form => {
      console.log(`  - Name: "${form.name}" | Title: "${form.formTitle}"`);
    });
    
    if (regularizationForms.length === 0) {
      console.log('❌ No regularization forms found!');
      console.log('💡 Make sure your regularization form has:');
      console.log('   - name field containing "regular" OR');
      console.log('   - formTitle field containing "regular"');
    }
    
  } catch (error) {
    console.error('❌ Error checking forms:', error);
  } finally {
    mongoose.connection.close();
    console.log('\n✅ Database connection closed');
  }
}

// Run the check
checkForms();