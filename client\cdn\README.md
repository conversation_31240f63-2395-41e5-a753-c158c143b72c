# BotNexus CDN Widget

A lightweight, embeddable chatbot widget that can be easily integrated into any website.

## 🚀 Quick Start

### Basic Usage

Simply include the script tag in your HTML:

```html
<script src="http://localhost:5000/chatbot-widget.js"></script>
```

### With Configuration

Customize the widget using data attributes:

```html
<script 
    src="http://localhost:5000/chatbot-widget.js"
    data-api-url="http://localhost:5000"
    data-position="bottom-right"
    data-primary-color="#007bff"
    data-title="Chat with us"
    data-subtitle="We're here to help!"
></script>
```

## ⚙️ Configuration Options

| Attribute | Default | Description |
|-----------|---------|-------------|
| `data-api-url` | `http://localhost:5000` | Backend API URL |
| `data-position` | `bottom-right` | Widget position: `bottom-right`, `bottom-left`, `top-right`, `top-left` |
| `data-primary-color` | `#007bff` | Primary color for the widget |
| `data-title` | `Chat with us` | Chat window title |
| `data-subtitle` | `We're here to help!` | Chat window subtitle |
| `data-theme` | `default` | Widget theme (currently only default) |
| `data-auto-init` | `true` | Auto-initialize widget on page load |

## 🔧 Manual Initialization

You can also initialize the widget manually:

```html
<script src="http://localhost:5000/chatbot-widget.js" data-auto-init="false"></script>
<script>
    // Initialize manually with custom config
    BotNexusChatbot.init({
        apiUrl: 'http://localhost:5000',
        position: 'bottom-left',
        primaryColor: '#28a745',
        title: 'Support Chat',
        subtitle: 'How can we help you today?'
    });
</script>
```

## 📱 Features

- ✅ Floating chat button
- ✅ Responsive chat window
- ✅ Customizable colors and position
- ✅ Auto-initialization from script attributes
- ✅ Manual initialization support
- ✅ High z-index to stay on top
- ✅ CSS isolation to prevent conflicts
- ✅ Mobile-friendly design
- ✅ Cross-origin support

## 🏗️ Building

To build the CDN widget:

```bash
cd client
npm run build:cdn
```

This will generate:
- `cdn/dist/chatbot-widget.js` - The main widget script
- `cdn/dist/chatbot-widget.css` - Widget styles (automatically included)

## 🌐 Deployment

### Self-hosted

1. Build the widget: `npm run build:cdn`
2. Upload `cdn/dist/chatbot-widget.js` to your CDN
3. Update the script src to point to your CDN URL

### Using with your backend

Make sure your backend:
1. Has CORS enabled for the domains where the widget will be embedded
2. Serves the chat API at `/api/chat/chatbot`
3. Accepts POST requests with `{ query: "user message" }`
4. Returns JSON with `{ response: "bot response" }`

## 🔒 Security Considerations

- The widget uses `origin: true` in CORS settings for maximum compatibility
- Consider implementing rate limiting on your chat API
- Validate and sanitize all user inputs on the backend
- Use HTTPS in production

## 📝 Example Integration

See `example.html` for a complete working example.

## 🐛 Troubleshooting

### Widget not appearing
- Check browser console for errors
- Ensure the script URL is accessible
- Verify CORS settings on your backend

### Chat not working
- Check if the API URL is correct
- Verify the backend is running and accessible
- Check network tab for failed requests

### Styling conflicts
- The widget uses high z-index values and inline styles to avoid conflicts
- If issues persist, check for CSS that might override the widget styles

## 📄 License

This project is part of the BotNexus system.