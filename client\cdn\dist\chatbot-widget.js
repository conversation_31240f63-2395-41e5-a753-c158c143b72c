/*! For license information please see chatbot-widget.js.LICENSE.txt */
!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.BotNexusChatbot=t():e.BotNexusChatbot=t()}(this,(()=>(()=>{"use strict";var e={221:(e,t,n)=>{var r=n(540);function a(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var l={d:{f:o,r:function(){throw Error(a(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},i=Symbol.for("react.portal"),s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function u(e,t){return"font"===e?"":"string"==typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(a(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=l.p;try{if(s.T=null,l.p=2,e)return e()}finally{s.T=t,l.p=n,l.d.f()}},t.preconnect=function(e,t){"string"==typeof e&&(t=t?"string"==typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:null,l.d.C(e,t))},t.prefetchDNS=function(e){"string"==typeof e&&l.d.D(e)},t.preinit=function(e,t){if("string"==typeof e&&t&&"string"==typeof t.as){var n=t.as,r=u(n,t.crossOrigin),a="string"==typeof t.integrity?t.integrity:void 0,o="string"==typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?l.d.S(e,"string"==typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:a,fetchPriority:o}):"script"===n&&l.d.X(e,{crossOrigin:r,integrity:a,fetchPriority:o,nonce:"string"==typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"==typeof e)if("object"==typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=u(t.as,t.crossOrigin);l.d.M(e,{crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0})}}else null==t&&l.d.M(e)},t.preload=function(e,t){if("string"==typeof e&&"object"==typeof t&&null!==t&&"string"==typeof t.as){var n=t.as,r=u(n,t.crossOrigin);l.d.L(e,n,{crossOrigin:r,integrity:"string"==typeof t.integrity?t.integrity:void 0,nonce:"string"==typeof t.nonce?t.nonce:void 0,type:"string"==typeof t.type?t.type:void 0,fetchPriority:"string"==typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"==typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"==typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"==typeof t.imageSizes?t.imageSizes:void 0,media:"string"==typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"==typeof e)if(t){var n=u(t.as,t.crossOrigin);l.d.m(e,{as:"string"==typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"==typeof t.integrity?t.integrity:void 0})}else l.d.m(e)},t.requestFormReset=function(e){l.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.1.0"},247:(e,t,n)=>{var r=n(982),a=n(540),o=n(961);function l(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function u(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function c(e){if(s(e)!==e)throw Error(l(188))}function d(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=d(e)))return t;e=e.sibling}return null}var f=Object.assign,p=Symbol.for("react.element"),m=Symbol.for("react.transitional.element"),g=Symbol.for("react.portal"),h=Symbol.for("react.fragment"),y=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),b=Symbol.for("react.provider"),w=Symbol.for("react.consumer"),k=Symbol.for("react.context"),S=Symbol.for("react.forward_ref"),E=Symbol.for("react.suspense"),x=Symbol.for("react.suspense_list"),C=Symbol.for("react.memo"),N=Symbol.for("react.lazy");Symbol.for("react.scope");var P=Symbol.for("react.activity");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var T=Symbol.for("react.memo_cache_sentinel");Symbol.for("react.view_transition");var O=Symbol.iterator;function R(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=O&&e[O]||e["@@iterator"])?e:null}var _=Symbol.for("react.client.reference");function A(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===_?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case h:return"Fragment";case v:return"Profiler";case y:return"StrictMode";case E:return"Suspense";case x:return"SuspenseList";case P:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case g:return"Portal";case k:return(e.displayName||"Context")+".Provider";case w:return(e._context.displayName||"Context")+".Consumer";case S:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case C:return null!==(t=e.displayName||null)?t:A(e.type)||"Memo";case N:t=e._payload,e=e._init;try{return A(e(t))}catch(e){}}return null}var L=Array.isArray,z=a.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,I={pending:!1,data:null,method:null,action:null},D=[],j=-1;function M(e){return{current:e}}function U(e){0>j||(e.current=D[j],D[j]=null,j--)}function B(e,t){j++,D[j]=e.current,e.current=t}var q=M(null),H=M(null),V=M(null),$=M(null);function W(e,t){switch(B(V,t),B(H,e),B(q,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ad(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=od(t=ad(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}U(q),B(q,e)}function Q(){U(q),U(H),U(V)}function K(e){null!==e.memoizedState&&B($,e);var t=q.current,n=od(t,e.type);t!==n&&(B(H,e),B(q,n))}function G(e){H.current===e&&(U(q),U(H)),$.current===e&&(U($),Kd._currentValue=I)}var X=Object.prototype.hasOwnProperty,Y=r.unstable_scheduleCallback,J=r.unstable_cancelCallback,Z=r.unstable_shouldYield,ee=r.unstable_requestPaint,te=r.unstable_now,ne=r.unstable_getCurrentPriorityLevel,re=r.unstable_ImmediatePriority,ae=r.unstable_UserBlockingPriority,oe=r.unstable_NormalPriority,le=r.unstable_LowPriority,ie=r.unstable_IdlePriority,se=r.log,ue=r.unstable_setDisableYieldValue,ce=null,de=null;function fe(e){if("function"==typeof se&&ue(e),de&&"function"==typeof de.setStrictMode)try{de.setStrictMode(ce,e)}catch(e){}}var pe=Math.clz32?Math.clz32:function(e){return 0==(e>>>=0)?32:31-(me(e)/ge|0)|0},me=Math.log,ge=Math.LN2,he=256,ye=4194304;function ve(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function be(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,o=e.suspendedLanes,l=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~o)?a=ve(r):0!==(l&=i)?a=ve(l):n||0!==(n=i&~e)&&(a=ve(n)):0!==(i=r&~o)?a=ve(i):0!==l?a=ve(l):n||0!==(n=r&~e)&&(a=ve(n)),0===a?0:0!==t&&t!==a&&0===(t&o)&&((o=a&-a)>=(n=t&-t)||32===o&&4194048&n)?t:a}function we(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function ke(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Se(){var e=he;return!(4194048&(he<<=1))&&(he=256),e}function Ee(){var e=ye;return!(62914560&(ye<<=1))&&(ye=4194304),e}function xe(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ce(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Ne(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Pe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function Te(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Oe(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function Re(){var e=F.p;return 0!==e?e:void 0===(e=window.event)?32:cf(e.type)}var _e=Math.random().toString(36).slice(2),Ae="__reactFiber$"+_e,Le="__reactProps$"+_e,ze="__reactContainer$"+_e,Fe="__reactEvents$"+_e,Ie="__reactListeners$"+_e,De="__reactHandles$"+_e,je="__reactResources$"+_e,Me="__reactMarker$"+_e;function Ue(e){delete e[Ae],delete e[Le],delete e[Fe],delete e[Ie],delete e[De]}function Be(e){var t=e[Ae];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ze]||n[Ae]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=bd(e);null!==e;){if(n=e[Ae])return n;e=bd(e)}return t}n=(e=n).parentNode}return null}function qe(e){if(e=e[Ae]||e[ze]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function He(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(l(33))}function Ve(e){var t=e[je];return t||(t=e[je]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[Me]=!0}var We=new Set,Qe={};function Ke(e,t){Ge(e,t),Ge(e+"Capture",t)}function Ge(e,t){for(Qe[e]=t,e=0;e<t.length;e++)We.add(t[e])}var Xe,Ye,Je=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Ze={},et={};function tt(e,t,n){if(a=t,X.call(et,a)||!X.call(Ze,a)&&(Je.test(a)?et[a]=!0:(Ze[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function nt(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function rt(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function at(e){if(void 0===Xe)try{throw Error()}catch(e){var t=e.stack.trim().match(/\n( *(at )?)/);Xe=t&&t[1]||"",Ye=-1<e.stack.indexOf("\n    at")?" (<anonymous>)":-1<e.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Xe+e+Ye}var ot=!1;function lt(e,t){if(!e||ot)return"";ot=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(e){var r=e}Reflect.construct(e,[],n)}else{try{n.call()}catch(e){r=e}e.call(n.prototype)}}else{try{throw Error()}catch(e){r=e}(n=e())&&"function"==typeof n.catch&&n.catch((function(){}))}}catch(e){if(e&&r&&"string"==typeof e.stack)return[e.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var o=r.DetermineComponentFrameRoot(),l=o[0],i=o[1];if(l&&i){var s=l.split("\n"),u=i.split("\n");for(a=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;a<u.length&&!u[a].includes("DetermineComponentFrameRoot");)a++;if(r===s.length||a===u.length)for(r=s.length-1,a=u.length-1;1<=r&&0<=a&&s[r]!==u[a];)a--;for(;1<=r&&0<=a;r--,a--)if(s[r]!==u[a]){if(1!==r||1!==a)do{if(r--,0>--a||s[r]!==u[a]){var c="\n"+s[r].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}}while(1<=r&&0<=a);break}}}finally{ot=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?at(n):""}function it(e){switch(e.tag){case 26:case 27:case 5:return at(e.type);case 16:return at("Lazy");case 13:return at("Suspense");case 19:return at("SuspenseList");case 0:case 15:return lt(e.type,!1);case 11:return lt(e.type.render,!1);case 1:return lt(e.type,!0);case 31:return at("Activity");default:return""}}function st(e){try{var t="";do{t+=it(e),e=e.return}while(e);return t}catch(e){return"\nError generating stack: "+e.message+"\n"+e.stack}}function ut(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ct(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ct(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ct(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var mt=/[\n"\\]/g;function gt(e){return e.replace(mt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function ht(e,t,n,r,a,o,l,i){e.name="",null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l?e.type=l:e.removeAttribute("type"),null!=t?"number"===l?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ut(t)):e.value!==""+ut(t)&&(e.value=""+ut(t)):"submit"!==l&&"reset"!==l||e.removeAttribute("value"),null!=t?vt(e,l,ut(t)):null!=n?vt(e,l,ut(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=o&&(e.defaultChecked=!!o),null!=a&&(e.checked=a&&"function"!=typeof a&&"symbol"!=typeof a),null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.name=""+ut(i):e.removeAttribute("name")}function yt(e,t,n,r,a,o,l,i){if(null!=o&&"function"!=typeof o&&"symbol"!=typeof o&&"boolean"!=typeof o&&(e.type=o),null!=t||null!=n){if(("submit"===o||"reset"===o)&&null==t)return;n=null!=n?""+ut(n):"",t=null!=t?""+ut(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:a)&&"symbol"!=typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l&&(e.name=l)}function vt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ut(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ut(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ut(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function kt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(l(92));if(L(r)){if(1<r.length)throw Error(l(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ut(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function St(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var Et=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function xt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||Et.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Ct(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(l(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var a in t)r=t[a],t.hasOwnProperty(a)&&n[a]!==r&&xt(e,a,r)}else for(var o in t)t.hasOwnProperty(o)&&xt(e,o,t[o])}function Nt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Pt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Tt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ot(e){return Tt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Rt=null;function _t(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var At=null,Lt=null;function zt(e){var t=qe(e);if(t&&(e=t.stateNode)){var n=e[Le]||null;e:switch(e=t.stateNode,t.type){case"input":if(ht(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+gt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=r[Le]||null;if(!a)throw Error(l(90));ht(r,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var Ft=!1;function It(e,t,n){if(Ft)return e(t,n);Ft=!0;try{return e(t)}finally{if(Ft=!1,(null!==At||null!==Lt)&&(Bu(),At&&(t=At,e=Lt,Lt=At=null,zt(t),e)))for(t=0;t<e.length;t++)zt(e[t])}}function Dt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Le]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(l(231,t,typeof n));return n}var jt=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Mt=!1;if(jt)try{var Ut={};Object.defineProperty(Ut,"passive",{get:function(){Mt=!0}}),window.addEventListener("test",Ut,Ut),window.removeEventListener("test",Ut,Ut)}catch(e){Mt=!1}var Bt=null,qt=null,Ht=null;function Vt(){if(Ht)return Ht;var e,t,n=qt,r=n.length,a="value"in Bt?Bt.value:Bt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[o-t];t++);return Ht=a.slice(e,1<t?1-t:void 0)}function $t(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Wt(){return!0}function Qt(){return!1}function Kt(e){function t(t,n,r,a,o){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Wt:Qt,this.isPropagationStopped=Qt,this}return f(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Wt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Wt)},persist:function(){},isPersistent:Wt}),t}var Gt,Xt,Yt,Jt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Kt(Jt),en=f({},Jt,{view:0,detail:0}),tn=Kt(en),nn=f({},en,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:mn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yt&&(Yt&&"mousemove"===e.type?(Gt=e.screenX-Yt.screenX,Xt=e.screenY-Yt.screenY):Xt=Gt=0,Yt=e),Gt)},movementY:function(e){return"movementY"in e?e.movementY:Xt}}),rn=Kt(nn),an=Kt(f({},nn,{dataTransfer:0})),on=Kt(f({},en,{relatedTarget:0})),ln=Kt(f({},Jt,{animationName:0,elapsedTime:0,pseudoElement:0})),sn=Kt(f({},Jt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),un=Kt(f({},Jt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},dn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function mn(){return pn}var gn=Kt(f({},en,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=$t(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?dn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:mn,charCode:function(e){return"keypress"===e.type?$t(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?$t(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),hn=Kt(f({},nn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),yn=Kt(f({},en,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:mn})),vn=Kt(f({},Jt,{propertyName:0,elapsedTime:0,pseudoElement:0})),bn=Kt(f({},nn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),wn=Kt(f({},Jt,{newState:0,oldState:0})),kn=[9,13,27,32],Sn=jt&&"CompositionEvent"in window,En=null;jt&&"documentMode"in document&&(En=document.documentMode);var xn=jt&&"TextEvent"in window&&!En,Cn=jt&&(!Sn||En&&8<En&&11>=En),Nn=String.fromCharCode(32),Pn=!1;function Tn(e,t){switch(e){case"keyup":return-1!==kn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function On(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Rn=!1,_n={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function An(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!_n[e.type]:"textarea"===t}function Ln(e,t,n,r){At?Lt?Lt.push(r):Lt=[r]:At=r,0<(t=Vc(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zn=null,Fn=null;function In(e){Ic(e,0)}function Dn(e){if(ft(He(e)))return e}function jn(e,t){if("change"===e)return t}var Mn=!1;if(jt){var Un;if(jt){var Bn="oninput"in document;if(!Bn){var qn=document.createElement("div");qn.setAttribute("oninput","return;"),Bn="function"==typeof qn.oninput}Un=Bn}else Un=!1;Mn=Un&&(!document.documentMode||9<document.documentMode)}function Hn(){zn&&(zn.detachEvent("onpropertychange",Vn),Fn=zn=null)}function Vn(e){if("value"===e.propertyName&&Dn(Fn)){var t=[];Ln(t,Fn,e,_t(e)),It(In,t)}}function $n(e,t,n){"focusin"===e?(Hn(),Fn=n,(zn=t).attachEvent("onpropertychange",Vn)):"focusout"===e&&Hn()}function Wn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Dn(Fn)}function Qn(e,t){if("click"===e)return Dn(t)}function Kn(e,t){if("input"===e||"change"===e)return Dn(t)}var Gn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Xn(e,t){if(Gn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!X.call(t,a)||!Gn(e[a],t[a]))return!1}return!0}function Yn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Jn(e,t){var n,r=Yn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Yn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function er(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(e){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function tr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var nr=jt&&"documentMode"in document&&11>=document.documentMode,rr=null,ar=null,or=null,lr=!1;function ir(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;lr||null==rr||rr!==pt(r)||(r="selectionStart"in(r=rr)&&tr(r)?{start:r.selectionStart,end:r.selectionEnd}:{anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},or&&Xn(or,r)||(or=r,0<(r=Vc(ar,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ur={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},cr={},dr={};function fr(e){if(cr[e])return cr[e];if(!ur[e])return e;var t,n=ur[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return cr[e]=n[t];return e}jt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete ur.animationend.animation,delete ur.animationiteration.animation,delete ur.animationstart.animation),"TransitionEvent"in window||delete ur.transitionend.transition);var pr=fr("animationend"),mr=fr("animationiteration"),gr=fr("animationstart"),hr=fr("transitionrun"),yr=fr("transitionstart"),vr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,kr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Sr(e,t){wr.set(e,t),Ke(t,[e])}kr.push("scrollEnd");var Er=new WeakMap;function xr(e,t){if("object"==typeof e&&null!==e){var n=Er.get(e);return void 0!==n?n:(t={value:e,source:t,stack:st(t)},Er.set(e,t),t)}return{value:e,source:t,stack:st(t)}}var Cr=[],Nr=0,Pr=0;function Tr(){for(var e=Nr,t=Pr=Nr=0;t<e;){var n=Cr[t];Cr[t++]=null;var r=Cr[t];Cr[t++]=null;var a=Cr[t];Cr[t++]=null;var o=Cr[t];if(Cr[t++]=null,null!==r&&null!==a){var l=r.pending;null===l?a.next=a:(a.next=l.next,l.next=a),r.pending=a}0!==o&&Ar(n,a,o)}}function Or(e,t,n,r){Cr[Nr++]=e,Cr[Nr++]=t,Cr[Nr++]=n,Cr[Nr++]=r,Pr|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Rr(e,t,n,r){return Or(e,t,n,r),Lr(e)}function _r(e,t){return Or(e,null,null,t),Lr(e)}function Ar(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,o=e.return;null!==o;)o.childLanes|=n,null!==(r=o.alternate)&&(r.childLanes|=n),22===o.tag&&(null===(e=o.stateNode)||1&e._visibility||(a=!0)),e=o,o=o.return;return 3===e.tag?(o=e.stateNode,a&&null!==t&&(a=31-pe(n),null===(r=(e=o.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),o):null}function Lr(e){if(50<Au)throw Au=0,Lu=null,Error(l(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var zr={};function Fr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ir(e,t,n,r){return new Fr(e,t,n,r)}function Dr(e){return!(!(e=e.prototype)||!e.isReactComponent)}function jr(e,t){var n=e.alternate;return null===n?((n=Ir(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Mr(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ur(e,t,n,r,a,o){var i=0;if(r=e,"function"==typeof e)Dr(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,q.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case P:return(e=Ir(31,n,t,a)).elementType=P,e.lanes=o,e;case h:return Br(n.children,a,o,t);case y:i=8,a|=24;break;case v:return(e=Ir(12,n,t,2|a)).elementType=v,e.lanes=o,e;case E:return(e=Ir(13,n,t,a)).elementType=E,e.lanes=o,e;case x:return(e=Ir(19,n,t,a)).elementType=x,e.lanes=o,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case b:case k:i=10;break e;case w:i=9;break e;case S:i=11;break e;case C:i=14;break e;case N:i=16,r=null;break e}i=29,n=Error(l(130,null===e?"null":typeof e,"")),r=null}return(t=Ir(i,n,t,a)).elementType=e,t.type=r,t.lanes=o,t}function Br(e,t,n,r){return(e=Ir(7,e,r,t)).lanes=n,e}function qr(e,t,n){return(e=Ir(6,e,null,t)).lanes=n,e}function Hr(e,t,n){return(t=Ir(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Vr=[],$r=0,Wr=null,Qr=0,Kr=[],Gr=0,Xr=null,Yr=1,Jr="";function Zr(e,t){Vr[$r++]=Qr,Vr[$r++]=Wr,Wr=e,Qr=t}function ea(e,t,n){Kr[Gr++]=Yr,Kr[Gr++]=Jr,Kr[Gr++]=Xr,Xr=e;var r=Yr;e=Jr;var a=32-pe(r)-1;r&=~(1<<a),n+=1;var o=32-pe(t)+a;if(30<o){var l=a-a%5;o=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Yr=1<<32-pe(t)+a|n<<a|r,Jr=o+e}else Yr=1<<o|n<<a|r,Jr=e}function ta(e){null!==e.return&&(Zr(e,1),ea(e,1,0))}function na(e){for(;e===Wr;)Wr=Vr[--$r],Vr[$r]=null,Qr=Vr[--$r],Vr[$r]=null;for(;e===Xr;)Xr=Kr[--Gr],Kr[Gr]=null,Jr=Kr[--Gr],Kr[Gr]=null,Yr=Kr[--Gr],Kr[Gr]=null}var ra=null,aa=null,oa=!1,la=null,ia=!1,sa=Error(l(519));function ua(e){throw ga(xr(Error(l(418,"")),e)),sa}function ca(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Ae]=e,t[Le]=r,n){case"dialog":Dc("cancel",t),Dc("close",t);break;case"iframe":case"object":case"embed":Dc("load",t);break;case"video":case"audio":for(n=0;n<zc.length;n++)Dc(zc[n],t);break;case"source":Dc("error",t);break;case"img":case"image":case"link":Dc("error",t),Dc("load",t);break;case"details":Dc("toggle",t);break;case"input":Dc("invalid",t),yt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":Dc("invalid",t);break;case"textarea":Dc("invalid",t),kt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Xc(t.textContent,n)?(null!=r.popover&&(Dc("beforetoggle",t),Dc("toggle",t)),null!=r.onScroll&&Dc("scroll",t),null!=r.onScrollEnd&&Dc("scrollend",t),null!=r.onClick&&(t.onclick=Yc),t=!0):t=!1,t||ua(e)}function da(e){for(ra=e.return;ra;)switch(ra.tag){case 5:case 13:return void(ia=!1);case 27:case 3:return void(ia=!0);default:ra=ra.return}}function fa(e){if(e!==ra)return!1;if(!oa)return da(e),oa=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||ld(e.type,e.memoizedProps)),t=!t),t&&aa&&ua(e),da(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){aa=yd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}aa=null}}else 27===n?(n=aa,pd(e.type)?(e=vd,vd=null,aa=e):aa=n):aa=ra?yd(e.stateNode.nextSibling):null;return!0}function pa(){aa=ra=null,oa=!1}function ma(){var e=la;return null!==e&&(null===bu?bu=e:bu.push.apply(bu,e),la=null),e}function ga(e){null===la?la=[e]:la.push(e)}var ha=M(null),ya=null,va=null;function ba(e,t,n){B(ha,t._currentValue),t._currentValue=n}function wa(e){e._currentValue=ha.current,U(ha)}function ka(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Sa(e,t,n,r){var a=e.child;for(null!==a&&(a.return=e);null!==a;){var o=a.dependencies;if(null!==o){var i=a.child;o=o.firstContext;e:for(;null!==o;){var s=o;o=a;for(var u=0;u<t.length;u++)if(s.context===t[u]){o.lanes|=n,null!==(s=o.alternate)&&(s.lanes|=n),ka(o.return,n,e),r||(i=null);break e}o=s.next}}else if(18===a.tag){if(null===(i=a.return))throw Error(l(341));i.lanes|=n,null!==(o=i.alternate)&&(o.lanes|=n),ka(i,n,e),i=null}else i=a.child;if(null!==i)i.return=a;else for(i=a;null!==i;){if(i===e){i=null;break}if(null!==(a=i.sibling)){a.return=i.return,i=a;break}i=i.return}a=i}}function Ea(e,t,n,r){e=null;for(var a=t,o=!1;null!==a;){if(!o)if(524288&a.flags)o=!0;else if(262144&a.flags)break;if(10===a.tag){var i=a.alternate;if(null===i)throw Error(l(387));if(null!==(i=i.memoizedProps)){var s=a.type;Gn(a.pendingProps.value,i.value)||(null!==e?e.push(s):e=[s])}}else if(a===$.current){if(null===(i=a.alternate))throw Error(l(387));i.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(null!==e?e.push(Kd):e=[Kd])}a=a.return}null!==e&&Sa(t,e,n,r),t.flags|=262144}function xa(e){for(e=e.firstContext;null!==e;){if(!Gn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ca(e){ya=e,va=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Na(e){return Ta(ya,e)}function Pa(e,t){return null===ya&&Ca(e),Ta(e,t)}function Ta(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===va){if(null===e)throw Error(l(308));va=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else va=va.next=t;return n}var Oa="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Ra=r.unstable_scheduleCallback,_a=r.unstable_NormalPriority,Aa={$$typeof:k,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function La(){return{controller:new Oa,data:new Map,refCount:0}}function za(e){e.refCount--,0===e.refCount&&Ra(_a,(function(){e.controller.abort()}))}var Fa=null,Ia=0,Da=0,ja=null;function Ma(){if(0===--Ia&&null!==Fa){null!==ja&&(ja.status="fulfilled");var e=Fa;Fa=null,Da=0,ja=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Ua=z.S;z.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Fa){var n=Fa=[];Ia=0,Da=Oc(),ja={status:"pending",value:void 0,then:function(e){n.push(e)}}}Ia++,t.then(Ma,Ma)}(0,t),null!==Ua&&Ua(e,t)};var Ba=M(null);function qa(){var e=Ba.current;return null!==e?e:ru.pooledCache}function Ha(e,t){B(Ba,null===t?Ba.current:t.pool)}function Va(){var e=qa();return null===e?null:{parent:Aa._currentValue,pool:e}}var $a=Error(l(460)),Wa=Error(l(474)),Qa=Error(l(542)),Ka={then:function(){}};function Ga(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Xa(){}function Ya(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Xa,Xa),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e;default:if("string"==typeof t.status)t.then(Xa,Xa);else{if(null!==(e=ru)&&100<e.shellSuspendCounter)throw Error(l(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":throw eo(e=t.reason),e}throw Ja=t,$a}}var Ja=null;function Za(){if(null===Ja)throw Error(l(459));var e=Ja;return Ja=null,e}function eo(e){if(e===$a||e===Qa)throw Error(l(483))}var to=!1;function no(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ro(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ao(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function oo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&nu){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Lr(e),Ar(e,null,n),t}return Or(e,r,t,n),Lr(e)}function lo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}function io(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var l={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===o?a=o=l:o=o.next=l,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var so=!1;function uo(){if(so&&null!==ja)throw ja}function co(e,t,n,r){so=!1;var a=e.updateQueue;to=!1;var o=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var s=i,u=s.next;s.next=null,null===l?o=u:l.next=u,l=s;var c=e.alternate;null!==c&&(i=(c=c.updateQueue).lastBaseUpdate)!==l&&(null===i?c.firstBaseUpdate=u:i.next=u,c.lastBaseUpdate=s)}if(null!==o){var d=a.baseState;for(l=0,c=u=s=null,i=o;;){var p=-536870913&i.lane,m=p!==i.lane;if(m?(ou&p)===p:(r&p)===p){0!==p&&p===Da&&(so=!0),null!==c&&(c=c.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var g=e,h=i;p=t;var y=n;switch(h.tag){case 1:if("function"==typeof(g=h.payload)){d=g.call(y,d,p);break e}d=g;break e;case 3:g.flags=-65537&g.flags|128;case 0:if(null==(p="function"==typeof(g=h.payload)?g.call(y,d,p):g))break e;d=f({},d,p);break e;case 2:to=!0}}null!==(p=i.callback)&&(e.flags|=64,m&&(e.flags|=8192),null===(m=a.callbacks)?a.callbacks=[p]:m.push(p))}else m={lane:p,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===c?(u=c=m,s=d):c=c.next=m,l|=p;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(m=i).next,m.next=null,a.lastBaseUpdate=m,a.shared.pending=null}}null===c&&(s=d),a.baseState=s,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null===o&&(a.shared.lanes=0),pu|=l,e.lanes=l,e.memoizedState=d}}function fo(e,t){if("function"!=typeof e)throw Error(l(191,e));e.call(t)}function po(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)fo(n[e],t)}var mo=M(null),go=M(0);function ho(e,t){B(go,e=du),B(mo,t),du=e|t.baseLanes}function yo(){B(go,du),B(mo,mo.current)}function vo(){du=go.current,U(mo),U(go)}var bo=0,wo=null,ko=null,So=null,Eo=!1,xo=!1,Co=!1,No=0,Po=0,To=null,Oo=0;function Ro(){throw Error(l(321))}function _o(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Gn(e[n],t[n]))return!1;return!0}function Ao(e,t,n,r,a,o){return bo=o,wo=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,z.H=null===e||null===e.memoizedState?Wl:Ql,Co=!1,o=n(r,a),Co=!1,xo&&(o=zo(t,n,r,a)),Lo(e),o}function Lo(e){z.H=$l;var t=null!==ko&&null!==ko.next;if(bo=0,So=ko=wo=null,Eo=!1,Po=0,To=null,t)throw Error(l(300));null===e||Pi||null!==(e=e.dependencies)&&xa(e)&&(Pi=!0)}function zo(e,t,n,r){wo=e;var a=0;do{if(xo&&(To=null),Po=0,xo=!1,25<=a)throw Error(l(301));if(a+=1,So=ko=null,null!=e.updateQueue){var o=e.updateQueue;o.lastEffect=null,o.events=null,o.stores=null,null!=o.memoCache&&(o.memoCache.index=0)}z.H=Kl,o=t(n,r)}while(xo);return o}function Fo(){var e=z.H,t=e.useState()[0];return t="function"==typeof t.then?Bo(t):t,e=e.useState()[0],(null!==ko?ko.memoizedState:null)!==e&&(wo.flags|=1024),t}function Io(){var e=0!==No;return No=0,e}function Do(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function jo(e){if(Eo){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Eo=!1}bo=0,So=ko=wo=null,xo=!1,Po=No=0,To=null}function Mo(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===So?wo.memoizedState=So=e:So=So.next=e,So}function Uo(){if(null===ko){var e=wo.alternate;e=null!==e?e.memoizedState:null}else e=ko.next;var t=null===So?wo.memoizedState:So.next;if(null!==t)So=t,ko=e;else{if(null===e){if(null===wo.alternate)throw Error(l(467));throw Error(l(310))}e={memoizedState:(ko=e).memoizedState,baseState:ko.baseState,baseQueue:ko.baseQueue,queue:ko.queue,next:null},null===So?wo.memoizedState=So=e:So=So.next=e}return So}function Bo(e){var t=Po;return Po+=1,null===To&&(To=[]),e=Ya(To,e,t),t=wo,null===(null===So?t.memoizedState:So.next)&&(t=t.alternate,z.H=null===t||null===t.memoizedState?Wl:Ql),e}function qo(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Bo(e);if(e.$$typeof===k)return Na(e)}throw Error(l(438,String(e)))}function Ho(e){var t=null,n=wo.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=wo.alternate;null!==r&&null!==(r=r.updateQueue)&&null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=T;return t.index++,n}function Vo(e,t){return"function"==typeof t?t(e):t}function $o(e){return Wo(Uo(),ko,e)}function Wo(e,t,n){var r=e.queue;if(null===r)throw Error(l(311));r.lastRenderedReducer=n;var a=e.baseQueue,o=r.pending;if(null!==o){if(null!==a){var i=a.next;a.next=o.next,o.next=i}t.baseQueue=a=o,r.pending=null}if(o=e.baseState,null===a)e.memoizedState=o;else{var s=i=null,u=null,c=t=a.next,d=!1;do{var f=-536870913&c.lane;if(f!==c.lane?(ou&f)===f:(bo&f)===f){var p=c.revertLane;if(0===p)null!==u&&(u=u.next={lane:0,revertLane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),f===Da&&(d=!0);else{if((bo&p)===p){c=c.next,p===Da&&(d=!0);continue}f={lane:0,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=f,i=o):u=u.next=f,wo.lanes|=p,pu|=p}f=c.action,Co&&n(o,f),o=c.hasEagerState?c.eagerState:n(o,f)}else p={lane:f,revertLane:c.revertLane,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null},null===u?(s=u=p,i=o):u=u.next=p,wo.lanes|=f,pu|=f;c=c.next}while(null!==c&&c!==t);if(null===u?i=o:u.next=s,!Gn(o,e.memoizedState)&&(Pi=!0,d&&null!==(n=ja)))throw n;e.memoizedState=o,e.baseState=i,e.baseQueue=u,r.lastRenderedState=o}return null===a&&(r.lanes=0),[e.memoizedState,r.dispatch]}function Qo(e){var t=Uo(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,o=t.memoizedState;if(null!==a){n.pending=null;var i=a=a.next;do{o=e(o,i.action),i=i.next}while(i!==a);Gn(o,t.memoizedState)||(Pi=!0),t.memoizedState=o,null===t.baseQueue&&(t.baseState=o),n.lastRenderedState=o}return[o,r]}function Ko(e,t,n){var r=wo,a=Uo(),o=oa;if(o){if(void 0===n)throw Error(l(407));n=n()}else n=t();var i=!Gn((ko||a).memoizedState,n);if(i&&(a.memoizedState=n,Pi=!0),a=a.queue,yl(2048,8,Yo.bind(null,r,a,e),[e]),a.getSnapshot!==t||i||null!==So&&1&So.memoizedState.tag){if(r.flags|=2048,ml(9,{destroy:void 0,resource:void 0},Xo.bind(null,r,a,n,t),null),null===ru)throw Error(l(349));o||124&bo||Go(r,t,n)}return n}function Go(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=wo.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Xo(e,t,n,r){t.value=n,t.getSnapshot=r,Jo(t)&&Zo(e)}function Yo(e,t,n){return n((function(){Jo(t)&&Zo(e)}))}function Jo(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Gn(e,n)}catch(e){return!0}}function Zo(e){var t=_r(e,2);null!==t&&Iu(t,0,2)}function el(e){var t=Mo();if("function"==typeof e){var n=e;if(e=n(),Co){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vo,lastRenderedState:e},t}function tl(e,t,n,r){return e.baseState=n,Wo(e,ko,"function"==typeof r?r:Vo)}function nl(e,t,n,r,a){if(ql(e))throw Error(l(485));if(null!==(e=t.action)){var o={payload:a,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){o.listeners.push(e)}};null!==z.T?n(!0):o.isTransition=!1,r(o),null===(n=t.pending)?(o.next=t.pending=o,rl(t,o)):(o.next=n.next,t.pending=n.next=o)}}function rl(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var o=z.T,l={};z.T=l;try{var i=n(a,r),s=z.S;null!==s&&s(l,i),al(e,t,i)}catch(n){ll(e,t,n)}finally{z.T=o}}else try{al(e,t,o=n(a,r))}catch(n){ll(e,t,n)}}function al(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then((function(n){ol(e,t,n)}),(function(n){return ll(e,t,n)})):ol(e,t,n)}function ol(e,t,n){t.status="fulfilled",t.value=n,il(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,rl(e,n)))}function ll(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,il(t),t=t.next}while(t!==r)}e.action=null}function il(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function sl(e,t){return t}function ul(e,t){if(oa){var n=ru.formState;if(null!==n){e:{var r=wo;if(oa){if(aa){t:{for(var a=aa,o=ia;8!==a.nodeType;){if(!o){a=null;break t}if(null===(a=yd(a.nextSibling))){a=null;break t}}a="F!"===(o=a.data)||"F"===o?a:null}if(a){aa=yd(a.nextSibling),r="F!"===a.data;break e}}ua(r)}r=!1}r&&(t=n[0])}}return(n=Mo()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:sl,lastRenderedState:t},n.queue=r,n=Ml.bind(null,wo,r),r.dispatch=n,r=el(!1),o=Bl.bind(null,wo,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Mo()).queue=a,n=nl.bind(null,wo,a,o,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function cl(e){return dl(Uo(),ko,e)}function dl(e,t,n){if(t=Wo(e,t,sl)[0],e=$o(Vo)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Bo(t)}catch(e){if(e===$a)throw Qa;throw e}else r=t;var a=(t=Uo()).queue,o=a.dispatch;return n!==t.memoizedState&&(wo.flags|=2048,ml(9,{destroy:void 0,resource:void 0},fl.bind(null,a,n),null)),[r,o,e]}function fl(e,t){e.action=t}function pl(e){var t=Uo(),n=ko;if(null!==n)return dl(t,n,e);Uo(),t=t.memoizedState;var r=(n=Uo()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function ml(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=wo.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},wo.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function gl(){return Uo().memoizedState}function hl(e,t,n,r){var a=Mo();r=void 0===r?null:r,wo.flags|=e,a.memoizedState=ml(1|t,{destroy:void 0,resource:void 0},n,r)}function yl(e,t,n,r){var a=Uo();r=void 0===r?null:r;var o=a.memoizedState.inst;null!==ko&&null!==r&&_o(r,ko.memoizedState.deps)?a.memoizedState=ml(t,o,n,r):(wo.flags|=e,a.memoizedState=ml(1|t,o,n,r))}function vl(e,t){hl(8390656,8,e,t)}function bl(e,t){yl(2048,8,e,t)}function wl(e,t){return yl(4,2,e,t)}function kl(e,t){return yl(4,4,e,t)}function Sl(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function El(e,t,n){n=null!=n?n.concat([e]):null,yl(4,4,Sl.bind(null,t,e),n)}function xl(){}function Cl(e,t){var n=Uo();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&_o(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Nl(e,t){var n=Uo();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&_o(t,r[1]))return r[0];if(r=e(),Co){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Pl(e,t,n){return void 0===n||1073741824&bo?e.memoizedState=t:(e.memoizedState=n,e=Fu(),wo.lanes|=e,pu|=e,n)}function Tl(e,t,n,r){return Gn(n,t)?n:null!==mo.current?(e=Pl(e,n,r),Gn(e,t)||(Pi=!0),e):42&bo?(e=Fu(),wo.lanes|=e,pu|=e,t):(Pi=!0,e.memoizedState=n)}function Ol(e,t,n,r,a){var o=F.p;F.p=0!==o&&8>o?o:8;var l,i,s,u=z.T,c={};z.T=c,Bl(e,!1,t,n);try{var d=a(),f=z.S;null!==f&&f(c,d),null!==d&&"object"==typeof d&&"function"==typeof d.then?Ul(e,t,(l=r,i=[],s={status:"pending",value:null,reason:null,then:function(e){i.push(e)}},d.then((function(){s.status="fulfilled",s.value=l;for(var e=0;e<i.length;e++)(0,i[e])(l)}),(function(e){for(s.status="rejected",s.reason=e,e=0;e<i.length;e++)(0,i[e])(void 0)})),s),zu()):Ul(e,t,r,zu())}catch(n){Ul(e,t,{then:function(){},status:"rejected",reason:n},zu())}finally{F.p=o,z.T=u}}function Rl(){}function _l(e,t,n,r){if(5!==e.tag)throw Error(l(476));var a=Al(e).queue;Ol(e,a,t,I,null===n?Rl:function(){return Ll(e),n(r)})}function Al(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:I,baseState:I,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vo,lastRenderedState:I},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vo,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ll(e){Ul(e,Al(e).next.queue,{},zu())}function zl(){return Na(Kd)}function Fl(){return Uo().memoizedState}function Il(){return Uo().memoizedState}function Dl(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=zu(),r=oo(t,e=ao(n),n);return null!==r&&(Iu(r,0,n),lo(r,t,n)),t={cache:La()},void(e.payload=t)}t=t.return}}function jl(e,t,n){var r=zu();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ql(e)?Hl(t,n):null!==(n=Rr(e,t,n,r))&&(Iu(n,0,r),Vl(n,t,r))}function Ml(e,t,n){Ul(e,t,n,zu())}function Ul(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ql(e))Hl(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=o(l,n);if(a.hasEagerState=!0,a.eagerState=i,Gn(i,l))return Or(e,t,a,0),null===ru&&Tr(),!1}catch(e){}if(null!==(n=Rr(e,t,a,r)))return Iu(n,0,r),Vl(n,t,r),!0}return!1}function Bl(e,t,n,r){if(r={lane:2,revertLane:Oc(),action:r,hasEagerState:!1,eagerState:null,next:null},ql(e)){if(t)throw Error(l(479))}else null!==(t=Rr(e,n,r,2))&&Iu(t,0,2)}function ql(e){var t=e.alternate;return e===wo||null!==t&&t===wo}function Hl(e,t){xo=Eo=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Vl(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Pe(e,n)}}var $l={readContext:Na,use:qo,useCallback:Ro,useContext:Ro,useEffect:Ro,useImperativeHandle:Ro,useLayoutEffect:Ro,useInsertionEffect:Ro,useMemo:Ro,useReducer:Ro,useRef:Ro,useState:Ro,useDebugValue:Ro,useDeferredValue:Ro,useTransition:Ro,useSyncExternalStore:Ro,useId:Ro,useHostTransitionStatus:Ro,useFormState:Ro,useActionState:Ro,useOptimistic:Ro,useMemoCache:Ro,useCacheRefresh:Ro},Wl={readContext:Na,use:qo,useCallback:function(e,t){return Mo().memoizedState=[e,void 0===t?null:t],e},useContext:Na,useEffect:vl,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,hl(4194308,4,Sl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return hl(4194308,4,e,t)},useInsertionEffect:function(e,t){hl(4,2,e,t)},useMemo:function(e,t){var n=Mo();t=void 0===t?null:t;var r=e();if(Co){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Mo();if(void 0!==n){var a=n(t);if(Co){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=jl.bind(null,wo,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Mo().memoizedState=e},useState:function(e){var t=(e=el(e)).queue,n=Ml.bind(null,wo,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:xl,useDeferredValue:function(e,t){return Pl(Mo(),e,t)},useTransition:function(){var e=el(!1);return e=Ol.bind(null,wo,e.queue,!0,!1),Mo().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=wo,a=Mo();if(oa){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===ru)throw Error(l(349));124&ou||Go(r,t,n)}a.memoizedState=n;var o={value:n,getSnapshot:t};return a.queue=o,vl(Yo.bind(null,r,o,e),[e]),r.flags|=2048,ml(9,{destroy:void 0,resource:void 0},Xo.bind(null,r,o,n,t),null),n},useId:function(){var e=Mo(),t=ru.identifierPrefix;if(oa){var n=Jr;t="«"+t+"R"+(n=(Yr&~(1<<32-pe(Yr)-1)).toString(32)+n),0<(n=No++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=Oo++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:zl,useFormState:ul,useActionState:ul,useOptimistic:function(e){var t=Mo();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Bl.bind(null,wo,!0,n),n.dispatch=t,[e,t]},useMemoCache:Ho,useCacheRefresh:function(){return Mo().memoizedState=Dl.bind(null,wo)}},Ql={readContext:Na,use:qo,useCallback:Cl,useContext:Na,useEffect:bl,useImperativeHandle:El,useInsertionEffect:wl,useLayoutEffect:kl,useMemo:Nl,useReducer:$o,useRef:gl,useState:function(){return $o(Vo)},useDebugValue:xl,useDeferredValue:function(e,t){return Tl(Uo(),ko.memoizedState,e,t)},useTransition:function(){var e=$o(Vo)[0],t=Uo().memoizedState;return["boolean"==typeof e?e:Bo(e),t]},useSyncExternalStore:Ko,useId:Fl,useHostTransitionStatus:zl,useFormState:cl,useActionState:cl,useOptimistic:function(e,t){return tl(Uo(),0,e,t)},useMemoCache:Ho,useCacheRefresh:Il},Kl={readContext:Na,use:qo,useCallback:Cl,useContext:Na,useEffect:bl,useImperativeHandle:El,useInsertionEffect:wl,useLayoutEffect:kl,useMemo:Nl,useReducer:Qo,useRef:gl,useState:function(){return Qo(Vo)},useDebugValue:xl,useDeferredValue:function(e,t){var n=Uo();return null===ko?Pl(n,e,t):Tl(n,ko.memoizedState,e,t)},useTransition:function(){var e=Qo(Vo)[0],t=Uo().memoizedState;return["boolean"==typeof e?e:Bo(e),t]},useSyncExternalStore:Ko,useId:Fl,useHostTransitionStatus:zl,useFormState:pl,useActionState:pl,useOptimistic:function(e,t){var n=Uo();return null!==ko?tl(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Ho,useCacheRefresh:Il},Gl=null,Xl=0;function Yl(e){var t=Xl;return Xl+=1,null===Gl&&(Gl=[]),Ya(Gl,e,t)}function Jl(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function Zl(e,t){if(t.$$typeof===p)throw Error(l(525));throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ei(e){return(0,e._init)(e._payload)}function ti(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function a(e,t){return(e=jr(e,t)).index=0,e.sibling=null,e}function o(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=qr(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===h?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"==typeof o&&null!==o&&o.$$typeof===N&&ei(o)===t.type)?(Jl(t=a(t,n.props),n),t.return=e,t):(Jl(t=Ur(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Hr(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=Br(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=qr(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case m:return Jl(n=Ur(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case g:return(t=Hr(t,e.mode,n)).return=e,t;case N:return f(e,t=(0,t._init)(t._payload),n)}if(L(t)||R(t))return(t=Br(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return f(e,Yl(t),n);if(t.$$typeof===k)return f(e,Pa(e,t),n);Zl(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==a?null:s(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case m:return n.key===a?u(e,t,n,r):null;case g:return n.key===a?c(e,t,n,r):null;case N:return p(e,t,n=(a=n._init)(n._payload),r)}if(L(n)||R(n))return null!==a?null:d(e,t,n,r,null);if("function"==typeof n.then)return p(e,t,Yl(n),r);if(n.$$typeof===k)return p(e,t,Pa(e,n),r);Zl(e,n)}return null}function y(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return s(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case m:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case g:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case N:return y(e,t,n,r=(0,r._init)(r._payload),a)}if(L(r)||R(r))return d(t,e=e.get(n)||null,r,a,null);if("function"==typeof r.then)return y(e,t,n,Yl(r),a);if(r.$$typeof===k)return y(e,t,n,Pa(t,r),a);Zl(t,r)}return null}function v(s,u,c,d){if("object"==typeof c&&null!==c&&c.type===h&&null===c.key&&(c=c.props.children),"object"==typeof c&&null!==c){switch(c.$$typeof){case m:e:{for(var b=c.key;null!==u;){if(u.key===b){if((b=c.type)===h){if(7===u.tag){n(s,u.sibling),(d=a(u,c.props.children)).return=s,s=d;break e}}else if(u.elementType===b||"object"==typeof b&&null!==b&&b.$$typeof===N&&ei(b)===u.type){n(s,u.sibling),Jl(d=a(u,c.props),c),d.return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}c.type===h?((d=Br(c.props.children,s.mode,d,c.key)).return=s,s=d):(Jl(d=Ur(c.type,c.key,c.props,null,s.mode,d),c),d.return=s,s=d)}return i(s);case g:e:{for(b=c.key;null!==u;){if(u.key===b){if(4===u.tag&&u.stateNode.containerInfo===c.containerInfo&&u.stateNode.implementation===c.implementation){n(s,u.sibling),(d=a(u,c.children||[])).return=s,s=d;break e}n(s,u);break}t(s,u),u=u.sibling}(d=Hr(c,s.mode,d)).return=s,s=d}return i(s);case N:return v(s,u,c=(b=c._init)(c._payload),d)}if(L(c))return function(a,l,i,s){for(var u=null,c=null,d=l,m=l=0,g=null;null!==d&&m<i.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var h=p(a,d,i[m],s);if(null===h){null===d&&(d=g);break}e&&d&&null===h.alternate&&t(a,d),l=o(h,l,m),null===c?u=h:c.sibling=h,c=h,d=g}if(m===i.length)return n(a,d),oa&&Zr(a,m),u;if(null===d){for(;m<i.length;m++)null!==(d=f(a,i[m],s))&&(l=o(d,l,m),null===c?u=d:c.sibling=d,c=d);return oa&&Zr(a,m),u}for(d=r(d);m<i.length;m++)null!==(g=y(d,a,m,i[m],s))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),l=o(g,l,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(a,e)})),oa&&Zr(a,m),u}(s,u,c,d);if(R(c)){if("function"!=typeof(b=R(c)))throw Error(l(150));return function(a,i,s,u){if(null==s)throw Error(l(151));for(var c=null,d=null,m=i,g=i=0,h=null,v=s.next();null!==m&&!v.done;g++,v=s.next()){m.index>g?(h=m,m=null):h=m.sibling;var b=p(a,m,v.value,u);if(null===b){null===m&&(m=h);break}e&&m&&null===b.alternate&&t(a,m),i=o(b,i,g),null===d?c=b:d.sibling=b,d=b,m=h}if(v.done)return n(a,m),oa&&Zr(a,g),c;if(null===m){for(;!v.done;g++,v=s.next())null!==(v=f(a,v.value,u))&&(i=o(v,i,g),null===d?c=v:d.sibling=v,d=v);return oa&&Zr(a,g),c}for(m=r(m);!v.done;g++,v=s.next())null!==(v=y(m,a,g,v.value,u))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),i=o(v,i,g),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach((function(e){return t(a,e)})),oa&&Zr(a,g),c}(s,u,c=b.call(c),d)}if("function"==typeof c.then)return v(s,u,Yl(c),d);if(c.$$typeof===k)return v(s,u,Pa(s,c),d);Zl(s,c)}return"string"==typeof c&&""!==c||"number"==typeof c||"bigint"==typeof c?(c=""+c,null!==u&&6===u.tag?(n(s,u.sibling),(d=a(u,c)).return=s,s=d):(n(s,u),(d=qr(c,s.mode,d)).return=s,s=d),i(s)):n(s,u)}return function(e,t,n,r){try{Xl=0;var a=v(e,t,n,r);return Gl=null,a}catch(t){if(t===$a||t===Qa)throw t;var o=Ir(29,t,null,e.mode);return o.lanes=r,o.return=e,o}}}var ni=ti(!0),ri=ti(!1),ai=M(null),oi=null;function li(e){var t=e.alternate;B(ci,1&ci.current),B(ai,e),null===oi&&(null===t||null!==mo.current||null!==t.memoizedState)&&(oi=e)}function ii(e){if(22===e.tag){if(B(ci,ci.current),B(ai,e),null===oi){var t=e.alternate;null!==t&&null!==t.memoizedState&&(oi=e)}}else si()}function si(){B(ci,ci.current),B(ai,ai.current)}function ui(e){U(ai),oi===e&&(oi=null),U(ci)}var ci=M(0);function di(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||hd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function fi(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:f({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var pi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=zu(),a=ao(r);a.payload=t,null!=n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Iu(t,0,r),lo(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=zu(),a=ao(r);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=oo(e,a,r))&&(Iu(t,0,r),lo(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=zu(),r=ao(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=oo(e,r,n))&&(Iu(t,0,n),lo(t,e,n))}};function mi(e,t,n,r,a,o,l){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,l):!(t.prototype&&t.prototype.isPureReactComponent&&Xn(n,r)&&Xn(a,o))}function gi(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&pi.enqueueReplaceState(t,t.state,null)}function hi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=f({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var yi="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function vi(e){yi(e)}function bi(e){console.error(e)}function wi(e){yi(e)}function ki(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(e){setTimeout((function(){throw e}))}}function Si(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(e){setTimeout((function(){throw e}))}}function Ei(e,t,n){return(n=ao(n)).tag=3,n.payload={element:null},n.callback=function(){ki(e,t)},n}function xi(e){return(e=ao(e)).tag=3,e}function Ci(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"==typeof a){var o=r.value;e.payload=function(){return a(o)},e.callback=function(){Si(t,n,r)}}var l=n.stateNode;null!==l&&"function"==typeof l.componentDidCatch&&(e.callback=function(){Si(t,n,r),"function"!=typeof a&&(null===xu?xu=new Set([this]):xu.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Ni=Error(l(461)),Pi=!1;function Ti(e,t,n,r){t.child=null===e?ri(t,null,n,r):ni(t,e.child,n,r)}function Oi(e,t,n,r,a){n=n.render;var o=t.ref;if("ref"in r){var l={};for(var i in r)"ref"!==i&&(l[i]=r[i])}else l=r;return Ca(t),r=Ao(e,t,n,l,o,a),i=Io(),null===e||Pi?(oa&&i&&ta(t),t.flags|=1,Ti(e,t,r,a),t.child):(Do(e,t,a),Gi(e,t,a))}function Ri(e,t,n,r,a){if(null===e){var o=n.type;return"function"!=typeof o||Dr(o)||void 0!==o.defaultProps||null!==n.compare?((e=Ur(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,_i(e,t,o,r,a))}if(o=e.child,!Xi(e,a)){var l=o.memoizedProps;if((n=null!==(n=n.compare)?n:Xn)(l,r)&&e.ref===t.ref)return Gi(e,t,a)}return t.flags|=1,(e=jr(o,r)).ref=t.ref,e.return=t,t.child=e}function _i(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(Xn(o,r)&&e.ref===t.ref){if(Pi=!1,t.pendingProps=r=o,!Xi(e,a))return t.lanes=e.lanes,Gi(e,t,a);131072&e.flags&&(Pi=!0)}}return Fi(e,t,n,r,a)}function Ai(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==o?o.baseLanes|n:n,null!==e){for(a=t.child=e.child,o=0;null!==a;)o=o|a.lanes|a.childLanes,a=a.sibling;t.childLanes=o&~r}else t.childLanes=0,t.child=null;return Li(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Li(e,t,null!==o?o.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Ha(0,null!==o?o.cachePool:null),null!==o?ho(t,o):yo(),ii(t)}else null!==o?(Ha(0,o.cachePool),ho(t,o),si(),t.memoizedState=null):(null!==e&&Ha(0,null),yo(),si());return Ti(e,t,a,n),t.child}function Li(e,t,n,r){var a=qa();return a=null===a?null:{parent:Aa._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Ha(0,null),yo(),ii(t),null!==e&&Ea(e,t,r,!0),null}function zi(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(l(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Fi(e,t,n,r,a){return Ca(t),n=Ao(e,t,n,r,void 0,a),r=Io(),null===e||Pi?(oa&&r&&ta(t),t.flags|=1,Ti(e,t,n,a),t.child):(Do(e,t,a),Gi(e,t,a))}function Ii(e,t,n,r,a,o){return Ca(t),t.updateQueue=null,n=zo(t,r,n,a),Lo(e),r=Io(),null===e||Pi?(oa&&r&&ta(t),t.flags|=1,Ti(e,t,n,o),t.child):(Do(e,t,o),Gi(e,t,o))}function Di(e,t,n,r,a){if(Ca(t),null===t.stateNode){var o=zr,l=n.contextType;"object"==typeof l&&null!==l&&(o=Na(l)),o=new n(r,o),t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,o.updater=pi,t.stateNode=o,o._reactInternals=t,(o=t.stateNode).props=r,o.state=t.memoizedState,o.refs={},no(t),l=n.contextType,o.context="object"==typeof l&&null!==l?Na(l):zr,o.state=t.memoizedState,"function"==typeof(l=n.getDerivedStateFromProps)&&(fi(t,n,l,r),o.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof o.getSnapshotBeforeUpdate||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||(l=o.state,"function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),l!==o.state&&pi.enqueueReplaceState(o,o.state,null),co(t,r,o,a),uo(),o.state=t.memoizedState),"function"==typeof o.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){o=t.stateNode;var i=t.memoizedProps,s=hi(n,i);o.props=s;var u=o.context,c=n.contextType;l=zr,"object"==typeof c&&null!==c&&(l=Na(c));var d=n.getDerivedStateFromProps;c="function"==typeof d||"function"==typeof o.getSnapshotBeforeUpdate,i=t.pendingProps!==i,c||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(i||u!==l)&&gi(t,o,r,l),to=!1;var f=t.memoizedState;o.state=f,co(t,r,o,a),uo(),u=t.memoizedState,i||f!==u||to?("function"==typeof d&&(fi(t,n,d,r),u=t.memoizedState),(s=to||mi(t,n,s,r,f,u,l))?(c||"function"!=typeof o.UNSAFE_componentWillMount&&"function"!=typeof o.componentWillMount||("function"==typeof o.componentWillMount&&o.componentWillMount(),"function"==typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount()),"function"==typeof o.componentDidMount&&(t.flags|=4194308)):("function"==typeof o.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=u),o.props=r,o.state=u,o.context=l,r=s):("function"==typeof o.componentDidMount&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,ro(e,t),c=hi(n,l=t.memoizedProps),o.props=c,d=t.pendingProps,f=o.context,u=n.contextType,s=zr,"object"==typeof u&&null!==u&&(s=Na(u)),(u="function"==typeof(i=n.getDerivedStateFromProps)||"function"==typeof o.getSnapshotBeforeUpdate)||"function"!=typeof o.UNSAFE_componentWillReceiveProps&&"function"!=typeof o.componentWillReceiveProps||(l!==d||f!==s)&&gi(t,o,r,s),to=!1,f=t.memoizedState,o.state=f,co(t,r,o,a),uo();var p=t.memoizedState;l!==d||f!==p||to||null!==e&&null!==e.dependencies&&xa(e.dependencies)?("function"==typeof i&&(fi(t,n,i,r),p=t.memoizedState),(c=to||mi(t,n,c,r,f,p,s)||null!==e&&null!==e.dependencies&&xa(e.dependencies))?(u||"function"!=typeof o.UNSAFE_componentWillUpdate&&"function"!=typeof o.componentWillUpdate||("function"==typeof o.componentWillUpdate&&o.componentWillUpdate(r,p,s),"function"==typeof o.UNSAFE_componentWillUpdate&&o.UNSAFE_componentWillUpdate(r,p,s)),"function"==typeof o.componentDidUpdate&&(t.flags|=4),"function"==typeof o.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof o.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),o.props=r,o.state=p,o.context=s,r=c):("function"!=typeof o.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof o.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return o=r,zi(e,t),r=!!(128&t.flags),o||r?(o=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:o.render(),t.flags|=1,null!==e&&r?(t.child=ni(t,e.child,null,a),t.child=ni(t,null,n,a)):Ti(e,t,n,a),t.memoizedState=o.state,e=t.child):e=Gi(e,t,a),e}function ji(e,t,n,r){return pa(),t.flags|=256,Ti(e,t,n,r),t.child}var Mi={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ui(e){return{baseLanes:e,cachePool:Va()}}function Bi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=hu),e}function qi(e,t,n){var r,a=t.pendingProps,o=!1,i=!!(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&!!(2&ci.current)),r&&(o=!0,t.flags&=-129),r=!!(32&t.flags),t.flags&=-33,null===e){if(oa){if(o?li(t):si(),oa){var s,u=aa;if(s=u){e:{for(s=u,u=ia;8!==s.nodeType;){if(!u){u=null;break e}if(null===(s=yd(s.nextSibling))){u=null;break e}}u=s}null!==u?(t.memoizedState={dehydrated:u,treeContext:null!==Xr?{id:Yr,overflow:Jr}:null,retryLane:536870912,hydrationErrors:null},(s=Ir(18,null,null,0)).stateNode=u,s.return=t,t.child=s,ra=t,aa=null,s=!0):s=!1}s||ua(t)}if(null!==(u=t.memoizedState)&&null!==(u=u.dehydrated))return hd(u)?t.lanes=32:t.lanes=536870912,null;ui(t)}return u=a.children,a=a.fallback,o?(si(),u=Vi({mode:"hidden",children:u},o=t.mode),a=Br(a,o,n,null),u.return=t,a.return=t,u.sibling=a,t.child=u,(o=t.child).memoizedState=Ui(n),o.childLanes=Bi(e,r,n),t.memoizedState=Mi,a):(li(t),Hi(t,u))}if(null!==(s=e.memoizedState)&&null!==(u=s.dehydrated)){if(i)256&t.flags?(li(t),t.flags&=-257,t=$i(e,t,n)):null!==t.memoizedState?(si(),t.child=e.child,t.flags|=128,t=null):(si(),o=a.fallback,u=t.mode,a=Vi({mode:"visible",children:a.children},u),(o=Br(o,u,n,null)).flags|=2,a.return=t,o.return=t,a.sibling=o,t.child=a,ni(t,e.child,null,n),(a=t.child).memoizedState=Ui(n),a.childLanes=Bi(e,r,n),t.memoizedState=Mi,t=o);else if(li(t),hd(u)){if(r=u.nextSibling&&u.nextSibling.dataset)var c=r.dgst;r=c,(a=Error(l(419))).stack="",a.digest=r,ga({value:a,source:null,stack:null}),t=$i(e,t,n)}else if(Pi||Ea(e,t,n,!1),r=0!==(n&e.childLanes),Pi||r){if(null!==(r=ru)&&0!==(a=0!==((a=42&(a=n&-n)?1:Te(a))&(r.suspendedLanes|n))?0:a)&&a!==s.retryLane)throw s.retryLane=a,_r(e,a),Iu(r,0,a),Ni;"$?"===u.data||Qu(),t=$i(e,t,n)}else"$?"===u.data?(t.flags|=192,t.child=e.child,t=null):(e=s.treeContext,aa=yd(u.nextSibling),ra=t,oa=!0,la=null,ia=!1,null!==e&&(Kr[Gr++]=Yr,Kr[Gr++]=Jr,Kr[Gr++]=Xr,Yr=e.id,Jr=e.overflow,Xr=t),(t=Hi(t,a.children)).flags|=4096);return t}return o?(si(),o=a.fallback,u=t.mode,c=(s=e.child).sibling,(a=jr(s,{mode:"hidden",children:a.children})).subtreeFlags=65011712&s.subtreeFlags,null!==c?o=jr(c,o):(o=Br(o,u,n,null)).flags|=2,o.return=t,a.return=t,a.sibling=o,t.child=a,a=o,o=t.child,null===(u=e.child.memoizedState)?u=Ui(n):(null!==(s=u.cachePool)?(c=Aa._currentValue,s=s.parent!==c?{parent:c,pool:c}:s):s=Va(),u={baseLanes:u.baseLanes|n,cachePool:s}),o.memoizedState=u,o.childLanes=Bi(e,r,n),t.memoizedState=Mi,a):(li(t),e=(n=e.child).sibling,(n=jr(n,{mode:"visible",children:a.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function Hi(e,t){return(t=Vi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Vi(e,t){return(e=Ir(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function $i(e,t,n){return ni(t,e.child,null,n),(e=Hi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Wi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ka(e.return,t,n)}function Qi(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Ki(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(Ti(e,t,r.children,n),2&(r=ci.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Wi(e,n,t);else if(19===e.tag)Wi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(B(ci,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===di(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Qi(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===di(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Qi(t,!0,n,null,o);break;case"together":Qi(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Gi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pu|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Ea(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=jr(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=jr(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Xi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!xa(e))}function Yi(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Pi=!0;else{if(!(Xi(e,n)||128&t.flags))return Pi=!1,function(e,t,n){switch(t.tag){case 3:W(t,t.stateNode.containerInfo),ba(0,Aa,e.memoizedState.cache),pa();break;case 27:case 5:K(t);break;case 4:W(t,t.stateNode.containerInfo);break;case 10:ba(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(li(t),t.flags|=128,null):0!==(n&t.child.childLanes)?qi(e,t,n):(li(t),null!==(e=Gi(e,t,n))?e.sibling:null);li(t);break;case 19:var a=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Ea(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Ki(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),B(ci,ci.current),r)break;return null;case 22:case 23:return t.lanes=0,Ai(e,t,n);case 24:ba(0,Aa,e.memoizedState.cache)}return Gi(e,t,n)}(e,t,n);Pi=!!(131072&e.flags)}else Pi=!1,oa&&1048576&t.flags&&ea(t,Qr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,a=r._init;if(r=a(r._payload),t.type=r,"function"!=typeof r){if(null!=r){if((a=r.$$typeof)===S){t.tag=11,t=Oi(null,t,r,e,n);break e}if(a===C){t.tag=14,t=Ri(null,t,r,e,n);break e}}throw t=A(r)||r,Error(l(306,t,""))}Dr(r)?(e=hi(r,e),t.tag=1,t=Di(null,t,r,e,n)):(t.tag=0,t=Fi(null,t,r,e,n))}return t;case 0:return Fi(e,t,t.type,t.pendingProps,n);case 1:return Di(e,t,r=t.type,a=hi(r,t.pendingProps),n);case 3:e:{if(W(t,t.stateNode.containerInfo),null===e)throw Error(l(387));r=t.pendingProps;var o=t.memoizedState;a=o.element,ro(e,t),co(t,r,null,n);var i=t.memoizedState;if(r=i.cache,ba(0,Aa,r),r!==o.cache&&Sa(t,[Aa],n,!0),uo(),r=i.element,o.isDehydrated){if(o={element:r,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=ji(e,t,r,n);break e}if(r!==a){ga(a=xr(Error(l(424)),t)),t=ji(e,t,r,n);break e}for(e=9===(e=t.stateNode.containerInfo).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,aa=yd(e.firstChild),ra=t,oa=!0,la=null,ia=!0,n=ri(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),r===a){t=Gi(e,t,n);break e}Ti(e,t,r,n)}t=t.child}return t;case 26:return zi(e,t),null===e?(n=Td(t.type,null,t.pendingProps,null))?t.memoizedState=n:oa||(n=t.type,e=t.pendingProps,(r=rd(V.current).createElement(n))[Ae]=t,r[Le]=e,ed(r,n,e),$e(r),t.stateNode=r):t.memoizedState=Td(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return K(t),null===e&&oa&&(r=t.stateNode=wd(t.type,t.pendingProps,V.current),ra=t,ia=!0,a=aa,pd(t.type)?(vd=a,aa=yd(r.firstChild)):aa=a),Ti(e,t,t.pendingProps.children,n),zi(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&oa&&((a=r=aa)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Me])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(o=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(o!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((o=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&o&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var o=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===o)return e}if(null===(e=yd(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,ia))?(t.stateNode=r,ra=t,aa=yd(r.firstChild),ia=!1,a=!0):a=!1),a||ua(t)),K(t),a=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,r=o.children,ld(a,o)?r=null:null!==i&&ld(a,i)&&(t.flags|=32),null!==t.memoizedState&&(a=Ao(e,t,Fo,null,null,n),Kd._currentValue=a),zi(e,t),Ti(e,t,r,n),t.child;case 6:return null===e&&oa&&((e=n=aa)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=yd(e.nextSibling)))return null}return e}(n,t.pendingProps,ia))?(t.stateNode=n,ra=t,aa=null,e=!0):e=!1),e||ua(t)),null;case 13:return qi(e,t,n);case 4:return W(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ni(t,null,r,n):Ti(e,t,r,n),t.child;case 11:return Oi(e,t,t.type,t.pendingProps,n);case 7:return Ti(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ti(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,ba(0,t.type,r.value),Ti(e,t,r.children,n),t.child;case 9:return a=t.type._context,r=t.pendingProps.children,Ca(t),r=r(a=Na(a)),t.flags|=1,Ti(e,t,r,n),t.child;case 14:return Ri(e,t,t.type,t.pendingProps,n);case 15:return _i(e,t,t.type,t.pendingProps,n);case 19:return Ki(e,t,n);case 31:return r=t.pendingProps,n=t.mode,r={mode:r.mode,children:r.children},null===e?((n=Vi(r,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=jr(e.child,r)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ai(e,t,n);case 24:return Ca(t),r=Na(Aa),null===e?(null===(a=qa())&&(a=ru,o=La(),a.pooledCache=o,o.refCount++,null!==o&&(a.pooledCacheLanes|=n),a=o),t.memoizedState={parent:r,cache:a},no(t),ba(0,Aa,a)):(0!==(e.lanes&n)&&(ro(e,t),co(t,null,null,n),uo()),a=e.memoizedState,o=t.memoizedState,a.parent!==r?(a={parent:r,cache:r},t.memoizedState=a,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=a),ba(0,Aa,r)):(r=o.cache,ba(0,Aa,r),r!==a.cache&&Sa(t,[Aa],n,!0))),Ti(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(l(156,t.tag))}function Ji(e){e.flags|=4}function Zi(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!Bd(t)){if(null!==(t=ai.current)&&((4194048&ou)===ou?null!==oi:(62914560&ou)!==ou&&!(536870912&ou)||t!==oi))throw Ja=Ka,Wa;e.flags|=8192}}function es(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Ee():536870912,e.lanes|=t,yu|=t)}function ts(e,t){if(!oa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ns(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function rs(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ns(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),wa(Aa),Q(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Ji(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,ma())),ns(t),null;case 26:return n=t.memoizedState,null===e?(Ji(t),null!==n?(ns(t),Zi(t,n)):(ns(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Ji(t),ns(t),Zi(t,n)):(ns(t),t.flags&=-16777217):(e.memoizedProps!==r&&Ji(t),ns(t),t.flags&=-16777217),null;case 27:G(t),n=V.current;var a=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Ji(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return ns(t),null}e=q.current,fa(t)?ca(t):(e=wd(a,r,n),t.stateNode=e,Ji(t))}return ns(t),null;case 5:if(G(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Ji(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return ns(t),null}if(e=q.current,fa(t))ca(t);else{switch(a=rd(V.current),e){case 1:e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=a.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=a.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof r.is?a.createElement("select",{is:r.is}):a.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"==typeof r.is?a.createElement(n,{is:r.is}):a.createElement(n)}}e[Ae]=t,e[Le]=r;e:for(a=t.child;null!==a;){if(5===a.tag||6===a.tag)e.appendChild(a.stateNode);else if(4!==a.tag&&27!==a.tag&&null!==a.child){a.child.return=a,a=a.child;continue}if(a===t)break e;for(;null===a.sibling;){if(null===a.return||a.return===t)break e;a=a.return}a.sibling.return=a.return,a=a.sibling}t.stateNode=e;e:switch(ed(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ji(t)}}return ns(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Ji(t);else{if("string"!=typeof r&&null===t.stateNode)throw Error(l(166));if(e=V.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(a=ra))switch(a.tag){case 27:case 5:r=a.memoizedProps}e[Ae]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Xc(e.nodeValue,n)))||ua(t)}else(e=rd(e).createTextNode(r))[Ae]=t,t.stateNode=e}return ns(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(a=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!a)throw Error(l(318));if(!(a=null!==(a=t.memoizedState)?a.dehydrated:null))throw Error(l(317));a[Ae]=t}else pa(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ns(t),a=!1}else a=ma(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=a),a=!0;if(!a)return 256&t.flags?(ui(t),t):(ui(t),null)}if(ui(t),128&t.flags)return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){a=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(a=r.alternate.memoizedState.cachePool.pool);var o=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(o=r.memoizedState.cachePool.pool),o!==a&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),es(t,t.updateQueue),ns(t),null;case 4:return Q(),null===e&&Uc(t.stateNode.containerInfo),ns(t),null;case 10:return wa(t.type),ns(t),null;case 19:if(U(ci),null===(a=t.memoizedState))return ns(t),null;if(r=!!(128&t.flags),null===(o=a.rendering))if(r)ts(a,!1);else{if(0!==fu||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(o=di(e))){for(t.flags|=128,ts(a,!1),e=o.updateQueue,t.updateQueue=e,es(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Mr(n,e),n=n.sibling;return B(ci,1&ci.current|2),t.child}e=e.sibling}null!==a.tail&&te()>Su&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=di(o))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,es(t,e),ts(a,!0),null===a.tail&&"hidden"===a.tailMode&&!o.alternate&&!oa)return ns(t),null}else 2*te()-a.renderingStartTime>Su&&536870912!==n&&(t.flags|=128,r=!0,ts(a,!1),t.lanes=4194304);a.isBackwards?(o.sibling=t.child,t.child=o):(null!==(e=a.last)?e.sibling=o:t.child=o,a.last=o)}return null!==a.tail?(t=a.tail,a.rendering=t,a.tail=t.sibling,a.renderingStartTime=te(),t.sibling=null,e=ci.current,B(ci,r?1&e|2:1&e),t):(ns(t),null);case 22:case 23:return ui(t),vo(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?!!(536870912&n)&&!(128&t.flags)&&(ns(t),6&t.subtreeFlags&&(t.flags|=8192)):ns(t),null!==(n=t.updateQueue)&&es(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&U(Ba),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),wa(Aa),ns(t),null;case 25:case 30:return null}throw Error(l(156,t.tag))}function as(e,t){switch(na(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return wa(Aa),Q(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return G(t),null;case 13:if(ui(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return U(ci),null;case 4:return Q(),null;case 10:return wa(t.type),null;case 22:case 23:return ui(t),vo(),null!==e&&U(Ba),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return wa(Aa),null;default:return null}}function os(e,t){switch(na(t),t.tag){case 3:wa(Aa),Q();break;case 26:case 27:case 5:G(t);break;case 4:Q();break;case 13:ui(t);break;case 19:U(ci);break;case 10:wa(t.type);break;case 22:case 23:ui(t),vo(),null!==e&&U(Ba);break;case 24:wa(Aa)}}function ls(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var o=n.create,l=n.inst;r=o(),l.destroy=r}n=n.next}while(n!==a)}}catch(e){cc(t,t.return,e)}}function is(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var o=a.next;r=o;do{if((r.tag&e)===e){var l=r.inst,i=l.destroy;if(void 0!==i){l.destroy=void 0,a=t;var s=n,u=i;try{u()}catch(e){cc(a,s,e)}}}r=r.next}while(r!==o)}}catch(e){cc(t,t.return,e)}}function ss(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{po(t,n)}catch(t){cc(e,e.return,t)}}}function us(e,t,n){n.props=hi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(n){cc(e,t,n)}}function cs(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(n){cc(e,t,n)}}function ds(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(n){cc(e,t,n)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(n){cc(e,t,n)}else n.current=null}function fs(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(t){cc(e,e.return,t)}}function ps(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,o=null,i=null,s=null,u=null,c=null,d=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&null!=f)switch(m){case"checked":case"value":break;case"defaultValue":u=f;default:r.hasOwnProperty(m)||Jc(e,t,m,null,r,f)}}for(var p in r){var m=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=m||null!=f))switch(p){case"type":o=m;break;case"name":a=m;break;case"checked":c=m;break;case"defaultChecked":d=m;break;case"value":i=m;break;case"defaultValue":s=m;break;case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(l(137,t));break;default:m!==f&&Jc(e,t,p,m,r,f)}}return void ht(e,i,s,u,c,d,o,a);case"select":for(o in m=i=s=p=null,n)if(u=n[o],n.hasOwnProperty(o)&&null!=u)switch(o){case"value":break;case"multiple":m=u;default:r.hasOwnProperty(o)||Jc(e,t,o,null,r,u)}for(a in r)if(o=r[a],u=n[a],r.hasOwnProperty(a)&&(null!=o||null!=u))switch(a){case"value":p=o;break;case"defaultValue":s=o;break;case"multiple":i=o;default:o!==u&&Jc(e,t,a,o,r,u)}return t=s,n=i,r=m,void(null!=p?bt(e,!!n,p,!1):!!r!=!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(s in m=p=null,n)if(a=n[s],n.hasOwnProperty(s)&&null!=a&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Jc(e,t,s,null,r,a)}for(i in r)if(a=r[i],o=n[i],r.hasOwnProperty(i)&&(null!=a||null!=o))switch(i){case"value":p=a;break;case"defaultValue":m=a;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=a)throw Error(l(91));break;default:a!==o&&Jc(e,t,i,a,r,o)}return void wt(e,p,m);case"option":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&("selected"===g?e.selected=!1:Jc(e,t,g,null,r,p));for(u in r)p=r[u],m=n[u],!r.hasOwnProperty(u)||p===m||null==p&&null==m||("selected"===u?e.selected=p&&"function"!=typeof p&&"symbol"!=typeof p:Jc(e,t,u,p,r,m));return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var h in n)p=n[h],n.hasOwnProperty(h)&&null!=p&&!r.hasOwnProperty(h)&&Jc(e,t,h,null,r,p);for(c in r)if(p=r[c],m=n[c],r.hasOwnProperty(c)&&p!==m&&(null!=p||null!=m))switch(c){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(l(137,t));break;default:Jc(e,t,c,p,r,m)}return;default:if(Nt(t)){for(var y in n)p=n[y],n.hasOwnProperty(y)&&void 0!==p&&!r.hasOwnProperty(y)&&Zc(e,t,y,void 0,r,p);for(d in r)p=r[d],m=n[d],!r.hasOwnProperty(d)||p===m||void 0===p&&void 0===m||Zc(e,t,d,p,r,m);return}}for(var v in n)p=n[v],n.hasOwnProperty(v)&&null!=p&&!r.hasOwnProperty(v)&&Jc(e,t,v,null,r,p);for(f in r)p=r[f],m=n[f],!r.hasOwnProperty(f)||p===m||null==p&&null==m||Jc(e,t,f,p,r,m)}(r,e.type,n,t),r[Le]=t}catch(t){cc(e,e.return,t)}}function ms(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function gs(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ms(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function hs(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Yc));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(hs(e,t,n),e=e.sibling;null!==e;)hs(e,t,n),e=e.sibling}function ys(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(ys(e,t,n),e=e.sibling;null!==e;)ys(e,t,n),e=e.sibling}function vs(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);ed(t,r,n),t[Ae]=e,t[Le]=n}catch(t){cc(e,e.return,t)}}var bs=!1,ws=!1,ks=!1,Ss="function"==typeof WeakSet?WeakSet:Set,Es=null;function xs(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Ds(e,n),4&r&&ls(5,n);break;case 1:if(Ds(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(e){cc(n,n.return,e)}else{var a=hi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(e){cc(n,n.return,e)}}64&r&&ss(n),512&r&&cs(n,n.return);break;case 3:if(Ds(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{po(e,t)}catch(e){cc(n,n.return,e)}}break;case 27:null===t&&4&r&&vs(n);case 26:case 5:Ds(e,n),null===t&&4&r&&fs(n),512&r&&cs(n,n.return);break;case 12:Ds(e,n);break;case 13:Ds(e,n),4&r&&Rs(e,n),64&r&&null!==(e=n.memoizedState)&&null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=mc.bind(null,n));break;case 22:if(!(r=null!==n.memoizedState||bs)){t=null!==t&&null!==t.memoizedState||ws,a=bs;var o=ws;bs=r,(ws=t)&&!o?Ms(e,n,!!(8772&n.subtreeFlags)):Ds(e,n),bs=a,ws=o}break;case 30:break;default:Ds(e,n)}}function Cs(e){var t=e.alternate;null!==t&&(e.alternate=null,Cs(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&Ue(t),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ns=null,Ps=!1;function Ts(e,t,n){for(n=n.child;null!==n;)Os(e,t,n),n=n.sibling}function Os(e,t,n){if(de&&"function"==typeof de.onCommitFiberUnmount)try{de.onCommitFiberUnmount(ce,n)}catch(e){}switch(n.tag){case 26:ws||ds(n,t),Ts(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:ws||ds(n,t);var r=Ns,a=Ps;pd(n.type)&&(Ns=n.stateNode,Ps=!1),Ts(e,t,n),kd(n.stateNode),Ns=r,Ps=a;break;case 5:ws||ds(n,t);case 6:if(r=Ns,a=Ps,Ns=null,Ts(e,t,n),Ps=a,null!==(Ns=r))if(Ps)try{(9===Ns.nodeType?Ns.body:"HTML"===Ns.nodeName?Ns.ownerDocument.body:Ns).removeChild(n.stateNode)}catch(e){cc(n,t,e)}else try{Ns.removeChild(n.stateNode)}catch(e){cc(n,t,e)}break;case 18:null!==Ns&&(Ps?(md(9===(e=Ns).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Tf(e)):md(Ns,n.stateNode));break;case 4:r=Ns,a=Ps,Ns=n.stateNode.containerInfo,Ps=!0,Ts(e,t,n),Ns=r,Ps=a;break;case 0:case 11:case 14:case 15:ws||is(2,n,t),ws||is(4,n,t),Ts(e,t,n);break;case 1:ws||(ds(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&us(n,t,r)),Ts(e,t,n);break;case 21:Ts(e,t,n);break;case 22:ws=(r=ws)||null!==n.memoizedState,Ts(e,t,n),ws=r;break;default:Ts(e,t,n)}}function Rs(e,t){if(null===t.memoizedState&&null!==(e=t.alternate)&&null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))try{Tf(e)}catch(e){cc(t,t.return,e)}}function _s(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new Ss),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new Ss),t;default:throw Error(l(435,e.tag))}}(e);t.forEach((function(t){var r=gc.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function As(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r],o=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 27:if(pd(s.type)){Ns=s.stateNode,Ps=!1;break e}break;case 5:Ns=s.stateNode,Ps=!1;break e;case 3:case 4:Ns=s.stateNode.containerInfo,Ps=!0;break e}s=s.return}if(null===Ns)throw Error(l(160));Os(o,i,a),Ns=null,Ps=!1,null!==(o=a.alternate)&&(o.return=null),a.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)zs(t,e),t=t.sibling}var Ls=null;function zs(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:As(t,e),Fs(e),4&r&&(is(3,e,e.return),ls(3,e),is(5,e,e.return));break;case 1:As(t,e),Fs(e),512&r&&(ws||null===n||ds(n,n.return)),64&r&&bs&&null!==(e=e.updateQueue)&&null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r));break;case 26:var a=Ls;if(As(t,e),Fs(e),512&r&&(ws||null===n||ds(n,n.return)),4&r){var o=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,a=a.ownerDocument||a;t:switch(r){case"title":(!(o=a.getElementsByTagName("title")[0])||o[Me]||o[Ae]||"http://www.w3.org/2000/svg"===o.namespaceURI||o.hasAttribute("itemprop"))&&(o=a.createElement(r),a.head.insertBefore(o,a.querySelector("head > title"))),ed(o,r,n),o[Ae]=e,$e(o),r=o;break e;case"link":var i=Md("link","href",a).get(r+(n.href||""));if(i)for(var s=0;s<i.length;s++)if((o=i[s]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&o.getAttribute("rel")===(null==n.rel?null:n.rel)&&o.getAttribute("title")===(null==n.title?null:n.title)&&o.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(s,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;case"meta":if(i=Md("meta","content",a).get(r+(n.content||"")))for(s=0;s<i.length;s++)if((o=i[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&o.getAttribute("name")===(null==n.name?null:n.name)&&o.getAttribute("property")===(null==n.property?null:n.property)&&o.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&o.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(s,1);break t}ed(o=a.createElement(r),r,n),a.head.appendChild(o);break;default:throw Error(l(468,r))}o[Ae]=e,$e(o),r=o}e.stateNode=r}else Ud(a,e.type,e.stateNode);else e.stateNode=zd(a,r,e.memoizedProps);else o!==r?(null===o?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):o.count--,null===r?Ud(a,e.type,e.stateNode):zd(a,r,e.memoizedProps)):null===r&&null!==e.stateNode&&ps(e,e.memoizedProps,n.memoizedProps)}break;case 27:As(t,e),Fs(e),512&r&&(ws||null===n||ds(n,n.return)),null!==n&&4&r&&ps(e,e.memoizedProps,n.memoizedProps);break;case 5:if(As(t,e),Fs(e),512&r&&(ws||null===n||ds(n,n.return)),32&e.flags){a=e.stateNode;try{St(a,"")}catch(t){cc(e,e.return,t)}}4&r&&null!=e.stateNode&&ps(e,a=e.memoizedProps,null!==n?n.memoizedProps:a),1024&r&&(ks=!0);break;case 6:if(As(t,e),Fs(e),4&r){if(null===e.stateNode)throw Error(l(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(t){cc(e,e.return,t)}}break;case 3:if(jd=null,a=Ls,Ls=xd(t.containerInfo),As(t,e),Ls=a,Fs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Tf(t.containerInfo)}catch(t){cc(e,e.return,t)}ks&&(ks=!1,Is(e));break;case 4:r=Ls,Ls=xd(e.stateNode.containerInfo),As(t,e),Fs(e),Ls=r;break;case 12:default:As(t,e),Fs(e);break;case 13:As(t,e),Fs(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(ku=te()),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,_s(e,r));break;case 22:a=null!==e.memoizedState;var u=null!==n&&null!==n.memoizedState,c=bs,d=ws;if(bs=c||a,ws=d||u,As(t,e),ws=d,bs=c,Fs(e),8192&r)e:for(t=e.stateNode,t._visibility=a?-2&t._visibility:1|t._visibility,a&&(null===n||u||bs||ws||js(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){u=n=t;try{if(o=u.stateNode,a)"function"==typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{s=u.stateNode;var f=u.memoizedProps.style,p=null!=f&&f.hasOwnProperty("display")?f.display:null;s.style.display=null==p||"boolean"==typeof p?"":(""+p).trim()}}catch(e){cc(u,u.return,e)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=a?"":u.memoizedProps}catch(e){cc(u,u.return,e)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&null!==(r=e.updateQueue)&&null!==(n=r.retryQueue)&&(r.retryQueue=null,_s(e,n));break;case 19:As(t,e),Fs(e),4&r&&null!==(r=e.updateQueue)&&(e.updateQueue=null,_s(e,r));case 30:case 21:}}function Fs(e){var t=e.flags;if(2&t){try{for(var n,r=e.return;null!==r;){if(ms(r)){n=r;break}r=r.return}if(null==n)throw Error(l(160));switch(n.tag){case 27:var a=n.stateNode;ys(e,gs(e),a);break;case 5:var o=n.stateNode;32&n.flags&&(St(o,""),n.flags&=-33),ys(e,gs(e),o);break;case 3:case 4:var i=n.stateNode.containerInfo;hs(e,gs(e),i);break;default:throw Error(l(161))}}catch(t){cc(e,e.return,t)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Is(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Is(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Ds(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)xs(e,t.alternate,t),t=t.sibling}function js(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:is(4,t,t.return),js(t);break;case 1:ds(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&us(t,t.return,n),js(t);break;case 27:kd(t.stateNode);case 26:case 5:ds(t,t.return),js(t);break;case 22:null===t.memoizedState&&js(t);break;default:js(t)}e=e.sibling}}function Ms(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,o=t,l=o.flags;switch(o.tag){case 0:case 11:case 15:Ms(a,o,n),ls(4,o);break;case 1:if(Ms(a,o,n),"function"==typeof(a=(r=o).stateNode).componentDidMount)try{a.componentDidMount()}catch(e){cc(r,r.return,e)}if(null!==(a=(r=o).updateQueue)){var i=r.stateNode;try{var s=a.shared.hiddenCallbacks;if(null!==s)for(a.shared.hiddenCallbacks=null,a=0;a<s.length;a++)fo(s[a],i)}catch(e){cc(r,r.return,e)}}n&&64&l&&ss(o),cs(o,o.return);break;case 27:vs(o);case 26:case 5:Ms(a,o,n),n&&null===r&&4&l&&fs(o),cs(o,o.return);break;case 12:Ms(a,o,n);break;case 13:Ms(a,o,n),n&&4&l&&Rs(a,o);break;case 22:null===o.memoizedState&&Ms(a,o,n),cs(o,o.return);break;case 30:break;default:Ms(a,o,n)}t=t.sibling}}function Us(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&za(n))}function Bs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&za(e))}function qs(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Hs(e,t,n,r),t=t.sibling}function Hs(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:qs(e,t,n,r),2048&a&&ls(9,t);break;case 1:case 13:default:qs(e,t,n,r);break;case 3:qs(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&za(e)));break;case 12:if(2048&a){qs(e,t,n,r),e=t.stateNode;try{var o=t.memoizedProps,l=o.id,i=o.onPostCommit;"function"==typeof i&&i(l,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(e){cc(t,t.return,e)}}else qs(e,t,n,r);break;case 23:break;case 22:o=t.stateNode,l=t.alternate,null!==t.memoizedState?2&o._visibility?qs(e,t,n,r):$s(e,t):2&o._visibility?qs(e,t,n,r):(o._visibility|=2,Vs(e,t,n,r,!!(10256&t.subtreeFlags))),2048&a&&Us(l,t);break;case 24:qs(e,t,n,r),2048&a&&Bs(t.alternate,t)}}function Vs(e,t,n,r,a){for(a=a&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var o=e,l=t,i=n,s=r,u=l.flags;switch(l.tag){case 0:case 11:case 15:Vs(o,l,i,s,a),ls(8,l);break;case 23:break;case 22:var c=l.stateNode;null!==l.memoizedState?2&c._visibility?Vs(o,l,i,s,a):$s(o,l):(c._visibility|=2,Vs(o,l,i,s,a)),a&&2048&u&&Us(l.alternate,l);break;case 24:Vs(o,l,i,s,a),a&&2048&u&&Bs(l.alternate,l);break;default:Vs(o,l,i,s,a)}t=t.sibling}}function $s(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:$s(n,r),2048&a&&Us(r.alternate,r);break;case 24:$s(n,r),2048&a&&Bs(r.alternate,r);break;default:$s(n,r)}t=t.sibling}}var Ws=8192;function Qs(e){if(e.subtreeFlags&Ws)for(e=e.child;null!==e;)Ks(e),e=e.sibling}function Ks(e){switch(e.tag){case 26:Qs(e),e.flags&Ws&&null!==e.memoizedState&&function(e,t,n){if(null===qd)throw Error(l(475));var r=qd;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var a=Od(n.href),o=e.querySelector(Rd(a));if(o)return null!==(e=o._p)&&"object"==typeof e&&"function"==typeof e.then&&(r.count++,r=Vd.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=o,void $e(o);o=e.ownerDocument||e,n=_d(n),(a=Sd.get(a))&&Id(n,a),$e(o=o.createElement("link"));var i=o;i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),ed(o,"link",n),t.instance=o}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(r.count++,t=Vd.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(Ls,e.memoizedState,e.memoizedProps);break;case 5:default:Qs(e);break;case 3:case 4:var t=Ls;Ls=xd(e.stateNode.containerInfo),Qs(e),Ls=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Ws,Ws=16777216,Qs(e),Ws=t):Qs(e))}}function Gs(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Xs(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Es=r,Zs(r,e)}Gs(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Ys(e),e=e.sibling}function Ys(e){switch(e.tag){case 0:case 11:case 15:Xs(e),2048&e.flags&&is(9,e,e.return);break;case 3:case 12:default:Xs(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,Js(e)):Xs(e)}}function Js(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];Es=r,Zs(r,e)}Gs(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:is(8,t,t.return),Js(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,Js(t));break;default:Js(t)}e=e.sibling}}function Zs(e,t){for(;null!==Es;){var n=Es;switch(n.tag){case 0:case 11:case 15:is(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:za(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,Es=r;else e:for(n=e;null!==Es;){var a=(r=Es).sibling,o=r.return;if(Cs(r),r===n){Es=null;break e}if(null!==a){a.return=o,Es=a;break e}Es=o}}}var eu={getCacheForType:function(e){var t=Na(Aa),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},tu="function"==typeof WeakMap?WeakMap:Map,nu=0,ru=null,au=null,ou=0,lu=0,iu=null,su=!1,uu=!1,cu=!1,du=0,fu=0,pu=0,mu=0,gu=0,hu=0,yu=0,vu=null,bu=null,wu=!1,ku=0,Su=1/0,Eu=null,xu=null,Cu=0,Nu=null,Pu=null,Tu=0,Ou=0,Ru=null,_u=null,Au=0,Lu=null;function zu(){return 2&nu&&0!==ou?ou&-ou:null!==z.T?0!==Da?Da:Oc():Re()}function Fu(){0===hu&&(hu=536870912&ou&&!oa?536870912:Se());var e=ai.current;return null!==e&&(e.flags|=32),hu}function Iu(e,t,n){(e!==ru||2!==lu&&9!==lu)&&null===e.cancelPendingCommit||(Hu(e,0),Uu(e,ou,hu,!1)),Ce(e,n),2&nu&&e===ru||(e===ru&&(!(2&nu)&&(mu|=n),4===fu&&Uu(e,ou,hu,!1)),Sc(e))}function Du(e,t,n){if(6&nu)throw Error(l(327));for(var r=!n&&!(124&t)&&0===(t&e.expiredLanes)||we(e,t),a=r?function(e,t){var n=nu;nu|=2;var r=$u(),a=Wu();ru!==e||ou!==t?(Eu=null,Su=te()+500,Hu(e,t)):uu=we(e,t);e:for(;;)try{if(0!==lu&&null!==au){t=au;var o=iu;t:switch(lu){case 1:lu=0,iu=null,Zu(e,t,o,1);break;case 2:case 9:if(Ga(o)){lu=0,iu=null,Ju(t);break}t=function(){2!==lu&&9!==lu||ru!==e||(lu=7),Sc(e)},o.then(t,t);break e;case 3:lu=7;break e;case 4:lu=5;break e;case 7:Ga(o)?(lu=0,iu=null,Ju(t)):(lu=0,iu=null,Zu(e,t,o,7));break;case 5:var i=null;switch(au.tag){case 26:i=au.memoizedState;case 5:case 27:var s=au;if(!i||Bd(i)){lu=0,iu=null;var u=s.sibling;if(null!==u)au=u;else{var c=s.return;null!==c?(au=c,ec(c)):au=null}break t}}lu=0,iu=null,Zu(e,t,o,5);break;case 6:lu=0,iu=null,Zu(e,t,o,6);break;case 8:qu(),fu=6;break e;default:throw Error(l(462))}}Xu();break}catch(t){Vu(e,t)}return va=ya=null,z.H=r,z.A=a,nu=n,null!==au?0:(ru=null,ou=0,Tr(),fu)}(e,t):Ku(e,t,!0),o=r;;){if(0===a){uu&&!r&&Uu(e,t,0,!1);break}if(n=e.current.alternate,!o||Mu(n)){if(2===a){if(o=t,e.errorRecoveryDisabledLanes&o)var i=0;else i=0!=(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var s=e;a=vu;var u=s.current.memoizedState.isDehydrated;if(u&&(Hu(s,i).flags|=256),2!==(i=Ku(s,i,!1))){if(cu&&!u){s.errorRecoveryDisabledLanes|=o,mu|=o,a=4;break e}o=bu,bu=a,null!==o&&(null===bu?bu=o:bu.push.apply(bu,o))}a=i}if(o=!1,2!==a)continue}}if(1===a){Hu(e,0),Uu(e,t,0,!0);break}e:{switch(r=e,o=a){case 0:case 1:throw Error(l(345));case 4:if((4194048&t)!==t)break;case 6:Uu(r,t,hu,!su);break e;case 2:bu=null;break;case 3:case 5:break;default:throw Error(l(329))}if((62914560&t)===t&&10<(a=ku+300-te())){if(Uu(r,t,hu,!su),0!==be(r,0,!0))break e;r.timeoutHandle=sd(ju.bind(null,r,n,bu,Eu,wu,t,hu,mu,yu,su,o,2,-0,0),a)}else ju(r,n,bu,Eu,wu,t,hu,mu,yu,su,o,0,-0,0)}break}a=Ku(e,t,!1),o=!1}Sc(e)}function ju(e,t,n,r,a,o,i,s,u,c,d,f,p,m){if(e.timeoutHandle=-1,(8192&(f=t.subtreeFlags)||!(16785408&~f))&&(qd={stylesheets:null,count:0,unsuspend:Hd},Ks(t),null!==(f=function(){if(null===qd)throw Error(l(475));var e=qd;return e.stylesheets&&0===e.count&&Wd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Wd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=f(nc.bind(null,e,t,o,n,r,a,i,s,u,d,1,p,m)),void Uu(e,o,i,!c);nc(e,t,o,n,r,a,i,s,u)}function Mu(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&null!==(n=t.updateQueue)&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!Gn(o(),a))return!1}catch(e){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Uu(e,t,n,r){t&=~gu,t&=~mu,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var o=31-pe(a),l=1<<o;r[o]=-1,a&=~l}0!==n&&Ne(e,n,t)}function Bu(){return!!(6&nu)||(Ec(0,!1),!1)}function qu(){if(null!==au){if(0===lu)var e=au.return;else va=ya=null,jo(e=au),Gl=null,Xl=0,e=au;for(;null!==e;)os(e.alternate,e),e=e.return;au=null}}function Hu(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,ud(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),qu(),ru=e,au=n=jr(e.current,null),ou=t,lu=0,iu=null,su=!1,uu=we(e,t),cu=!1,yu=hu=gu=mu=pu=fu=0,bu=vu=null,wu=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-pe(r),o=1<<a;t|=e[a],r&=~o}return du=t,Tr(),n}function Vu(e,t){wo=null,z.H=$l,t===$a||t===Qa?(t=Za(),lu=3):t===Wa?(t=Za(),lu=4):lu=t===Ni?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,iu=t,null===au&&(fu=1,ki(e,xr(t,e.current)))}function $u(){var e=z.H;return z.H=$l,null===e?$l:e}function Wu(){var e=z.A;return z.A=eu,e}function Qu(){fu=4,su||(4194048&ou)!==ou&&null!==ai.current||(uu=!0),!(134217727&pu)&&!(134217727&mu)||null===ru||Uu(ru,ou,hu,!1)}function Ku(e,t,n){var r=nu;nu|=2;var a=$u(),o=Wu();ru===e&&ou===t||(Eu=null,Hu(e,t)),t=!1;var l=fu;e:for(;;)try{if(0!==lu&&null!==au){var i=au,s=iu;switch(lu){case 8:qu(),l=6;break e;case 3:case 2:case 9:case 6:null===ai.current&&(t=!0);var u=lu;if(lu=0,iu=null,Zu(e,i,s,u),n&&uu){l=0;break e}break;default:u=lu,lu=0,iu=null,Zu(e,i,s,u)}}Gu(),l=fu;break}catch(t){Vu(e,t)}return t&&e.shellSuspendCounter++,va=ya=null,nu=r,z.H=a,z.A=o,null===au&&(ru=null,ou=0,Tr()),l}function Gu(){for(;null!==au;)Yu(au)}function Xu(){for(;null!==au&&!Z();)Yu(au)}function Yu(e){var t=Yi(e.alternate,e,du);e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Ju(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Ii(n,t,t.pendingProps,t.type,void 0,ou);break;case 11:t=Ii(n,t,t.pendingProps,t.type.render,t.ref,ou);break;case 5:jo(t);default:os(n,t),t=Yi(n,t=au=Mr(t,du),du)}e.memoizedProps=e.pendingProps,null===t?ec(e):au=t}function Zu(e,t,n,r){va=ya=null,jo(t),Gl=null,Xl=0;var a=t.return;try{if(function(e,t,n,r,a){if(n.flags|=32768,null!==r&&"object"==typeof r&&"function"==typeof r.then){if(null!==(t=n.alternate)&&Ea(t,n,a,!0),null!==(n=ai.current)){switch(n.tag){case 13:return null===oi?Qu():null===n.alternate&&0===fu&&(fu=3),n.flags&=-257,n.flags|=65536,n.lanes=a,r===Ka?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),dc(e,r,a)),!1;case 22:return n.flags|=65536,r===Ka?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),dc(e,r,a)),!1}throw Error(l(435,n.tag))}return dc(e,r,a),Qu(),!1}if(oa)return null!==(t=ai.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=a,r!==sa&&ga(xr(e=Error(l(422),{cause:r}),n))):(r!==sa&&ga(xr(t=Error(l(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,a&=-a,e.lanes|=a,r=xr(r,n),io(e,a=Ei(e.stateNode,r,a)),4!==fu&&(fu=2)),!1;var o=Error(l(520),{cause:r});if(o=xr(o,n),null===vu?vu=[o]:vu.push(o),4!==fu&&(fu=2),null===t)return!0;r=xr(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=a&-a,n.lanes|=e,io(n,e=Ei(n.stateNode,r,e)),!1;case 1:if(t=n.type,o=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===o||"function"!=typeof o.componentDidCatch||null!==xu&&xu.has(o))))return n.flags|=65536,a&=-a,n.lanes|=a,Ci(a=xi(a),e,n,r),io(n,a),!1}n=n.return}while(null!==n);return!1}(e,a,t,n,ou))return fu=1,ki(e,xr(n,e.current)),void(au=null)}catch(t){if(null!==a)throw au=a,t;return fu=1,ki(e,xr(n,e.current)),void(au=null)}32768&t.flags?(oa||1===r?e=!0:uu||536870912&ou?e=!1:(su=e=!0,(2===r||9===r||3===r||6===r)&&null!==(r=ai.current)&&13===r.tag&&(r.flags|=16384)),tc(t,e)):ec(t)}function ec(e){var t=e;do{if(32768&t.flags)return void tc(t,su);e=t.return;var n=rs(t.alternate,t,du);if(null!==n)return void(au=n);if(null!==(t=t.sibling))return void(au=t);au=t=e}while(null!==t);0===fu&&(fu=5)}function tc(e,t){do{var n=as(e.alternate,e);if(null!==n)return n.flags&=32767,void(au=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(au=e);au=e=n}while(null!==e);fu=6,au=null}function nc(e,t,n,r,a,o,i,s,u){e.cancelPendingCommit=null;do{ic()}while(0!==Cu);if(6&nu)throw Error(l(327));if(null!==t){if(t===e.current)throw Error(l(177));if(o=t.lanes|t.childLanes,function(e,t,n,r,a,o){var l=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,s=e.expirationTimes,u=e.hiddenUpdates;for(n=l&~n;0<n;){var c=31-pe(n),d=1<<c;i[c]=0,s[c]=-1;var f=u[c];if(null!==f)for(u[c]=null,c=0;c<f.length;c++){var p=f[c];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&Ne(e,r,0),0!==o&&0===a&&0!==e.tag&&(e.suspendedLanes|=o&~(l&~t))}(e,n,o|=Pr,i,s,u),e===ru&&(au=ru=null,ou=0),Pu=t,Nu=e,Tu=n,Ou=o,Ru=a,_u=r,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,Y(oe,(function(){return sc(),null}))):(e.callbackNode=null,e.callbackPriority=0),r=!!(13878&t.flags),13878&t.subtreeFlags||r){r=z.T,z.T=null,a=F.p,F.p=2,i=nu,nu|=4;try{!function(e,t){if(e=e.containerInfo,td=nf,tr(e=er(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,o=r.focusNode;r=r.focusOffset;try{n.nodeType,o.nodeType}catch(e){n=null;break e}var i=0,s=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==a&&3!==f.nodeType||(s=i+a),f!==o||0!==r&&3!==f.nodeType||(u=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++c===a&&(s=i),p===o&&++d===r&&(u=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===u?null:{start:s,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(nd={focusedElem:e,selectionRange:n},nf=!1,Es=t;null!==Es;)if(e=(t=Es).child,1024&t.subtreeFlags&&null!==e)e.return=t,Es=e;else for(;null!==Es;){switch(o=(t=Es).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==o){e=void 0,n=t,a=o.memoizedProps,o=o.memoizedState,r=n.stateNode;try{var g=hi(n.type,a,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(g,o),r.__reactInternalSnapshotBeforeUpdate=e}catch(e){cc(n,n.return,e)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))gd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":gd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(l(163))}if(null!==(e=t.sibling)){e.return=t.return,Es=e;break}Es=t.return}}(e,t)}finally{nu=i,F.p=a,z.T=r}}Cu=1,rc(),ac(),oc()}}function rc(){if(1===Cu){Cu=0;var e=Nu,t=Pu,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=z.T,z.T=null;var r=F.p;F.p=2;var a=nu;nu|=4;try{zs(t,e);var o=nd,l=er(e.containerInfo),i=o.focusedElem,s=o.selectionRange;if(l!==i&&i&&i.ownerDocument&&Zn(i.ownerDocument.documentElement,i)){if(null!==s&&tr(i)){var u=s.start,c=s.end;if(void 0===c&&(c=u),"selectionStart"in i)i.selectionStart=u,i.selectionEnd=Math.min(c,i.value.length);else{var d=i.ownerDocument||document,f=d&&d.defaultView||window;if(f.getSelection){var p=f.getSelection(),m=i.textContent.length,g=Math.min(s.start,m),h=void 0===s.end?g:Math.min(s.end,m);!p.extend&&g>h&&(l=h,h=g,g=l);var y=Jn(i,g),v=Jn(i,h);if(y&&v&&(1!==p.rangeCount||p.anchorNode!==y.node||p.anchorOffset!==y.offset||p.focusNode!==v.node||p.focusOffset!==v.offset)){var b=d.createRange();b.setStart(y.node,y.offset),p.removeAllRanges(),g>h?(p.addRange(b),p.extend(v.node,v.offset)):(b.setEnd(v.node,v.offset),p.addRange(b))}}}}for(d=[],p=i;p=p.parentNode;)1===p.nodeType&&d.push({element:p,left:p.scrollLeft,top:p.scrollTop});for("function"==typeof i.focus&&i.focus(),i=0;i<d.length;i++){var w=d[i];w.element.scrollLeft=w.left,w.element.scrollTop=w.top}}nf=!!td,nd=td=null}finally{nu=a,F.p=r,z.T=n}}e.current=t,Cu=2}}function ac(){if(2===Cu){Cu=0;var e=Nu,t=Pu,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=z.T,z.T=null;var r=F.p;F.p=2;var a=nu;nu|=4;try{xs(e,t.alternate,t)}finally{nu=a,F.p=r,z.T=n}}Cu=3}}function oc(){if(4===Cu||3===Cu){Cu=0,ee();var e=Nu,t=Pu,n=Tu,r=_u;10256&t.subtreeFlags||10256&t.flags?Cu=5:(Cu=0,Pu=Nu=null,lc(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(xu=null),Oe(n),t=t.stateNode,de&&"function"==typeof de.onCommitFiberRoot)try{de.onCommitFiberRoot(ce,t,void 0,!(128&~t.current.flags))}catch(e){}if(null!==r){t=z.T,a=F.p,F.p=2,z.T=null;try{for(var o=e.onRecoverableError,l=0;l<r.length;l++){var i=r[l];o(i.value,{componentStack:i.stack})}}finally{z.T=t,F.p=a}}3&Tu&&ic(),Sc(e),a=e.pendingLanes,4194090&n&&42&a?e===Lu?Au++:(Au=0,Lu=e):Au=0,Ec(0,!1)}}function lc(e,t){0===(e.pooledCacheLanes&=t)&&null!=(t=e.pooledCache)&&(e.pooledCache=null,za(t))}function ic(e){return rc(),ac(),oc(),sc()}function sc(){if(5!==Cu)return!1;var e=Nu,t=Ou;Ou=0;var n=Oe(Tu),r=z.T,a=F.p;try{F.p=32>n?32:n,z.T=null,n=Ru,Ru=null;var o=Nu,i=Tu;if(Cu=0,Pu=Nu=null,Tu=0,6&nu)throw Error(l(331));var s=nu;if(nu|=4,Ys(o.current),Hs(o,o.current,i,n),nu=s,Ec(0,!1),de&&"function"==typeof de.onPostCommitFiberRoot)try{de.onPostCommitFiberRoot(ce,o)}catch(e){}return!0}finally{F.p=a,z.T=r,lc(e,t)}}function uc(e,t,n){t=xr(n,t),null!==(e=oo(e,t=Ei(e.stateNode,t,2),2))&&(Ce(e,2),Sc(e))}function cc(e,t,n){if(3===e.tag)uc(e,e,n);else for(;null!==t;){if(3===t.tag){uc(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===xu||!xu.has(r))){e=xr(n,e),null!==(r=oo(t,n=xi(2),2))&&(Ci(n,r,t,e),Ce(r,2),Sc(r));break}}t=t.return}}function dc(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new tu;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(cu=!0,a.add(n),e=fc.bind(null,e,t,n),t.then(e,e))}function fc(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,ru===e&&(ou&n)===n&&(4===fu||3===fu&&(62914560&ou)===ou&&300>te()-ku?!(2&nu)&&Hu(e,0):gu|=n,yu===ou&&(yu=0)),Sc(e)}function pc(e,t){0===t&&(t=Ee()),null!==(e=_r(e,t))&&(Ce(e,t),Sc(e))}function mc(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pc(e,n)}function gc(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(l(314))}null!==r&&r.delete(t),pc(e,n)}var hc=null,yc=null,vc=!1,bc=!1,wc=!1,kc=0;function Sc(e){e!==yc&&null===e.next&&(null===yc?hc=yc=e:yc=yc.next=e),bc=!0,vc||(vc=!0,dd((function(){6&nu?Y(re,xc):Cc()})))}function Ec(e,t){if(!wc&&bc){wc=!0;do{for(var n=!1,r=hc;null!==r;){if(!t)if(0!==e){var a=r.pendingLanes;if(0===a)var o=0;else{var l=r.suspendedLanes,i=r.pingedLanes;o=(1<<31-pe(42|e)+1)-1,o=201326741&(o&=a&~(l&~i))?201326741&o|1:o?2|o:0}0!==o&&(n=!0,Tc(r,o))}else o=ou,!(3&(o=be(r,r===ru?o:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||we(r,o)||(n=!0,Tc(r,o));r=r.next}}while(n);wc=!1}}function xc(){Cc()}function Cc(){bc=vc=!1;var e,t=0;0!==kc&&(((e=window.event)&&"popstate"===e.type?e!==id&&(id=e,!0):(id=null,!1))&&(t=kc),kc=0);for(var n=te(),r=null,a=hc;null!==a;){var o=a.next,l=Nc(a,n);0===l?(a.next=null,null===r?hc=o:r.next=o,null===o&&(yc=r)):(r=a,(0!==t||3&l)&&(bc=!0)),a=o}Ec(t,!1)}function Nc(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=-62914561&e.pendingLanes;0<o;){var l=31-pe(o),i=1<<l,s=a[l];-1===s?0!==(i&n)&&0===(i&r)||(a[l]=ke(i,t)):s<=t&&(e.expiredLanes|=i),o&=~i}if(n=ou,n=be(e,e===(t=ru)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===lu||9===lu)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&J(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||we(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&J(r),Oe(n)){case 2:case 8:n=ae;break;case 32:default:n=oe;break;case 268435456:n=ie}return r=Pc.bind(null,e),n=Y(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&J(r),e.callbackPriority=2,e.callbackNode=null,2}function Pc(e,t){if(0!==Cu&&5!==Cu)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(ic()&&e.callbackNode!==n)return null;var r=ou;return 0===(r=be(e,e===ru?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Du(e,r,t),Nc(e,te()),null!=e.callbackNode&&e.callbackNode===n?Pc.bind(null,e):null)}function Tc(e,t){if(ic())return null;Du(e,t,!0)}function Oc(){return 0===kc&&(kc=Se()),kc}function Rc(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:Ot(""+e)}function _c(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ac=0;Ac<kr.length;Ac++){var Lc=kr[Ac];Sr(Lc.toLowerCase(),"on"+(Lc[0].toUpperCase()+Lc.slice(1)))}Sr(pr,"onAnimationEnd"),Sr(mr,"onAnimationIteration"),Sr(gr,"onAnimationStart"),Sr("dblclick","onDoubleClick"),Sr("focusin","onFocus"),Sr("focusout","onBlur"),Sr(hr,"onTransitionRun"),Sr(yr,"onTransitionStart"),Sr(vr,"onTransitionCancel"),Sr(br,"onTransitionEnd"),Ge("onMouseEnter",["mouseout","mouseover"]),Ge("onMouseLeave",["mouseout","mouseover"]),Ge("onPointerEnter",["pointerout","pointerover"]),Ge("onPointerLeave",["pointerout","pointerover"]),Ke("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Ke("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Ke("onBeforeInput",["compositionend","keypress","textInput","paste"]),Ke("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Ke("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zc="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Fc=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(zc));function Ic(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],s=i.instance,u=i.currentTarget;if(i=i.listener,s!==o&&a.isPropagationStopped())break e;o=i,a.currentTarget=u;try{o(a)}catch(e){yi(e)}a.currentTarget=null,o=s}else for(l=0;l<r.length;l++){if(s=(i=r[l]).instance,u=i.currentTarget,i=i.listener,s!==o&&a.isPropagationStopped())break e;o=i,a.currentTarget=u;try{o(a)}catch(e){yi(e)}a.currentTarget=null,o=s}}}}function Dc(e,t){var n=t[Fe];void 0===n&&(n=t[Fe]=new Set);var r=e+"__bubble";n.has(r)||(Bc(t,e,2,!1),n.add(r))}function jc(e,t,n){var r=0;t&&(r|=4),Bc(n,e,r,t)}var Mc="_reactListening"+Math.random().toString(36).slice(2);function Uc(e){if(!e[Mc]){e[Mc]=!0,We.forEach((function(t){"selectionchange"!==t&&(Fc.has(t)||jc(t,!1,e),jc(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Mc]||(t[Mc]=!0,jc("selectionchange",!1,t))}}function Bc(e,t,n,r){switch(cf(t)){case 2:var a=rf;break;case 8:a=af;break;default:a=of}n=a.bind(null,t,n,e),a=void 0,!Mt||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function qc(e,t,n,r,a){var o=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===a)break;if(4===l)for(l=r.return;null!==l;){var u=l.tag;if((3===u||4===u)&&l.stateNode.containerInfo===a)return;l=l.return}for(;null!==i;){if(null===(l=Be(i)))return;if(5===(u=l.tag)||6===u||26===u||27===u){r=o=l;continue e}i=i.parentNode}}r=r.return}It((function(){var r=o,a=_t(n),l=[];e:{var i=wr.get(e);if(void 0!==i){var u=Zt,c=e;switch(e){case"keypress":if(0===$t(n))break e;case"keydown":case"keyup":u=gn;break;case"focusin":c="focus",u=on;break;case"focusout":c="blur",u=on;break;case"beforeblur":case"afterblur":u=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":u=rn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":u=an;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":u=yn;break;case pr:case mr:case gr:u=ln;break;case br:u=vn;break;case"scroll":case"scrollend":u=tn;break;case"wheel":u=bn;break;case"copy":case"cut":case"paste":u=sn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":u=hn;break;case"toggle":case"beforetoggle":u=wn}var d=!!(4&t),f=!d&&("scroll"===e||"scrollend"===e),p=d?null!==i?i+"Capture":null:i;d=[];for(var m,g=r;null!==g;){var h=g;if(m=h.stateNode,5!==(h=h.tag)&&26!==h&&27!==h||null===m||null===p||null!=(h=Dt(g,p))&&d.push(Hc(g,h,m)),f)break;g=g.return}0<d.length&&(i=new u(i,c,null,n,a),l.push({event:i,listeners:d}))}}if(!(7&t)){if(u="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===Rt||!(c=n.relatedTarget||n.fromElement)||!Be(c)&&!c[ze])&&(u||i)&&(i=a.window===a?a:(i=a.ownerDocument)?i.defaultView||i.parentWindow:window,u?(u=r,null!==(c=(c=n.relatedTarget||n.toElement)?Be(c):null)&&(f=s(c),d=c.tag,c!==f||5!==d&&27!==d&&6!==d)&&(c=null)):(u=null,c=r),u!==c)){if(d=rn,h="onMouseLeave",p="onMouseEnter",g="mouse","pointerout"!==e&&"pointerover"!==e||(d=hn,h="onPointerLeave",p="onPointerEnter",g="pointer"),f=null==u?i:He(u),m=null==c?i:He(c),(i=new d(h,g+"leave",u,n,a)).target=f,i.relatedTarget=m,h=null,Be(a)===r&&((d=new d(p,g+"enter",c,n,a)).target=m,d.relatedTarget=f,h=d),f=h,u&&c)e:{for(p=c,g=0,m=d=u;m;m=$c(m))g++;for(m=0,h=p;h;h=$c(h))m++;for(;0<g-m;)d=$c(d),g--;for(;0<m-g;)p=$c(p),m--;for(;g--;){if(d===p||null!==p&&d===p.alternate)break e;d=$c(d),p=$c(p)}d=null}else d=null;null!==u&&Wc(l,i,u,d,!1),null!==c&&null!==f&&Wc(l,f,c,d,!0)}if("select"===(u=(i=r?He(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===u&&"file"===i.type)var y=jn;else if(An(i))if(Mn)y=Kn;else{y=Wn;var v=$n}else!(u=i.nodeName)||"input"!==u.toLowerCase()||"checkbox"!==i.type&&"radio"!==i.type?r&&Nt(r.elementType)&&(y=jn):y=Qn;switch(y&&(y=y(e,r))?Ln(l,y,n,a):(v&&v(e,i,r),"focusout"===e&&r&&"number"===i.type&&null!=r.memoizedProps.value&&vt(i,"number",i.value)),v=r?He(r):window,e){case"focusin":(An(v)||"true"===v.contentEditable)&&(rr=v,ar=r,or=null);break;case"focusout":or=ar=rr=null;break;case"mousedown":lr=!0;break;case"contextmenu":case"mouseup":case"dragend":lr=!1,ir(l,n,a);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":ir(l,n,a)}var b;if(Sn)e:{switch(e){case"compositionstart":var w="onCompositionStart";break e;case"compositionend":w="onCompositionEnd";break e;case"compositionupdate":w="onCompositionUpdate";break e}w=void 0}else Rn?Tn(e,n)&&(w="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(w="onCompositionStart");w&&(Cn&&"ko"!==n.locale&&(Rn||"onCompositionStart"!==w?"onCompositionEnd"===w&&Rn&&(b=Vt()):(qt="value"in(Bt=a)?Bt.value:Bt.textContent,Rn=!0)),0<(v=Vc(r,w)).length&&(w=new un(w,e,null,n,a),l.push({event:w,listeners:v}),(b||null!==(b=On(n)))&&(w.data=b))),(b=xn?function(e,t){switch(e){case"compositionend":return On(t);case"keypress":return 32!==t.which?null:(Pn=!0,Nn);case"textInput":return(e=t.data)===Nn&&Pn?null:e;default:return null}}(e,n):function(e,t){if(Rn)return"compositionend"===e||!Sn&&Tn(e,t)?(e=Vt(),Ht=qt=Bt=null,Rn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Cn&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(w=Vc(r,"onBeforeInput")).length&&(v=new un("onBeforeInput","beforeinput",null,n,a),l.push({event:v,listeners:w}),v.data=b),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var o=Rc((a[Le]||null).action),l=r.submitter;l&&null!==(t=(t=l[Le]||null)?Rc(t.formAction):l.getAttribute("formAction"))&&(o=t,l=null);var i=new Zt("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==kc){var e=l?_c(a,l):new FormData(a);_l(n,{pending:!0,data:e,method:a.method,action:o},null,e)}}else"function"==typeof o&&(i.preventDefault(),e=l?_c(a,l):new FormData(a),_l(n,{pending:!0,data:e,method:a.method,action:o},o,e))},currentTarget:a}]})}}(l,e,r,n,a)}Ic(l,t)}))}function Hc(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Vc(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===o||(null!=(a=Dt(e,n))&&r.unshift(Hc(e,a,o)),null!=(a=Dt(e,t))&&r.push(Hc(e,a,o))),3===e.tag)return r;e=e.return}return[]}function $c(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Wc(e,t,n,r,a){for(var o=t._reactName,l=[];null!==n&&n!==r;){var i=n,s=i.alternate,u=i.stateNode;if(i=i.tag,null!==s&&s===r)break;5!==i&&26!==i&&27!==i||null===u||(s=u,a?null!=(u=Dt(n,o))&&l.unshift(Hc(n,u,s)):a||null!=(u=Dt(n,o))&&l.push(Hc(n,u,s))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Qc=/\r\n?/g,Kc=/\u0000|\uFFFD/g;function Gc(e){return("string"==typeof e?e:""+e).replace(Qc,"\n").replace(Kc,"")}function Xc(e,t){return t=Gc(t),Gc(e)===t}function Yc(){}function Jc(e,t,n,r,a,o){switch(n){case"children":"string"==typeof r?"body"===t||"textarea"===t&&""===r||St(e,r):("number"==typeof r||"bigint"==typeof r)&&"body"!==t&&St(e,""+r);break;case"className":nt(e,"class",r);break;case"tabIndex":nt(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":nt(e,n,r);break;case"style":Ct(e,r,o);break;case"data":if("object"!==t){nt(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"==typeof r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=Ot(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"==typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof o&&("formAction"===n?("input"!==t&&Jc(e,t,"name",a.name,a,null),Jc(e,t,"formEncType",a.formEncType,a,null),Jc(e,t,"formMethod",a.formMethod,a,null),Jc(e,t,"formTarget",a.formTarget,a,null)):(Jc(e,t,"encType",a.encType,a,null),Jc(e,t,"method",a.method,a,null),Jc(e,t,"target",a.target,a,null))),null==r||"symbol"==typeof r||"boolean"==typeof r){e.removeAttribute(n);break}r=Ot(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Yc);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(l(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"muted":e.muted=r&&"function"!=typeof r&&"symbol"!=typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"==typeof r||"boolean"==typeof r||"symbol"==typeof r){e.removeAttribute("xlink:href");break}n=Ot(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!=typeof r&&"symbol"!=typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!=typeof r&&"symbol"!=typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"==typeof r||"symbol"==typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":Dc("beforetoggle",e),Dc("toggle",e),tt(e,"popover",r);break;case"xlinkActuate":rt(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":rt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":rt(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":rt(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":rt(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":rt(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":rt(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":rt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":rt(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":tt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&tt(e,n=Pt.get(n)||n,r)}}function Zc(e,t,n,r,a,o){switch(n){case"style":Ct(e,r,o);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!=typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=a.children)throw Error(l(60));e.innerHTML=n}}break;case"children":"string"==typeof r?St(e,r):("number"==typeof r||"bigint"==typeof r)&&St(e,""+r);break;case"onScroll":null!=r&&Dc("scroll",e);break;case"onScrollEnd":null!=r&&Dc("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Yc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Qe.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(a=n.endsWith("Capture"),t=n.slice(2,a?n.length-7:void 0),"function"==typeof(o=null!=(o=e[Le]||null)?o[n]:null)&&e.removeEventListener(t,o,a),"function"!=typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):tt(e,n,r):("function"!=typeof o&&null!==o&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,a)))}}function ed(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Dc("error",e),Dc("load",e);var r,a=!1,o=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":a=!0;break;case"srcSet":o=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Jc(e,t,r,i,n,null)}}return o&&Jc(e,t,"srcSet",n.srcSet,n,null),void(a&&Jc(e,t,"src",n.src,n,null));case"input":Dc("invalid",e);var s=r=i=o=null,u=null,c=null;for(a in n)if(n.hasOwnProperty(a)){var d=n[a];if(null!=d)switch(a){case"name":o=d;break;case"type":i=d;break;case"checked":u=d;break;case"defaultChecked":c=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(l(137,t));break;default:Jc(e,t,a,d,n,null)}}return yt(e,r,s,u,c,i,o,!1),void dt(e);case"select":for(o in Dc("invalid",e),a=i=r=null,n)if(n.hasOwnProperty(o)&&null!=(s=n[o]))switch(o){case"value":r=s;break;case"defaultValue":i=s;break;case"multiple":a=s;default:Jc(e,t,o,s,n,null)}return t=r,n=i,e.multiple=!!a,void(null!=t?bt(e,!!a,t,!1):null!=n&&bt(e,!!a,n,!0));case"textarea":for(i in Dc("invalid",e),r=o=a=null,n)if(n.hasOwnProperty(i)&&null!=(s=n[i]))switch(i){case"value":a=s;break;case"defaultValue":o=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(l(91));break;default:Jc(e,t,i,s,n,null)}return kt(e,a,o,r),void dt(e);case"option":for(u in n)n.hasOwnProperty(u)&&null!=(a=n[u])&&("selected"===u?e.selected=a&&"function"!=typeof a&&"symbol"!=typeof a:Jc(e,t,u,a,n,null));return;case"dialog":Dc("beforetoggle",e),Dc("toggle",e),Dc("cancel",e),Dc("close",e);break;case"iframe":case"object":Dc("load",e);break;case"video":case"audio":for(a=0;a<zc.length;a++)Dc(zc[a],e);break;case"image":Dc("error",e),Dc("load",e);break;case"details":Dc("toggle",e);break;case"embed":case"source":case"link":Dc("error",e),Dc("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(c in n)if(n.hasOwnProperty(c)&&null!=(a=n[c]))switch(c){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Jc(e,t,c,a,n,null)}return;default:if(Nt(t)){for(d in n)n.hasOwnProperty(d)&&void 0!==(a=n[d])&&Zc(e,t,d,a,n,void 0);return}}for(s in n)n.hasOwnProperty(s)&&null!=(a=n[s])&&Jc(e,t,s,a,n,null)}var td=null,nd=null;function rd(e){return 9===e.nodeType?e:e.ownerDocument}function ad(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function od(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function ld(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var id=null,sd="function"==typeof setTimeout?setTimeout:void 0,ud="function"==typeof clearTimeout?clearTimeout:void 0,cd="function"==typeof Promise?Promise:void 0,dd="function"==typeof queueMicrotask?queueMicrotask:void 0!==cd?function(e){return cd.resolve(null).then(e).catch(fd)}:sd;function fd(e){setTimeout((function(){throw e}))}function pd(e){return"head"===e}function md(e,t){var n=t,r=0,a=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0<r&&8>r){n=r;var l=e.ownerDocument;if(1&n&&kd(l.documentElement),2&n&&kd(l.body),4&n)for(kd(n=l.head),l=n.firstChild;l;){var i=l.nextSibling,s=l.nodeName;l[Me]||"SCRIPT"===s||"STYLE"===s||"LINK"===s&&"stylesheet"===l.rel.toLowerCase()||n.removeChild(l),l=i}}if(0===a)return e.removeChild(o),void Tf(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=o}while(n);Tf(t)}function gd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":gd(n),Ue(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function hd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function yd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function bd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function wd(e,t,n){switch(t=rd(n),e){case"html":if(!(e=t.documentElement))throw Error(l(452));return e;case"head":if(!(e=t.head))throw Error(l(453));return e;case"body":if(!(e=t.body))throw Error(l(454));return e;default:throw Error(l(451))}}function kd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);Ue(e)}var Sd=new Map,Ed=new Set;function xd(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Cd=F.d;F.d={f:function(){var e=Cd.f(),t=Bu();return e||t},r:function(e){var t=qe(e);null!==t&&5===t.tag&&"form"===t.type?Ll(t):Cd.r(e)},D:function(e){Cd.D(e),Pd("dns-prefetch",e,null)},C:function(e,t){Cd.C(e,t),Pd("preconnect",e,t)},L:function(e,t,n){Cd.L(e,t,n);var r=Nd;if(r&&e&&t){var a='link[rel="preload"][as="'+gt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+gt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(a+='[imagesizes="'+gt(n.imageSizes)+'"]')):a+='[href="'+gt(e)+'"]';var o=a;switch(t){case"style":o=Od(e);break;case"script":o=Ad(e)}Sd.has(o)||(e=f({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Sd.set(o,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(Rd(o))||"script"===t&&r.querySelector(Ld(o))||(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}},m:function(e,t){Cd.m(e,t);var n=Nd;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+gt(r)+'"][href="'+gt(e)+'"]',o=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":o=Ad(e)}if(!Sd.has(o)&&(e=f({rel:"modulepreload",href:e},t),Sd.set(o,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Ld(o)))return}ed(r=n.createElement("link"),"link",e),$e(r),n.head.appendChild(r)}}},X:function(e,t){Cd.X(e,t);var n=Nd;if(n&&e){var r=Ve(n).hoistableScripts,a=Ad(e),o=r.get(a);o||((o=n.querySelector(Ld(a)))||(e=f({src:e,async:!0},t),(t=Sd.get(a))&&Dd(e,t),$e(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}},S:function(e,t,n){Cd.S(e,t,n);var r=Nd;if(r&&e){var a=Ve(r).hoistableStyles,o=Od(e);t=t||"default";var l=a.get(o);if(!l){var i={loading:0,preload:null};if(l=r.querySelector(Rd(o)))i.loading=5;else{e=f({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Sd.get(o))&&Id(e,n);var s=l=r.createElement("link");$e(s),ed(s,"link",e),s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),s.addEventListener("load",(function(){i.loading|=1})),s.addEventListener("error",(function(){i.loading|=2})),i.loading|=4,Fd(l,t,r)}l={type:"stylesheet",instance:l,count:1,state:i},a.set(o,l)}}},M:function(e,t){Cd.M(e,t);var n=Nd;if(n&&e){var r=Ve(n).hoistableScripts,a=Ad(e),o=r.get(a);o||((o=n.querySelector(Ld(a)))||(e=f({src:e,async:!0,type:"module"},t),(t=Sd.get(a))&&Dd(e,t),$e(o=n.createElement("script")),ed(o,"link",e),n.head.appendChild(o)),o={type:"script",instance:o,count:1,state:null},r.set(a,o))}}};var Nd="undefined"==typeof document?null:document;function Pd(e,t,n){var r=Nd;if(r&&"string"==typeof t&&t){var a=gt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"==typeof n&&(a+='[crossorigin="'+n+'"]'),Ed.has(a)||(Ed.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(ed(t=r.createElement("link"),"link",e),$e(t),r.head.appendChild(t)))}}function Td(e,t,n,r){var a,o,i,s,u=(u=V.current)?xd(u):null;if(!u)throw Error(l(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Od(n.href),(r=(n=Ve(u).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Od(n.href);var c=Ve(u).hoistableStyles,d=c.get(e);if(d||(u=u.ownerDocument||u,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},c.set(e,d),(c=u.querySelector(Rd(e)))&&!c._p&&(d.instance=c,d.state.loading=5),Sd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Sd.set(e,n),c||(a=u,o=e,i=n,s=d.state,a.querySelector('link[rel="preload"][as="style"]['+o+"]")?s.loading=1:(o=a.createElement("link"),s.preload=o,o.addEventListener("load",(function(){return s.loading|=1})),o.addEventListener("error",(function(){return s.loading|=2})),ed(o,"link",i),$e(o),a.head.appendChild(o))))),t&&null===r)throw Error(l(528,""));return d}if(t&&null!==r)throw Error(l(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Ad(n),(r=(n=Ve(u).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,e))}}function Od(e){return'href="'+gt(e)+'"'}function Rd(e){return'link[rel="stylesheet"]['+e+"]"}function _d(e){return f({},e,{"data-precedence":e.precedence,precedence:null})}function Ad(e){return'[src="'+gt(e)+'"]'}function Ld(e){return"script[async]"+e}function zd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+gt(n.href)+'"]');if(r)return t.instance=r,$e(r),r;var a=f({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return $e(r=(e.ownerDocument||e).createElement("style")),ed(r,"style",a),Fd(r,n.precedence,e),t.instance=r;case"stylesheet":a=Od(n.href);var o=e.querySelector(Rd(a));if(o)return t.state.loading|=4,t.instance=o,$e(o),o;r=_d(n),(a=Sd.get(a))&&Id(r,a),$e(o=(e.ownerDocument||e).createElement("link"));var i=o;return i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),ed(o,"link",r),t.state.loading|=4,Fd(o,n.precedence,e),t.instance=o;case"script":return o=Ad(n.src),(a=e.querySelector(Ld(o)))?(t.instance=a,$e(a),a):(r=n,(a=Sd.get(o))&&Dd(r=f({},n),a),$e(a=(e=e.ownerDocument||e).createElement("script")),ed(a,"link",r),e.head.appendChild(a),t.instance=a);case"void":return null;default:throw Error(l(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Fd(r,n.precedence,e));return t.instance}function Fd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,o=a,l=0;l<r.length;l++){var i=r[l];if(i.dataset.precedence===t)o=i;else if(o!==a)break}o?o.parentNode.insertBefore(e,o.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Id(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Dd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var jd=null;function Md(e,t,n){if(null===jd){var r=new Map,a=jd=new Map;a.set(n,r)}else(r=(a=jd).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var o=n[a];if(!(o[Me]||o[Ae]||"link"===e&&"stylesheet"===o.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==o.namespaceURI){var l=o.getAttribute(t)||"";l=e+l;var i=r.get(l);i?i.push(o):r.set(l,[o])}}return r}function Ud(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function Bd(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var qd=null;function Hd(){}function Vd(){if(this.count--,0===this.count)if(this.stylesheets)Wd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var $d=null;function Wd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,$d=new Map,t.forEach(Qd,e),$d=null,Vd.call(e))}function Qd(e,t){if(!(4&t.state.loading)){var n=$d.get(e);if(n)var r=n.get(null);else{n=new Map,$d.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),o=0;o<a.length;o++){var l=a[o];"LINK"!==l.nodeName&&"not all"===l.getAttribute("media")||(n.set(l.dataset.precedence,l),r=l)}r&&n.set(null,r)}l=(a=t.instance).getAttribute("data-precedence"),(o=n.get(l)||r)===r&&n.set(null,a),n.set(l,a),this.count++,r=Vd.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),o?o.parentNode.insertBefore(a,o.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Kd={$$typeof:k,Provider:null,Consumer:null,_currentValue:I,_currentValue2:I,_threadCount:0};function Gd(e,t,n,r,a,o,l,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=xe(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=xe(0),this.hiddenUpdates=xe(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=o,this.onRecoverableError=l,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Xd(e,t,n,r,a,o,l,i,s,u,c,d){return e=new Gd(e,t,n,l,i,s,u,d),t=1,!0===o&&(t|=24),o=Ir(3,null,null,t),e.current=o,o.stateNode=e,(t=La()).refCount++,e.pooledCache=t,t.refCount++,o.memoizedState={element:r,isDehydrated:n,cache:t},no(o),e}function Yd(e){return e?e=zr:zr}function Jd(e,t,n,r,a,o){a=Yd(a),null===r.context?r.context=a:r.pendingContext=a,(r=ao(t)).payload={element:n},null!==(o=void 0===o?null:o)&&(r.callback=o),null!==(n=oo(e,r,t))&&(Iu(n,0,t),lo(n,e,t))}function Zd(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function ef(e,t){Zd(e,t),(e=e.alternate)&&Zd(e,t)}function tf(e){if(13===e.tag){var t=_r(e,67108864);null!==t&&Iu(t,0,67108864),ef(e,67108864)}}var nf=!0;function rf(e,t,n,r){var a=z.T;z.T=null;var o=F.p;try{F.p=2,of(e,t,n,r)}finally{F.p=o,z.T=a}}function af(e,t,n,r){var a=z.T;z.T=null;var o=F.p;try{F.p=8,of(e,t,n,r)}finally{F.p=o,z.T=a}}function of(e,t,n,r){if(nf){var a=lf(r);if(null===a)qc(e,t,r,sf,n),bf(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return ff=wf(ff,e,t,n,r,a),!0;case"dragenter":return pf=wf(pf,e,t,n,r,a),!0;case"mouseover":return mf=wf(mf,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return gf.set(o,wf(gf.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,hf.set(o,wf(hf.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(bf(e,r),4&t&&-1<vf.indexOf(e)){for(;null!==a;){var o=qe(a);if(null!==o)switch(o.tag){case 3:if((o=o.stateNode).current.memoizedState.isDehydrated){var l=ve(o.pendingLanes);if(0!==l){var i=o;for(i.pendingLanes|=2,i.entangledLanes|=2;l;){var s=1<<31-pe(l);i.entanglements[1]|=s,l&=~s}Sc(o),!(6&nu)&&(Su=te()+500,Ec(0,!1))}}break;case 13:null!==(i=_r(o,2))&&Iu(i,0,2),Bu(),ef(o,2)}if(null===(o=lf(r))&&qc(e,t,r,sf,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else qc(e,t,r,null,n)}}function lf(e){return uf(e=_t(e))}var sf=null;function uf(e){if(sf=null,null!==(e=Be(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=u(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return sf=e,null}function cf(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ne()){case re:return 2;case ae:return 8;case oe:case le:return 32;case ie:return 268435456;default:return 32}default:return 32}}var df=!1,ff=null,pf=null,mf=null,gf=new Map,hf=new Map,yf=[],vf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function bf(e,t){switch(e){case"focusin":case"focusout":ff=null;break;case"dragenter":case"dragleave":pf=null;break;case"mouseover":case"mouseout":mf=null;break;case"pointerover":case"pointerout":gf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":hf.delete(t.pointerId)}}function wf(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&null!==(t=qe(t))&&tf(t),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function kf(e){var t=Be(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=u(n)))return e.blockedOn=t,void function(e){var t=F.p;try{return F.p=e,function(){if(13===n.tag){var e=zu();e=Te(e);var t=_r(n,e);null!==t&&Iu(t,0,e),ef(n,e)}}()}finally{F.p=t}}(e.priority)}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Sf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=lf(e.nativeEvent);if(null!==n)return null!==(t=qe(n))&&tf(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Rt=r,n.target.dispatchEvent(r),Rt=null,t.shift()}return!0}function Ef(e,t,n){Sf(e)&&n.delete(t)}function xf(){df=!1,null!==ff&&Sf(ff)&&(ff=null),null!==pf&&Sf(pf)&&(pf=null),null!==mf&&Sf(mf)&&(mf=null),gf.forEach(Ef),hf.forEach(Ef)}function Cf(e,t){e.blockedOn===t&&(e.blockedOn=null,df||(df=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,xf)))}var Nf=null;function Pf(e){Nf!==e&&(Nf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,(function(){Nf===e&&(Nf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],a=e[t+2];if("function"!=typeof r){if(null===uf(r||n))continue;break}var o=qe(n);null!==o&&(e.splice(t,3),t-=3,_l(o,{pending:!0,data:a,method:n.method,action:r},r,a))}})))}function Tf(e){function t(t){return Cf(t,e)}null!==ff&&Cf(ff,e),null!==pf&&Cf(pf,e),null!==mf&&Cf(mf,e),gf.forEach(t),hf.forEach(t);for(var n=0;n<yf.length;n++){var r=yf[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<yf.length&&null===(n=yf[0]).blockedOn;)kf(n),null===n.blockedOn&&yf.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],o=n[r+1],l=a[Le]||null;if("function"==typeof o)l||Pf(n);else if(l){var i=null;if(o&&o.hasAttribute("formAction")){if(a=o,l=o[Le]||null)i=l.formAction;else if(null!==uf(a))continue}else i=l.action;"function"==typeof i?n[r+1]=i:(n.splice(r,3),r-=3),Pf(n)}}}function Of(e){this._internalRoot=e}function Rf(e){this._internalRoot=e}Rf.prototype.render=Of.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Jd(t.current,zu(),e,t,null,null)},Rf.prototype.unmount=Of.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;Jd(e.current,2,null,e,null,null),Bu(),t[ze]=null}},Rf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Re();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yf.length&&0!==t&&t<yf[n].priority;n++);yf.splice(n,0,e),0===n&&kf(e)}};var _f=a.version;if("19.1.0"!==_f)throw Error(l(527,_f,"19.1.0"));F.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var o=a.alternate;if(null===o){if(null!==(r=a.return)){n=r;continue}break}if(a.child===o.child){for(o=a.child;o;){if(o===n)return c(a),e;if(o===r)return c(a),t;o=o.sibling}throw Error(l(188))}if(n.return!==r.return)n=a,r=o;else{for(var i=!1,u=a.child;u;){if(u===n){i=!0,n=a,r=o;break}if(u===r){i=!0,r=a,n=o;break}u=u.sibling}if(!i){for(u=o.child;u;){if(u===n){i=!0,n=o,r=a;break}if(u===r){i=!0,r=o,n=a;break}u=u.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(t),null===(e=null!==e?d(e):null)?null:e.stateNode};var Af={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:z,reconcilerVersion:"19.1.0"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Lf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Lf.isDisabled&&Lf.supportsFiber)try{ce=Lf.inject(Af),de=Lf}catch(e){}}t.createRoot=function(e,t){if(!i(e))throw Error(l(299));var n=!1,r="",a=vi,o=bi,s=wi;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(a=t.onUncaughtError),void 0!==t.onCaughtError&&(o=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Xd(e,1,!1,null,0,n,r,a,o,s,0,null),e[ze]=t.current,Uc(e),new Of(t)},t.hydrateRoot=function(e,t,n){if(!i(e))throw Error(l(299));var r=!1,a="",o=vi,s=bi,u=wi,c=null;return null!=n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(a=n.identifierPrefix),void 0!==n.onUncaughtError&&(o=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(u=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(c=n.formState)),(t=Xd(e,1,!0,t,0,r,a,o,s,u,0,c)).context=Yd(null),n=t.current,(a=ao(r=Te(r=zu()))).callback=null,oo(n,a,r),n=r,t.current.lanes=n,Ce(t,n),Sc(t),e[ze]=t.current,Uc(e),new Rf(t)},t.version="19.1.0"},338:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(247)},477:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,l=a>>>1;r<l;){var i=2*(r+1)-1,s=e[i],u=i+1,c=e[u];if(0>o(s,n))u<a&&0>o(c,s)?(e[r]=c,e[u]=n,r=u):(e[r]=s,e[i]=n,r=i);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var u=[],c=[],d=1,f=null,p=3,m=!1,g=!1,h=!1,y=!1,v="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,w="undefined"!=typeof setImmediate?setImmediate:null;function k(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function S(e){if(h=!1,k(e),!g)if(null!==r(u))g=!0,x||(x=!0,E());else{var t=r(c);null!==t&&A(S,t.startTime-e)}}var E,x=!1,C=-1,N=5,P=-1;function T(){return!(!y&&t.unstable_now()-P<N)}function O(){if(y=!1,x){var e=t.unstable_now();P=e;var n=!0;try{e:{g=!1,h&&(h=!1,b(C),C=-1),m=!0;var o=p;try{t:{for(k(e),f=r(u);null!==f&&!(f.expirationTime>e&&T());){var l=f.callback;if("function"==typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=e);if(e=t.unstable_now(),"function"==typeof i){f.callback=i,k(e),n=!0;break t}f===r(u)&&a(u),k(e)}else a(u);f=r(u)}if(null!==f)n=!0;else{var s=r(c);null!==s&&A(S,s.startTime-e),n=!1}}break e}finally{f=null,p=o,m=!1}n=void 0}}finally{n?E():x=!1}}}if("function"==typeof w)E=function(){w(O)};else if("undefined"!=typeof MessageChannel){var R=new MessageChannel,_=R.port2;R.port1.onmessage=O,E=function(){_.postMessage(null)}}else E=function(){v(O,0)};function A(e,n){C=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):N=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_requestPaint=function(){y=!0},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var l=t.unstable_now();switch(o="object"==typeof o&&null!==o&&"number"==typeof(o=o.delay)&&0<o?l+o:l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:i=o+i,sortIndex:-1},o>l?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(h?(b(C),C=-1):h=!0,A(S,o-l))):(e.sortIndex=i,n(u,e),g||m||(g=!0,x||(x=!0,E()))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},540:(e,t,n)=>{e.exports=n(869)},869:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator,m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,h={};function y(e,t,n){this.props=e,this.context=t,this.refs=h,this.updater=n||m}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=h,this.updater=n||m}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,g(w,y.prototype),w.isPureReactComponent=!0;var k=Array.isArray,S={H:null,A:null,T:null,S:null,V:null},E=Object.prototype.hasOwnProperty;function x(e,t,r,a,o,l){return r=l.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:l}}function C(e){return"object"==typeof e&&null!==e&&e.$$typeof===n}var N=/\/+/g;function P(e,t){return"object"==typeof e&&null!==e&&null!=e.key?(n=""+e.key,r={"=":"=0",":":"=2"},"$"+n.replace(/[=:]/g,(function(e){return r[e]}))):t.toString(36);var n,r}function T(){}function O(e,t,a,o,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s,u,c=!1;if(null===e)c=!0;else switch(i){case"bigint":case"string":case"number":c=!0;break;case"object":switch(e.$$typeof){case n:case r:c=!0;break;case f:return O((c=e._init)(e._payload),t,a,o,l)}}if(c)return l=l(e),c=""===o?"."+P(e,0):o,k(l)?(a="",null!=c&&(a=c.replace(N,"$&/")+"/"),O(l,t,a,"",(function(e){return e}))):null!=l&&(C(l)&&(s=l,u=a+(null==l.key||e&&e.key===l.key?"":(""+l.key).replace(N,"$&/")+"/")+c,l=x(s.type,u,void 0,0,0,s.props)),t.push(l)),1;c=0;var d,m=""===o?".":o+":";if(k(e))for(var g=0;g<e.length;g++)c+=O(o=e[g],t,a,i=m+P(o,g),l);else if("function"==typeof(g=null===(d=e)||"object"!=typeof d?null:"function"==typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=g.call(e),g=0;!(o=e.next()).done;)c+=O(o=o.value,t,a,i=m+P(o,g++),l);else if("object"===i){if("function"==typeof e.then)return O(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"==typeof e.status?e.then(T,T):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,a,o,l);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return c}function R(e,t,n){if(null==e)return e;var r=[],a=0;return O(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function _(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function L(){}t.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=l,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=S,t.__COMPILER_RUNTIME={__proto__:null,c:function(e){return S.H.useMemoCache(e)}},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null==e)throw Error("The argument must be a React element, but you passed "+e+".");var r=g({},e.props),a=e.key;if(null!=t)for(o in t.ref,void 0!==t.key&&(a=""+t.key),t)!E.call(t,o)||"key"===o||"__self"===o||"__source"===o||"ref"===o&&void 0===t.ref||(r[o]=t[o]);var o=arguments.length-2;if(1===o)r.children=n;else if(1<o){for(var l=Array(o),i=0;i<o;i++)l[i]=arguments[i+2];r.children=l}return x(e.type,a,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,a={},o=null;if(null!=t)for(r in void 0!==t.key&&(o=""+t.key),t)E.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(a[r]=t[r]);var l=arguments.length-2;if(1===l)a.children=n;else if(1<l){for(var i=Array(l),s=0;s<l;s++)i[s]=arguments[s+2];a.children=i}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===a[r]&&(a[r]=l[r]);return x(e,o,void 0,0,0,a)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:_}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=S.T,n={};S.T=n;try{var r=e(),a=S.S;null!==a&&a(n,r),"object"==typeof r&&null!==r&&"function"==typeof r.then&&r.then(L,A)}catch(e){A(e)}finally{S.T=t}},t.unstable_useCacheRefresh=function(){return S.H.useCacheRefresh()},t.use=function(e){return S.H.use(e)},t.useActionState=function(e,t,n){return S.H.useActionState(e,t,n)},t.useCallback=function(e,t){return S.H.useCallback(e,t)},t.useContext=function(e){return S.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return S.H.useDeferredValue(e,t)},t.useEffect=function(e,t,n){var r=S.H;if("function"==typeof n)throw Error("useEffect CRUD overload is not enabled in this build of React.");return r.useEffect(e,t)},t.useId=function(){return S.H.useId()},t.useImperativeHandle=function(e,t,n){return S.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return S.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return S.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return S.H.useMemo(e,t)},t.useOptimistic=function(e,t){return S.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return S.H.useReducer(e,t,n)},t.useRef=function(e){return S.H.useRef(e)},t.useState=function(e){return S.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return S.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return S.H.useTransition()},t.version="19.1.0"},961:(e,t,n)=>{!function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(e){console.error(e)}}(),e.exports=n(221)},982:(e,t,n)=>{e.exports=n(477)}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};n.r(r),n.d(r,{default:()=>Zt});var a={};n.r(a),n.d(a,{hasBrowserEnv:()=>ge,hasStandardBrowserEnv:()=>ye,hasStandardBrowserWebWorkerEnv:()=>ve,navigator:()=>he,origin:()=>be});var o=n(540),l=n(338);function i(e,t){return function(){return e.apply(t,arguments)}}const{toString:s}=Object.prototype,{getPrototypeOf:u}=Object,{iterator:c,toStringTag:d}=Symbol,f=(p=Object.create(null),e=>{const t=s.call(e);return p[t]||(p[t]=t.slice(8,-1).toLowerCase())});var p;const m=e=>(e=e.toLowerCase(),t=>f(t)===e),g=e=>t=>typeof t===e,{isArray:h}=Array,y=g("undefined"),v=m("ArrayBuffer"),b=g("string"),w=g("function"),k=g("number"),S=e=>null!==e&&"object"==typeof e,E=e=>{if("object"!==f(e))return!1;const t=u(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||d in e||c in e)},x=m("Date"),C=m("File"),N=m("Blob"),P=m("FileList"),T=m("URLSearchParams"),[O,R,_,A]=["ReadableStream","Request","Response","Headers"].map(m);function L(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,a;if("object"!=typeof e&&(e=[e]),h(e))for(r=0,a=e.length;r<a;r++)t.call(null,e[r],r,e);else{const a=n?Object.getOwnPropertyNames(e):Object.keys(e),o=a.length;let l;for(r=0;r<o;r++)l=a[r],t.call(null,e[l],l,e)}}function z(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const F="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,I=e=>!y(e)&&e!==F,D=(j="undefined"!=typeof Uint8Array&&u(Uint8Array),e=>j&&e instanceof j);var j;const M=m("HTMLFormElement"),U=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),B=m("RegExp"),q=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};L(n,((n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)})),Object.defineProperties(e,r)},H=m("AsyncFunction"),V=($="function"==typeof setImmediate,W=w(F.postMessage),$?setImmediate:W?(Q=`axios@${Math.random()}`,K=[],F.addEventListener("message",(({source:e,data:t})=>{e===F&&t===Q&&K.length&&K.shift()()}),!1),e=>{K.push(e),F.postMessage(Q,"*")}):e=>setTimeout(e));var $,W,Q,K;const G="undefined"!=typeof queueMicrotask?queueMicrotask.bind(F):"undefined"!=typeof process&&process.nextTick||V,X={isArray:h,isArrayBuffer:v,isBuffer:function(e){return null!==e&&!y(e)&&null!==e.constructor&&!y(e.constructor)&&w(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||w(e.append)&&("formdata"===(t=f(e))||"object"===t&&w(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&v(e.buffer),t},isString:b,isNumber:k,isBoolean:e=>!0===e||!1===e,isObject:S,isPlainObject:E,isReadableStream:O,isRequest:R,isResponse:_,isHeaders:A,isUndefined:y,isDate:x,isFile:C,isBlob:N,isRegExp:B,isFunction:w,isStream:e=>S(e)&&w(e.pipe),isURLSearchParams:T,isTypedArray:D,isFileList:P,forEach:L,merge:function e(){const{caseless:t}=I(this)&&this||{},n={},r=(r,a)=>{const o=t&&z(n,a)||a;E(n[o])&&E(r)?n[o]=e(n[o],r):E(r)?n[o]=e({},r):h(r)?n[o]=r.slice():n[o]=r};for(let e=0,t=arguments.length;e<t;e++)arguments[e]&&L(arguments[e],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(L(t,((t,r)=>{n&&w(t)?e[r]=i(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,l;const i={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)l=a[o],r&&!r(l,e,t)||i[l]||(t[l]=e[l],i[l]=!0);e=!1!==n&&u(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:f,kindOfTest:m,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(h(e))return e;let t=e.length;if(!k(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[c]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:M,hasOwnProperty:U,hasOwnProp:U,reduceDescriptors:q,freezeMethods:e=>{q(e,((t,n)=>{if(w(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];w(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return h(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:z,global:F,isContextDefined:I,isSpecCompliantForm:function(e){return!!(e&&w(e.append)&&"FormData"===e[d]&&e[c])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(S(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=h(e)?[]:{};return L(e,((e,t)=>{const o=n(e,r+1);!y(o)&&(a[t]=o)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:H,isThenable:e=>e&&(S(e)||w(e))&&w(e.then)&&w(e.catch),setImmediate:V,asap:G,isIterable:e=>null!=e&&w(e[c])};function Y(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}X.inherits(Y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:X.toJSONObject(this.config),code:this.code,status:this.status}}});const J=Y.prototype,Z={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{Z[e]={value:e}})),Object.defineProperties(Y,Z),Object.defineProperty(J,"isAxiosError",{value:!0}),Y.from=(e,t,n,r,a,o)=>{const l=Object.create(J);return X.toFlatObject(e,l,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Y.call(l,e.message,t,n,r,a),l.cause=e,l.name=e.name,o&&Object.assign(l,o),l};const ee=Y;function te(e){return X.isPlainObject(e)||X.isArray(e)}function ne(e){return X.endsWith(e,"[]")?e.slice(0,-2):e}function re(e,t,n){return e?e.concat(t).map((function(e,t){return e=ne(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const ae=X.toFlatObject(X,{},null,(function(e){return/^is[A-Z]/.test(e)})),oe=function(e,t,n){if(!X.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=X.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!X.isUndefined(t[e])}))).metaTokens,a=n.visitor||u,o=n.dots,l=n.indexes,i=(n.Blob||"undefined"!=typeof Blob&&Blob)&&X.isSpecCompliantForm(t);if(!X.isFunction(a))throw new TypeError("visitor must be a function");function s(e){if(null===e)return"";if(X.isDate(e))return e.toISOString();if(X.isBoolean(e))return e.toString();if(!i&&X.isBlob(e))throw new ee("Blob is not supported. Use a Buffer instead.");return X.isArrayBuffer(e)||X.isTypedArray(e)?i&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let i=e;if(e&&!a&&"object"==typeof e)if(X.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(X.isArray(e)&&function(e){return X.isArray(e)&&!e.some(te)}(e)||(X.isFileList(e)||X.endsWith(n,"[]"))&&(i=X.toArray(e)))return n=ne(n),i.forEach((function(e,r){!X.isUndefined(e)&&null!==e&&t.append(!0===l?re([n],r,o):null===l?n:n+"[]",s(e))})),!1;return!!te(e)||(t.append(re(a,n,o),s(e)),!1)}const c=[],d=Object.assign(ae,{defaultVisitor:u,convertValue:s,isVisitable:te});if(!X.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!X.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),X.forEach(n,(function(n,o){!0===(!(X.isUndefined(n)||null===n)&&a.call(t,n,X.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])})),c.pop()}}(e),t};function le(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function ie(e,t){this._pairs=[],e&&oe(e,this,t)}const se=ie.prototype;se.append=function(e,t){this._pairs.push([e,t])},se.toString=function(e){const t=e?function(t){return e.call(this,t,le)}:le;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const ue=ie;function ce(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function de(e,t,n){if(!t)return e;const r=n&&n.encode||ce;X.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):X.isURLSearchParams(t)?t.toString():new ue(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const fe=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){X.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},pe={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},me={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:ue,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},ge="undefined"!=typeof window&&"undefined"!=typeof document,he="object"==typeof navigator&&navigator||void 0,ye=ge&&(!he||["ReactNative","NativeScript","NS"].indexOf(he.product)<0),ve="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,be=ge&&window.location.href||"http://localhost",we={...a,...me},ke=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const l=Number.isFinite(+o),i=a>=e.length;return o=!o&&X.isArray(r)?r.length:o,i?(X.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!l):(r[o]&&X.isObject(r[o])||(r[o]=[]),t(e,n,r[o],a)&&X.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!l)}if(X.isFormData(e)&&X.isFunction(e.entries)){const n={};return X.forEachEntry(e,((e,r)=>{t(function(e){return X.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null},Se={transitional:pe,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=X.isObject(e);if(a&&X.isHTMLForm(e)&&(e=new FormData(e)),X.isFormData(e))return r?JSON.stringify(ke(e)):e;if(X.isArrayBuffer(e)||X.isBuffer(e)||X.isStream(e)||X.isFile(e)||X.isBlob(e)||X.isReadableStream(e))return e;if(X.isArrayBufferView(e))return e.buffer;if(X.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return oe(e,new we.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return we.isNode&&X.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=X.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return oe(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e){if(X.isString(e))try{return(0,JSON.parse)(e),X.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Se.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(X.isResponse(e)||X.isReadableStream(e))return e;if(e&&X.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw ee.from(e,ee.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:we.classes.FormData,Blob:we.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};X.forEach(["delete","get","head","post","put","patch"],(e=>{Se.headers[e]={}}));const Ee=Se,xe=X.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ce=Symbol("internals");function Ne(e){return e&&String(e).trim().toLowerCase()}function Pe(e){return!1===e||null==e?e:X.isArray(e)?e.map(Pe):String(e)}function Te(e,t,n,r,a){return X.isFunction(r)?r.call(this,t,n):(a&&(t=n),X.isString(t)?X.isString(r)?-1!==t.indexOf(r):X.isRegExp(r)?r.test(t):void 0:void 0)}class Oe{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=Ne(t);if(!a)throw new Error("header name must be a non-empty string");const o=X.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=Pe(e))}const o=(e,t)=>X.forEach(e,((e,n)=>a(e,n,t)));if(X.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(X.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&xe[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(X.isObject(e)&&X.isIterable(e)){let n,r,a={};for(const t of e){if(!X.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?X.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=Ne(e)){const n=X.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(X.isFunction(t))return t.call(this,e,n);if(X.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=Ne(e)){const n=X.findKey(this,e);return!(!n||void 0===this[n]||t&&!Te(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=Ne(e)){const a=X.findKey(n,e);!a||t&&!Te(0,n[a],a,t)||(delete n[a],r=!0)}}return X.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!Te(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return X.forEach(this,((r,a)=>{const o=X.findKey(n,a);if(o)return t[o]=Pe(r),void delete t[a];const l=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();l!==a&&delete t[a],t[l]=Pe(r),n[l]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return X.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&X.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[Ce]=this[Ce]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=Ne(e);t[r]||(function(e,t){const n=X.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return X.isArray(e)?e.forEach(r):r(e),this}}Oe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),X.reduceDescriptors(Oe.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),X.freezeMethods(Oe);const Re=Oe;function _e(e,t){const n=this||Ee,r=t||n,a=Re.from(r.headers);let o=r.data;return X.forEach(e,(function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)})),a.normalize(),o}function Ae(e){return!(!e||!e.__CANCEL__)}function Le(e,t,n){ee.call(this,null==e?"canceled":e,ee.ERR_CANCELED,t,n),this.name="CanceledError"}X.inherits(Le,ee,{__CANCEL__:!0});const ze=Le;function Fe(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new ee("Request failed with status code "+n.status,[ee.ERR_BAD_REQUEST,ee.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Ie=(e,t,n=3)=>{let r=0;const a=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,l=0;return t=void 0!==t?t:1e3,function(i){const s=Date.now(),u=r[l];a||(a=s),n[o]=i,r[o]=s;let c=l,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===l&&(l=(l+1)%e),s-a<t)return;const f=u&&s-u;return f?Math.round(1e3*d/f):void 0}}(50,250);return function(e,t){let n,r,a=0,o=1e3/t;const l=(t,o=Date.now())=>{a=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),i=t-a;i>=o?l(e,t):(n=e,r||(r=setTimeout((()=>{r=null,l(n)}),o-i)))},()=>n&&l(n)]}((n=>{const o=n.loaded,l=n.lengthComputable?n.total:void 0,i=o-r,s=a(i);r=o,e({loaded:o,total:l,progress:l?o/l:void 0,bytes:i,rate:s||void 0,estimated:s&&l&&o<=l?(l-o)/s:void 0,event:n,lengthComputable:null!=l,[t?"download":"upload"]:!0})}),n)},De=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},je=e=>(...t)=>X.asap((()=>e(...t))),Me=we.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,we.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(we.origin),we.navigator&&/(msie|trident)/i.test(we.navigator.userAgent)):()=>!0,Ue=we.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const l=[e+"="+encodeURIComponent(t)];X.isNumber(n)&&l.push("expires="+new Date(n).toGMTString()),X.isString(r)&&l.push("path="+r),X.isString(a)&&l.push("domain="+a),!0===o&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Be(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const qe=e=>e instanceof Re?{...e}:e;function He(e,t){t=t||{};const n={};function r(e,t,n,r){return X.isPlainObject(e)&&X.isPlainObject(t)?X.merge.call({caseless:r},e,t):X.isPlainObject(t)?X.merge({},t):X.isArray(t)?t.slice():t}function a(e,t,n,a){return X.isUndefined(t)?X.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!X.isUndefined(t))return r(void 0,t)}function l(e,t){return X.isUndefined(t)?X.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function i(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const s={url:o,method:o,data:o,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:i,headers:(e,t,n)=>a(qe(e),qe(t),0,!0)};return X.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=s[r]||a,l=o(e[r],t[r],r);X.isUndefined(l)&&o!==i||(n[r]=l)})),n}const Ve=e=>{const t=He({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:l,headers:i,auth:s}=t;if(t.headers=i=Re.from(i),t.url=de(Be(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):""))),X.isFormData(r))if(we.hasStandardBrowserEnv||we.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if(!1!==(n=i.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];i.setContentType([e||"multipart/form-data",...t].join("; "))}if(we.hasStandardBrowserEnv&&(a&&X.isFunction(a)&&(a=a(t)),a||!1!==a&&Me(t.url))){const e=o&&l&&Ue.read(l);e&&i.set(o,e)}return t},$e="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Ve(e);let a=r.data;const o=Re.from(r.headers).normalize();let l,i,s,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function m(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(l),r.signal&&r.signal.removeEventListener("abort",l)}let g=new XMLHttpRequest;function h(){if(!g)return;const r=Re.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders());Fe((function(e){t(e),m()}),(function(e){n(e),m()}),{data:d&&"text"!==d&&"json"!==d?g.response:g.responseText,status:g.status,statusText:g.statusText,headers:r,config:e,request:g}),g=null}g.open(r.method.toUpperCase(),r.url,!0),g.timeout=r.timeout,"onloadend"in g?g.onloadend=h:g.onreadystatechange=function(){g&&4===g.readyState&&(0!==g.status||g.responseURL&&0===g.responseURL.indexOf("file:"))&&setTimeout(h)},g.onabort=function(){g&&(n(new ee("Request aborted",ee.ECONNABORTED,e,g)),g=null)},g.onerror=function(){n(new ee("Network Error",ee.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||pe;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new ee(t,a.clarifyTimeoutError?ee.ETIMEDOUT:ee.ECONNABORTED,e,g)),g=null},void 0===a&&o.setContentType(null),"setRequestHeader"in g&&X.forEach(o.toJSON(),(function(e,t){g.setRequestHeader(t,e)})),X.isUndefined(r.withCredentials)||(g.withCredentials=!!r.withCredentials),d&&"json"!==d&&(g.responseType=r.responseType),p&&([s,c]=Ie(p,!0),g.addEventListener("progress",s)),f&&g.upload&&([i,u]=Ie(f),g.upload.addEventListener("progress",i),g.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(l=t=>{g&&(n(!t||t.type?new ze(null,e,g):t),g.abort(),g=null)},r.cancelToken&&r.cancelToken.subscribe(l),r.signal&&(r.signal.aborted?l():r.signal.addEventListener("abort",l)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===we.protocols.indexOf(y)?n(new ee("Unsupported protocol "+y+":",ee.ERR_BAD_REQUEST,e)):g.send(a||null)}))},We=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,l();const t=e instanceof Error?e:this.reason;r.abort(t instanceof ee?t:new ze(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,a(new ee(`timeout ${t} of ms exceeded`,ee.ETIMEDOUT))}),t);const l=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:i}=r;return i.unsubscribe=()=>X.asap(l),i}},Qe=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Ke=(e,t,n,r)=>{const a=async function*(e,t){for await(const n of async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}}(e))yield*Qe(n,t)}(e,t);let o,l=0,i=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return i(),void e.close();let o=r.byteLength;if(n){let e=l+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(e){throw i(e),e}},cancel:e=>(i(e),a.return())},{highWaterMark:2})},Ge="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,Xe=Ge&&"function"==typeof ReadableStream,Ye=Ge&&("function"==typeof TextEncoder?(Je=new TextEncoder,e=>Je.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var Je;const Ze=(e,...t)=>{try{return!!e(...t)}catch(e){return!1}},et=Xe&&Ze((()=>{let e=!1;const t=new Request(we.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),tt=Xe&&Ze((()=>X.isReadableStream(new Response("").body))),nt={stream:tt&&(e=>e.body)};var rt;Ge&&(rt=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!nt[e]&&(nt[e]=X.isFunction(rt[e])?t=>t[e]():(t,n)=>{throw new ee(`Response type '${e}' is not supported`,ee.ERR_NOT_SUPPORT,n)})})));const at={http:null,xhr:$e,fetch:Ge&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:l,onDownloadProgress:i,onUploadProgress:s,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Ve(e);u=u?(u+"").toLowerCase():"text";let p,m=We([a,o&&o.toAbortSignal()],l);const g=m&&m.unsubscribe&&(()=>{m.unsubscribe()});let h;try{if(s&&et&&"get"!==n&&"head"!==n&&0!==(h=await(async(e,t)=>{const n=X.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(X.isBlob(e))return e.size;if(X.isSpecCompliantForm(e)){const t=new Request(we.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return X.isArrayBufferView(e)||X.isArrayBuffer(e)?e.byteLength:(X.isURLSearchParams(e)&&(e+=""),X.isString(e)?(await Ye(e)).byteLength:void 0)})(t):n})(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(X.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=De(h,Ie(je(s)));r=Ke(n.body,65536,e,t)}}X.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,{...f,signal:m,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0});let o=await fetch(p,f);const l=tt&&("stream"===u||"response"===u);if(tt&&(i||l&&g)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=X.toFiniteNumber(o.headers.get("content-length")),[n,r]=i&&De(t,Ie(je(i),!0))||[];o=new Response(Ke(o.body,65536,n,(()=>{r&&r(),g&&g()})),e)}u=u||"text";let y=await nt[X.findKey(nt,u)||"text"](o,e);return!l&&g&&g(),await new Promise(((t,n)=>{Fe(t,n,{data:y,headers:Re.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})}))}catch(t){if(g&&g(),t&&"TypeError"===t.name&&/Load failed|fetch/i.test(t.message))throw Object.assign(new ee("Network Error",ee.ERR_NETWORK,e,p),{cause:t.cause||t});throw ee.from(t,t&&t.code,e,p)}})};X.forEach(at,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(e){}Object.defineProperty(e,"adapterName",{value:t})}}));const ot=e=>`- ${e}`,lt=e=>X.isFunction(e)||null===e||!1===e,it=e=>{e=X.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!lt(n)&&(r=at[(t=String(n)).toLowerCase()],void 0===r))throw new ee(`Unknown adapter '${t}'`);if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));let n=t?e.length>1?"since :\n"+e.map(ot).join("\n"):" "+ot(e[0]):"as no adapter specified";throw new ee("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function st(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ze(null,e)}function ut(e){return st(e),e.headers=Re.from(e.headers),e.data=_e.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),it(e.adapter||Ee.adapter)(e).then((function(t){return st(e),t.data=_e.call(e,e.transformResponse,t),t.headers=Re.from(t.headers),t}),(function(t){return Ae(t)||(st(e),t&&t.response&&(t.response.data=_e.call(e,e.transformResponse,t.response),t.response.headers=Re.from(t.response.headers))),Promise.reject(t)}))}const ct="1.10.0",dt={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{dt[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const ft={};dt.transitional=function(e,t,n){function r(e,t){return"[Axios v"+ct+"] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new ee(r(a," has been removed"+(t?" in "+t:"")),ee.ERR_DEPRECATED);return t&&!ft[a]&&(ft[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},dt.spelling=function(e){return(t,n)=>(console.warn(`${n} is likely a misspelling of ${e}`),!0)};const pt={assertOptions:function(e,t,n){if("object"!=typeof e)throw new ee("options must be an object",ee.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],l=t[o];if(l){const t=e[o],n=void 0===t||l(t,o,e);if(!0!==n)throw new ee("option "+o+" must be "+n,ee.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new ee("Unknown option "+o,ee.ERR_BAD_OPTION)}},validators:dt},mt=pt.validators;class gt{constructor(e){this.defaults=e||{},this.interceptors={request:new fe,response:new fe}}async request(e,t){try{return await this._request(e,t)}catch(e){if(e instanceof Error){let t={};Error.captureStackTrace?Error.captureStackTrace(t):t=new Error;const n=t.stack?t.stack.replace(/^.+\n/,""):"";try{e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}catch(e){}}throw e}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=He(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&pt.assertOptions(n,{silentJSONParsing:mt.transitional(mt.boolean),forcedJSONParsing:mt.transitional(mt.boolean),clarifyTimeoutError:mt.transitional(mt.boolean)},!1),null!=r&&(X.isFunction(r)?t.paramsSerializer={serialize:r}:pt.assertOptions(r,{encode:mt.function,serialize:mt.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),pt.assertOptions(t,{baseUrl:mt.spelling("baseURL"),withXsrfToken:mt.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&X.merge(a.common,a[t.method]);a&&X.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=Re.concat(o,a);const l=[];let i=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(i=i&&e.synchronous,l.unshift(e.fulfilled,e.rejected))}));const s=[];let u;this.interceptors.response.forEach((function(e){s.push(e.fulfilled,e.rejected)}));let c,d=0;if(!i){const e=[ut.bind(this),void 0];for(e.unshift.apply(e,l),e.push.apply(e,s),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=l.length;let f=t;for(d=0;d<c;){const e=l[d++],t=l[d++];try{f=e(f)}catch(e){t.call(this,e);break}}try{u=ut.call(this,f)}catch(e){return Promise.reject(e)}for(d=0,c=s.length;d<c;)u=u.then(s[d++],s[d++]);return u}getUri(e){return de(Be((e=He(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}X.forEach(["delete","get","head","options"],(function(e){gt.prototype[e]=function(t,n){return this.request(He(n||{},{method:e,url:t,data:(n||{}).data}))}})),X.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(He(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}gt.prototype[e]=t(),gt.prototype[e+"Form"]=t(!0)}));const ht=gt;class yt{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new ze(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;return{token:new yt((function(t){e=t})),cancel:e}}}const vt=yt,bt={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(bt).forEach((([e,t])=>{bt[t]=e}));const wt=bt,kt=function e(t){const n=new ht(t),r=i(ht.prototype.request,n);return X.extend(r,ht.prototype,n,{allOwnKeys:!0}),X.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(He(t,n))},r}(Ee);kt.Axios=ht,kt.CanceledError=ze,kt.CancelToken=vt,kt.isCancel=Ae,kt.VERSION=ct,kt.toFormData=oe,kt.AxiosError=ee,kt.Cancel=kt.CanceledError,kt.all=function(e){return Promise.all(e)},kt.spread=function(e){return function(t){return e.apply(null,t)}},kt.isAxiosError=function(e){return X.isObject(e)&&!0===e.isAxiosError},kt.mergeConfig=He,kt.AxiosHeaders=Re,kt.formToJSON=e=>ke(X.isHTMLForm(e)?new FormData(e):e),kt.getAdapter=it,kt.HttpStatusCode=wt,kt.default=kt;const St=kt,Et="https://dev.cavinkare.in/botnexusdev/";console.log("API baseURL:",Et);const xt=St.create({baseURL:Et,headers:{"Content-Type":"application/json"}});xt.interceptors.request.use((e=>(console.log("API Request:",{method:e.method.toUpperCase(),url:e.baseURL+e.url,headers:e.headers,data:e.data}),e)),(e=>Promise.reject(e))),xt.interceptors.response.use((e=>(console.log("API Response:",{status:e.status,statusText:e.statusText,url:e.config.url,data:e.data}),e)),(e=>{var t,n,r,a,o,l,i;return console.error("API Error:",{message:e.message,status:null===(t=e.response)||void 0===t?void 0:t.status,statusText:null===(n=e.response)||void 0===n?void 0:n.statusText,url:null===(r=e.config)||void 0===r?void 0:r.url,baseURL:null===(a=e.config)||void 0===a?void 0:a.baseURL,fullURL:(null===(o=e.config)||void 0===o?void 0:o.baseURL)+(null===(l=e.config)||void 0===l?void 0:l.url),data:null===(i=e.response)||void 0===i?void 0:i.data}),Promise.reject(e)}));const Ct=xt;function Nt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Pt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Nt(Object(n),!0).forEach((function(t){Tt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Nt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Tt(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Ot=(0,o.createContext)(),Rt=e=>{let{children:t}=e;const[n,r]=(0,o.useState)([]),[a,l]=(0,o.useState)(!1),[i,s]=(0,o.useState)(null),[u,c]=(0,o.useState)(null),[d,f]=(0,o.useState)(null),[p,m]=(0,o.useState)(null);(0,o.useEffect)((()=>{const e=localStorage.getItem("conversationId");e&&(c(e),g(e))}),[]),(0,o.useEffect)((()=>{u&&localStorage.setItem("conversationId",u)}),[u]);const g=async e=>{try{l(!0);const t=await Ct.get("/chat/conversations/".concat(e));r(t.data.messages||[]),c(e)}catch(e){s("Failed to load conversation"),console.error("Error loading conversation:",e)}finally{l(!1)}},h=e=>{const t={role:"assistant",content:e};return r((e=>[...e,t])),t},y=(e,t)=>{try{var n,r;if(console.log("🔍 Processing form data with custom structure:"),console.log("📋 Form config provided:",!!t),console.log("📋 Form config name:",null==t?void 0:t.name),console.log("📋 Form config type:",typeof t),console.log("📋 Form config structure:",t),console.log("📋 Custom payload enabled:",null==t||null===(n=t.formConfig)||void 0===n||null===(n=n.submitApiConfig)||void 0===n||null===(n=n.customPayload)||void 0===n?void 0:n.enabled),!t)return console.log("❌ No form config provided, returning original data"),e;if(null==t||null===(r=t.formConfig)||void 0===r||null===(r=r.submitApiConfig)||void 0===r||null===(r=r.customPayload)||void 0===r||!r.enabled)return console.log("⚠️ Custom payload not enabled, returning original data"),e;const a=t.formConfig.submitApiConfig.customPayload.structure||{},o=t.formConfig.submitApiConfig.customPayload.mergeStrategy||"replace";console.log("🔧 Processing form data with custom structure:",a),console.log("📊 Original form data:",e);const l=t=>{if("string"==typeof t){const n=t.match(/^{{data\.(\w+)}}$/);if(n){const t=n[1];if(e.hasOwnProperty(t))return e[t]}let r=t;return Object.keys(e).forEach((t=>{const n="{{data.".concat(t,"}}"),a=e[t];if(void 0!==a){const e="object"==typeof a?JSON.stringify(a):a;r=r.replace(new RegExp(n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"g"),e)}})),r}if(Array.isArray(t))return t.map(l);if("object"==typeof t&&null!==t){const e={};return Object.keys(t).forEach((n=>{e[n]=l(t[n])})),e}return t};let i={};switch(o){case"replace":default:i=l(a);break;case"merge":i=Pt(Pt({},e),l(a));break;case"append":i=Pt(Pt({},e),{},{custom:l(a)})}return console.log("✅ Processed form data:",i),i}catch(t){return console.error("❌ Error processing form data with custom structure:",t),e}};return o.createElement(Ot.Provider,{value:{messages:n,loading:a,error:i,conversationId:u,activeForm:d,conversationalFlow:p,sendMessage:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"user";try{var a,o,i,p,g,v,b,w;if(s(null),"assistant"===t)return h(e);const S={role:"user",content:e};if(r((e=>[...e,S])),l(!0),(e=>{const t=e.toLowerCase();return["leave balance","leave status","how many leaves","remaining leaves","available leaves","leave quota","check my leave","my leave balance","leave information","leave details"].some((e=>t.includes(e)))})(e)){console.log("📋 Legacy leave balance query detected - this will be handled by dynamic APIs");const e=await(async()=>{try{const t=JSON.parse(localStorage.getItem("user"));if(!t||!t.empId||!t.token)return"You need to be logged in to check your leave balance.";const n=(new Date).getFullYear(),r=await St.get("https://dev.budgie.co.in/budgie/v3/api/employee/leave/".concat(t.empId,"/").concat(n),{headers:{Authorization:"Bearer ".concat(t.token),"Content-Type":"application/json"}});if(r.data&&!0===r.data.status){const e=r.data.data;let t="Here's your leave balance information:\n\n";if(e)if(Array.isArray(e))0===e.length?t+="You don't have any leave records for this year.":e.forEach((e=>{const n=e.leaveType||e.type||"Leave",r=e.balance||e.availableLeaves||e.remainingLeaves||0;t+="".concat(n,": ").concat(r," days remaining\n")}));else if("object"==typeof e)if(0===Object.keys(e).length)t+="You don't have any leave records for this year.";else if(e.leaveDetails&&Array.isArray(e.leaveDetails))e.leaveDetails.forEach((e=>{const n=e.leaveType||e.type||"Leave",r=e.balance||e.availableLeaves||e.remainingLeaves||0;t+="".concat(n,": ").concat(r," days remaining\n")}));else{const n=e.totalBalance||e.balance||e.availableLeaves||0;t+="Total leave balance: ".concat(n," days\n");let r=!1;["casual","sick","earned","compensatory","maternity","paternity"].forEach((n=>{void 0!==e[n]&&(r=!0,t+="".concat(n.charAt(0).toUpperCase()+n.slice(1)," leave: ").concat(e[n]," days\n"))})),r||n||(t+="No specific leave balance information available.")}else t+="No specific leave balance information available.";else t+="No leave balance information available for this year.";return t}{var e;const t=(null===(e=r.data)||void 0===e?void 0:e.message)||"No information available";return"I couldn't retrieve your leave balance at the moment. ".concat(t)}}catch(e){return console.error("Error fetching leave balance:",e),"There was an error retrieving your leave balance. Please try again later."}})(),t={role:"assistant",content:e,isLegacyResponse:!0};return await new Promise((e=>setTimeout(e,1e3))),r((e=>[...e,t])),l(!1),t}if((e=>{const t=e.toLowerCase();return["regularization apply","apply regularization","regularization","regularize attendance","attendance regularization","regularize","attendance correction","correct attendance","attendance issue","missed punch","punch missing"].some((e=>t.includes(e)))})(e)){console.log("📋 Legacy regularization query detected - this will be handled by dynamic APIs"),console.log("🚀 Calling fetchRegularizationData...");const e=await(async()=>{try{console.log("🔧 Starting fetchRegularizationData with dynamic config...");const r=JSON.parse(localStorage.getItem("user")||"{}").token;if(console.log("🔑 Token exists:",!!r),!r)return console.log("❌ No token found"),{message:"Authentication required. Please log in first.",apiResponse:null};console.log("🌐 Calling backend endpoint: /api/chat/regularization-data");const a=await Ct.get("/auth/regularization-quick-add",{headers:{Authorization:"Bearer ".concat(r)}});if(console.log("✅ Regularization data fetched via dynamic config:",a.data),a.data.success&&a.data.data){var e;const t=a.data.data;return console.log("📊 Regularization response data:",{message:t.message,attendanceInfoLength:null===(e=t.attendanceInfo)||void 0===e?void 0:e.length,isRegularizationData:t.isRegularizationData}),{message:t.message,apiResponse:t.apiResponse,attendanceInfo:t.attendanceInfo,isRegularizationData:t.isRegularizationData}}var t,n;return console.log("⚠️ Backend response indicates failure:",a.data),{message:(null===(t=a.data.data)||void 0===t?void 0:t.message)||"❌ **Error fetching regularization data**\n\nUnexpected response format",apiResponse:(null===(n=a.data.data)||void 0===n?void 0:n.apiResponse)||null}}catch(e){if(console.error("❌ Error fetching regularization data via dynamic config:",e),e.response){if(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data),e.response.data&&e.response.data.data)return{message:e.response.data.data.message,apiResponse:e.response.data.data.apiResponse}}else e.request?console.error("Network error - no response received"):console.error("Request setup error:",e.message);return{message:"❌ **Error fetching regularization data**\n\nNetwork error: ".concat(e.message),apiResponse:null}}})();if(console.log("📊 Regularization data received:",e),e){const t={role:"assistant",content:e.message,apiResponse:e.apiResponse,attendanceInfo:e.attendanceInfo,isRegularizationData:e.isRegularizationData,isLegacyResponse:!0};return await new Promise((e=>setTimeout(e,1e3))),r((e=>[...e,t])),l(!1),t}console.log("❌ No regularization data returned")}else console.log("🔍 Not a legacy regularization query:",e);const E=JSON.parse(localStorage.getItem("user")||"{}"),x=await Ct.post("/chat",{message:e,conversationId:u,empId:E.empId,roleType:E.roleType},{headers:{"x-emp-id":E.empId,"x-role-type":E.roleType,Authorization:E.token?"Bearer ".concat(E.token):void 0}});console.log("📊 Dynamic chat response:",{queryIntent:x.data.queryIntent,hasFormData:!!x.data.formData,hasApiResponse:!!x.data.apiResponse,confidence:null===(a=x.data.matchResults)||void 0===a?void 0:a.confidence,matchType:null===(o=x.data.matchResults)||void 0===o?void 0:o.matchType,conversationalFlow:x.data.conversationalFlow,isUnified:!0});const C={role:"assistant",content:x.data.message,formData:x.data.formData,queryIntent:x.data.queryIntent,apiResponse:x.data.apiResponse,matchResults:x.data.matchResults,conversationalFlow:x.data.conversationalFlow,fieldType:(null===(i=x.data.conversationalFlow)||void 0===i?void 0:i.fieldType)||x.data.fieldType,options:(null===(p=x.data.conversationalFlow)||void 0===p?void 0:p.options)||x.data.options,isFormFlow:(null===(g=x.data.conversationalFlow)||void 0===g?void 0:g.isActive)||x.data.isFormFlow,fieldName:(null===(v=x.data.conversationalFlow)||void 0===v?void 0:v.fieldName)||x.data.fieldName,currentStep:(null===(b=x.data.conversationalFlow)||void 0===b?void 0:b.currentStep)||x.data.currentStep,totalSteps:(null===(w=x.data.conversationalFlow)||void 0===w?void 0:w.totalSteps)||x.data.totalSteps,isDynamicResponse:!0};if(x.data.conversationId&&!u&&c(x.data.conversationId),x.data.conversationalFlow&&"form_conversation"===x.data.queryIntent)console.log("🗣️ Conversational flow active:",x.data.conversationalFlow),console.log("🎯 Conversational form options:",{fieldType:x.data.conversationalFlow.fieldType,options:x.data.conversationalFlow.options,isActive:x.data.conversationalFlow.isActive}),m(x.data.conversationalFlow),f(null);else if("form_completed"===x.data.queryIntent){var k;if(console.log("✅ Conversational form completed"),console.log("📋 Full server response:",x.data),console.log("📋 Form config from server:",!!x.data.formConfig),console.log("📋 Form config structure:",x.data.formConfig),m(null),f(null),x.data.needsSubmission&&x.data.formData&&x.data.formId)return console.log("📤 Auto-submitting conversational form to show in Network tab..."),console.log("📋 Form config name:",null===(k=x.data.formConfig)||void 0===k?void 0:k.name),await async function(e,t,n,a){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null;try{var l;console.log("📤 Submitting conversational form via client-side API call...");const a=JSON.parse(localStorage.getItem("user")||"{}");let i=a.token;const s=o||d;s&&!0===(null===(l=s.formConfig)||void 0===l||null===(l=l.submitApiConfig)||void 0===l||null===(l=l.authConfig)||void 0===l?void 0:l.useCustomToken)?(i=s.formConfig.submitApiConfig.authConfig.token,console.log("🔑 Using custom token from form configuration for submission")):console.log("🔑 Using user login token for submission");const u=y(t,s),c=await Ct.post("/chat/submit-form",{formId:e,formData:u,conversationId:n},{headers:{"x-emp-id":a.empId,"x-role-type":a.roleType,Authorization:i?"Bearer ".concat(i):void 0}});let f;return console.log("📊 Conversational form API response:",c.data),c.data.success?(console.log("✅ Conversational form submitted successfully"),f="Thank you! Your form has been submitted successfully."):(console.log("❌ Conversational form submission failed:",c.data.message),f=c.data.message||"Failed to submit form. Please try again."),r((e=>[...e,{role:"assistant",content:f,queryIntent:"form_completed",apiResponse:c.data}])),c.data}catch(e){console.error("❌ Network error submitting conversational form:",e);let t="Sorry, there was an error submitting your form. Please try again.";throw e.response&&e.response.data&&e.response.data.message&&(t=e.response.data.message),r((e=>[...e,{role:"assistant",content:t,queryIntent:"form_completed",apiResponse:{success:!1,message:t}}])),e}}(x.data.formId,x.data.formData,x.data.conversationId,n,x.data.formConfig),x.data}else"form_data_retrieved"===x.data.queryIntent?(console.log("🔍 Form GET request completed - data retrieved"),console.log("📋 API response:",x.data.apiResponse),m(null),f(null)):"form_error"===x.data.queryIntent?(console.log("❌ Form GET request failed"),console.log("📋 Error details:",x.data.apiResponse),m(null),f(null)):"form_cancelled"===x.data.queryIntent?(console.log("❌ Conversational form cancelled"),m(null),f(null)):x.data.formData&&"form"===x.data.queryIntent?(f(x.data.formData),m(null),console.log("Form detected and displayed: ".concat(x.data.formData.name)),console.log("Form data:",x.data.formData)):(f(null),m(null),console.log("No form displayed. Query intent: ".concat(x.data.queryIntent)),"information"===x.data.queryIntent&&console.log("Using LLaMA 3.2 model for informational query"));return await new Promise((e=>setTimeout(e,1e3))),r((e=>[...e,C])),x.data}catch(e){s("Failed to send message"),console.error("Error sending message:",e);const t={role:"assistant",content:"Sorry, there was an error processing your request. Please try again."};return r((e=>[...e,t])),null}finally{l(!1)}},addAssistantMessage:h,submitForm:async function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];try{if(s(null),n){if(a)return f(a),a;const t=await Ct.get("/unifiedconfigs/".concat(e));return f(t.data),t.data}const c={role:"user",content:"Form submitted",formData:t};let p;r((e=>[...e,c])),l(!0);let m=null;if(o){let e;i?t&&t.hasOwnProperty("date")&&t.hasOwnProperty("reason")&&t.hasOwnProperty("inTime")&&t.hasOwnProperty("outTime")?e="Regularization submitted successfully.":(e="Form submitted successfully! API responded with status ".concat(o.status,"."),o.data&&"object"==typeof o.data?o.data.message&&(e+=" Response: ".concat(o.data.message)):o.data&&(e+=" Response: ".concat(o.data))):(e="Regularization submitted successfully.",o.data&&o.data.error&&(e+=" Error: ".concat(o.data.error))),p={role:"assistant",content:e,apiResponse:o},m={message:e,apiResponse:o,success:i}}else{const n=JSON.parse(localStorage.getItem("user")||"{}");let r=n.token;d&&!0===d.formConfig.submitApiConfig.authConfig.useCustomToken&&d.formConfig.submitApiConfig.authConfig.useCustomToken?(r=d.formConfig.submitApiConfig.authConfig.token,console.log("🔑 Using custom token from form configuration for fallback submission")):console.log("🔑 Using user login token for fallback submission");const a=y(t,d),o=await Ct.post("/chat/submit-form",{formId:e,formData:a,conversationId:u},{headers:{Authorization:r?"Bearer ".concat(r):void 0,"x-emp-id":n.empId||void 0,"x-role-type":n.roleType||void 0}});p={role:"assistant",content:o.data.message,apiResponse:o.data.apiResponse},m=o.data}return await new Promise((e=>setTimeout(e,1e3))),r((e=>[...e,p])),f(null),m}catch(e){s("Failed to submit form"),console.error("Error submitting form:",e);const t={role:"assistant",content:"Sorry, there was an error submitting the form. Please try again."};return r((e=>[...e,t])),null}finally{l(!1)}},clearChat:()=>{r([]),c(null),f(null),localStorage.removeItem("conversationId")},dismissForm:()=>{f(null);const e={role:"user",content:"I'd like to cancel this form."},t={role:"assistant",content:"Form cancelled. How else can I help you?"};r((n=>[...n,e,t]))},loadConversation:g,setMessages:r}},t)},_t=(0,o.createContext)(),At=e=>{let{children:t}=e;const[n,r]=(0,o.useState)([]),[a,l]=(0,o.useState)(!1),[i,s]=(0,o.useState)(null),[u,c]=(0,o.useState)(null);(0,o.useEffect)((()=>{d()}),[]);const d=async()=>{try{var e,t;l(!0),console.log("🔄 Fetching forms from:",Ct.defaults.baseURL+"/unifiedconfigs");const n=await Ct.get("/unifiedconfigs");return console.log("📋 Forms response:",n.data),console.log("📋 Forms count:",(null===(e=n.data)||void 0===e?void 0:e.length)||0),console.log("📋 Form titles:",(null===(t=n.data)||void 0===t?void 0:t.map((e=>e.formTitle||e.name||e._id)))||[]),r(n.data),n.data}catch(e){var n,a,o,i,u,c,d;return s("Failed to fetch forms"),console.error("Error fetching forms:",e),console.error("Error details:",{message:e.message,status:null===(n=e.response)||void 0===n?void 0:n.status,statusText:null===(a=e.response)||void 0===a?void 0:a.statusText,url:null===(o=e.config)||void 0===o?void 0:o.url,baseURL:null===(i=e.config)||void 0===i?void 0:i.baseURL,fullURL:(null===(u=e.config)||void 0===u?void 0:u.baseURL)+(null===(c=e.config)||void 0===c?void 0:c.url),data:null===(d=e.response)||void 0===d?void 0:d.data}),[]}finally{l(!1)}};return o.createElement(_t.Provider,{value:{forms:n,loading:a,error:i,currentForm:u,fetchForms:d,getForms:async()=>n.length>0?n:await d(),getFormById:async e=>{try{l(!0);const t=await Ct.get("/unifiedconfigs/".concat(e));return c(t.data),t.data}catch(e){return s("Failed to fetch form"),console.error("Error fetching form:",e),null}finally{l(!1)}},getFormByName:async e=>{try{l(!0),console.log('🔍 Searching for form by name: "'.concat(e,'"')),console.log("🌐 API call: ".concat(Ct.defaults.baseURL,"/unifiedconfigs/name/").concat(e));const t=await Ct.get("/unifiedconfigs/name/".concat(e));return console.log('✅ Found form by name "'.concat(e,'":'),t.data),c(t.data),t.data}catch(o){var t,n,r,a;return s("Failed to fetch form"),console.error('❌ Error fetching form "'.concat(e,'":'),o),console.error("Error details:",{status:null===(t=o.response)||void 0===t?void 0:t.status,statusText:null===(n=o.response)||void 0===n?void 0:n.statusText,url:null===(r=o.config)||void 0===r?void 0:r.url,data:null===(a=o.response)||void 0===a?void 0:a.data}),null}finally{l(!1)}},createForm:async e=>{try{l(!0);const t=await Ct.post("/unifiedconfigs",e);return r([...n,t.data]),c(t.data),t.data}catch(e){var t;return s((null===(t=e.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||"Failed to create form"),console.error("Error creating form:",e),null}finally{l(!1)}},updateForm:async(e,t)=>{try{l(!0);const a=await Ct.put("/unifiedconfigs/".concat(e),t);return r(n.map((t=>t._id===e?a.data:t))),c(a.data),a.data}catch(e){var a;return s((null===(a=e.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"Failed to update form"),console.error("Error updating form:",e),null}finally{l(!1)}},deleteForm:async e=>{try{return l(!0),await Ct.delete("/unifiedconfigs/".concat(e)),r(n.filter((t=>t._id!==e))),u&&u._id===e&&c(null),!0}catch(e){return s("Failed to delete form"),console.error("Error deleting form:",e),!1}finally{l(!1)}},searchForms:async e=>{try{return l(!0),(await Ct.get("/unifiedconfigs/search?query=".concat(e))).data}catch(e){return s("Failed to search forms"),console.error("Error searching forms:",e),[]}finally{l(!1)}},clearCurrentForm:()=>{c(null)},clearError:()=>{s(null)}}},t)},Lt=(0,o.createContext)(),zt=e=>{let{children:t}=e;const[n,r]=(0,o.useState)(null),[a,l]=(0,o.useState)(!0),i=async e=>{try{console.log("🔄 Fetching supervisor and reviewer information using dynamic config...");const t=await Ct.get("/auth/supervisor-reviewer-info",{headers:{Authorization:"Bearer ".concat(e)}});if(console.log("✅ Supervisor and reviewer info fetched via dynamic config:",t.data),t.data.success&&t.data.data){const e=t.data.data;!0===e.status&&e.data?(e.data.primary&&(localStorage.setItem("supervisorName",e.data.primary.primaryManagerName),localStorage.setItem("supervisorId",e.data.primary.primaryManagerId),console.log("💾 Stored supervisor info:",{name:e.data.primary.primaryManagerName,id:e.data.primary.primaryManagerId})),e.data.reviewer&&(localStorage.setItem("reviewerName",e.data.reviewer.reviewerManagerName),localStorage.setItem("reviewerId",e.data.reviewer.reviewerManagerId),console.log("💾 Stored reviewer info:",{name:e.data.reviewer.reviewerManagerName,id:e.data.reviewer.reviewerManagerId})),console.log("✅ Supervisor and reviewer information stored successfully via dynamic config")):console.log("⚠️ Unexpected response format from dynamic config:",e)}else console.log("⚠️ Backend response indicates failure:",t.data)}catch(e){console.error("❌ Error fetching supervisor and reviewer information via dynamic config:",e),e.response?(console.error("Response status:",e.response.status),console.error("Response data:",e.response.data)):e.request?console.error("Network error - no response received"):console.error("Request setup error:",e.message)}};return(0,o.useEffect)((()=>{(async()=>{try{const e=localStorage.getItem("user");if(e){const t=JSON.parse(e);r(t);const n=localStorage.getItem("supervisorName"),a=localStorage.getItem("reviewerName");n&&a?console.log("✅ Supervisor/reviewer info already available"):(console.log("🔄 Supervisor/reviewer info missing, fetching..."),t.token&&await i(t.token))}}catch(e){console.error("Error checking login status:",e),localStorage.removeItem("user")}finally{l(!1)}})()}),[]),o.createElement(Lt.Provider,{value:{user:n,loading:a,login:async e=>{r(e),localStorage.setItem("user",JSON.stringify(e)),e.token&&await i(e.token)},logout:()=>{r(null),localStorage.removeItem("user"),localStorage.removeItem("supervisorName"),localStorage.removeItem("supervisorId"),localStorage.removeItem("reviewerName"),localStorage.removeItem("reviewerId"),localStorage.removeItem("roleType")},isLoggedIn:()=>!!n}},t)},Ft=e=>{let{onSendMessage:t,loading:n,conversationalFlow:r}=e;const[a,l]=(0,o.useState)("");return o.createElement("div",null,o.createElement("form",{onSubmit:e=>{e.preventDefault(),a.trim()&&!n&&(t(a),l(""))},className:"flex items-center border-t border-gray-200 p-4"},o.createElement("input",{type:"text",value:a,onChange:e=>l(e.target.value),placeholder:r&&r.isActive?"Type your answer or 'cancel' to exit the form...":"Ask a question...",className:"flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500",disabled:n}),r&&r.isActive&&o.createElement("button",{type:"button",onClick:()=>{t("cancel"),l("")},className:"bg-red-500 hover:bg-red-600 text-white px-3 py-2 transition-colors",disabled:n},"Cancel"),o.createElement("button",{type:"submit",className:"bg-blue-500 text-white p-2 ".concat((r&&r.isActive,"rounded-r-md")," ").concat(n?"opacity-50 cursor-not-allowed":"hover:bg-blue-600"),disabled:n},n?o.createElement("svg",{className:"animate-spin h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},o.createElement("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),o.createElement("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})):o.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z",clipRule:"evenodd"})))))},It=e=>{let{message:t,onOptionSelect:n}=e;const r="user"===t.role,[a,l]=(0,o.useState)([]);return o.createElement("div",{className:"flex ".concat(r?"justify-end":"justify-start"," mb-4")},o.createElement("div",{className:"max-w-[80%] p-3 rounded-lg ".concat(r?"bg-blue-500 text-white rounded-br-none":"bg-gray-200 text-gray-800 rounded-bl-none")},o.createElement("p",{className:"whitespace-pre-wrap"},t.content),!r&&t.options&&Array.isArray(t.options)&&t.options.length>0&&o.createElement("div",{className:"mt-3"},"checkbox"===t.fieldType?o.createElement("div",null,o.createElement("div",{className:"grid grid-cols-1 gap-2 max-w-xs mb-3"},t.options.map(((e,t)=>o.createElement("button",{key:t,onClick:()=>(e=>{const t=a.includes(e)?a.filter((t=>t!==e)):[...a,e];l(t)})(e),className:"px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ".concat(a.includes(e)?"bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700":"bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400")},o.createElement("div",{className:"flex items-center"},o.createElement("div",{className:"w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ".concat(a.includes(e)?"bg-white border-white":"bg-transparent border-gray-400")},a.includes(e)&&o.createElement("svg",{className:"w-3 h-3 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"}))),e))))),a.length>0&&o.createElement("div",{className:"mb-2"},o.createElement("button",{onClick:()=>{a.length>0&&(n&&n(a.join(", ")),l([]))},className:"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200"},"Submit Selection (",a.length,")")),o.createElement("div",{className:"text-xs text-gray-500"},"Select multiple options and click Submit, or select one option and Submit")):o.createElement("div",{className:"grid grid-cols-1 gap-2 max-w-xs"},t.options.map(((e,t)=>o.createElement("button",{key:t,onClick:()=>(e=>{n&&n(e)})(e),className:"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105"},e)))),t.currentStep&&t.totalSteps&&o.createElement("div",{className:"mt-2 text-xs text-gray-500"},"Step ",t.currentStep," of ",t.totalSteps)),!r&&"form_completed"===t.queryIntent&&t.apiResponse&&t.apiResponse.success&&o.createElement("div",{className:"mt-2 p-2 bg-green-100 rounded text-xs text-green-700"},o.createElement("div",{className:"flex items-center"},o.createElement("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})),"Form Submitted Successfully")),!r&&"form_completed"===t.queryIntent&&t.apiResponse&&!t.apiResponse.success&&o.createElement("div",{className:"mt-2 p-2 bg-red-100 rounded text-xs text-red-700"},o.createElement("div",{className:"flex items-center"},o.createElement("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})),"Form Submission Failed")),!r&&"form_cancelled"===t.queryIntent&&o.createElement("div",{className:"mt-2 p-2 bg-red-100 rounded text-xs text-red-700"},o.createElement("div",{className:"flex items-center"},o.createElement("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})),"Form Cancelled")),t.apiResponse&&(e=>{if(!e)return null})(t.apiResponse)))};function Dt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function jt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Dt(Object(n),!0).forEach((function(t){Mt(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Dt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Mt(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Ut=e=>{let{form:t,onSubmit:n,onCancel:r,submitButtonText:a="Submit"}=e;const[l,i]=(0,o.useState)({}),[s,u]=(0,o.useState)({}),[c,d]=(0,o.useState)(!1),[f,p]=(0,o.useState)(null),[m,g]=(0,o.useState)(null);(0,o.useEffect)((()=>{if(console.log("ChatFormDisplay received form:",t),console.log("Form has fields:",t&&Array.isArray(t.fields)),console.log("Fields count:",t&&t.fields?t.fields.length:0),t&&t.formConfig.fields&&Array.isArray(t.formConfig.fields)){const e={},n=JSON.parse(localStorage.getItem("user")||"{}"),r=localStorage.getItem("roleType");console.log("formmmmm:",t.formConfig.fields),t.formConfig.fields.forEach((a=>{let o="";o="empId"===a.name&&n.empId?n.empId:"type"===a.name&&r?r:t.prefillData&&t.prefillData[a.name]?t.prefillData[a.name]:"",e[a.name]=o,console.log("formmmmm:",o)})),i(e)}}),[t]);const h=e=>{const t=JSON.parse(localStorage.getItem("user")||"{}"),n=localStorage.getItem("roleType");return"empId"===e&&t.empId||"type"===e&&n},y=e=>{const{name:t,value:n,type:r,checked:a,files:o}=e.target;let c;c="checkbox"===r?a:"file"===r?o&&o.length>0?o[0].name:"":n,i(jt(jt({},l),{},{[t]:c})),s[t]&&u(jt(jt({},s),{},{[t]:null}))};return t?o.createElement("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden mb-4"},o.createElement("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3"},o.createElement("h3",{className:"text-white font-medium"},t.name),t.description&&o.createElement("p",{className:"text-blue-100 text-sm mt-1"},t.description)),o.createElement("div",{className:"p-4"},o.createElement("form",{onSubmit:async e=>{e.preventDefault();const r=(()=>{const e={};return t&&t.fields&&t.fields.forEach((t=>{t.required&&!l[t.name]&&(e[t.name]="".concat(t.label," is required")),"email"===t.type&&l[t.name]&&!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(l[t.name])&&(e[t.name]="Invalid email address")})),e})();if(Object.keys(r).length>0)return void u(r);d(!0),p(null),g(null);const a=JSON.parse(localStorage.getItem("user")||"{}"),o=localStorage.getItem("roleType");let i=jt({},l);t.name.toLowerCase().includes("leave")&&!i.empId&&a.empId&&(i.empId=a.empId),t.name.toLowerCase().includes("leave")&&!i.type&&o&&(i.type=o);try{console.log("Form submission - Enhanced formData:",i),console.log("Form API Config:",t.formConfig.submitApiConfig.endpoint);let e=null;if(t.formConfig&&t.formConfig.submitApiConfig.endpoint)try{const n=t.name&&t.name.toLowerCase().includes("regular");let r;if(console.log("🔍 Form detection:",{formName:t.name,isRegularizationForm:n,formTitle:t.formTitle||"Not set"}),r){const{request:t,endpoint:n}=r;console.log("🚀 Making API call to:",n);const a=await fetch(n,t),o=await a.text();let l;try{l=JSON.parse(o)}catch(e){l=o}e={status:a.status,statusText:a.statusText,data:l,success:a.ok},a.ok?p(e):(console.log("login response ----------- ",a),console.log("login response ----------- ",e.data.message),g("".concat(e.data.message)))}}catch(t){console.error("API call failed:",t),g("API call failed: ".concat(t.message)),e={status:0,statusText:"Network Error",data:{error:t.message},success:!1}}var s;n&&n({formData:i,formId:t._id,apiResponse:e,success:(null===(s=e)||void 0===s?void 0:s.success)||!1}),e||p({status:200,statusText:"OK",data:{message:"Form submitted successfully (no API configured)"}})}catch(e){console.error("Form submission failed:",e),g(e.message||"Failed to submit form"),n&&n({formData:i||l,formId:t._id,error:e.message||"Failed to submit form",success:!1})}finally{d(!1)}}},o.createElement("div",{className:"space-y-4"},t&&t.formConfig&&t.formConfig.fields&&Array.isArray(t.formConfig.fields)?t.formConfig.fields.map((e=>o.createElement("div",{key:e.name,className:"form-field"},o.createElement("label",{className:"block text-sm font-medium text-gray-700 mb-1"},e.label,e.required&&o.createElement("span",{className:"text-red-500 ml-1"},"*"),h(e.name)&&o.createElement("span",{className:"text-blue-500 text-xs ml-2"},"(Auto-filled)")),"text"===e.type&&o.createElement("input",{type:"text",name:e.name,value:l[e.name]||"",onChange:y,placeholder:e.placeholder,readOnly:e.readonly,className:"w-full p-2 border rounded-md ".concat(s[e.name]?"border-red-500":e.readonly?"border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed":h(e.name)?"border-blue-300 bg-blue-50":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-blue-500")}),"email"===e.type&&o.createElement("input",{type:"email",name:e.name,value:l[e.name]||"",onChange:y,placeholder:e.placeholder,readOnly:e.readonly,className:"w-full p-2 border rounded-md ".concat(s[e.name]?"border-red-500":e.readonly?"border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-blue-500")}),"number"===e.type&&o.createElement("input",{type:"number",name:e.name,value:l[e.name]||"",onChange:y,placeholder:e.placeholder,readOnly:e.readonly,className:"w-full p-2 border rounded-md ".concat(s[e.name]?"border-red-500":e.readonly?"border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-blue-500")}),"textarea"===e.type&&o.createElement("textarea",{name:e.name,value:l[e.name]||"",onChange:y,placeholder:e.placeholder,rows:"3",className:"w-full p-2 border rounded-md ".concat(s[e.name]?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-blue-500")}),"select"===e.type&&o.createElement("select",{name:e.name,value:l[e.name]||"",onChange:y,className:"w-full p-2 border rounded-md ".concat(s[e.name]?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-blue-500")},o.createElement("option",{value:""},"Select an option"),e.options&&e.options.map(((e,t)=>o.createElement("option",{key:t,value:e},e)))),"checkbox"===e.type&&o.createElement("div",{className:"flex items-center"},o.createElement("input",{type:"checkbox",name:e.name,checked:l[e.name]||!1,onChange:y,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"}),o.createElement("span",{className:"ml-2 text-sm text-gray-600"},e.placeholder)),"radio"===e.type&&e.options&&o.createElement("div",{className:"space-y-2"},e.options.map(((t,n)=>o.createElement("div",{key:n,className:"flex items-center"},o.createElement("input",{type:"radio",id:"".concat(e.name,"-").concat(n),name:e.name,value:t,checked:l[e.name]===t,onChange:y,className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"}),o.createElement("label",{htmlFor:"".concat(e.name,"-").concat(n),className:"ml-2 text-sm text-gray-600"},t))))),"date"===e.type&&o.createElement("input",{type:"date",name:e.name,value:l[e.name]||"",onChange:y,className:"w-full p-2 border rounded-md ".concat(s[e.name]?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-blue-500")}),"file"===e.type&&o.createElement("input",{type:"file",name:e.name,onChange:y,className:"w-full p-2 border rounded-md ".concat(s[e.name]?"border-red-500":"border-gray-300"," focus:outline-none focus:ring-2 focus:ring-blue-500")}),s[e.name]&&o.createElement("p",{className:"mt-1 text-sm text-red-500"},s[e.name])))):o.createElement("div",{className:"p-4 bg-red-50 border border-red-200 rounded-md"},o.createElement("p",{className:"text-red-700 font-medium"},"❌ Form Error"),o.createElement("p",{className:"text-red-600 text-sm mt-1"},"Form fields are not available. Please contact your administrator."),o.createElement("p",{className:"text-gray-600 text-xs mt-2"},"Debug: form=",t?"exists":"null",", fields=",t&&t.fields?"exists":"null"))),m&&o.createElement("div",{className:"mt-4 p-3 bg-red-50 rounded-md border border-red-200"},o.createElement("p",{className:"text-sm text-red-600"},m)),o.createElement("div",{className:"mt-4 flex justify-end space-x-2"},r&&o.createElement("button",{type:"button",onClick:r,className:"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},"Cancel"),o.createElement("button",{type:"submit",disabled:c,className:"px-3 py-2 rounded-md text-sm font-medium text-white ".concat(c?"bg-blue-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"," focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500")},c?o.createElement("span",{className:"flex items-center"},o.createElement("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},o.createElement("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),o.createElement("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})),"Submitting..."):a))))):null},Bt=()=>o.createElement("div",{className:"flex justify-start mb-4"},o.createElement("div",{className:"bg-gray-200 text-gray-800 p-3 rounded-lg rounded-bl-none max-w-[80%]"},o.createElement("div",{className:"flex space-x-2"},o.createElement("div",{className:"w-2 h-2 rounded-full bg-gray-600 animate-bounce",style:{animationDelay:"0ms"}}),o.createElement("div",{className:"w-2 h-2 rounded-full bg-gray-600 animate-bounce",style:{animationDelay:"150ms"}}),o.createElement("div",{className:"w-2 h-2 rounded-full bg-gray-600 animate-bounce",style:{animationDelay:"300ms"}})))),qt=e=>{let{attendanceData:t,onApplyClick:n}=e;const r=e=>{if(!e||"N/A"===e||"00:00"===e)return"Not Punched";try{if(e.includes(":")){const[t,n]=e.split(":"),r=parseInt(t);if(0===r&&"00"===n)return"Not Punched";const a=r>=12?"PM":"AM";return"".concat(0===r?12:r>12?r-12:r,":").concat(n," ").concat(a)}return e}catch(t){return console.error("Time formatting error:",t),e}};return console.log("AttendanceRegularizationDisplay received:",t),console.log("Is array:",Array.isArray(t)),console.log("Length:",t?t.length:"undefined"),t&&Array.isArray(t)&&0!==t.length?o.createElement("div",{className:"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden mb-4"},o.createElement("div",{className:"bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3"},o.createElement("div",{className:"flex items-center"},o.createElement("svg",{className:"h-5 w-5 text-white mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2m-3 7h3m0 0h3m-3 0v3m0-3V9"})),o.createElement("h3",{className:"text-white font-medium"},"Attendance Records for Regularization")),o.createElement("p",{className:"text-blue-100 text-sm mt-1"},'Click "Apply" next to any date to submit a regularization request')),o.createElement("div",{className:"p-4"},o.createElement("div",{className:"space-y-3"},t.map(((e,t)=>o.createElement("div",{key:t,className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"},o.createElement("div",{className:"flex-1"},o.createElement("div",{className:"flex items-center space-x-6"},o.createElement("div",{className:"flex items-center space-x-2"},o.createElement("svg",{className:"h-4 w-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})),o.createElement("span",{className:"font-medium text-gray-900"},(e=>{if(!e)return"Invalid Date";try{const t=new Date(e),n={day:"2-digit",month:"short",year:"numeric"};return t.toLocaleDateString("en-GB",n)}catch(t){return console.error("Date formatting error:",t),e}})(e.attendanceDate||e.date))),o.createElement("div",{className:"flex items-center space-x-2"},o.createElement("svg",{className:"h-4 w-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"})),o.createElement("span",{className:"text-sm text-gray-600"},"In: ",o.createElement("span",{className:"font-medium text-green-600"},r("00:00"!==e.actualInTime?e.actualInTime:e.inTime)))),o.createElement("div",{className:"flex items-center space-x-2"},o.createElement("svg",{className:"h-4 w-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})),o.createElement("span",{className:"text-sm text-gray-600"},"Out: ",o.createElement("span",{className:"font-medium text-red-600"},r("00:00"!==e.actualOutTime?e.actualOutTime:e.outTime)))))),o.createElement("div",{className:"flex-shrink-0 ml-4"},o.createElement("button",{onClick:()=>((e,t)=>{n&&n(e,t)})(e,t),className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"},o.createElement("svg",{className:"h-4 w-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M12 4v16m8-8H4"})),"Apply"))))))),o.createElement("div",{className:"px-4 py-3 bg-gray-50 border-t border-gray-200"},o.createElement("p",{className:"text-sm text-gray-600"},o.createElement("span",{className:"font-medium"},t.length)," attendance record",1!==t.length?"s":""," available for regularization"))):o.createElement("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4"},o.createElement("div",{className:"flex items-center"},o.createElement("div",{className:"flex-shrink-0"},o.createElement("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor"},o.createElement("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}))),o.createElement("div",{className:"ml-3"},o.createElement("p",{className:"text-sm text-yellow-700"},"No attendance records found for regularization."))))};function Ht(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ht(Object(n),!0).forEach((function(t){$t(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ht(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $t(e,t,n){return(t=function(e){var t=function(e){if("object"!=typeof e||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Wt=()=>{const{forms:e,getForms:t,getFormByName:n}=(0,o.useContext)(_t),{messages:r,loading:a,activeForm:l,conversationalFlow:i,sendMessage:s,addAssistantMessage:u,submitForm:c,dismissForm:d,clearChat:f}=(0,o.useContext)(Ot),{user:p,login:m,isLoggedIn:g,logout:h}=(0,o.useContext)(Lt),[y,v]=(0,o.useState)(null),[b,w]=(0,o.useState)(!0),k=(0,o.useRef)(null);(0,o.useEffect)((()=>{g()||y||(async()=>{if(!y)try{w(!0);const e=await n("Login");v(e)}catch(e){console.error("Error fetching login form:",e)}finally{w(!1)}})()}),[g,y,n]),(0,o.useEffect)((()=>{g()&&t()}),[t,g]),(0,o.useEffect)((()=>{if(g()&&0===r.length){const e=p.firstName||p.username||p.empId;u("Hey ".concat(e,", how can I help you today?"))}}),[g,r.length,u,p]),(0,o.useEffect)((()=>{var e;null===(e=k.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})}),[r]);const S=async e=>{await s(e)},E=async e=>{if(l&&"Login"===l.name)try{const t=await Ct.post("/auth/login",{empId:e.formData.empId,password:e.formData.password});if(t.data&&!0===t.data.success&&t.data.data){const e=t.data.data;e.roleType&&localStorage.setItem("roleType",e.roleType),await m(e),f(),u("Hey ".concat(e.firstName,", how can I help you today?"))}else u("Login failed. Please check your credentials and try again.")}catch(e){console.error("Login error:",e),e.response&&401===e.response.status?u("Invalid employee ID or password. Please try again."):e.response&&503===e.response.status?u("Login service is temporarily unavailable. Please try again later."):u("Login failed. Please try again later.")}else c(l._id,e.formData,!1,null,e.apiResponse,e.success)},x=e=>{S(e)},C=async(r,a)=>{try{if(console.log("🎯 Regularization apply clicked for:",r),console.log("📋 Available forms:",e),console.log("📋 Forms count:",e?e.length:0),console.log("📋 Forms is array:",Array.isArray(e)),!e||0===e.length){console.log("⏳ Forms not loaded yet, attempting to fetch...");try{const e=await t();console.log("📋 Fetched forms:",e)}catch(e){return console.error("❌ Failed to fetch forms:",e),void u("❌ Unable to load forms. Please try again or contact your administrator.")}}let a=null;const o=["Regularization","regularization","Attendance Regularization","attendance regularization","AttendanceRegularization","attendance_regularization","RegularizationForm","regularization_form"];if(e&&Array.isArray(e))for(const t of o)if(a=e.find((e=>e.name===t||e.formTitle===t||e.name&&e.name.toLowerCase()===t.toLowerCase()||e.formTitle&&e.formTitle.toLowerCase()===t.toLowerCase())),console.log('🔍 Searching in loaded forms for "'.concat(t,'":'),a?"Found":"Not found"),a)break;if(!a){console.log("🌐 Form not found locally, trying API calls...");for(const e of o)try{if(a=await n(e),console.log('🔍 API search for "'.concat(e,'":'),a?"Found":"Not found"),a)break}catch(t){console.log('❌ API error for "'.concat(e,'":'),t.message)}}if(!a&&e&&Array.isArray(e)&&(a=e.find((e=>{if(!e||!e.fields||!Array.isArray(e.fields))return!1;const t=e.fields.map((e=>e.name.toLowerCase()));return t.includes("date")&&(t.includes("intime")||t.includes("reason"))}))),!a)return console.log("❌ No regularization form found"),console.log("📋 Available form names:",e?e.map((e=>e.formTitle||e.name||e._id)):"No forms"),void u("❌ Regularization form not found. Please contact your administrator.");console.log("✅ Found regularization form:",a.formTitle||a.name),console.log("📋 Form fields:",a.fields?a.fields.map((e=>e.name)):"No fields");const l="00:00"!==r.actualInTime?r.actualInTime:r.inTime,i="00:00"!==r.actualOutTime?r.actualOutTime:r.outTime,s={date:r.attendanceDate||r.date,inTime:l||r.inTime||r.checkIn,outTime:i||r.outTime||r.checkOut,appliedTo:localStorage.getItem("supervisorName"),attendanceDate:r.attendanceDate||r.date,checkIn:l||r.inTime||r.checkIn,checkOut:i||r.outTime||r.checkOut};console.log("Pre-fill data prepared:",s);const d=Vt(Vt({},a),{},{prefillData:s});c(a._id,{},!0,d),u("📝 Preparing regularization form for ".concat(r.attendanceDate||r.date,". Please fill in the required details."))}catch(e){console.error("Error handling regularization apply:",e),u("❌ Error opening regularization form. Please try again.")}};return(0,o.useEffect)((()=>{g()||!y||l&&"Login"===l.name||c(y._id,null,!0,y)}),[g,y,l,c]),o.createElement("div",{className:"flex flex-col h-full bg-white rounded-lg shadow-lg overflow-hidden"},o.createElement("div",{className:"bg-gradient-to-r from-blue-600 to-blue-800 p-4 text-white flex justify-between items-center"},o.createElement("h2",{className:"text-xl font-bold"},"AI Form Assistant"),g()&&o.createElement("div",{className:"flex items-center"},o.createElement("span",{className:"text-sm mr-3"},"Logged in as ",p.firstName||p.username||p.empId),o.createElement("button",{onClick:()=>{h(),f()},className:"bg-blue-700 hover:bg-blue-800 text-white text-sm py-1 px-3 rounded"},"Logout"))),o.createElement("div",{className:"flex-1 overflow-y-auto p-4"},g()?o.createElement(o.Fragment,null,r.map(((e,t)=>o.createElement("div",{key:t},o.createElement(It,{message:e,onOptionSelect:x}),e.isLegacyResponse&&o.createElement("div",{className:"mt-2 p-2 bg-orange-50 border border-orange-200 rounded"},o.createElement("div",{className:"flex items-center text-orange-700 text-xs"},o.createElement("svg",{className:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},o.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"})),"Legacy Response (will be replaced by dynamic APIs)")),e.isRegularizationData&&e.attendanceInfo&&o.createElement("div",{className:"mt-2"},console.log("🎨 Rendering AttendanceRegularizationDisplay with:",e.attendanceInfo),o.createElement(qt,{attendanceData:e.attendanceInfo,onApplyClick:C})),e.isRegularizationData&&!e.attendanceInfo&&o.createElement("div",{className:"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded"},console.log("⚠️ Regularization data found but no attendanceInfo:",e),o.createElement("p",{className:"text-yellow-700"},"Regularization data received but no attendance info available."))))),a&&o.createElement(Bt,null),l&&"Login"!==l.name&&o.createElement("div",{className:"my-4"},o.createElement(Ut,{form:l,onSubmit:E,onCancel:()=>{d()}})),o.createElement("div",{ref:k})):o.createElement("div",{className:"flex items-center justify-center h-full"},b?o.createElement("div",{className:"text-center"},o.createElement(Bt,null),o.createElement("p",{className:"mt-2 text-gray-600"},"Loading login form...")):o.createElement(o.Fragment,null,l&&"Login"===l.name?o.createElement("div",{className:"w-full max-w-md"},o.createElement("h2",{className:"text-2xl font-bold text-gray-800 mb-6 text-center"},"Login to Chat"),o.createElement(Ut,{form:l,onSubmit:E,submitButtonText:"Login"})):o.createElement("div",{className:"text-center"},o.createElement("p",{className:"text-gray-600"},"Login form not found. Please try again later."))))),g()&&o.createElement(Ft,{onSendMessage:S,loading:a,conversationalFlow:i}))},Qt=()=>o.createElement("div",{className:"max-w-2xl p-4 h-[calc(100vh-60px)] absolute bottom-0 right-[75px]"},o.createElement(Wt,null)),Kt=()=>{const[e,t]=(0,o.useState)(!1),[n,r]=(0,o.useState)([{type:"bot",text:"👋 Hi! I provide comprehensive details about:\n• Employee Directory & Information\n• Attendance Records & Analysis\n• Leave Management & Balances\n• Project Overview & Statistics\n\nAsk me anything for full detailed responses!"}]),[a,l]=(0,o.useState)(""),[i,s]=(0,o.useState)(!1),u=(0,o.useRef)(null);return(0,o.useEffect)((()=>{var e;null===(e=u.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})}),[n]),o.createElement(o.Fragment,null,o.createElement("div",{onClick:()=>t(!e),style:{position:"fixed",bottom:"20px",right:"20px",width:"60px",height:"60px",backgroundColor:"#007bff",borderRadius:"50%",display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer",boxShadow:"0 4px 12px rgba(0,123,255,0.3)",zIndex:1e3,transition:"all 0.3s ease"}},o.createElement("span",{style:{color:"white",fontSize:"24px"}},e?"✕":"💬")),e&&o.createElement(Qt,null))},Gt=(0,o.createContext)(),Xt=e=>{let{children:t}=e;const[n,r]=(0,o.useState)([]),[a,l]=(0,o.useState)(!1),[i,s]=(0,o.useState)(null),[u,c]=(0,o.useState)(null);(0,o.useEffect)((()=>{d()}),[]);const d=async()=>{try{l(!0),console.log("🔄 Fetching API configurations from:",Ct.defaults.baseURL+"/api-configs");const e=await Ct.get("/api-configs");return console.log("⚙️ API configs response:",e.data),r(e.data),e.data}catch(e){return s("Failed to fetch API configurations"),console.error("Error fetching API configurations:",e),[]}finally{l(!1)}};return o.createElement(Gt.Provider,{value:{apiConfigs:n,loading:a,error:i,currentApiConfig:u,fetchApiConfigs:d,getApiConfigById:async e=>{try{l(!0);const t=await Ct.get("/api-configs/".concat(e));return c(t.data),t.data}catch(e){return s("Failed to fetch API configuration"),console.error("Error fetching API configuration:",e),null}finally{l(!1)}},getApiConfigByName:async e=>{try{l(!0),console.log('🔍 Searching for API config by name: "'.concat(e,'"'));const t=await Ct.get("/api-configs/name/".concat(e));return console.log('✅ Found API config by name "'.concat(e,'":'),t.data),c(t.data),t.data}catch(t){return s("Failed to fetch API configuration"),console.error('❌ Error fetching API config "'.concat(e,'":'),t),null}finally{l(!1)}},createApiConfig:async e=>{try{l(!0);const t=await Ct.post("/api-configs",e);return r([...n,t.data]),c(t.data),t.data}catch(e){var t;return s((null===(t=e.response)||void 0===t||null===(t=t.data)||void 0===t?void 0:t.message)||"Failed to create API configuration"),console.error("Error creating API configuration:",e),null}finally{l(!1)}},updateApiConfig:async(e,t)=>{try{l(!0);const a=await Ct.put("/api-configs/".concat(e),t);return r(n.map((t=>t._id===e?a.data:t))),c(a.data),a.data}catch(e){var a;return s((null===(a=e.response)||void 0===a||null===(a=a.data)||void 0===a?void 0:a.message)||"Failed to update API configuration"),console.error("Error updating API configuration:",e),null}finally{l(!1)}},deleteApiConfig:async e=>{try{return l(!0),await Ct.delete("/api-configs/".concat(e)),r(n.filter((t=>t._id!==e))),u&&u._id===e&&c(null),!0}catch(e){return s("Failed to delete API configuration"),console.error("Error deleting API configuration:",e),!1}finally{l(!1)}},clearCurrentApiConfig:()=>{c(null)},clearError:()=>{s(null)}}},t)},Yt={init:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=document.getElementById("botnexus-chatbot-container");t||(t=document.createElement("div"),t.id="botnexus-chatbot-container",document.body.appendChild(t)),l.createRoot(t).render(o.createElement(zt,null,o.createElement(At,null,o.createElement(Xt,null,o.createElement(Rt,null,o.createElement(Kt,e))))))}};function Jt(){document.querySelectorAll('script[src*="chatbot-widget.js"]').forEach((e=>{const t={apiUrl:e.getAttribute("data-api-url")||"http://localhost:5000",theme:e.getAttribute("data-theme")||"default",position:e.getAttribute("data-position")||"bottom-right",primaryColor:e.getAttribute("data-primary-color")||"#007bff",title:e.getAttribute("data-title")||"Chat with us",subtitle:e.getAttribute("data-subtitle")||"We're here to help!",autoInit:"false"!==e.getAttribute("data-auto-init")};t.autoInit&&Yt.init(t)}))}window.BotNexusChatbot=Yt,"loading"===document.readyState?document.addEventListener("DOMContentLoaded",Jt):Jt();const Zt=Yt;return r})()));