[{"E:\\kumaran\\botnexus\\client\\src\\index.js": "1", "E:\\kumaran\\botnexus\\client\\src\\reportWebVitals.js": "2", "E:\\kumaran\\botnexus\\client\\src\\App.js": "3", "E:\\kumaran\\botnexus\\client\\src\\context\\FormContext.js": "4", "E:\\kumaran\\botnexus\\client\\src\\context\\ApiConfigContext.js": "5", "E:\\kumaran\\botnexus\\client\\src\\context\\AuthContext.js": "6", "E:\\kumaran\\botnexus\\client\\src\\context\\ChatContext.js": "7", "E:\\kumaran\\botnexus\\client\\src\\pages\\ApiConfigPage.js": "8", "E:\\kumaran\\botnexus\\client\\src\\pages\\FormsPage.js": "9", "E:\\kumaran\\botnexus\\client\\src\\pages\\ChatPage.js": "10", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatbotWidget.js": "11", "E:\\kumaran\\botnexus\\client\\src\\pages\\UnifiedConfigPage.js": "12", "E:\\kumaran\\botnexus\\client\\src\\pages\\DocumentsPage.js": "13", "E:\\kumaran\\botnexus\\client\\src\\layouts\\index.js": "14", "E:\\kumaran\\botnexus\\client\\src\\components\\ApiConfigForm.js": "15", "E:\\kumaran\\botnexus\\client\\src\\components\\FormBuilder.js": "16", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInterface.js": "17", "E:\\kumaran\\botnexus\\client\\src\\components\\UnifiedConfigBuilder.js": "18", "E:\\kumaran\\botnexus\\client\\src\\utils\\api.js": "19", "E:\\kumaran\\botnexus\\client\\src\\layouts\\MainLayout.js": "20", "E:\\kumaran\\botnexus\\client\\src\\layouts\\SimpleLayout.js": "21", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInput.js": "22", "E:\\kumaran\\botnexus\\client\\src\\components\\HybridForm.js": "23", "E:\\kumaran\\botnexus\\client\\src\\components\\AttendanceRegularizationDisplay.js": "24", "E:\\kumaran\\botnexus\\client\\src\\components\\TypingIndicator.js": "25", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatMessage.js": "26", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatFormDisplay.js": "27", "E:\\kumaran\\botnexus\\client\\src\\components\\JsonEditor.js": "28", "E:\\kumaran\\botnexus\\client\\src\\components\\FormLinkingConfig.js": "29", "E:\\kumaran\\botnexus\\client\\src\\components\\PayloadPreview.js": "30", "E:\\kumaran\\botnexus\\client\\src\\components\\RecordDisplayWithActions.js": "31", "E:\\kumaran\\botnexus\\client\\src\\components\\AutoTriggerHandler.js": "32", "E:\\kumaran\\botnexus\\client\\src\\components\\DynamicForm.js": "33"}, {"size": 552, "mtime": 1753416831573, "results": "34", "hashOfConfig": "35"}, {"size": 375, "mtime": 1753416831576, "results": "36", "hashOfConfig": "35"}, {"size": 2669, "mtime": 1753416831565, "results": "37", "hashOfConfig": "35"}, {"size": 5495, "mtime": 1753416831573, "results": "38", "hashOfConfig": "35"}, {"size": 4486, "mtime": 1753416831573, "results": "39", "hashOfConfig": "35"}, {"size": 5212, "mtime": 1753416831573, "results": "40", "hashOfConfig": "35"}, {"size": 39883, "mtime": 1753693866677, "results": "41", "hashOfConfig": "35"}, {"size": 9914, "mtime": 1753416831575, "results": "42", "hashOfConfig": "35"}, {"size": 6682, "mtime": 1753416831576, "results": "43", "hashOfConfig": "35"}, {"size": 288, "mtime": 1753416831575, "results": "44", "hashOfConfig": "35"}, {"size": 1687, "mtime": 1753686559293, "results": "45", "hashOfConfig": "35"}, {"size": 19531, "mtime": 1753416831576, "results": "46", "hashOfConfig": "35"}, {"size": 38815, "mtime": 1753416831575, "results": "47", "hashOfConfig": "35"}, {"size": 112, "mtime": 1753416831574, "results": "48", "hashOfConfig": "35"}, {"size": 16914, "mtime": 1753416831566, "results": "49", "hashOfConfig": "35"}, {"size": 54076, "mtime": 1753416831570, "results": "50", "hashOfConfig": "35"}, {"size": 25758, "mtime": 1753691710368, "results": "51", "hashOfConfig": "35"}, {"size": 87165, "mtime": 1753416831572, "results": "52", "hashOfConfig": "35"}, {"size": 1583, "mtime": 1753688257329, "results": "53", "hashOfConfig": "35"}, {"size": 1729, "mtime": 1753416831574, "results": "54", "hashOfConfig": "35"}, {"size": 247, "mtime": 1753416831574, "results": "55", "hashOfConfig": "35"}, {"size": 3176, "mtime": 1753427812502, "results": "56", "hashOfConfig": "35"}, {"size": 12339, "mtime": 1753416831571, "results": "57", "hashOfConfig": "35"}, {"size": 8356, "mtime": 1753416831566, "results": "58", "hashOfConfig": "35"}, {"size": 698, "mtime": 1753416831572, "results": "59", "hashOfConfig": "35"}, {"size": 25300, "mtime": 1753693322997, "results": "60", "hashOfConfig": "35"}, {"size": 38935, "mtime": 1753681146121, "results": "61", "hashOfConfig": "35"}, {"size": 2943, "mtime": 1753416831571, "results": "62", "hashOfConfig": "35"}, {"size": 24078, "mtime": 1753681146130, "results": "63", "hashOfConfig": "35"}, {"size": 9253, "mtime": 1753416831571, "results": "64", "hashOfConfig": "35"}, {"size": 18261, "mtime": 1753693855372, "results": "65", "hashOfConfig": "35"}, {"size": 9299, "mtime": 1753692953783, "results": "66", "hashOfConfig": "35"}, {"size": 12337, "mtime": 1753416831569, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2rg5jr", {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\kumaran\\botnexus\\client\\src\\index.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\reportWebVitals.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\App.js", ["167"], [], "E:\\kumaran\\botnexus\\client\\src\\context\\FormContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\ApiConfigContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\AuthContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\ChatContext.js", ["168", "169", "170", "171", "172", "173", "174", "175", "176", "177", "178"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\ApiConfigPage.js", ["179"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\FormsPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\ChatPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatbotWidget.js", ["180", "181", "182", "183", "184"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\UnifiedConfigPage.js", ["185", "186"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\DocumentsPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\index.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ApiConfigForm.js", ["187"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\FormBuilder.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInterface.js", ["188", "189"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\UnifiedConfigBuilder.js", ["190", "191", "192"], [], "E:\\kumaran\\botnexus\\client\\src\\utils\\api.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\MainLayout.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\SimpleLayout.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInput.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\HybridForm.js", ["193"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\AttendanceRegularizationDisplay.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\TypingIndicator.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatMessage.js", ["194"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatFormDisplay.js", ["195", "196", "197", "198", "199", "200"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\JsonEditor.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\FormLinkingConfig.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\PayloadPreview.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\RecordDisplayWithActions.js", ["201", "202", "203"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\AutoTriggerHandler.js", ["204", "205", "206"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\DynamicForm.js", [], [], {"ruleId": "207", "severity": 1, "message": "208", "line": 8, "column": 8, "nodeType": "209", "messageId": "210", "endLine": 8, "endColumn": 16}, {"ruleId": "207", "severity": 1, "message": "211", "line": 2, "column": 8, "nodeType": "209", "messageId": "210", "endLine": 2, "endColumn": 13}, {"ruleId": "207", "severity": 1, "message": "212", "line": 21, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 21, "endColumn": 28}, {"ruleId": "213", "severity": 1, "message": "214", "line": 35, "column": 6, "nodeType": "215", "endLine": 35, "endColumn": 8, "suggestions": "216"}, {"ruleId": "207", "severity": 1, "message": "217", "line": 125, "column": 13, "nodeType": "209", "messageId": "210", "endLine": 125, "endColumn": 25}, {"ruleId": "218", "severity": 1, "message": "219", "line": 196, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 196, "endColumn": 18}, {"ruleId": "218", "severity": 1, "message": "222", "line": 197, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 197, "endColumn": 20}, {"ruleId": "218", "severity": 1, "message": "223", "line": 198, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 198, "endColumn": 19}, {"ruleId": "218", "severity": 1, "message": "224", "line": 207, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 207, "endColumn": 26}, {"ruleId": "225", "severity": 1, "message": "226", "line": 859, "column": 40, "nodeType": "227", "messageId": "221", "endLine": 859, "endColumn": 42}, {"ruleId": "225", "severity": 1, "message": "226", "line": 860, "column": 44, "nodeType": "227", "messageId": "221", "endLine": 860, "endColumn": 46}, {"ruleId": "207", "severity": 1, "message": "228", "line": 889, "column": 13, "nodeType": "209", "messageId": "210", "endLine": 889, "endColumn": 25}, {"ruleId": "207", "severity": 1, "message": "229", "line": 21, "column": 23, "nodeType": "209", "messageId": "210", "endLine": 21, "endColumn": 37}, {"ruleId": "207", "severity": 1, "message": "230", "line": 6, "column": 20, "nodeType": "209", "messageId": "210", "endLine": 6, "endColumn": 31}, {"ruleId": "207", "severity": 1, "message": "231", "line": 12, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 12, "endColumn": 20}, {"ruleId": "207", "severity": 1, "message": "232", "line": 12, "column": 22, "nodeType": "209", "messageId": "210", "endLine": 12, "endColumn": 35}, {"ruleId": "207", "severity": 1, "message": "233", "line": 13, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 13, "endColumn": 19}, {"ruleId": "207", "severity": 1, "message": "234", "line": 13, "column": 21, "nodeType": "209", "messageId": "210", "endLine": 13, "endColumn": 33}, {"ruleId": "213", "severity": 1, "message": "235", "line": 19, "column": 6, "nodeType": "215", "endLine": 19, "endColumn": 18, "suggestions": "236"}, {"ruleId": "207", "severity": 1, "message": "237", "line": 137, "column": 9, "nodeType": "209", "messageId": "210", "endLine": 137, "endColumn": 24}, {"ruleId": "238", "severity": 1, "message": "239", "line": 158, "column": 7, "nodeType": "240", "messageId": "241", "endLine": 179, "endColumn": 8}, {"ruleId": "207", "severity": 1, "message": "242", "line": 38, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 38, "endColumn": 22}, {"ruleId": "207", "severity": 1, "message": "243", "line": 38, "column": 24, "nodeType": "209", "messageId": "210", "endLine": 38, "endColumn": 39}, {"ruleId": "207", "severity": 1, "message": "244", "line": 304, "column": 9, "nodeType": "209", "messageId": "210", "endLine": 304, "endColumn": 37}, {"ruleId": "207", "severity": 1, "message": "245", "line": 351, "column": 11, "nodeType": "209", "messageId": "210", "endLine": 351, "endColumn": 23}, {"ruleId": "207", "severity": 1, "message": "246", "line": 352, "column": 11, "nodeType": "209", "messageId": "210", "endLine": 352, "endColumn": 21}, {"ruleId": "207", "severity": 1, "message": "247", "line": 137, "column": 13, "nodeType": "209", "messageId": "210", "endLine": 137, "endColumn": 27}, {"ruleId": "207", "severity": 1, "message": "248", "line": 61, "column": 9, "nodeType": "209", "messageId": "210", "endLine": 61, "endColumn": 27}, {"ruleId": "207", "severity": 1, "message": "249", "line": 12, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 12, "endColumn": 21}, {"ruleId": "207", "severity": 1, "message": "250", "line": 280, "column": 9, "nodeType": "209", "messageId": "210", "endLine": 280, "endColumn": 26}, {"ruleId": "207", "severity": 1, "message": "251", "line": 372, "column": 9, "nodeType": "209", "messageId": "210", "endLine": 372, "endColumn": 40}, {"ruleId": "207", "severity": 1, "message": "252", "line": 375, "column": 31, "nodeType": "209", "messageId": "210", "endLine": 375, "endColumn": 38}, {"ruleId": "207", "severity": 1, "message": "253", "line": 375, "column": 40, "nodeType": "209", "messageId": "210", "endLine": 375, "endColumn": 48}, {"ruleId": "207", "severity": 1, "message": "254", "line": 375, "column": 50, "nodeType": "209", "messageId": "210", "endLine": 375, "endColumn": 61}, {"ruleId": "213", "severity": 1, "message": "255", "line": 256, "column": 6, "nodeType": "215", "endLine": 265, "endColumn": 4, "suggestions": "256"}, {"ruleId": "213", "severity": 1, "message": "257", "line": 284, "column": 6, "nodeType": "215", "endLine": 284, "endColumn": 126, "suggestions": "258"}, {"ruleId": "213", "severity": 1, "message": "259", "line": 332, "column": 6, "nodeType": "215", "endLine": 332, "endColumn": 76, "suggestions": "260"}, {"ruleId": "207", "severity": 1, "message": "261", "line": 17, "column": 10, "nodeType": "209", "messageId": "210", "endLine": 17, "endColumn": 20}, {"ruleId": "213", "severity": 1, "message": "262", "line": 142, "column": 6, "nodeType": "215", "endLine": 142, "endColumn": 64, "suggestions": "263"}, {"ruleId": "213", "severity": 1, "message": "264", "line": 148, "column": 24, "nodeType": "209", "endLine": 148, "endColumn": 31}, "no-unused-vars", "'ChatPage' is defined but never used.", "Identifier", "unusedVar", "'axios' is defined but never used.", "'conversationLoaded' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadConversation' and 'loading'. Either include them or remove the dependency array.", "ArrayExpression", ["265"], "'supervisorId' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'fieldName'.", "ObjectExpression", "unexpected", "Duplicate key 'currentStep'.", "Duplicate key 'totalSteps'.", "Duplicate key 'isDynamicResponse'.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "'errorMessage' is assigned a value but never used.", "'setTestResults' is assigned a value but never used.", "'setMessages' is assigned a value but never used.", "'inputValue' is assigned a value but never used.", "'setInputValue' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchConfigs'. Either include it or remove the dependency array.", ["266"], "'handleCreateNew' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'hybridFormId' is assigned a value but never used.", "'setHybridFormId' is assigned a value but never used.", "'handlePayloadStructureChange' is assigned a value but never used.", "'targetConfig' is assigned a value but never used.", "'targetPath' is assigned a value but never used.", "'successMessage' is assigned a value but never used.", "'isLeaveBalanceData' is assigned a value but never used.", "'apiResponse' is assigned a value but never used.", "'prepareApiRequest' is assigned a value but never used.", "'prepareRegularizationApiRequest' is assigned a value but never used.", "'headers' is assigned a value but never used.", "'authType' is assigned a value but never used.", "'authDetails' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'handleApplyClick'. Either include it or remove the dependency array.", ["267"], "React Hook useEffect has a missing dependency: 'formLinkingConfig?.recordActions'. Either include it or remove the dependency array.", ["268"], "React Hook useCallback has a missing dependency: 'handleAutoSubmitLinkingForm'. Either include it or remove the dependency array.", ["269"], "'isChecking' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isLeaveBalanceData'. Either include it or remove the dependency array.", ["270"], "The ref value 'processedDataRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'processedDataRef.current' to a variable inside the effect, and use that variable in the cleanup function.", {"desc": "271", "fix": "272"}, {"desc": "273", "fix": "274"}, {"desc": "275", "fix": "276"}, {"desc": "277", "fix": "278"}, {"desc": "279", "fix": "280"}, {"desc": "281", "fix": "282"}, "Update the dependencies array to be: [loadConversation, loading]", {"range": "283", "text": "284"}, "Update the dependencies array to be: [fetchConfigs, filterType]", {"range": "285", "text": "286"}, "Update the dependencies array to be: [getEligibleRecords, getRecordsToTrigger, dataHash, autoTriggerConfig, executedTriggers, autoTriggerState, formLinkingConfig.recordActions, hideRecordDisplay, handleApplyClick]", {"range": "287", "text": "288"}, "Update the dependencies array to be: [executeAutoTrigger, autoTriggerConfig.enabled, autoTriggerConfig.skipLeaveBalanceAutoTrigger, data, isLeaveBalanceData, formLinkingConfig?.recordActions]", {"range": "289", "text": "290"}, "Update the dependencies array to be: [loadingActions, formLinkingConfig?.recordActions, formId, data, onFormLinkTriggered, handleAutoSubmitLinkingForm]", {"range": "291", "text": "292"}, "Update the dependencies array to be: [formId, recordData, enabled, skipLeaveBalanceAutoTrigger, isLeaveBalanceData]", {"range": "293", "text": "294"}, [1527, 1529], "[loadConversation, loading]", [740, 752], "[fetchConfigs, filterType]", [9148, 9343], "[getEligibleRecords, getRecordsToTrigger, dataHash, autoTriggerConfig, executedTriggers, autoTriggerState, formLinkingConfig.recordActions, hideRecordDisplay, handleApplyClick]", [10019, 10139], "[executeAutoTrigger, autoTriggerConfig.enabled, autoTriggerConfig.skipLeaveBalanceAutoTrigger, data, isLeaveBalanceData, formLinkingConfig?.recordActions]", [11729, 11799], "[loadingActions, formLinkingConfig?.recordActions, formId, data, onFormLinkTriggered, handleAutoSubmitLinkingForm]", [4853, 4911], "[formId, recordData, enabled, skipLeaveBalanceAutoTrigger, isLeaveBalanceData]"]