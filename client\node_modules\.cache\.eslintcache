[{"E:\\kumaran\\botnexus\\client\\src\\index.js": "1", "E:\\kumaran\\botnexus\\client\\src\\reportWebVitals.js": "2", "E:\\kumaran\\botnexus\\client\\src\\App.js": "3", "E:\\kumaran\\botnexus\\client\\src\\context\\FormContext.js": "4", "E:\\kumaran\\botnexus\\client\\src\\context\\ApiConfigContext.js": "5", "E:\\kumaran\\botnexus\\client\\src\\context\\AuthContext.js": "6", "E:\\kumaran\\botnexus\\client\\src\\context\\ChatContext.js": "7", "E:\\kumaran\\botnexus\\client\\src\\pages\\ApiConfigPage.js": "8", "E:\\kumaran\\botnexus\\client\\src\\pages\\FormsPage.js": "9", "E:\\kumaran\\botnexus\\client\\src\\pages\\ChatPage.js": "10", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatbotWidget.js": "11", "E:\\kumaran\\botnexus\\client\\src\\pages\\UnifiedConfigPage.js": "12", "E:\\kumaran\\botnexus\\client\\src\\pages\\DocumentsPage.js": "13", "E:\\kumaran\\botnexus\\client\\src\\layouts\\index.js": "14", "E:\\kumaran\\botnexus\\client\\src\\components\\ApiConfigForm.js": "15", "E:\\kumaran\\botnexus\\client\\src\\components\\FormBuilder.js": "16", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInterface.js": "17", "E:\\kumaran\\botnexus\\client\\src\\components\\UnifiedConfigBuilder.js": "18", "E:\\kumaran\\botnexus\\client\\src\\utils\\api.js": "19", "E:\\kumaran\\botnexus\\client\\src\\layouts\\MainLayout.js": "20", "E:\\kumaran\\botnexus\\client\\src\\layouts\\SimpleLayout.js": "21", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInput.js": "22", "E:\\kumaran\\botnexus\\client\\src\\components\\HybridForm.js": "23", "E:\\kumaran\\botnexus\\client\\src\\components\\AttendanceRegularizationDisplay.js": "24", "E:\\kumaran\\botnexus\\client\\src\\components\\TypingIndicator.js": "25", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatMessage.js": "26", "E:\\kumaran\\botnexus\\client\\src\\components\\ChatFormDisplay.js": "27", "E:\\kumaran\\botnexus\\client\\src\\components\\JsonEditor.js": "28", "E:\\kumaran\\botnexus\\client\\src\\components\\FormLinkingConfig.js": "29", "E:\\kumaran\\botnexus\\client\\src\\components\\PayloadPreview.js": "30", "E:\\kumaran\\botnexus\\client\\src\\components\\RecordDisplayWithActions.js": "31", "E:\\kumaran\\botnexus\\client\\src\\components\\AutoTriggerHandler.js": "32", "E:\\kumaran\\botnexus\\client\\src\\components\\DynamicForm.js": "33", "E:\\kumaran\\botnexus\\client\\src\\utils\\leaveBalanceCache.js": "34"}, {"size": 552, "mtime": 1753416831573, "results": "35", "hashOfConfig": "36"}, {"size": 375, "mtime": 1753416831576, "results": "37", "hashOfConfig": "36"}, {"size": 2669, "mtime": 1753416831565, "results": "38", "hashOfConfig": "36"}, {"size": 5495, "mtime": 1753416831573, "results": "39", "hashOfConfig": "36"}, {"size": 4486, "mtime": 1753416831573, "results": "40", "hashOfConfig": "36"}, {"size": 5212, "mtime": 1753416831573, "results": "41", "hashOfConfig": "36"}, {"size": 40060, "mtime": 1753692435073, "results": "42", "hashOfConfig": "36"}, {"size": 9914, "mtime": 1753416831575, "results": "43", "hashOfConfig": "36"}, {"size": 6682, "mtime": 1753416831576, "results": "44", "hashOfConfig": "36"}, {"size": 288, "mtime": 1753416831575, "results": "45", "hashOfConfig": "36"}, {"size": 1687, "mtime": 1753686559293, "results": "46", "hashOfConfig": "36"}, {"size": 19531, "mtime": 1753416831576, "results": "47", "hashOfConfig": "36"}, {"size": 38815, "mtime": 1753416831575, "results": "48", "hashOfConfig": "36"}, {"size": 112, "mtime": 1753416831574, "results": "49", "hashOfConfig": "36"}, {"size": 16914, "mtime": 1753416831566, "results": "50", "hashOfConfig": "36"}, {"size": 54076, "mtime": 1753416831570, "results": "51", "hashOfConfig": "36"}, {"size": 25758, "mtime": 1753691710368, "results": "52", "hashOfConfig": "36"}, {"size": 87165, "mtime": 1753416831572, "results": "53", "hashOfConfig": "36"}, {"size": 1583, "mtime": 1753688257329, "results": "54", "hashOfConfig": "36"}, {"size": 1729, "mtime": 1753416831574, "results": "55", "hashOfConfig": "36"}, {"size": 247, "mtime": 1753416831574, "results": "56", "hashOfConfig": "36"}, {"size": 3176, "mtime": 1753427812502, "results": "57", "hashOfConfig": "36"}, {"size": 12339, "mtime": 1753416831571, "results": "58", "hashOfConfig": "36"}, {"size": 8356, "mtime": 1753416831566, "results": "59", "hashOfConfig": "36"}, {"size": 698, "mtime": 1753416831572, "results": "60", "hashOfConfig": "36"}, {"size": 25465, "mtime": 1753693089275, "results": "61", "hashOfConfig": "36"}, {"size": 38935, "mtime": 1753681146121, "results": "62", "hashOfConfig": "36"}, {"size": 2943, "mtime": 1753416831571, "results": "63", "hashOfConfig": "36"}, {"size": 24078, "mtime": 1753681146130, "results": "64", "hashOfConfig": "36"}, {"size": 9253, "mtime": 1753416831571, "results": "65", "hashOfConfig": "36"}, {"size": 18322, "mtime": 1753692987053, "results": "66", "hashOfConfig": "36"}, {"size": 9299, "mtime": 1753692953783, "results": "67", "hashOfConfig": "36"}, {"size": 12337, "mtime": 1753416831569, "results": "68", "hashOfConfig": "36"}, {"size": 2079, "mtime": 1753692536750, "results": "69", "hashOfConfig": "36"}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "2rg5jr", {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\kumaran\\botnexus\\client\\src\\index.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\reportWebVitals.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\App.js", ["172"], [], "E:\\kumaran\\botnexus\\client\\src\\context\\FormContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\ApiConfigContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\AuthContext.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\context\\ChatContext.js", ["173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\ApiConfigPage.js", ["186"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\FormsPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\ChatPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatbotWidget.js", ["187", "188", "189", "190", "191"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\UnifiedConfigPage.js", ["192", "193"], [], "E:\\kumaran\\botnexus\\client\\src\\pages\\DocumentsPage.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\index.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ApiConfigForm.js", ["194"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\FormBuilder.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInterface.js", ["195", "196"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\UnifiedConfigBuilder.js", ["197", "198", "199"], [], "E:\\kumaran\\botnexus\\client\\src\\utils\\api.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\MainLayout.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\layouts\\SimpleLayout.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatInput.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\HybridForm.js", ["200"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\AttendanceRegularizationDisplay.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\TypingIndicator.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatMessage.js", ["201"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\ChatFormDisplay.js", ["202", "203", "204", "205", "206", "207"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\JsonEditor.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\FormLinkingConfig.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\PayloadPreview.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\components\\RecordDisplayWithActions.js", ["208", "209", "210", "211"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\AutoTriggerHandler.js", ["212", "213", "214"], [], "E:\\kumaran\\botnexus\\client\\src\\components\\DynamicForm.js", [], [], "E:\\kumaran\\botnexus\\client\\src\\utils\\leaveBalanceCache.js", [], [], {"ruleId": "215", "severity": 1, "message": "216", "line": 8, "column": 8, "nodeType": "217", "messageId": "218", "endLine": 8, "endColumn": 16}, {"ruleId": "215", "severity": 1, "message": "219", "line": 2, "column": 8, "nodeType": "217", "messageId": "218", "endLine": 2, "endColumn": 13}, {"ruleId": "215", "severity": 1, "message": "220", "line": 14, "column": 7, "nodeType": "217", "messageId": "218", "endLine": 14, "endColumn": 24}, {"ruleId": "215", "severity": 1, "message": "221", "line": 15, "column": 7, "nodeType": "217", "messageId": "218", "endLine": 15, "endColumn": 35}, {"ruleId": "215", "severity": 1, "message": "222", "line": 25, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 25, "endColumn": 28}, {"ruleId": "223", "severity": 1, "message": "224", "line": 39, "column": 6, "nodeType": "225", "endLine": 39, "endColumn": 8, "suggestions": "226"}, {"ruleId": "215", "severity": 1, "message": "227", "line": 129, "column": 13, "nodeType": "217", "messageId": "218", "endLine": 129, "endColumn": 25}, {"ruleId": "228", "severity": 1, "message": "229", "line": 200, "column": 9, "nodeType": "230", "messageId": "231", "endLine": 200, "endColumn": 18}, {"ruleId": "228", "severity": 1, "message": "232", "line": 201, "column": 9, "nodeType": "230", "messageId": "231", "endLine": 201, "endColumn": 20}, {"ruleId": "228", "severity": 1, "message": "233", "line": 202, "column": 9, "nodeType": "230", "messageId": "231", "endLine": 202, "endColumn": 19}, {"ruleId": "228", "severity": 1, "message": "234", "line": 211, "column": 9, "nodeType": "230", "messageId": "231", "endLine": 211, "endColumn": 26}, {"ruleId": "235", "severity": 1, "message": "236", "line": 863, "column": 40, "nodeType": "237", "messageId": "231", "endLine": 863, "endColumn": 42}, {"ruleId": "235", "severity": 1, "message": "236", "line": 864, "column": 44, "nodeType": "237", "messageId": "231", "endLine": 864, "endColumn": 46}, {"ruleId": "215", "severity": 1, "message": "238", "line": 893, "column": 13, "nodeType": "217", "messageId": "218", "endLine": 893, "endColumn": 25}, {"ruleId": "215", "severity": 1, "message": "239", "line": 21, "column": 23, "nodeType": "217", "messageId": "218", "endLine": 21, "endColumn": 37}, {"ruleId": "215", "severity": 1, "message": "240", "line": 6, "column": 20, "nodeType": "217", "messageId": "218", "endLine": 6, "endColumn": 31}, {"ruleId": "215", "severity": 1, "message": "241", "line": 12, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 12, "endColumn": 20}, {"ruleId": "215", "severity": 1, "message": "242", "line": 12, "column": 22, "nodeType": "217", "messageId": "218", "endLine": 12, "endColumn": 35}, {"ruleId": "215", "severity": 1, "message": "243", "line": 13, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 13, "endColumn": 19}, {"ruleId": "215", "severity": 1, "message": "244", "line": 13, "column": 21, "nodeType": "217", "messageId": "218", "endLine": 13, "endColumn": 33}, {"ruleId": "223", "severity": 1, "message": "245", "line": 19, "column": 6, "nodeType": "225", "endLine": 19, "endColumn": 18, "suggestions": "246"}, {"ruleId": "215", "severity": 1, "message": "247", "line": 137, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 137, "endColumn": 24}, {"ruleId": "248", "severity": 1, "message": "249", "line": 158, "column": 7, "nodeType": "250", "messageId": "251", "endLine": 179, "endColumn": 8}, {"ruleId": "215", "severity": 1, "message": "252", "line": 38, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 38, "endColumn": 22}, {"ruleId": "215", "severity": 1, "message": "253", "line": 38, "column": 24, "nodeType": "217", "messageId": "218", "endLine": 38, "endColumn": 39}, {"ruleId": "215", "severity": 1, "message": "254", "line": 304, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 304, "endColumn": 37}, {"ruleId": "215", "severity": 1, "message": "255", "line": 351, "column": 11, "nodeType": "217", "messageId": "218", "endLine": 351, "endColumn": 23}, {"ruleId": "215", "severity": 1, "message": "256", "line": 352, "column": 11, "nodeType": "217", "messageId": "218", "endLine": 352, "endColumn": 21}, {"ruleId": "215", "severity": 1, "message": "257", "line": 137, "column": 13, "nodeType": "217", "messageId": "218", "endLine": 137, "endColumn": 27}, {"ruleId": "215", "severity": 1, "message": "258", "line": 61, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 61, "endColumn": 27}, {"ruleId": "215", "severity": 1, "message": "259", "line": 12, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 12, "endColumn": 21}, {"ruleId": "215", "severity": 1, "message": "260", "line": 280, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 280, "endColumn": 26}, {"ruleId": "215", "severity": 1, "message": "261", "line": 372, "column": 9, "nodeType": "217", "messageId": "218", "endLine": 372, "endColumn": 40}, {"ruleId": "215", "severity": 1, "message": "262", "line": 375, "column": 31, "nodeType": "217", "messageId": "218", "endLine": 375, "endColumn": 38}, {"ruleId": "215", "severity": 1, "message": "263", "line": 375, "column": 40, "nodeType": "217", "messageId": "218", "endLine": 375, "endColumn": 48}, {"ruleId": "215", "severity": 1, "message": "264", "line": 375, "column": 50, "nodeType": "217", "messageId": "218", "endLine": 375, "endColumn": 61}, {"ruleId": "215", "severity": 1, "message": "265", "line": 3, "column": 8, "nodeType": "217", "messageId": "218", "endLine": 3, "endColumn": 25}, {"ruleId": "223", "severity": 1, "message": "266", "line": 257, "column": 6, "nodeType": "225", "endLine": 266, "endColumn": 4, "suggestions": "267"}, {"ruleId": "223", "severity": 1, "message": "268", "line": 285, "column": 6, "nodeType": "225", "endLine": 285, "endColumn": 126, "suggestions": "269"}, {"ruleId": "223", "severity": 1, "message": "270", "line": 333, "column": 6, "nodeType": "225", "endLine": 333, "endColumn": 76, "suggestions": "271"}, {"ruleId": "215", "severity": 1, "message": "272", "line": 17, "column": 10, "nodeType": "217", "messageId": "218", "endLine": 17, "endColumn": 20}, {"ruleId": "223", "severity": 1, "message": "273", "line": 142, "column": 6, "nodeType": "225", "endLine": 142, "endColumn": 64, "suggestions": "274"}, {"ruleId": "223", "severity": 1, "message": "275", "line": 148, "column": 24, "nodeType": "217", "endLine": 148, "endColumn": 31}, "no-unused-vars", "'ChatPage' is defined but never used.", "Identifier", "unusedVar", "'axios' is defined but never used.", "'leaveBalanceCache' is assigned a value but never used.", "'LEAVE_BALANCE_CACHE_DURATION' is assigned a value but never used.", "'conversationLoaded' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'loadConversation' and 'loading'. Either include them or remove the dependency array.", "ArrayExpression", ["276"], "'supervisorId' is assigned a value but never used.", "no-dupe-keys", "Duplicate key 'fieldName'.", "ObjectExpression", "unexpected", "Duplicate key 'currentStep'.", "Duplicate key 'totalSteps'.", "Duplicate key 'isDynamicResponse'.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "'errorMessage' is assigned a value but never used.", "'setTestResults' is assigned a value but never used.", "'setMessages' is assigned a value but never used.", "'inputValue' is assigned a value but never used.", "'setInputValue' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchConfigs'. Either include it or remove the dependency array.", ["277"], "'handleCreateNew' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'hybridFormId' is assigned a value but never used.", "'setHybridFormId' is assigned a value but never used.", "'handlePayloadStructureChange' is assigned a value but never used.", "'targetConfig' is assigned a value but never used.", "'targetPath' is assigned a value but never used.", "'successMessage' is assigned a value but never used.", "'isLeaveBalanceData' is assigned a value but never used.", "'apiResponse' is assigned a value but never used.", "'prepareApiRequest' is assigned a value but never used.", "'prepareRegularizationApiRequest' is assigned a value but never used.", "'headers' is assigned a value but never used.", "'authType' is assigned a value but never used.", "'authDetails' is assigned a value but never used.", "'leaveBalanceCache' is defined but never used.", "React Hook useCallback has a missing dependency: 'handleApplyClick'. Either include it or remove the dependency array.", ["278"], "React Hook useEffect has a missing dependency: 'formLinkingConfig?.recordActions'. Either include it or remove the dependency array.", ["279"], "React Hook useCallback has a missing dependency: 'handleAutoSubmitLinkingForm'. Either include it or remove the dependency array.", ["280"], "'isChecking' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'isLeaveBalanceData'. Either include it or remove the dependency array.", ["281"], "The ref value 'processedDataRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'processedDataRef.current' to a variable inside the effect, and use that variable in the cleanup function.", {"desc": "282", "fix": "283"}, {"desc": "284", "fix": "285"}, {"desc": "286", "fix": "287"}, {"desc": "288", "fix": "289"}, {"desc": "290", "fix": "291"}, {"desc": "292", "fix": "293"}, "Update the dependencies array to be: [loadConversation, loading]", {"range": "294", "text": "295"}, "Update the dependencies array to be: [fetchConfigs, filterType]", {"range": "296", "text": "297"}, "Update the dependencies array to be: [getEligibleRecords, getRecordsToTrigger, dataHash, autoTriggerConfig, executedTriggers, autoTriggerState, formLinkingConfig.recordActions, hideRecordDisplay, handleApplyClick]", {"range": "298", "text": "299"}, "Update the dependencies array to be: [executeAutoTrigger, autoTriggerConfig.enabled, autoTriggerConfig.skipLeaveBalanceAutoTrigger, data, isLeaveBalanceData, formLinkingConfig?.recordActions]", {"range": "300", "text": "301"}, "Update the dependencies array to be: [loadingActions, formLinkingConfig?.recordActions, formId, data, onFormLinkTriggered, handleAutoSubmitLinkingForm]", {"range": "302", "text": "303"}, "Update the dependencies array to be: [formId, recordData, enabled, skipLeaveBalanceAutoTrigger, isLeaveBalanceData]", {"range": "304", "text": "305"}, [1704, 1706], "[loadConversation, loading]", [740, 752], "[fetchConfigs, filterType]", [9209, 9404], "[getEligibleRecords, getRecordsToTrigger, dataHash, autoTriggerConfig, executedTriggers, autoTriggerState, formLinkingConfig.recordActions, hideRecordDisplay, handleApplyClick]", [10080, 10200], "[executeAutoTrigger, autoTriggerConfig.enabled, autoTriggerConfig.skipLeaveBalanceAutoTrigger, data, isLeaveBalanceData, formLinkingConfig?.recordActions]", [11790, 11860], "[loadingActions, formLinkingConfig?.recordActions, formId, data, onFormLinkTriggered, handleAutoSubmitLinkingForm]", [4853, 4911], "[formId, recordData, enabled, skipLeaveBalanceAutoTrigger, isLeaveBalanceData]"]