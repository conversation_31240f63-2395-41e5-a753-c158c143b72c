{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\AutoTriggerHandler.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport DynamicForm from './DynamicForm';\nimport api from '../utils/api';\n\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AutoTriggerHandler = ({\n  formId,\n  recordData,\n  onFormSubmit,\n  enabled = true,\n  skipLeaveBalanceAutoTrigger = false // New prop to skip auto-trigger for leave balance queries\n}) => {\n  _s();\n  const [isChecking, setIsChecking] = useState(false);\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  const processedDataRef = useRef(new Set()); // Track processed data to prevent duplicates\n\n  // Helper function to check if data is leave balance related\n  const isLeaveBalanceData = data => {\n    if (!data) return false;\n\n    // Check if data contains leave balance information\n    if (Array.isArray(data)) {\n      return data.some(item => item.hasOwnProperty('leaveTypeName') || item.hasOwnProperty('leaveType') || item.hasOwnProperty('balance') || item.hasOwnProperty('report'));\n    }\n    if (typeof data === 'object') {\n      return data.hasOwnProperty('leaveTypeName') || data.hasOwnProperty('leaveType') || data.hasOwnProperty('balance') || data.hasOwnProperty('report') || data.data && isLeaveBalanceData(data.data);\n    }\n    return false;\n  };\n\n  // Generate a unique key for the current data to prevent duplicate processing\n  const getDataKey = data => {\n    try {\n      return JSON.stringify(data);\n    } catch (error) {\n      return String(data);\n    }\n  };\n  useEffect(() => {\n    if (!enabled || !formId || !recordData) return;\n\n    // Only skip auto-trigger for leave balance data if specifically requested\n    // This prevents unwanted auto-form-opening, but still allows leave balance display\n    if (skipLeaveBalanceAutoTrigger && isLeaveBalanceData(recordData)) {\n      console.log('🚫 Skipping auto-trigger for leave balance data (display still works)');\n      return;\n    }\n\n    // Check if we've already processed this data to prevent duplicates\n    const dataKey = getDataKey(recordData);\n    if (processedDataRef.current.has(dataKey)) {\n      console.log('🔄 Skipping duplicate auto-trigger check for same data');\n      return;\n    }\n    const checkAutoTrigger = async () => {\n      setIsChecking(true);\n      try {\n        console.log('🔍 Checking auto-trigger for form:', formId);\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\n          recordData\n        });\n        if (response.data.success && response.data.shouldTrigger) {\n          const triggerInfo = response.data;\n          const delay = triggerInfo.delaySeconds || 0;\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\n\n          // Mark this data as processed\n          processedDataRef.current.add(dataKey);\n          if (delay > 0) {\n            // Show countdown\n            setCountdown(delay);\n            const countdownInterval = setInterval(() => {\n              setCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  triggerForm(triggerInfo);\n                  return 0;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else {\n            // Trigger immediately\n            triggerForm(triggerInfo);\n          }\n        }\n      } catch (error) {\n        console.error('❌ Error checking auto-trigger:', error);\n      } finally {\n        setIsChecking(false);\n      }\n    };\n    const triggerForm = async triggerInfo => {\n      try {\n        // Process the form linking to get the target form and prefilled data\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\n          recordData,\n          parentData: {},\n          actionIndex: triggerInfo.actionIndex\n        });\n        if (linkingResponse.data.success) {\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\n\n          // Set the auto-triggered form data\n          setAutoTriggerForm({\n            ...linkingResponse.data.targetForm,\n            prefillData: linkingResponse.data.prefillData,\n            buttonText: linkingResponse.data.buttonText,\n            isAutoTriggered: true\n          });\n          setShowAutoTriggerModal(true);\n        }\n      } catch (error) {\n        console.error('❌ Error processing auto-trigger:', error);\n      }\n    };\n    checkAutoTrigger();\n  }, [formId, recordData, enabled, skipLeaveBalanceAutoTrigger]);\n\n  // Clean up processed data when component unmounts or data changes significantly\n  useEffect(() => {\n    return () => {\n      // Clear processed data when component unmounts\n      processedDataRef.current.clear();\n    };\n  }, []);\n\n  // Clear processed data when formId changes (new form context)\n  useEffect(() => {\n    processedDataRef.current.clear();\n  }, [formId]);\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\n    try {\n      // Submit the form\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\n      if (response.data.success) {\n        setShowAutoTriggerModal(false);\n        setAutoTriggerForm(null);\n\n        // Notify parent component\n        if (onFormSubmit) {\n          onFormSubmit(formId, formData, response.data);\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error submitting auto-triggered form:', error);\n    }\n  };\n  const handleCloseAutoTriggerModal = () => {\n    setShowAutoTriggerModal(false);\n    setAutoTriggerForm(null);\n    setCountdown(0);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [countdown > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: [\"Auto-opening form in \", countdown, \"s\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 9\n    }, this), showAutoTriggerModal && autoTriggerForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-500 text-white px-6 py-4 rounded-t-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold\",\n                children: \"\\uD83D\\uDE80 Auto-Opened Form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 206,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-100 mt-1\",\n                children: autoTriggerForm.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 205,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseAutoTriggerModal,\n              className: \"text-white hover:text-gray-200 focus:outline-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 218,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 217,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-blue-400\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 230,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-blue-800\",\n                  children: \"Auto-Triggered Form\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 text-sm text-blue-700\",\n                  children: \"This form was automatically opened based on the data you're viewing. Some fields may be pre-filled based on the original record.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DynamicForm, {\n            form: autoTriggerForm,\n            onSubmit: handleAutoTriggerFormSubmit,\n            onCancel: handleCloseAutoTriggerModal\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(AutoTriggerHandler, \"KroPXYeQmttcZKKVX/aYLd+L0vs=\");\n_c = AutoTriggerHandler;\nexport default AutoTriggerHandler;\nvar _c;\n$RefreshReg$(_c, \"AutoTriggerHandler\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "DynamicForm", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AutoTriggerHandler", "formId", "recordData", "onFormSubmit", "enabled", "skipLeaveBalanceAutoTrigger", "_s", "isChecking", "setIsChecking", "autoTriggerForm", "setAutoTriggerForm", "showAutoTriggerModal", "setShowAutoTriggerModal", "countdown", "setCountdown", "processedDataRef", "Set", "isLeaveBalanceData", "data", "Array", "isArray", "some", "item", "hasOwnProperty", "getDataKey", "JSON", "stringify", "error", "String", "console", "log", "dataKey", "current", "has", "checkAutoTrigger", "response", "post", "success", "should<PERSON><PERSON>ger", "triggerInfo", "delay", "delaySeconds", "add", "countdownInterval", "setInterval", "prev", "clearInterval", "triggerForm", "linkingResponse", "parentData", "actionIndex", "targetForm", "name", "prefillData", "buttonText", "isAutoTriggered", "clear", "handleAutoTriggerFormSubmit", "formData", "handleCloseAutoTriggerModal", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fillRule", "clipRule", "form", "onSubmit", "onCancel", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/AutoTriggerHandler.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport DynamicForm from './DynamicForm';\r\nimport api from '../utils/api';\r\n\r\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\r\nconst AutoTriggerHandler = ({\r\n  formId,\r\n  recordData,\r\n  onFormSubmit,\r\n  enabled = true,\r\n  skipLeaveBalanceAutoTrigger = false // New prop to skip auto-trigger for leave balance queries\r\n}) => {\r\n  const [isChecking, setIsChecking] = useState(false);\r\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\r\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\r\n  const [countdown, setCountdown] = useState(0);\r\n  const processedDataRef = useRef(new Set()); // Track processed data to prevent duplicates\r\n\r\n  // Helper function to check if data is leave balance related\r\n  const isLeaveBalanceData = (data) => {\r\n    if (!data) return false;\r\n\r\n    // Check if data contains leave balance information\r\n    if (Array.isArray(data)) {\r\n      return data.some(item =>\r\n        item.hasOwnProperty('leaveTypeName') ||\r\n        item.hasOwnProperty('leaveType') ||\r\n        item.hasOwnProperty('balance') ||\r\n        item.hasOwnProperty('report')\r\n      );\r\n    }\r\n\r\n    if (typeof data === 'object') {\r\n      return data.hasOwnProperty('leaveTypeName') ||\r\n             data.hasOwnProperty('leaveType') ||\r\n             data.hasOwnProperty('balance') ||\r\n             data.hasOwnProperty('report') ||\r\n             (data.data && isLeaveBalanceData(data.data));\r\n    }\r\n\r\n    return false;\r\n  };\r\n\r\n  // Generate a unique key for the current data to prevent duplicate processing\r\n  const getDataKey = (data) => {\r\n    try {\r\n      return JSON.stringify(data);\r\n    } catch (error) {\r\n      return String(data);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (!enabled || !formId || !recordData) return;\r\n\r\n    // Only skip auto-trigger for leave balance data if specifically requested\r\n    // This prevents unwanted auto-form-opening, but still allows leave balance display\r\n    if (skipLeaveBalanceAutoTrigger && isLeaveBalanceData(recordData)) {\r\n      console.log('🚫 Skipping auto-trigger for leave balance data (display still works)');\r\n      return;\r\n    }\r\n\r\n    // Check if we've already processed this data to prevent duplicates\r\n    const dataKey = getDataKey(recordData);\r\n    if (processedDataRef.current.has(dataKey)) {\r\n      console.log('🔄 Skipping duplicate auto-trigger check for same data');\r\n      return;\r\n    }\r\n\r\n    const checkAutoTrigger = async () => {\r\n      setIsChecking(true);\r\n      try {\r\n        console.log('🔍 Checking auto-trigger for form:', formId);\r\n\r\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\r\n          recordData\r\n        });\r\n\r\n        if (response.data.success && response.data.shouldTrigger) {\r\n          const triggerInfo = response.data;\r\n          const delay = triggerInfo.delaySeconds || 0;\r\n\r\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\r\n\r\n          // Mark this data as processed\r\n          processedDataRef.current.add(dataKey);\r\n\r\n          if (delay > 0) {\r\n            // Show countdown\r\n            setCountdown(delay);\r\n            const countdownInterval = setInterval(() => {\r\n              setCountdown(prev => {\r\n                if (prev <= 1) {\r\n                  clearInterval(countdownInterval);\r\n                  triggerForm(triggerInfo);\r\n                  return 0;\r\n                }\r\n                return prev - 1;\r\n              });\r\n            }, 1000);\r\n          } else {\r\n            // Trigger immediately\r\n            triggerForm(triggerInfo);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Error checking auto-trigger:', error);\r\n      } finally {\r\n        setIsChecking(false);\r\n      }\r\n    };\r\n\r\n    const triggerForm = async (triggerInfo) => {\r\n      try {\r\n        // Process the form linking to get the target form and prefilled data\r\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\r\n          recordData,\r\n          parentData: {},\r\n          actionIndex: triggerInfo.actionIndex\r\n        });\r\n\r\n        if (linkingResponse.data.success) {\r\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\r\n\r\n          // Set the auto-triggered form data\r\n          setAutoTriggerForm({\r\n            ...linkingResponse.data.targetForm,\r\n            prefillData: linkingResponse.data.prefillData,\r\n            buttonText: linkingResponse.data.buttonText,\r\n            isAutoTriggered: true\r\n          });\r\n          setShowAutoTriggerModal(true);\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Error processing auto-trigger:', error);\r\n      }\r\n    };\r\n\r\n    checkAutoTrigger();\r\n  }, [formId, recordData, enabled, skipLeaveBalanceAutoTrigger]);\r\n\r\n  // Clean up processed data when component unmounts or data changes significantly\r\n  useEffect(() => {\r\n    return () => {\r\n      // Clear processed data when component unmounts\r\n      processedDataRef.current.clear();\r\n    };\r\n  }, []);\r\n\r\n  // Clear processed data when formId changes (new form context)\r\n  useEffect(() => {\r\n    processedDataRef.current.clear();\r\n  }, [formId]);\r\n\r\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\r\n    try {\r\n      // Submit the form\r\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\r\n      \r\n      if (response.data.success) {\r\n        setShowAutoTriggerModal(false);\r\n        setAutoTriggerForm(null);\r\n        \r\n        // Notify parent component\r\n        if (onFormSubmit) {\r\n          onFormSubmit(formId, formData, response.data);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error submitting auto-triggered form:', error);\r\n    }\r\n  };\r\n\r\n  const handleCloseAutoTriggerModal = () => {\r\n    setShowAutoTriggerModal(false);\r\n    setAutoTriggerForm(null);\r\n    setCountdown(0);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Countdown Indicator */}\r\n      {countdown > 0 && (\r\n        <div className=\"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n            <span className=\"text-sm font-medium\">\r\n              Auto-opening form in {countdown}s\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Auto-Trigger Form Modal */}\r\n      {showAutoTriggerModal && autoTriggerForm && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n            {/* Modal Header */}\r\n            <div className=\"bg-blue-500 text-white px-6 py-4 rounded-t-lg\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <h2 className=\"text-lg font-semibold\">\r\n                    🚀 Auto-Opened Form\r\n                  </h2>\r\n                  <p className=\"text-sm text-blue-100 mt-1\">\r\n                    {autoTriggerForm.name}\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={handleCloseAutoTriggerModal}\r\n                  className=\"text-white hover:text-gray-200 focus:outline-none\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Modal Content */}\r\n            <div className=\"p-6\">\r\n              <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <h3 className=\"text-sm font-medium text-blue-800\">Auto-Triggered Form</h3>\r\n                    <div className=\"mt-1 text-sm text-blue-700\">\r\n                      This form was automatically opened based on the data you're viewing. \r\n                      Some fields may be pre-filled based on the original record.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <DynamicForm\r\n                form={autoTriggerForm}\r\n                onSubmit={handleAutoTriggerFormSubmit}\r\n                onCancel={handleCloseAutoTriggerModal}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AutoTriggerHandler;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,GAAG,MAAM,cAAc;;AAE9B;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKA,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,UAAU;EACVC,YAAY;EACZC,OAAO,GAAG,IAAI;EACdC,2BAA2B,GAAG,KAAK,CAAC;AACtC,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGjB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACkB,eAAe,EAAEC,kBAAkB,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACoB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAMwB,gBAAgB,GAAGtB,MAAM,CAAC,IAAIuB,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5C;EACA,MAAMC,kBAAkB,GAAIC,IAAI,IAAK;IACnC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACG,IAAI,CAACC,IAAI,IACnBA,IAAI,CAACC,cAAc,CAAC,eAAe,CAAC,IACpCD,IAAI,CAACC,cAAc,CAAC,WAAW,CAAC,IAChCD,IAAI,CAACC,cAAc,CAAC,SAAS,CAAC,IAC9BD,IAAI,CAACC,cAAc,CAAC,QAAQ,CAC9B,CAAC;IACH;IAEA,IAAI,OAAOL,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOA,IAAI,CAACK,cAAc,CAAC,eAAe,CAAC,IACpCL,IAAI,CAACK,cAAc,CAAC,WAAW,CAAC,IAChCL,IAAI,CAACK,cAAc,CAAC,SAAS,CAAC,IAC9BL,IAAI,CAACK,cAAc,CAAC,QAAQ,CAAC,IAC5BL,IAAI,CAACA,IAAI,IAAID,kBAAkB,CAACC,IAAI,CAACA,IAAI,CAAE;IACrD;IAEA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMM,UAAU,GAAIN,IAAI,IAAK;IAC3B,IAAI;MACF,OAAOO,IAAI,CAACC,SAAS,CAACR,IAAI,CAAC;IAC7B,CAAC,CAAC,OAAOS,KAAK,EAAE;MACd,OAAOC,MAAM,CAACV,IAAI,CAAC;IACrB;EACF,CAAC;EAED1B,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,OAAO,IAAI,CAACH,MAAM,IAAI,CAACC,UAAU,EAAE;;IAExC;IACA;IACA,IAAIG,2BAA2B,IAAIY,kBAAkB,CAACf,UAAU,CAAC,EAAE;MACjE2B,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;MACpF;IACF;;IAEA;IACA,MAAMC,OAAO,GAAGP,UAAU,CAACtB,UAAU,CAAC;IACtC,IAAIa,gBAAgB,CAACiB,OAAO,CAACC,GAAG,CAACF,OAAO,CAAC,EAAE;MACzCF,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;MACrE;IACF;IAEA,MAAMI,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC1B,aAAa,CAAC,IAAI,CAAC;MACnB,IAAI;QACFqB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE7B,MAAM,CAAC;QAEzD,MAAMkC,QAAQ,GAAG,MAAMxC,GAAG,CAACyC,IAAI,CAAC,mBAAmBnC,MAAM,qBAAqB,EAAE;UAC9EC;QACF,CAAC,CAAC;QAEF,IAAIiC,QAAQ,CAACjB,IAAI,CAACmB,OAAO,IAAIF,QAAQ,CAACjB,IAAI,CAACoB,aAAa,EAAE;UACxD,MAAMC,WAAW,GAAGJ,QAAQ,CAACjB,IAAI;UACjC,MAAMsB,KAAK,GAAGD,WAAW,CAACE,YAAY,IAAI,CAAC;UAE3CZ,OAAO,CAACC,GAAG,CAAC,8BAA8BU,KAAK,aAAa,CAAC;;UAE7D;UACAzB,gBAAgB,CAACiB,OAAO,CAACU,GAAG,CAACX,OAAO,CAAC;UAErC,IAAIS,KAAK,GAAG,CAAC,EAAE;YACb;YACA1B,YAAY,CAAC0B,KAAK,CAAC;YACnB,MAAMG,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1C9B,YAAY,CAAC+B,IAAI,IAAI;gBACnB,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChCI,WAAW,CAACR,WAAW,CAAC;kBACxB,OAAO,CAAC;gBACV;gBACA,OAAOM,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACL;YACAE,WAAW,CAACR,WAAW,CAAC;UAC1B;QACF;MACF,CAAC,CAAC,OAAOZ,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD,CAAC,SAAS;QACRnB,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC;IAED,MAAMuC,WAAW,GAAG,MAAOR,WAAW,IAAK;MACzC,IAAI;QACF;QACA,MAAMS,eAAe,GAAG,MAAMrD,GAAG,CAACyC,IAAI,CAAC,mBAAmBnC,MAAM,YAAY,EAAE;UAC5EC,UAAU;UACV+C,UAAU,EAAE,CAAC,CAAC;UACdC,WAAW,EAAEX,WAAW,CAACW;QAC3B,CAAC,CAAC;QAEF,IAAIF,eAAe,CAAC9B,IAAI,CAACmB,OAAO,EAAE;UAChCR,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkB,eAAe,CAAC9B,IAAI,CAACiC,UAAU,CAACC,IAAI,CAAC;;UAE7E;UACA1C,kBAAkB,CAAC;YACjB,GAAGsC,eAAe,CAAC9B,IAAI,CAACiC,UAAU;YAClCE,WAAW,EAAEL,eAAe,CAAC9B,IAAI,CAACmC,WAAW;YAC7CC,UAAU,EAAEN,eAAe,CAAC9B,IAAI,CAACoC,UAAU;YAC3CC,eAAe,EAAE;UACnB,CAAC,CAAC;UACF3C,uBAAuB,CAAC,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC,OAAOe,KAAK,EAAE;QACdE,OAAO,CAACF,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDO,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACjC,MAAM,EAAEC,UAAU,EAAEE,OAAO,EAAEC,2BAA2B,CAAC,CAAC;;EAE9D;EACAb,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX;MACAuB,gBAAgB,CAACiB,OAAO,CAACwB,KAAK,CAAC,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAhE,SAAS,CAAC,MAAM;IACduB,gBAAgB,CAACiB,OAAO,CAACwB,KAAK,CAAC,CAAC;EAClC,CAAC,EAAE,CAACvD,MAAM,CAAC,CAAC;EAEZ,MAAMwD,2BAA2B,GAAG,MAAAA,CAAOxD,MAAM,EAAEyD,QAAQ,KAAK;IAC9D,IAAI;MACF;MACA,MAAMvB,QAAQ,GAAG,MAAMxC,GAAG,CAACyC,IAAI,CAAC,mBAAmBnC,MAAM,SAAS,EAAEyD,QAAQ,CAAC;MAE7E,IAAIvB,QAAQ,CAACjB,IAAI,CAACmB,OAAO,EAAE;QACzBzB,uBAAuB,CAAC,KAAK,CAAC;QAC9BF,kBAAkB,CAAC,IAAI,CAAC;;QAExB;QACA,IAAIP,YAAY,EAAE;UAChBA,YAAY,CAACF,MAAM,EAAEyD,QAAQ,EAAEvB,QAAQ,CAACjB,IAAI,CAAC;QAC/C;MACF;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdE,OAAO,CAACF,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;EAED,MAAMgC,2BAA2B,GAAGA,CAAA,KAAM;IACxC/C,uBAAuB,CAAC,KAAK,CAAC;IAC9BF,kBAAkB,CAAC,IAAI,CAAC;IACxBI,YAAY,CAAC,CAAC,CAAC;EACjB,CAAC;EAED,oBACEjB,OAAA,CAAAE,SAAA;IAAA6D,QAAA,GAEG/C,SAAS,GAAG,CAAC,iBACZhB,OAAA;MAAKgE,SAAS,EAAC,mFAAmF;MAAAD,QAAA,eAChG/D,OAAA;QAAKgE,SAAS,EAAC,6BAA6B;QAAAD,QAAA,gBAC1C/D,OAAA;UAAKgE,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpGpE,OAAA;UAAMgE,SAAS,EAAC,qBAAqB;UAAAD,QAAA,GAAC,uBACf,EAAC/C,SAAS,EAAC,GAClC;QAAA;UAAAiD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAtD,oBAAoB,IAAIF,eAAe,iBACtCZ,OAAA;MAAKgE,SAAS,EAAC,4EAA4E;MAAAD,QAAA,eACzF/D,OAAA;QAAKgE,SAAS,EAAC,kFAAkF;QAAAD,QAAA,gBAE/F/D,OAAA;UAAKgE,SAAS,EAAC,+CAA+C;UAAAD,QAAA,eAC5D/D,OAAA;YAAKgE,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChD/D,OAAA;cAAA+D,QAAA,gBACE/D,OAAA;gBAAIgE,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLpE,OAAA;gBAAGgE,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EACtCnD,eAAe,CAAC2C;cAAI;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNpE,OAAA;cACEqE,OAAO,EAAEP,2BAA4B;cACrCE,SAAS,EAAC,mDAAmD;cAAAD,QAAA,eAE7D/D,OAAA;gBAAKgE,SAAS,EAAC,SAAS;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5E/D,OAAA;kBAAMyE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNpE,OAAA;UAAKgE,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClB/D,OAAA;YAAKgE,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACpE/D,OAAA;cAAKgE,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC/B/D,OAAA;gBAAKgE,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC5B/D,OAAA;kBAAKgE,SAAS,EAAC,uBAAuB;kBAACQ,OAAO,EAAC,WAAW;kBAACF,IAAI,EAAC,cAAc;kBAAAP,QAAA,eAC5E/D,OAAA;oBAAM6E,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,kIAAkI;oBAACE,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNpE,OAAA;gBAAKgE,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnB/D,OAAA;kBAAIgE,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1EpE,OAAA;kBAAKgE,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,EAAC;gBAG5C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENpE,OAAA,CAACH,WAAW;YACVkF,IAAI,EAAEnE,eAAgB;YACtBoE,QAAQ,EAAEpB,2BAA4B;YACtCqB,QAAQ,EAAEnB;UAA4B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAAC3D,EAAA,CApPIN,kBAAkB;AAAA+E,EAAA,GAAlB/E,kBAAkB;AAsPxB,eAAeA,kBAAkB;AAAC,IAAA+E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}