{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\AutoTriggerHandler.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport DynamicForm from './DynamicForm';\nimport api from '../utils/api';\n\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AutoTriggerHandler = ({\n  formId,\n  recordData,\n  onFormSubmit,\n  enabled = true\n}) => {\n  _s();\n  const [isChecking, setIsChecking] = useState(false);\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  useEffect(() => {\n    if (!enabled || !formId || !recordData) return;\n\n    // Check if this is a chat opening scenario (prevent auto-trigger on chat open)\n    const isChatOpening = () => {\n      const lastChatLoad = localStorage.getItem('lastChatLoadTime');\n      const timeSinceLoad = lastChatLoad ? Date.now() - parseInt(lastChatLoad) : Infinity;\n      if (timeSinceLoad < 5000) {\n        console.log(`⏸️ AutoTriggerHandler: Skipping auto-trigger check - chat recently opened (${timeSinceLoad}ms ago)`);\n        return true;\n      }\n      return false;\n    };\n\n    // Don't check auto-trigger if this is a chat opening scenario\n    if (isChatOpening()) {\n      return;\n    }\n    const checkAutoTrigger = async () => {\n      setIsChecking(true);\n      try {\n        console.log('🔍 Checking auto-trigger for form:', formId);\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\n          recordData\n        });\n        if (response.data.success && response.data.shouldTrigger) {\n          const triggerInfo = response.data;\n          const delay = triggerInfo.delaySeconds || 0;\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\n          if (delay > 0) {\n            // Show countdown\n            setCountdown(delay);\n            const countdownInterval = setInterval(() => {\n              setCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  triggerForm(triggerInfo);\n                  return 0;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else {\n            // Trigger immediately\n            triggerForm(triggerInfo);\n          }\n        }\n      } catch (error) {\n        console.error('❌ Error checking auto-trigger:', error);\n      } finally {\n        setIsChecking(false);\n      }\n    };\n    const triggerForm = async triggerInfo => {\n      try {\n        // Process the form linking to get the target form and prefilled data\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\n          recordData,\n          parentData: {},\n          actionIndex: triggerInfo.actionIndex\n        });\n        if (linkingResponse.data.success) {\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\n\n          // Set the auto-triggered form data\n          setAutoTriggerForm({\n            ...linkingResponse.data.targetForm,\n            prefillData: linkingResponse.data.prefillData,\n            buttonText: linkingResponse.data.buttonText,\n            isAutoTriggered: true\n          });\n          setShowAutoTriggerModal(true);\n        }\n      } catch (error) {\n        console.error('❌ Error processing auto-trigger:', error);\n      }\n    };\n    checkAutoTrigger();\n  }, [formId, recordData, enabled]);\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\n    try {\n      // Submit the form\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\n      if (response.data.success) {\n        setShowAutoTriggerModal(false);\n        setAutoTriggerForm(null);\n\n        // Notify parent component\n        if (onFormSubmit) {\n          onFormSubmit(formId, formData, response.data);\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error submitting auto-triggered form:', error);\n    }\n  };\n  const handleCloseAutoTriggerModal = () => {\n    setShowAutoTriggerModal(false);\n    setAutoTriggerForm(null);\n    setCountdown(0);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [countdown > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: [\"Auto-opening form in \", countdown, \"s\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 9\n    }, this), showAutoTriggerModal && autoTriggerForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-500 text-white px-6 py-4 rounded-t-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold\",\n                children: \"\\uD83D\\uDE80 Auto-Opened Form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 158,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-100 mt-1\",\n                children: autoTriggerForm.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseAutoTriggerModal,\n              className: \"text-white hover:text-gray-200 focus:outline-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 156,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-blue-400\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-blue-800\",\n                  children: \"Auto-Triggered Form\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 text-sm text-blue-700\",\n                  children: \"This form was automatically opened based on the data you're viewing. Some fields may be pre-filled based on the original record.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DynamicForm, {\n            form: autoTriggerForm,\n            onSubmit: handleAutoTriggerFormSubmit,\n            onCancel: handleCloseAutoTriggerModal\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(AutoTriggerHandler, \"AMZ419gay0+2AF7PRQoJXJ6hDfU=\");\n_c = AutoTriggerHandler;\nexport default AutoTriggerHandler;\nvar _c;\n$RefreshReg$(_c, \"AutoTriggerHandler\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "DynamicForm", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AutoTriggerHandler", "formId", "recordData", "onFormSubmit", "enabled", "_s", "isChecking", "setIsChecking", "autoTriggerForm", "setAutoTriggerForm", "showAutoTriggerModal", "setShowAutoTriggerModal", "countdown", "setCountdown", "isChatOpening", "lastChatLoad", "localStorage", "getItem", "timeSinceLoad", "Date", "now", "parseInt", "Infinity", "console", "log", "checkAutoTrigger", "response", "post", "data", "success", "should<PERSON><PERSON>ger", "triggerInfo", "delay", "delaySeconds", "countdownInterval", "setInterval", "prev", "clearInterval", "triggerForm", "error", "linkingResponse", "parentData", "actionIndex", "targetForm", "name", "prefillData", "buttonText", "isAutoTriggered", "handleAutoTriggerFormSubmit", "formData", "handleCloseAutoTriggerModal", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fillRule", "clipRule", "form", "onSubmit", "onCancel", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/AutoTriggerHandler.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DynamicForm from './DynamicForm';\r\nimport api from '../utils/api';\r\n\r\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\r\nconst AutoTriggerHandler = ({ \r\n  formId, \r\n  recordData, \r\n  onFormSubmit,\r\n  enabled = true \r\n}) => {\r\n  const [isChecking, setIsChecking] = useState(false);\r\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\r\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\r\n  const [countdown, setCountdown] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (!enabled || !formId || !recordData) return;\r\n\r\n    // Check if this is a chat opening scenario (prevent auto-trigger on chat open)\r\n    const isChatOpening = () => {\r\n      const lastChatLoad = localStorage.getItem('lastChatLoadTime');\r\n      const timeSinceLoad = lastChatLoad ? Date.now() - parseInt(lastChatLoad) : Infinity;\r\n\r\n      if (timeSinceLoad < 5000) {\r\n        console.log(`⏸️ AutoTriggerHandler: Skipping auto-trigger check - chat recently opened (${timeSinceLoad}ms ago)`);\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    };\r\n\r\n    // Don't check auto-trigger if this is a chat opening scenario\r\n    if (isChatOpening()) {\r\n      return;\r\n    }\r\n\r\n    const checkAutoTrigger = async () => {\r\n      setIsChecking(true);\r\n      try {\r\n        console.log('🔍 Checking auto-trigger for form:', formId);\r\n\r\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\r\n          recordData\r\n        });\r\n\r\n        if (response.data.success && response.data.shouldTrigger) {\r\n          const triggerInfo = response.data;\r\n          const delay = triggerInfo.delaySeconds || 0;\r\n          \r\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\r\n          \r\n          if (delay > 0) {\r\n            // Show countdown\r\n            setCountdown(delay);\r\n            const countdownInterval = setInterval(() => {\r\n              setCountdown(prev => {\r\n                if (prev <= 1) {\r\n                  clearInterval(countdownInterval);\r\n                  triggerForm(triggerInfo);\r\n                  return 0;\r\n                }\r\n                return prev - 1;\r\n              });\r\n            }, 1000);\r\n          } else {\r\n            // Trigger immediately\r\n            triggerForm(triggerInfo);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Error checking auto-trigger:', error);\r\n      } finally {\r\n        setIsChecking(false);\r\n      }\r\n    };\r\n\r\n    const triggerForm = async (triggerInfo) => {\r\n      try {\r\n        // Process the form linking to get the target form and prefilled data\r\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\r\n          recordData,\r\n          parentData: {},\r\n          actionIndex: triggerInfo.actionIndex\r\n        });\r\n\r\n        if (linkingResponse.data.success) {\r\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\r\n          \r\n          // Set the auto-triggered form data\r\n          setAutoTriggerForm({\r\n            ...linkingResponse.data.targetForm,\r\n            prefillData: linkingResponse.data.prefillData,\r\n            buttonText: linkingResponse.data.buttonText,\r\n            isAutoTriggered: true\r\n          });\r\n          setShowAutoTriggerModal(true);\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Error processing auto-trigger:', error);\r\n      }\r\n    };\r\n\r\n    checkAutoTrigger();\r\n  }, [formId, recordData, enabled]);\r\n\r\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\r\n    try {\r\n      // Submit the form\r\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\r\n      \r\n      if (response.data.success) {\r\n        setShowAutoTriggerModal(false);\r\n        setAutoTriggerForm(null);\r\n        \r\n        // Notify parent component\r\n        if (onFormSubmit) {\r\n          onFormSubmit(formId, formData, response.data);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error submitting auto-triggered form:', error);\r\n    }\r\n  };\r\n\r\n  const handleCloseAutoTriggerModal = () => {\r\n    setShowAutoTriggerModal(false);\r\n    setAutoTriggerForm(null);\r\n    setCountdown(0);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Countdown Indicator */}\r\n      {countdown > 0 && (\r\n        <div className=\"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n            <span className=\"text-sm font-medium\">\r\n              Auto-opening form in {countdown}s\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Auto-Trigger Form Modal */}\r\n      {showAutoTriggerModal && autoTriggerForm && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n            {/* Modal Header */}\r\n            <div className=\"bg-blue-500 text-white px-6 py-4 rounded-t-lg\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <h2 className=\"text-lg font-semibold\">\r\n                    🚀 Auto-Opened Form\r\n                  </h2>\r\n                  <p className=\"text-sm text-blue-100 mt-1\">\r\n                    {autoTriggerForm.name}\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={handleCloseAutoTriggerModal}\r\n                  className=\"text-white hover:text-gray-200 focus:outline-none\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Modal Content */}\r\n            <div className=\"p-6\">\r\n              <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <h3 className=\"text-sm font-medium text-blue-800\">Auto-Triggered Form</h3>\r\n                    <div className=\"mt-1 text-sm text-blue-700\">\r\n                      This form was automatically opened based on the data you're viewing. \r\n                      Some fields may be pre-filled based on the original record.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <DynamicForm\r\n                form={autoTriggerForm}\r\n                onSubmit={handleAutoTriggerFormSubmit}\r\n                onCancel={handleCloseAutoTriggerModal}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AutoTriggerHandler;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,GAAG,MAAM,cAAc;;AAE9B;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKA,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,UAAU;EACVC,YAAY;EACZC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,OAAO,IAAI,CAACH,MAAM,IAAI,CAACC,UAAU,EAAE;;IAExC;IACA,MAAMY,aAAa,GAAGA,CAAA,KAAM;MAC1B,MAAMC,YAAY,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;MAC7D,MAAMC,aAAa,GAAGH,YAAY,GAAGI,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,QAAQ,CAACN,YAAY,CAAC,GAAGO,QAAQ;MAEnF,IAAIJ,aAAa,GAAG,IAAI,EAAE;QACxBK,OAAO,CAACC,GAAG,CAAC,8EAA8EN,aAAa,SAAS,CAAC;QACjH,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,IAAIJ,aAAa,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,MAAMW,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnClB,aAAa,CAAC,IAAI,CAAC;MACnB,IAAI;QACFgB,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEvB,MAAM,CAAC;QAEzD,MAAMyB,QAAQ,GAAG,MAAM/B,GAAG,CAACgC,IAAI,CAAC,mBAAmB1B,MAAM,qBAAqB,EAAE;UAC9EC;QACF,CAAC,CAAC;QAEF,IAAIwB,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACE,aAAa,EAAE;UACxD,MAAMC,WAAW,GAAGL,QAAQ,CAACE,IAAI;UACjC,MAAMI,KAAK,GAAGD,WAAW,CAACE,YAAY,IAAI,CAAC;UAE3CV,OAAO,CAACC,GAAG,CAAC,8BAA8BQ,KAAK,aAAa,CAAC;UAE7D,IAAIA,KAAK,GAAG,CAAC,EAAE;YACb;YACAnB,YAAY,CAACmB,KAAK,CAAC;YACnB,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1CtB,YAAY,CAACuB,IAAI,IAAI;gBACnB,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChCI,WAAW,CAACP,WAAW,CAAC;kBACxB,OAAO,CAAC;gBACV;gBACA,OAAOK,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACL;YACAE,WAAW,CAACP,WAAW,CAAC;UAC1B;QACF;MACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD,CAAC,SAAS;QACRhC,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC;IAED,MAAM+B,WAAW,GAAG,MAAOP,WAAW,IAAK;MACzC,IAAI;QACF;QACA,MAAMS,eAAe,GAAG,MAAM7C,GAAG,CAACgC,IAAI,CAAC,mBAAmB1B,MAAM,YAAY,EAAE;UAC5EC,UAAU;UACVuC,UAAU,EAAE,CAAC,CAAC;UACdC,WAAW,EAAEX,WAAW,CAACW;QAC3B,CAAC,CAAC;QAEF,IAAIF,eAAe,CAACZ,IAAI,CAACC,OAAO,EAAE;UAChCN,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgB,eAAe,CAACZ,IAAI,CAACe,UAAU,CAACC,IAAI,CAAC;;UAE7E;UACAnC,kBAAkB,CAAC;YACjB,GAAG+B,eAAe,CAACZ,IAAI,CAACe,UAAU;YAClCE,WAAW,EAAEL,eAAe,CAACZ,IAAI,CAACiB,WAAW;YAC7CC,UAAU,EAAEN,eAAe,CAACZ,IAAI,CAACkB,UAAU;YAC3CC,eAAe,EAAE;UACnB,CAAC,CAAC;UACFpC,uBAAuB,CAAC,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC,OAAO4B,KAAK,EAAE;QACdhB,OAAO,CAACgB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDd,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACxB,MAAM,EAAEC,UAAU,EAAEE,OAAO,CAAC,CAAC;EAEjC,MAAM4C,2BAA2B,GAAG,MAAAA,CAAO/C,MAAM,EAAEgD,QAAQ,KAAK;IAC9D,IAAI;MACF;MACA,MAAMvB,QAAQ,GAAG,MAAM/B,GAAG,CAACgC,IAAI,CAAC,mBAAmB1B,MAAM,SAAS,EAAEgD,QAAQ,CAAC;MAE7E,IAAIvB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBlB,uBAAuB,CAAC,KAAK,CAAC;QAC9BF,kBAAkB,CAAC,IAAI,CAAC;;QAExB;QACA,IAAIN,YAAY,EAAE;UAChBA,YAAY,CAACF,MAAM,EAAEgD,QAAQ,EAAEvB,QAAQ,CAACE,IAAI,CAAC;QAC/C;MACF;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdhB,OAAO,CAACgB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;EAED,MAAMW,2BAA2B,GAAGA,CAAA,KAAM;IACxCvC,uBAAuB,CAAC,KAAK,CAAC;IAC9BF,kBAAkB,CAAC,IAAI,CAAC;IACxBI,YAAY,CAAC,CAAC,CAAC;EACjB,CAAC;EAED,oBACEhB,OAAA,CAAAE,SAAA;IAAAoD,QAAA,GAEGvC,SAAS,GAAG,CAAC,iBACZf,OAAA;MAAKuD,SAAS,EAAC,mFAAmF;MAAAD,QAAA,eAChGtD,OAAA;QAAKuD,SAAS,EAAC,6BAA6B;QAAAD,QAAA,gBAC1CtD,OAAA;UAAKuD,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpG3D,OAAA;UAAMuD,SAAS,EAAC,qBAAqB;UAAAD,QAAA,GAAC,uBACf,EAACvC,SAAS,EAAC,GAClC;QAAA;UAAAyC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA9C,oBAAoB,IAAIF,eAAe,iBACtCX,OAAA;MAAKuD,SAAS,EAAC,4EAA4E;MAAAD,QAAA,eACzFtD,OAAA;QAAKuD,SAAS,EAAC,kFAAkF;QAAAD,QAAA,gBAE/FtD,OAAA;UAAKuD,SAAS,EAAC,+CAA+C;UAAAD,QAAA,eAC5DtD,OAAA;YAAKuD,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDtD,OAAA;cAAAsD,QAAA,gBACEtD,OAAA;gBAAIuD,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL3D,OAAA;gBAAGuD,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EACtC3C,eAAe,CAACoC;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN3D,OAAA;cACE4D,OAAO,EAAEP,2BAA4B;cACrCE,SAAS,EAAC,mDAAmD;cAAAD,QAAA,eAE7DtD,OAAA;gBAAKuD,SAAS,EAAC,SAAS;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5EtD,OAAA;kBAAMgE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN3D,OAAA;UAAKuD,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBtD,OAAA;YAAKuD,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACpEtD,OAAA;cAAKuD,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC/BtD,OAAA;gBAAKuD,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC5BtD,OAAA;kBAAKuD,SAAS,EAAC,uBAAuB;kBAACQ,OAAO,EAAC,WAAW;kBAACF,IAAI,EAAC,cAAc;kBAAAP,QAAA,eAC5EtD,OAAA;oBAAMoE,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,kIAAkI;oBAACE,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN3D,OAAA;gBAAKuD,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBtD,OAAA;kBAAIuD,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1E3D,OAAA;kBAAKuD,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,EAAC;gBAG5C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN3D,OAAA,CAACH,WAAW;YACVyE,IAAI,EAAE3D,eAAgB;YACtB4D,QAAQ,EAAEpB,2BAA4B;YACtCqB,QAAQ,EAAEnB;UAA4B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAACnD,EAAA,CApMIL,kBAAkB;AAAAsE,EAAA,GAAlBtE,kBAAkB;AAsMxB,eAAeA,kBAAkB;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}