{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\RecordDisplayWithActions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport api from '../utils/api';\nimport leaveBalanceCache from '../utils/leaveBalanceCache';\n\n/**\r\n * Component for displaying records with Apply buttons for form linking\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RecordDisplayWithActions = ({\n  data,\n  formId,\n  formLinkingConfig,\n  onFormLinkTriggered,\n  hideRecordDisplay = false,\n  showOnlyButtons = false,\n  autoTriggerOptions = {} // New flexible auto-trigger options\n}) => {\n  _s();\n  const [loadingActions, setLoadingActions] = useState({});\n  const [autoTriggerState, setAutoTriggerState] = useState({});\n  const [executedTriggers, setExecutedTriggers] = useState(new Set());\n  const componentId = useRef(Math.random().toString(36).substring(2, 11));\n  const timeoutRefs = useRef({});\n\n  // Helper function to check if data is leave balance related\n  const isLeaveBalanceData = useCallback(data => {\n    if (!data) return false;\n\n    // Check if data contains leave balance information\n    if (Array.isArray(data)) {\n      return data.some(item => item.hasOwnProperty('leaveTypeName') || item.hasOwnProperty('leaveType') || item.hasOwnProperty('balance'));\n    }\n    if (typeof data === 'object') {\n      return data.hasOwnProperty('leaveTypeName') || data.hasOwnProperty('leaveType') || data.hasOwnProperty('balance') || data.data && isLeaveBalanceData(data.data);\n    }\n    return false;\n  }, []);\n\n  // Auto-trigger configuration with defaults\n  const autoTriggerConfig = useMemo(() => {\n    var _autoTriggerOptions$s, _autoTriggerOptions$e, _autoTriggerOptions$d, _autoTriggerOptions$s2, _autoTriggerOptions$t, _autoTriggerOptions$r, _autoTriggerOptions$t2, _autoTriggerOptions$s3;\n    const isLeaveData = isLeaveBalanceData(data);\n    const shouldSkipAutoTrigger = isLeaveData && ((_autoTriggerOptions$s = autoTriggerOptions.skipLeaveBalanceAutoTrigger) !== null && _autoTriggerOptions$s !== void 0 ? _autoTriggerOptions$s : true);\n    return {\n      enabled: shouldSkipAutoTrigger ? false : (_autoTriggerOptions$e = autoTriggerOptions.enabled) !== null && _autoTriggerOptions$e !== void 0 ? _autoTriggerOptions$e : false,\n      delay: (_autoTriggerOptions$d = autoTriggerOptions.delay) !== null && _autoTriggerOptions$d !== void 0 ? _autoTriggerOptions$d : 2,\n      showCountdown: (_autoTriggerOptions$s2 = autoTriggerOptions.showCountdown) !== null && _autoTriggerOptions$s2 !== void 0 ? _autoTriggerOptions$s2 : true,\n      triggerOnce: (_autoTriggerOptions$t = autoTriggerOptions.triggerOnce) !== null && _autoTriggerOptions$t !== void 0 ? _autoTriggerOptions$t : true,\n      resetOnDataChange: (_autoTriggerOptions$r = autoTriggerOptions.resetOnDataChange) !== null && _autoTriggerOptions$r !== void 0 ? _autoTriggerOptions$r : true,\n      triggerCondition: (_autoTriggerOptions$t2 = autoTriggerOptions.triggerCondition) !== null && _autoTriggerOptions$t2 !== void 0 ? _autoTriggerOptions$t2 : 'first',\n      // 'first', 'all', 'custom'\n      customTriggerLogic: autoTriggerOptions.customTriggerLogic,\n      // Custom function\n      onBeforeTrigger: autoTriggerOptions.onBeforeTrigger,\n      // Callback before trigger\n      onAfterTrigger: autoTriggerOptions.onAfterTrigger,\n      // Callback after trigger\n      skipLeaveBalanceAutoTrigger: (_autoTriggerOptions$s3 = autoTriggerOptions.skipLeaveBalanceAutoTrigger) !== null && _autoTriggerOptions$s3 !== void 0 ? _autoTriggerOptions$s3 : true,\n      ...autoTriggerOptions\n    };\n  }, [autoTriggerOptions, data, isLeaveBalanceData]);\n\n  // Generate stable data hash for change detection\n  const dataHash = useMemo(() => JSON.stringify(data), [data]);\n\n  // Process records from various data structures\n  const processedRecords = useMemo(() => {\n    if (!data) return [];\n    if (Array.isArray(data)) return data;\n    if (typeof data === 'object') {\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n      for (const field of arrayFields) {\n        if (data[field] && Array.isArray(data[field])) {\n          return data[field];\n        }\n      }\n      return [data]; // Single object as record\n    }\n    return [];\n  }, [data]);\n\n  // Check if record meets action conditions\n  const shouldShowApplyButton = useCallback((record, conditions = []) => {\n    if (!(conditions !== null && conditions !== void 0 && conditions.length)) return true;\n    const validConditions = conditions.filter(condition => {\n      var _condition$field;\n      return (_condition$field = condition.field) === null || _condition$field === void 0 ? void 0 : _condition$field.trim();\n    });\n    if (!validConditions.length) return true;\n    return validConditions.every(condition => {\n      const fieldValue = record[condition.field];\n      const conditionValue = condition.value;\n      switch (condition.operator) {\n        case 'equals':\n          return fieldValue === conditionValue;\n        case 'not_equals':\n          return fieldValue !== conditionValue;\n        case 'contains':\n          return fieldValue === null || fieldValue === void 0 ? void 0 : fieldValue.toString().includes(conditionValue);\n        case 'not_contains':\n          return !(fieldValue !== null && fieldValue !== void 0 && fieldValue.toString().includes(conditionValue));\n        case 'exists':\n          return fieldValue != null && fieldValue !== '';\n        case 'not_exists':\n          return fieldValue == null || fieldValue === '';\n        default:\n          return true;\n      }\n    });\n  }, []);\n\n  // Get eligible records for auto-trigger\n  const getEligibleRecords = useCallback(() => {\n    if (!(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.recordActions)) return [];\n    const eligible = [];\n    formLinkingConfig.recordActions.forEach((action, actionIndex) => {\n      var _action$autoTrigger;\n      const shouldAutoTrigger = ((_action$autoTrigger = action.autoTrigger) === null || _action$autoTrigger === void 0 ? void 0 : _action$autoTrigger.enabled) || autoTriggerConfig.enabled;\n      if (shouldAutoTrigger) {\n        processedRecords.forEach((record, recordIndex) => {\n          if (shouldShowApplyButton(record, action.conditions)) {\n            eligible.push({\n              record,\n              recordIndex,\n              action,\n              actionIndex,\n              key: `${recordIndex}-${actionIndex}`\n            });\n          }\n        });\n      }\n    });\n    return eligible;\n  }, [processedRecords, formLinkingConfig, autoTriggerConfig.enabled, shouldShowApplyButton]);\n\n  // Determine which records to trigger based on condition\n  const getRecordsToTrigger = useCallback(eligibleRecords => {\n    if (!eligibleRecords.length) return [];\n    switch (autoTriggerConfig.triggerCondition) {\n      case 'first':\n        return [eligibleRecords[0]];\n      case 'all':\n        return eligibleRecords;\n      case 'custom':\n        return autoTriggerConfig.customTriggerLogic ? autoTriggerConfig.customTriggerLogic(eligibleRecords) : [eligibleRecords[0]];\n      default:\n        return [eligibleRecords[0]];\n    }\n  }, [autoTriggerConfig]);\n\n  // Clear all timeouts\n  const clearAllTimeouts = useCallback(() => {\n    Object.values(timeoutRefs.current).forEach(clearTimeout);\n    timeoutRefs.current = {};\n  }, []);\n\n  // Reset auto-trigger state when data changes\n  useEffect(() => {\n    if (!autoTriggerConfig.resetOnDataChange) return;\n    console.log(`🔄 [${componentId.current}] Data changed, resetting auto-trigger state`);\n    clearAllTimeouts();\n    setExecutedTriggers(new Set());\n    setAutoTriggerState({});\n  }, [dataHash, autoTriggerConfig.resetOnDataChange, clearAllTimeouts]);\n\n  // Execute auto-trigger logic\n  const executeAutoTrigger = useCallback(() => {\n    const eligibleRecords = getEligibleRecords();\n    const recordsToTrigger = getRecordsToTrigger(eligibleRecords);\n    recordsToTrigger.forEach(({\n      record,\n      actionIndex,\n      key\n    }) => {\n      var _autoTriggerState$key, _action$autoTrigger$d, _action$autoTrigger2;\n      const triggerKey = `${dataHash}-${key}`;\n\n      // Skip if already triggered and triggerOnce is enabled\n      if (autoTriggerConfig.triggerOnce && executedTriggers.has(triggerKey)) {\n        return;\n      }\n\n      // Skip if currently processing\n      if ((_autoTriggerState$key = autoTriggerState[key]) !== null && _autoTriggerState$key !== void 0 && _autoTriggerState$key.processing) {\n        return;\n      }\n      const action = formLinkingConfig.recordActions[actionIndex];\n      const delay = (_action$autoTrigger$d = (_action$autoTrigger2 = action.autoTrigger) === null || _action$autoTrigger2 === void 0 ? void 0 : _action$autoTrigger2.delaySeconds) !== null && _action$autoTrigger$d !== void 0 ? _action$autoTrigger$d : autoTriggerConfig.delay;\n      console.log(`⏰ [${componentId.current}] Auto-triggering in ${delay}s for key: ${key}`);\n\n      // Call before trigger callback\n      if (autoTriggerConfig.onBeforeTrigger) {\n        autoTriggerConfig.onBeforeTrigger({\n          record,\n          action,\n          actionIndex\n        });\n      }\n\n      // Mark as executed\n      setExecutedTriggers(prev => new Set(prev).add(triggerKey));\n      if (hideRecordDisplay || delay === 0) {\n        // Immediate trigger\n        handleApplyClick(record, actionIndex);\n        if (autoTriggerConfig.onAfterTrigger) {\n          autoTriggerConfig.onAfterTrigger({\n            record,\n            action,\n            actionIndex\n          });\n        }\n      } else {\n        // Delayed trigger with countdown\n        setAutoTriggerState(prev => ({\n          ...prev,\n          [key]: {\n            processing: true,\n            countdown: delay\n          }\n        }));\n\n        // Countdown logic\n        if (autoTriggerConfig.showCountdown) {\n          const countdownInterval = setInterval(() => {\n            setAutoTriggerState(prev => {\n              const current = prev[key];\n              if (!current || current.countdown <= 1) {\n                clearInterval(countdownInterval);\n                return {\n                  ...prev,\n                  [key]: {\n                    ...current,\n                    countdown: 0\n                  }\n                };\n              }\n              return {\n                ...prev,\n                [key]: {\n                  ...current,\n                  countdown: current.countdown - 1\n                }\n              };\n            });\n          }, 1000);\n        }\n\n        // Execute trigger after delay\n        const timeoutId = setTimeout(() => {\n          handleApplyClick(record, actionIndex);\n          setAutoTriggerState(prev => ({\n            ...prev,\n            [key]: undefined\n          }));\n          if (autoTriggerConfig.onAfterTrigger) {\n            autoTriggerConfig.onAfterTrigger({\n              record,\n              action,\n              actionIndex\n            });\n          }\n        }, delay * 1000);\n        timeoutRefs.current[key] = timeoutId;\n      }\n    });\n  }, [getEligibleRecords, getRecordsToTrigger, autoTriggerConfig, executedTriggers, autoTriggerState, dataHash, hideRecordDisplay, formLinkingConfig]);\n\n  // Auto-trigger effect\n  useEffect(() => {\n    var _formLinkingConfig$re;\n    // Skip auto-trigger if disabled or if it's leave balance data and skip flag is set\n    const isLeaveData = isLeaveBalanceData(data);\n    const shouldSkipForLeaveData = isLeaveData && autoTriggerConfig.skipLeaveBalanceAutoTrigger;\n    if (shouldSkipForLeaveData) {\n      console.log('🚫 Skipping auto-trigger for leave balance data');\n      return;\n    }\n    if (!autoTriggerConfig.enabled && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && (_formLinkingConfig$re = formLinkingConfig.recordActions) !== null && _formLinkingConfig$re !== void 0 && _formLinkingConfig$re.some(action => {\n      var _action$autoTrigger3;\n      return (_action$autoTrigger3 = action.autoTrigger) === null || _action$autoTrigger3 === void 0 ? void 0 : _action$autoTrigger3.enabled;\n    }))) {\n      return;\n    }\n    const timeoutId = setTimeout(executeAutoTrigger, 100);\n    return () => clearTimeout(timeoutId);\n  }, [executeAutoTrigger, autoTriggerConfig.enabled, autoTriggerConfig.skipLeaveBalanceAutoTrigger, data, isLeaveBalanceData]);\n\n  // Handle Apply button click\n  const handleApplyClick = useCallback(async (record, actionIndex = 0) => {\n    const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\n    if (loadingActions[actionKey]) return;\n    setLoadingActions(prev => ({\n      ...prev,\n      [actionKey]: true\n    }));\n    try {\n      var _formLinkingConfig$re2;\n      const action = formLinkingConfig === null || formLinkingConfig === void 0 ? void 0 : (_formLinkingConfig$re2 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re2 === void 0 ? void 0 : _formLinkingConfig$re2[actionIndex];\n      const buttonText = (action === null || action === void 0 ? void 0 : action.buttonText) || 'Apply';\n      const response = await api.post(`/unifiedconfigs/${formId}/form-link`, {\n        recordData: record,\n        parentData: data,\n        actionIndex,\n        buttonText\n      });\n      if (response.data.success) {\n        const {\n          autoSubmitOnClick,\n          targetForm,\n          prefillData\n        } = response.data;\n        if (autoSubmitOnClick) {\n          await handleAutoSubmitLinkingForm(targetForm._id, prefillData, targetForm.name, actionKey);\n        } else if (onFormLinkTriggered) {\n          onFormLinkTriggered({\n            targetForm,\n            prefillData,\n            buttonText: response.data.buttonText,\n            buttonStyle: response.data.buttonStyle\n          });\n        }\n      } else {\n        throw new Error(response.data.message || 'Form linking failed');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Error during form linking:', error);\n      alert('Error opening form: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message));\n    } finally {\n      setLoadingActions(prev => ({\n        ...prev,\n        [actionKey]: false\n      }));\n    }\n  }, [formLinkingConfig, formId, data, loadingActions, onFormLinkTriggered]);\n\n  // Handle auto-submit linking form\n  const handleAutoSubmitLinkingForm = useCallback(async (targetFormId, prefillData, formName, actionKey) => {\n    try {\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n      const headers = {\n        'Authorization': userData.token ? `Bearer ${userData.token}` : undefined,\n        'x-emp-id': userData.empId || undefined,\n        'x-role-type': userData.roleType || undefined\n      };\n      const response = await api.post(`/unifiedconfigs/${targetFormId}/submit`, {\n        formData: prefillData\n      }, {\n        headers\n      });\n      if (response.data.success) {\n        alert(`✅ ${formName} submitted successfully!`);\n      } else {\n        throw new Error(response.data.message || 'Auto-submit failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('❌ Error auto-submitting linking form:', error);\n      alert(`❌ Error submitting ${formName}: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message}`);\n    } finally {\n      setLoadingActions(prev => ({\n        ...prev,\n        [actionKey]: false\n      }));\n    }\n  }, []);\n\n  // Get button style classes\n  const getButtonClasses = (style, isLoading) => {\n    const baseClasses = 'px-3 py-1 rounded text-sm font-medium transition-colors duration-200';\n    if (isLoading) {\n      return `${baseClasses} bg-gray-400 text-white cursor-not-allowed`;\n    }\n    const styles = {\n      primary: 'bg-blue-500 hover:bg-blue-600 text-white',\n      secondary: 'bg-gray-500 hover:bg-gray-600 text-white',\n      success: 'bg-green-500 hover:bg-green-600 text-white',\n      warning: 'bg-yellow-500 hover:bg-yellow-600 text-white',\n      danger: 'bg-red-500 hover:bg-red-600 text-white'\n    };\n    return `${baseClasses} ${styles[style] || styles.primary}`;\n  };\n\n  // Format field value for display\n  const formatFieldValue = value => {\n    if (value == null) return '-';\n    if (typeof value === 'boolean') return value ? 'Yes' : 'No';\n    if (typeof value === 'object') return JSON.stringify(value);\n    return value.toString();\n  };\n\n  // Render action buttons for a record\n  const renderActionButtons = (record, recordIndex) => {\n    if (!(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled) || !formLinkingConfig.recordActions) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-2\",\n      children: formLinkingConfig.recordActions.map((action, actionIndex) => {\n        var _action$autoTrigger4;\n        if (!shouldShowApplyButton(record, action.conditions)) return null;\n        const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\n        const stateKey = `${recordIndex}-${actionIndex}`;\n        const isLoading = loadingActions[actionKey];\n        const triggerState = autoTriggerState[stateKey];\n        const shouldAutoTrigger = ((_action$autoTrigger4 = action.autoTrigger) === null || _action$autoTrigger4 === void 0 ? void 0 : _action$autoTrigger4.enabled) || autoTriggerConfig.enabled;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [!shouldAutoTrigger && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleApplyClick(record, actionIndex),\n            disabled: isLoading,\n            className: getButtonClasses(action.buttonStyle, isLoading),\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"animate-spin -ml-1 mr-1 h-3 w-3 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  className: \"opacity-25\",\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  className: \"opacity-75\",\n                  fill: \"currentColor\",\n                  d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 23\n              }, this), \"Loading...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 21\n            }, this) : action.buttonText || 'Apply'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 17\n          }, this), (triggerState === null || triggerState === void 0 ? void 0 : triggerState.countdown) > 0 && autoTriggerConfig.showCountdown && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 19\n            }, this), \"Auto-triggering in \", triggerState.countdown, \"s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 17\n          }, this)]\n        }, actionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 394,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render individual record\n  const renderRecord = (record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-4\",\n    children: [!showOnlyButtons && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-800\",\n          children: [\"Record \", index + 1, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 448,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3 text-sm text-gray-700 bg-gray-50 p-3 rounded-lg\",\n        children: (() => {\n          const isLeaveData = record.hasOwnProperty('leaveTypeName') && record.hasOwnProperty('balance');\n          const fieldsToDisplay = isLeaveData ? [['leaveTypeName', record.leaveTypeName], ['balance', record.balance]] : Object.entries(record);\n          return fieldsToDisplay.map(([key, value], fieldIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [fieldIndex > 0 && ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [key, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this), \" \", formatFieldValue(value), fieldIndex < fieldsToDisplay.length - 1 && ' ']\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 17\n          }, this));\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), renderActionButtons(record, index)]\n  }, index, true, {\n    fileName: _jsxFileName,\n    lineNumber: 445,\n    columnNumber: 5\n  }, this);\n\n  // Early return for hidden display\n  if (hideRecordDisplay) return null;\n\n  // Render records\n  if (!processedRecords.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-500 text-center py-4\",\n      children: \"No records to display\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4\",\n    children: processedRecords.map(renderRecord)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 488,\n    columnNumber: 5\n  }, this);\n};\n_s(RecordDisplayWithActions, \"aRvCXEpcDYIHLTTdXO22CnRLcfA=\");\n_c = RecordDisplayWithActions;\nexport default RecordDisplayWithActions;\nvar _c;\n$RefreshReg$(_c, \"RecordDisplayWithActions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "api", "leaveBalanceCache", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RecordDisplayWithActions", "data", "formId", "formLinkingConfig", "onFormLinkTriggered", "hideRecordDisplay", "showOnlyButtons", "autoTriggerOptions", "_s", "loadingActions", "setLoadingActions", "autoTriggerState", "setAutoTriggerState", "executedTriggers", "setExecutedTriggers", "Set", "componentId", "Math", "random", "toString", "substring", "timeoutRefs", "isLeaveBalanceData", "Array", "isArray", "some", "item", "hasOwnProperty", "autoTriggerConfig", "_autoTriggerOptions$s", "_autoTriggerOptions$e", "_autoTriggerOptions$d", "_autoTriggerOptions$s2", "_autoTriggerOptions$t", "_autoTriggerOptions$r", "_autoTriggerOptions$t2", "_autoTriggerOptions$s3", "isLeaveData", "shouldSkipAutoTrigger", "skipLeaveBalanceAutoTrigger", "enabled", "delay", "showCountdown", "triggerOnce", "resetOnDataChange", "triggerCondition", "customTriggerLogic", "onBeforeTrigger", "onAfterTrigger", "dataHash", "JSON", "stringify", "processedRecords", "arrayFields", "field", "shouldShowApplyButton", "record", "conditions", "length", "validConditions", "filter", "condition", "_condition$field", "trim", "every", "fieldValue", "conditionValue", "value", "operator", "includes", "getEligibleRecords", "recordActions", "eligible", "for<PERSON>ach", "action", "actionIndex", "_action$autoTrigger", "should<PERSON>utoTrigger", "autoTrigger", "recordIndex", "push", "key", "getRecordsToTrigger", "eligibleRecords", "clearAllTimeouts", "Object", "values", "current", "clearTimeout", "console", "log", "executeAutoTrigger", "recordsToTrigger", "_autoTriggerState$key", "_action$autoTrigger$d", "_action$autoTrigger2", "<PERSON><PERSON><PERSON>", "has", "processing", "delaySeconds", "prev", "add", "handleApplyClick", "countdown", "countdownInterval", "setInterval", "clearInterval", "timeoutId", "setTimeout", "undefined", "_formLinkingConfig$re", "shouldSkipForLeaveData", "_action$autoTrigger3", "action<PERSON>ey", "_formLinkingConfig$re2", "buttonText", "response", "post", "recordData", "parentData", "success", "autoSubmitOnClick", "targetForm", "prefillData", "handleAutoSubmitLinkingForm", "_id", "name", "buttonStyle", "Error", "message", "error", "_error$response", "_error$response$data", "alert", "targetFormId", "formName", "userData", "parse", "localStorage", "getItem", "headers", "token", "empId", "roleType", "formData", "_error$response2", "_error$response2$data", "getButtonClasses", "style", "isLoading", "baseClasses", "styles", "primary", "secondary", "warning", "danger", "formatFieldValue", "renderActionButtons", "className", "children", "map", "_action$autoTrigger4", "stateKey", "triggerState", "onClick", "disabled", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "strokeLinecap", "strokeLinejoin", "renderRecord", "index", "fieldsToDisplay", "leaveTypeName", "balance", "entries", "fieldIndex", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/RecordDisplayWithActions.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\r\nimport api from '../utils/api';\r\nimport leaveBalanceCache from '../utils/leaveBalanceCache';\r\n\r\n/**\r\n * Component for displaying records with Apply buttons for form linking\r\n */\r\nconst RecordDisplayWithActions = ({ \r\n  data, \r\n  formId, \r\n  formLinkingConfig, \r\n  onFormLinkTriggered,\r\n  hideRecordDisplay = false,\r\n  showOnlyButtons = false,\r\n  autoTriggerOptions = {} // New flexible auto-trigger options\r\n}) => {\r\n  const [loadingActions, setLoadingActions] = useState({});\r\n  const [autoTriggerState, setAutoTriggerState] = useState({});\r\n  const [executedTriggers, setExecutedTriggers] = useState(new Set());\r\n  const componentId = useRef(Math.random().toString(36).substring(2, 11));\r\n  const timeoutRefs = useRef({});\r\n\r\n  // Helper function to check if data is leave balance related\r\n  const isLeaveBalanceData = useCallback((data) => {\r\n    if (!data) return false;\r\n\r\n    // Check if data contains leave balance information\r\n    if (Array.isArray(data)) {\r\n      return data.some(item =>\r\n        item.hasOwnProperty('leaveTypeName') ||\r\n        item.hasOwnProperty('leaveType') ||\r\n        item.hasOwnProperty('balance')\r\n      );\r\n    }\r\n\r\n    if (typeof data === 'object') {\r\n      return data.hasOwnProperty('leaveTypeName') ||\r\n             data.hasOwnProperty('leaveType') ||\r\n             data.hasOwnProperty('balance') ||\r\n             (data.data && isLeaveBalanceData(data.data));\r\n    }\r\n\r\n    return false;\r\n  }, []);\r\n\r\n  // Auto-trigger configuration with defaults\r\n  const autoTriggerConfig = useMemo(() => {\r\n    const isLeaveData = isLeaveBalanceData(data);\r\n    const shouldSkipAutoTrigger = isLeaveData && (autoTriggerOptions.skipLeaveBalanceAutoTrigger ?? true);\r\n\r\n    return {\r\n      enabled: shouldSkipAutoTrigger ? false : (autoTriggerOptions.enabled ?? false),\r\n      delay: autoTriggerOptions.delay ?? 2,\r\n      showCountdown: autoTriggerOptions.showCountdown ?? true,\r\n      triggerOnce: autoTriggerOptions.triggerOnce ?? true,\r\n      resetOnDataChange: autoTriggerOptions.resetOnDataChange ?? true,\r\n      triggerCondition: autoTriggerOptions.triggerCondition ?? 'first', // 'first', 'all', 'custom'\r\n      customTriggerLogic: autoTriggerOptions.customTriggerLogic, // Custom function\r\n      onBeforeTrigger: autoTriggerOptions.onBeforeTrigger, // Callback before trigger\r\n      onAfterTrigger: autoTriggerOptions.onAfterTrigger, // Callback after trigger\r\n      skipLeaveBalanceAutoTrigger: autoTriggerOptions.skipLeaveBalanceAutoTrigger ?? true,\r\n      ...autoTriggerOptions\r\n    };\r\n  }, [autoTriggerOptions, data, isLeaveBalanceData]);\r\n\r\n  // Generate stable data hash for change detection\r\n  const dataHash = useMemo(() => JSON.stringify(data), [data]);\r\n\r\n  // Process records from various data structures\r\n  const processedRecords = useMemo(() => {\r\n    if (!data) return [];\r\n    \r\n    if (Array.isArray(data)) return data;\r\n    \r\n    if (typeof data === 'object') {\r\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n      \r\n      for (const field of arrayFields) {\r\n        if (data[field] && Array.isArray(data[field])) {\r\n          return data[field];\r\n        }\r\n      }\r\n      \r\n      return [data]; // Single object as record\r\n    }\r\n    \r\n    return [];\r\n  }, [data]);\r\n\r\n  // Check if record meets action conditions\r\n  const shouldShowApplyButton = useCallback((record, conditions = []) => {\r\n    if (!conditions?.length) return true;\r\n    \r\n    const validConditions = conditions.filter(condition => \r\n      condition.field?.trim()\r\n    );\r\n    \r\n    if (!validConditions.length) return true;\r\n    \r\n    return validConditions.every(condition => {\r\n      const fieldValue = record[condition.field];\r\n      const conditionValue = condition.value;\r\n      \r\n      switch (condition.operator) {\r\n        case 'equals':\r\n          return fieldValue === conditionValue;\r\n        case 'not_equals':\r\n          return fieldValue !== conditionValue;\r\n        case 'contains':\r\n          return fieldValue?.toString().includes(conditionValue);\r\n        case 'not_contains':\r\n          return !fieldValue?.toString().includes(conditionValue);\r\n        case 'exists':\r\n          return fieldValue != null && fieldValue !== '';\r\n        case 'not_exists':\r\n          return fieldValue == null || fieldValue === '';\r\n        default:\r\n          return true;\r\n      }\r\n    });\r\n  }, []);\r\n\r\n  // Get eligible records for auto-trigger\r\n  const getEligibleRecords = useCallback(() => {\r\n    if (!formLinkingConfig?.recordActions) return [];\r\n    \r\n    const eligible = [];\r\n    \r\n    formLinkingConfig.recordActions.forEach((action, actionIndex) => {\r\n      const shouldAutoTrigger = action.autoTrigger?.enabled || autoTriggerConfig.enabled;\r\n      \r\n      if (shouldAutoTrigger) {\r\n        processedRecords.forEach((record, recordIndex) => {\r\n          if (shouldShowApplyButton(record, action.conditions)) {\r\n            eligible.push({\r\n              record,\r\n              recordIndex,\r\n              action,\r\n              actionIndex,\r\n              key: `${recordIndex}-${actionIndex}`\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n    \r\n    return eligible;\r\n  }, [processedRecords, formLinkingConfig, autoTriggerConfig.enabled, shouldShowApplyButton]);\r\n\r\n  // Determine which records to trigger based on condition\r\n  const getRecordsToTrigger = useCallback((eligibleRecords) => {\r\n    if (!eligibleRecords.length) return [];\r\n    \r\n    switch (autoTriggerConfig.triggerCondition) {\r\n      case 'first':\r\n        return [eligibleRecords[0]];\r\n      case 'all':\r\n        return eligibleRecords;\r\n      case 'custom':\r\n        return autoTriggerConfig.customTriggerLogic \r\n          ? autoTriggerConfig.customTriggerLogic(eligibleRecords)\r\n          : [eligibleRecords[0]];\r\n      default:\r\n        return [eligibleRecords[0]];\r\n    }\r\n  }, [autoTriggerConfig]);\r\n\r\n  // Clear all timeouts\r\n  const clearAllTimeouts = useCallback(() => {\r\n    Object.values(timeoutRefs.current).forEach(clearTimeout);\r\n    timeoutRefs.current = {};\r\n  }, []);\r\n\r\n  // Reset auto-trigger state when data changes\r\n  useEffect(() => {\r\n    if (!autoTriggerConfig.resetOnDataChange) return;\r\n    \r\n    console.log(`🔄 [${componentId.current}] Data changed, resetting auto-trigger state`);\r\n    \r\n    clearAllTimeouts();\r\n    setExecutedTriggers(new Set());\r\n    setAutoTriggerState({});\r\n  }, [dataHash, autoTriggerConfig.resetOnDataChange, clearAllTimeouts]);\r\n\r\n  // Execute auto-trigger logic\r\n  const executeAutoTrigger = useCallback(() => {\r\n    const eligibleRecords = getEligibleRecords();\r\n    const recordsToTrigger = getRecordsToTrigger(eligibleRecords);\r\n    \r\n    recordsToTrigger.forEach(({ record, actionIndex, key }) => {\r\n      const triggerKey = `${dataHash}-${key}`;\r\n      \r\n      // Skip if already triggered and triggerOnce is enabled\r\n      if (autoTriggerConfig.triggerOnce && executedTriggers.has(triggerKey)) {\r\n        return;\r\n      }\r\n      \r\n      // Skip if currently processing\r\n      if (autoTriggerState[key]?.processing) {\r\n        return;\r\n      }\r\n      \r\n      const action = formLinkingConfig.recordActions[actionIndex];\r\n      const delay = action.autoTrigger?.delaySeconds ?? autoTriggerConfig.delay;\r\n      \r\n      console.log(`⏰ [${componentId.current}] Auto-triggering in ${delay}s for key: ${key}`);\r\n      \r\n      // Call before trigger callback\r\n      if (autoTriggerConfig.onBeforeTrigger) {\r\n        autoTriggerConfig.onBeforeTrigger({ record, action, actionIndex });\r\n      }\r\n      \r\n      // Mark as executed\r\n      setExecutedTriggers(prev => new Set(prev).add(triggerKey));\r\n      \r\n      if (hideRecordDisplay || delay === 0) {\r\n        // Immediate trigger\r\n        handleApplyClick(record, actionIndex);\r\n        \r\n        if (autoTriggerConfig.onAfterTrigger) {\r\n          autoTriggerConfig.onAfterTrigger({ record, action, actionIndex });\r\n        }\r\n      } else {\r\n        // Delayed trigger with countdown\r\n        setAutoTriggerState(prev => ({\r\n          ...prev,\r\n          [key]: { processing: true, countdown: delay }\r\n        }));\r\n        \r\n        // Countdown logic\r\n        if (autoTriggerConfig.showCountdown) {\r\n          const countdownInterval = setInterval(() => {\r\n            setAutoTriggerState(prev => {\r\n              const current = prev[key];\r\n              if (!current || current.countdown <= 1) {\r\n                clearInterval(countdownInterval);\r\n                return { ...prev, [key]: { ...current, countdown: 0 } };\r\n              }\r\n              return { ...prev, [key]: { ...current, countdown: current.countdown - 1 } };\r\n            });\r\n          }, 1000);\r\n        }\r\n        \r\n        // Execute trigger after delay\r\n        const timeoutId = setTimeout(() => {\r\n          handleApplyClick(record, actionIndex);\r\n          setAutoTriggerState(prev => ({ ...prev, [key]: undefined }));\r\n          \r\n          if (autoTriggerConfig.onAfterTrigger) {\r\n            autoTriggerConfig.onAfterTrigger({ record, action, actionIndex });\r\n          }\r\n        }, delay * 1000);\r\n        \r\n        timeoutRefs.current[key] = timeoutId;\r\n      }\r\n    });\r\n  }, [\r\n    getEligibleRecords, \r\n    getRecordsToTrigger, \r\n    autoTriggerConfig, \r\n    executedTriggers, \r\n    autoTriggerState, \r\n    dataHash, \r\n    hideRecordDisplay,\r\n    formLinkingConfig\r\n  ]);\r\n\r\n  // Auto-trigger effect\r\n  useEffect(() => {\r\n    // Skip auto-trigger if disabled or if it's leave balance data and skip flag is set\r\n    const isLeaveData = isLeaveBalanceData(data);\r\n    const shouldSkipForLeaveData = isLeaveData && autoTriggerConfig.skipLeaveBalanceAutoTrigger;\r\n\r\n    if (shouldSkipForLeaveData) {\r\n      console.log('🚫 Skipping auto-trigger for leave balance data');\r\n      return;\r\n    }\r\n\r\n    if (!autoTriggerConfig.enabled && !formLinkingConfig?.recordActions?.some(action => action.autoTrigger?.enabled)) {\r\n      return;\r\n    }\r\n\r\n    const timeoutId = setTimeout(executeAutoTrigger, 100);\r\n    return () => clearTimeout(timeoutId);\r\n  }, [executeAutoTrigger, autoTriggerConfig.enabled, autoTriggerConfig.skipLeaveBalanceAutoTrigger, data, isLeaveBalanceData]);\r\n\r\n  // Handle Apply button click\r\n  const handleApplyClick = useCallback(async (record, actionIndex = 0) => {\r\n    const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\r\n\r\n    if (loadingActions[actionKey]) return;\r\n\r\n    setLoadingActions(prev => ({ ...prev, [actionKey]: true }));\r\n\r\n    try {\r\n      const action = formLinkingConfig?.recordActions?.[actionIndex];\r\n      const buttonText = action?.buttonText || 'Apply';\r\n      \r\n      const response = await api.post(`/unifiedconfigs/${formId}/form-link`, {\r\n        recordData: record,\r\n        parentData: data,\r\n        actionIndex,\r\n        buttonText\r\n      });\r\n\r\n      if (response.data.success) {\r\n        const { autoSubmitOnClick, targetForm, prefillData } = response.data;\r\n        \r\n        if (autoSubmitOnClick) {\r\n          await handleAutoSubmitLinkingForm(\r\n            targetForm._id, \r\n            prefillData,\r\n            targetForm.name,\r\n            actionKey\r\n          );\r\n        } else if (onFormLinkTriggered) {\r\n          onFormLinkTriggered({\r\n            targetForm,\r\n            prefillData,\r\n            buttonText: response.data.buttonText,\r\n            buttonStyle: response.data.buttonStyle\r\n          });\r\n        }\r\n      } else {\r\n        throw new Error(response.data.message || 'Form linking failed');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error during form linking:', error);\r\n      alert('Error opening form: ' + (error.response?.data?.message || error.message));\r\n    } finally {\r\n      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));\r\n    }\r\n  }, [formLinkingConfig, formId, data, loadingActions, onFormLinkTriggered]);\r\n\r\n  // Handle auto-submit linking form\r\n  const handleAutoSubmitLinkingForm = useCallback(async (targetFormId, prefillData, formName, actionKey) => {\r\n    try {\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      const headers = {\r\n        'Authorization': userData.token ? `Bearer ${userData.token}` : undefined,\r\n        'x-emp-id': userData.empId || undefined,\r\n        'x-role-type': userData.roleType || undefined,\r\n      };\r\n      \r\n      const response = await api.post(`/unifiedconfigs/${targetFormId}/submit`, {\r\n        formData: prefillData\r\n      }, { headers });\r\n      \r\n      if (response.data.success) {\r\n        alert(`✅ ${formName} submitted successfully!`);\r\n      } else {\r\n        throw new Error(response.data.message || 'Auto-submit failed');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error auto-submitting linking form:', error);\r\n      alert(`❌ Error submitting ${formName}: ${error.response?.data?.message || error.message}`);\r\n    } finally {\r\n      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));\r\n    }\r\n  }, []);\r\n\r\n  // Get button style classes\r\n  const getButtonClasses = (style, isLoading) => {\r\n    const baseClasses = 'px-3 py-1 rounded text-sm font-medium transition-colors duration-200';\r\n    \r\n    if (isLoading) {\r\n      return `${baseClasses} bg-gray-400 text-white cursor-not-allowed`;\r\n    }\r\n\r\n    const styles = {\r\n      primary: 'bg-blue-500 hover:bg-blue-600 text-white',\r\n      secondary: 'bg-gray-500 hover:bg-gray-600 text-white',\r\n      success: 'bg-green-500 hover:bg-green-600 text-white',\r\n      warning: 'bg-yellow-500 hover:bg-yellow-600 text-white',\r\n      danger: 'bg-red-500 hover:bg-red-600 text-white'\r\n    };\r\n\r\n    return `${baseClasses} ${styles[style] || styles.primary}`;\r\n  };\r\n\r\n  // Format field value for display\r\n  const formatFieldValue = (value) => {\r\n    if (value == null) return '-';\r\n    if (typeof value === 'boolean') return value ? 'Yes' : 'No';\r\n    if (typeof value === 'object') return JSON.stringify(value);\r\n    return value.toString();\r\n  };\r\n\r\n  // Render action buttons for a record\r\n  const renderActionButtons = (record, recordIndex) => {\r\n    if (!formLinkingConfig?.enabled || !formLinkingConfig.recordActions) return null;\r\n\r\n    return (\r\n      <div className=\"flex flex-wrap gap-2\">\r\n        {formLinkingConfig.recordActions.map((action, actionIndex) => {\r\n          if (!shouldShowApplyButton(record, action.conditions)) return null;\r\n          \r\n          const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\r\n          const stateKey = `${recordIndex}-${actionIndex}`;\r\n          const isLoading = loadingActions[actionKey];\r\n          const triggerState = autoTriggerState[stateKey];\r\n          const shouldAutoTrigger = action.autoTrigger?.enabled || autoTriggerConfig.enabled;\r\n          \r\n          return (\r\n            <div key={actionIndex} className=\"flex items-center gap-2\">\r\n              {/* Show button if not auto-triggering or if auto-trigger is disabled */}\r\n              {!shouldAutoTrigger && (\r\n                <button\r\n                  onClick={() => handleApplyClick(record, actionIndex)}\r\n                  disabled={isLoading}\r\n                  className={getButtonClasses(action.buttonStyle, isLoading)}\r\n                >\r\n                  {isLoading ? (\r\n                    <span className=\"flex items-center\">\r\n                      <svg className=\"animate-spin -ml-1 mr-1 h-3 w-3 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                      </svg>\r\n                      Loading...\r\n                    </span>\r\n                  ) : (\r\n                    action.buttonText || 'Apply'\r\n                  )}\r\n                </button>\r\n              )}\r\n              \r\n              {/* Auto-trigger countdown */}\r\n              {triggerState?.countdown > 0 && autoTriggerConfig.showCountdown && (\r\n                <div className=\"flex items-center text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded\">\r\n                  <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                  Auto-triggering in {triggerState.countdown}s\r\n                </div>\r\n              )}\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Render individual record\r\n  const renderRecord = (record, index) => (\r\n    <div key={index} className=\"mb-4\">\r\n      {!showOnlyButtons && (\r\n        <>\r\n          <div className=\"mb-2\">\r\n            <span className=\"font-medium text-gray-800\">Record {index + 1}:</span>\r\n          </div>\r\n          \r\n          <div className=\"mb-3 text-sm text-gray-700 bg-gray-50 p-3 rounded-lg\">\r\n            {(() => {\r\n              const isLeaveData = record.hasOwnProperty('leaveTypeName') && record.hasOwnProperty('balance');\r\n              const fieldsToDisplay = isLeaveData \r\n                ? [['leaveTypeName', record.leaveTypeName], ['balance', record.balance]]\r\n                : Object.entries(record);\r\n              \r\n              return fieldsToDisplay.map(([key, value], fieldIndex) => (\r\n                <span key={key}>\r\n                  {fieldIndex > 0 && ' '}\r\n                  <span className=\"font-medium\">{key}:</span> {formatFieldValue(value)}\r\n                  {fieldIndex < fieldsToDisplay.length - 1 && ' '}\r\n                </span>\r\n              ));\r\n            })()}\r\n          </div>\r\n        </>\r\n      )}\r\n      \r\n      {renderActionButtons(record, index)}\r\n    </div>\r\n  );\r\n\r\n  // Early return for hidden display\r\n  if (hideRecordDisplay) return null;\r\n\r\n  // Render records\r\n  if (!processedRecords.length) {\r\n    return (\r\n      <div className=\"text-gray-500 text-center py-4\">\r\n        No records to display\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {processedRecords.map(renderRecord)}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RecordDisplayWithActions;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAChF,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,iBAAiB,MAAM,4BAA4B;;AAE1D;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,wBAAwB,GAAGA,CAAC;EAChCC,IAAI;EACJC,MAAM;EACNC,iBAAiB;EACjBC,mBAAmB;EACnBC,iBAAiB,GAAG,KAAK;EACzBC,eAAe,GAAG,KAAK;EACvBC,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACwB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,IAAI0B,GAAG,CAAC,CAAC,CAAC;EACnE,MAAMC,WAAW,GAAGzB,MAAM,CAAC0B,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;EACvE,MAAMC,WAAW,GAAG9B,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE9B;EACA,MAAM+B,kBAAkB,GAAG9B,WAAW,CAAES,IAAI,IAAK;IAC/C,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAIsB,KAAK,CAACC,OAAO,CAACvB,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACwB,IAAI,CAACC,IAAI,IACnBA,IAAI,CAACC,cAAc,CAAC,eAAe,CAAC,IACpCD,IAAI,CAACC,cAAc,CAAC,WAAW,CAAC,IAChCD,IAAI,CAACC,cAAc,CAAC,SAAS,CAC/B,CAAC;IACH;IAEA,IAAI,OAAO1B,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOA,IAAI,CAAC0B,cAAc,CAAC,eAAe,CAAC,IACpC1B,IAAI,CAAC0B,cAAc,CAAC,WAAW,CAAC,IAChC1B,IAAI,CAAC0B,cAAc,CAAC,SAAS,CAAC,IAC7B1B,IAAI,CAACA,IAAI,IAAIqB,kBAAkB,CAACrB,IAAI,CAACA,IAAI,CAAE;IACrD;IAEA,OAAO,KAAK;EACd,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM2B,iBAAiB,GAAGnC,OAAO,CAAC,MAAM;IAAA,IAAAoC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACtC,MAAMC,WAAW,GAAGf,kBAAkB,CAACrB,IAAI,CAAC;IAC5C,MAAMqC,qBAAqB,GAAGD,WAAW,MAAAR,qBAAA,GAAKtB,kBAAkB,CAACgC,2BAA2B,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,IAAI,CAAC;IAErG,OAAO;MACLW,OAAO,EAAEF,qBAAqB,GAAG,KAAK,IAAAR,qBAAA,GAAIvB,kBAAkB,CAACiC,OAAO,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,KAAM;MAC9EW,KAAK,GAAAV,qBAAA,GAAExB,kBAAkB,CAACkC,KAAK,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,CAAC;MACpCW,aAAa,GAAAV,sBAAA,GAAEzB,kBAAkB,CAACmC,aAAa,cAAAV,sBAAA,cAAAA,sBAAA,GAAI,IAAI;MACvDW,WAAW,GAAAV,qBAAA,GAAE1B,kBAAkB,CAACoC,WAAW,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MACnDW,iBAAiB,GAAAV,qBAAA,GAAE3B,kBAAkB,CAACqC,iBAAiB,cAAAV,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MAC/DW,gBAAgB,GAAAV,sBAAA,GAAE5B,kBAAkB,CAACsC,gBAAgB,cAAAV,sBAAA,cAAAA,sBAAA,GAAI,OAAO;MAAE;MAClEW,kBAAkB,EAAEvC,kBAAkB,CAACuC,kBAAkB;MAAE;MAC3DC,eAAe,EAAExC,kBAAkB,CAACwC,eAAe;MAAE;MACrDC,cAAc,EAAEzC,kBAAkB,CAACyC,cAAc;MAAE;MACnDT,2BAA2B,GAAAH,sBAAA,GAAE7B,kBAAkB,CAACgC,2BAA2B,cAAAH,sBAAA,cAAAA,sBAAA,GAAI,IAAI;MACnF,GAAG7B;IACL,CAAC;EACH,CAAC,EAAE,CAACA,kBAAkB,EAAEN,IAAI,EAAEqB,kBAAkB,CAAC,CAAC;;EAElD;EACA,MAAM2B,QAAQ,GAAGxD,OAAO,CAAC,MAAMyD,IAAI,CAACC,SAAS,CAAClD,IAAI,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMmD,gBAAgB,GAAG3D,OAAO,CAAC,MAAM;IACrC,IAAI,CAACQ,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAIsB,KAAK,CAACC,OAAO,CAACvB,IAAI,CAAC,EAAE,OAAOA,IAAI;IAEpC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAMoD,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;MAE7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;QAC/B,IAAIpD,IAAI,CAACqD,KAAK,CAAC,IAAI/B,KAAK,CAACC,OAAO,CAACvB,IAAI,CAACqD,KAAK,CAAC,CAAC,EAAE;UAC7C,OAAOrD,IAAI,CAACqD,KAAK,CAAC;QACpB;MACF;MAEA,OAAO,CAACrD,IAAI,CAAC,CAAC,CAAC;IACjB;IAEA,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMsD,qBAAqB,GAAG/D,WAAW,CAAC,CAACgE,MAAM,EAAEC,UAAU,GAAG,EAAE,KAAK;IACrE,IAAI,EAACA,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEC,MAAM,GAAE,OAAO,IAAI;IAEpC,MAAMC,eAAe,GAAGF,UAAU,CAACG,MAAM,CAACC,SAAS;MAAA,IAAAC,gBAAA;MAAA,QAAAA,gBAAA,GACjDD,SAAS,CAACP,KAAK,cAAAQ,gBAAA,uBAAfA,gBAAA,CAAiBC,IAAI,CAAC,CAAC;IAAA,CACzB,CAAC;IAED,IAAI,CAACJ,eAAe,CAACD,MAAM,EAAE,OAAO,IAAI;IAExC,OAAOC,eAAe,CAACK,KAAK,CAACH,SAAS,IAAI;MACxC,MAAMI,UAAU,GAAGT,MAAM,CAACK,SAAS,CAACP,KAAK,CAAC;MAC1C,MAAMY,cAAc,GAAGL,SAAS,CAACM,KAAK;MAEtC,QAAQN,SAAS,CAACO,QAAQ;QACxB,KAAK,QAAQ;UACX,OAAOH,UAAU,KAAKC,cAAc;QACtC,KAAK,YAAY;UACf,OAAOD,UAAU,KAAKC,cAAc;QACtC,KAAK,UAAU;UACb,OAAOD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE9C,QAAQ,CAAC,CAAC,CAACkD,QAAQ,CAACH,cAAc,CAAC;QACxD,KAAK,cAAc;UACjB,OAAO,EAACD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAE9C,QAAQ,CAAC,CAAC,CAACkD,QAAQ,CAACH,cAAc,CAAC;QACzD,KAAK,QAAQ;UACX,OAAOD,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,EAAE;QAChD,KAAK,YAAY;UACf,OAAOA,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,EAAE;QAChD;UACE,OAAO,IAAI;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,kBAAkB,GAAG9E,WAAW,CAAC,MAAM;IAC3C,IAAI,EAACW,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEoE,aAAa,GAAE,OAAO,EAAE;IAEhD,MAAMC,QAAQ,GAAG,EAAE;IAEnBrE,iBAAiB,CAACoE,aAAa,CAACE,OAAO,CAAC,CAACC,MAAM,EAAEC,WAAW,KAAK;MAAA,IAAAC,mBAAA;MAC/D,MAAMC,iBAAiB,GAAG,EAAAD,mBAAA,GAAAF,MAAM,CAACI,WAAW,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBpC,OAAO,KAAIZ,iBAAiB,CAACY,OAAO;MAElF,IAAIqC,iBAAiB,EAAE;QACrBzB,gBAAgB,CAACqB,OAAO,CAAC,CAACjB,MAAM,EAAEuB,WAAW,KAAK;UAChD,IAAIxB,qBAAqB,CAACC,MAAM,EAAEkB,MAAM,CAACjB,UAAU,CAAC,EAAE;YACpDe,QAAQ,CAACQ,IAAI,CAAC;cACZxB,MAAM;cACNuB,WAAW;cACXL,MAAM;cACNC,WAAW;cACXM,GAAG,EAAE,GAAGF,WAAW,IAAIJ,WAAW;YACpC,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOH,QAAQ;EACjB,CAAC,EAAE,CAACpB,gBAAgB,EAAEjD,iBAAiB,EAAEyB,iBAAiB,CAACY,OAAO,EAAEe,qBAAqB,CAAC,CAAC;;EAE3F;EACA,MAAM2B,mBAAmB,GAAG1F,WAAW,CAAE2F,eAAe,IAAK;IAC3D,IAAI,CAACA,eAAe,CAACzB,MAAM,EAAE,OAAO,EAAE;IAEtC,QAAQ9B,iBAAiB,CAACiB,gBAAgB;MACxC,KAAK,OAAO;QACV,OAAO,CAACsC,eAAe,CAAC,CAAC,CAAC,CAAC;MAC7B,KAAK,KAAK;QACR,OAAOA,eAAe;MACxB,KAAK,QAAQ;QACX,OAAOvD,iBAAiB,CAACkB,kBAAkB,GACvClB,iBAAiB,CAACkB,kBAAkB,CAACqC,eAAe,CAAC,GACrD,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC;MAC1B;QACE,OAAO,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACvD,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMwD,gBAAgB,GAAG5F,WAAW,CAAC,MAAM;IACzC6F,MAAM,CAACC,MAAM,CAACjE,WAAW,CAACkE,OAAO,CAAC,CAACd,OAAO,CAACe,YAAY,CAAC;IACxDnE,WAAW,CAACkE,OAAO,GAAG,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAjG,SAAS,CAAC,MAAM;IACd,IAAI,CAACsC,iBAAiB,CAACgB,iBAAiB,EAAE;IAE1C6C,OAAO,CAACC,GAAG,CAAC,OAAO1E,WAAW,CAACuE,OAAO,8CAA8C,CAAC;IAErFH,gBAAgB,CAAC,CAAC;IAClBtE,mBAAmB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;IAC9BH,mBAAmB,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAACqC,QAAQ,EAAErB,iBAAiB,CAACgB,iBAAiB,EAAEwC,gBAAgB,CAAC,CAAC;;EAErE;EACA,MAAMO,kBAAkB,GAAGnG,WAAW,CAAC,MAAM;IAC3C,MAAM2F,eAAe,GAAGb,kBAAkB,CAAC,CAAC;IAC5C,MAAMsB,gBAAgB,GAAGV,mBAAmB,CAACC,eAAe,CAAC;IAE7DS,gBAAgB,CAACnB,OAAO,CAAC,CAAC;MAAEjB,MAAM;MAAEmB,WAAW;MAAEM;IAAI,CAAC,KAAK;MAAA,IAAAY,qBAAA,EAAAC,qBAAA,EAAAC,oBAAA;MACzD,MAAMC,UAAU,GAAG,GAAG/C,QAAQ,IAAIgC,GAAG,EAAE;;MAEvC;MACA,IAAIrD,iBAAiB,CAACe,WAAW,IAAI9B,gBAAgB,CAACoF,GAAG,CAACD,UAAU,CAAC,EAAE;QACrE;MACF;;MAEA;MACA,KAAAH,qBAAA,GAAIlF,gBAAgB,CAACsE,GAAG,CAAC,cAAAY,qBAAA,eAArBA,qBAAA,CAAuBK,UAAU,EAAE;QACrC;MACF;MAEA,MAAMxB,MAAM,GAAGvE,iBAAiB,CAACoE,aAAa,CAACI,WAAW,CAAC;MAC3D,MAAMlC,KAAK,IAAAqD,qBAAA,IAAAC,oBAAA,GAAGrB,MAAM,CAACI,WAAW,cAAAiB,oBAAA,uBAAlBA,oBAAA,CAAoBI,YAAY,cAAAL,qBAAA,cAAAA,qBAAA,GAAIlE,iBAAiB,CAACa,KAAK;MAEzEgD,OAAO,CAACC,GAAG,CAAC,MAAM1E,WAAW,CAACuE,OAAO,wBAAwB9C,KAAK,cAAcwC,GAAG,EAAE,CAAC;;MAEtF;MACA,IAAIrD,iBAAiB,CAACmB,eAAe,EAAE;QACrCnB,iBAAiB,CAACmB,eAAe,CAAC;UAAES,MAAM;UAAEkB,MAAM;UAAEC;QAAY,CAAC,CAAC;MACpE;;MAEA;MACA7D,mBAAmB,CAACsF,IAAI,IAAI,IAAIrF,GAAG,CAACqF,IAAI,CAAC,CAACC,GAAG,CAACL,UAAU,CAAC,CAAC;MAE1D,IAAI3F,iBAAiB,IAAIoC,KAAK,KAAK,CAAC,EAAE;QACpC;QACA6D,gBAAgB,CAAC9C,MAAM,EAAEmB,WAAW,CAAC;QAErC,IAAI/C,iBAAiB,CAACoB,cAAc,EAAE;UACpCpB,iBAAiB,CAACoB,cAAc,CAAC;YAAEQ,MAAM;YAAEkB,MAAM;YAAEC;UAAY,CAAC,CAAC;QACnE;MACF,CAAC,MAAM;QACL;QACA/D,mBAAmB,CAACwF,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP,CAACnB,GAAG,GAAG;YAAEiB,UAAU,EAAE,IAAI;YAAEK,SAAS,EAAE9D;UAAM;QAC9C,CAAC,CAAC,CAAC;;QAEH;QACA,IAAIb,iBAAiB,CAACc,aAAa,EAAE;UACnC,MAAM8D,iBAAiB,GAAGC,WAAW,CAAC,MAAM;YAC1C7F,mBAAmB,CAACwF,IAAI,IAAI;cAC1B,MAAMb,OAAO,GAAGa,IAAI,CAACnB,GAAG,CAAC;cACzB,IAAI,CAACM,OAAO,IAAIA,OAAO,CAACgB,SAAS,IAAI,CAAC,EAAE;gBACtCG,aAAa,CAACF,iBAAiB,CAAC;gBAChC,OAAO;kBAAE,GAAGJ,IAAI;kBAAE,CAACnB,GAAG,GAAG;oBAAE,GAAGM,OAAO;oBAAEgB,SAAS,EAAE;kBAAE;gBAAE,CAAC;cACzD;cACA,OAAO;gBAAE,GAAGH,IAAI;gBAAE,CAACnB,GAAG,GAAG;kBAAE,GAAGM,OAAO;kBAAEgB,SAAS,EAAEhB,OAAO,CAACgB,SAAS,GAAG;gBAAE;cAAE,CAAC;YAC7E,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV;;QAEA;QACA,MAAMI,SAAS,GAAGC,UAAU,CAAC,MAAM;UACjCN,gBAAgB,CAAC9C,MAAM,EAAEmB,WAAW,CAAC;UACrC/D,mBAAmB,CAACwF,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACnB,GAAG,GAAG4B;UAAU,CAAC,CAAC,CAAC;UAE5D,IAAIjF,iBAAiB,CAACoB,cAAc,EAAE;YACpCpB,iBAAiB,CAACoB,cAAc,CAAC;cAAEQ,MAAM;cAAEkB,MAAM;cAAEC;YAAY,CAAC,CAAC;UACnE;QACF,CAAC,EAAElC,KAAK,GAAG,IAAI,CAAC;QAEhBpB,WAAW,CAACkE,OAAO,CAACN,GAAG,CAAC,GAAG0B,SAAS;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CACDrC,kBAAkB,EAClBY,mBAAmB,EACnBtD,iBAAiB,EACjBf,gBAAgB,EAChBF,gBAAgB,EAChBsC,QAAQ,EACR5C,iBAAiB,EACjBF,iBAAiB,CAClB,CAAC;;EAEF;EACAb,SAAS,CAAC,MAAM;IAAA,IAAAwH,qBAAA;IACd;IACA,MAAMzE,WAAW,GAAGf,kBAAkB,CAACrB,IAAI,CAAC;IAC5C,MAAM8G,sBAAsB,GAAG1E,WAAW,IAAIT,iBAAiB,CAACW,2BAA2B;IAE3F,IAAIwE,sBAAsB,EAAE;MAC1BtB,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9D;IACF;IAEA,IAAI,CAAC9D,iBAAiB,CAACY,OAAO,IAAI,EAACrC,iBAAiB,aAAjBA,iBAAiB,gBAAA2G,qBAAA,GAAjB3G,iBAAiB,CAAEoE,aAAa,cAAAuC,qBAAA,eAAhCA,qBAAA,CAAkCrF,IAAI,CAACiD,MAAM;MAAA,IAAAsC,oBAAA;MAAA,QAAAA,oBAAA,GAAItC,MAAM,CAACI,WAAW,cAAAkC,oBAAA,uBAAlBA,oBAAA,CAAoBxE,OAAO;IAAA,EAAC,GAAE;MAChH;IACF;IAEA,MAAMmE,SAAS,GAAGC,UAAU,CAACjB,kBAAkB,EAAE,GAAG,CAAC;IACrD,OAAO,MAAMH,YAAY,CAACmB,SAAS,CAAC;EACtC,CAAC,EAAE,CAAChB,kBAAkB,EAAE/D,iBAAiB,CAACY,OAAO,EAAEZ,iBAAiB,CAACW,2BAA2B,EAAEtC,IAAI,EAAEqB,kBAAkB,CAAC,CAAC;;EAE5H;EACA,MAAMgF,gBAAgB,GAAG9G,WAAW,CAAC,OAAOgE,MAAM,EAAEmB,WAAW,GAAG,CAAC,KAAK;IACtE,MAAMsC,SAAS,GAAG,GAAG/D,IAAI,CAACC,SAAS,CAACK,MAAM,CAAC,IAAImB,WAAW,EAAE;IAE5D,IAAIlE,cAAc,CAACwG,SAAS,CAAC,EAAE;IAE/BvG,iBAAiB,CAAC0F,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACa,SAAS,GAAG;IAAK,CAAC,CAAC,CAAC;IAE3D,IAAI;MAAA,IAAAC,sBAAA;MACF,MAAMxC,MAAM,GAAGvE,iBAAiB,aAAjBA,iBAAiB,wBAAA+G,sBAAA,GAAjB/G,iBAAiB,CAAEoE,aAAa,cAAA2C,sBAAA,uBAAhCA,sBAAA,CAAmCvC,WAAW,CAAC;MAC9D,MAAMwC,UAAU,GAAG,CAAAzC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEyC,UAAU,KAAI,OAAO;MAEhD,MAAMC,QAAQ,GAAG,MAAM1H,GAAG,CAAC2H,IAAI,CAAC,mBAAmBnH,MAAM,YAAY,EAAE;QACrEoH,UAAU,EAAE9D,MAAM;QAClB+D,UAAU,EAAEtH,IAAI;QAChB0E,WAAW;QACXwC;MACF,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAACnH,IAAI,CAACuH,OAAO,EAAE;QACzB,MAAM;UAAEC,iBAAiB;UAAEC,UAAU;UAAEC;QAAY,CAAC,GAAGP,QAAQ,CAACnH,IAAI;QAEpE,IAAIwH,iBAAiB,EAAE;UACrB,MAAMG,2BAA2B,CAC/BF,UAAU,CAACG,GAAG,EACdF,WAAW,EACXD,UAAU,CAACI,IAAI,EACfb,SACF,CAAC;QACH,CAAC,MAAM,IAAI7G,mBAAmB,EAAE;UAC9BA,mBAAmB,CAAC;YAClBsH,UAAU;YACVC,WAAW;YACXR,UAAU,EAAEC,QAAQ,CAACnH,IAAI,CAACkH,UAAU;YACpCY,WAAW,EAAEX,QAAQ,CAACnH,IAAI,CAAC8H;UAC7B,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACZ,QAAQ,CAACnH,IAAI,CAACgI,OAAO,IAAI,qBAAqB,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACd3C,OAAO,CAACyC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDG,KAAK,CAAC,sBAAsB,IAAI,EAAAF,eAAA,GAAAD,KAAK,CAACd,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBlI,IAAI,cAAAmI,oBAAA,uBAApBA,oBAAA,CAAsBH,OAAO,KAAIC,KAAK,CAACD,OAAO,CAAC,CAAC;IAClF,CAAC,SAAS;MACRvH,iBAAiB,CAAC0F,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACa,SAAS,GAAG;MAAM,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC,EAAE,CAAC9G,iBAAiB,EAAED,MAAM,EAAED,IAAI,EAAEQ,cAAc,EAAEL,mBAAmB,CAAC,CAAC;;EAE1E;EACA,MAAMwH,2BAA2B,GAAGpI,WAAW,CAAC,OAAO8I,YAAY,EAAEX,WAAW,EAAEY,QAAQ,EAAEtB,SAAS,KAAK;IACxG,IAAI;MACF,MAAMuB,QAAQ,GAAGtF,IAAI,CAACuF,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MACjE,MAAMC,OAAO,GAAG;QACd,eAAe,EAAEJ,QAAQ,CAACK,KAAK,GAAG,UAAUL,QAAQ,CAACK,KAAK,EAAE,GAAGhC,SAAS;QACxE,UAAU,EAAE2B,QAAQ,CAACM,KAAK,IAAIjC,SAAS;QACvC,aAAa,EAAE2B,QAAQ,CAACO,QAAQ,IAAIlC;MACtC,CAAC;MAED,MAAMO,QAAQ,GAAG,MAAM1H,GAAG,CAAC2H,IAAI,CAAC,mBAAmBiB,YAAY,SAAS,EAAE;QACxEU,QAAQ,EAAErB;MACZ,CAAC,EAAE;QAAEiB;MAAQ,CAAC,CAAC;MAEf,IAAIxB,QAAQ,CAACnH,IAAI,CAACuH,OAAO,EAAE;QACzBa,KAAK,CAAC,KAAKE,QAAQ,0BAA0B,CAAC;MAChD,CAAC,MAAM;QACL,MAAM,IAAIP,KAAK,CAACZ,QAAQ,CAACnH,IAAI,CAACgI,OAAO,IAAI,oBAAoB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAe,gBAAA,EAAAC,qBAAA;MACdzD,OAAO,CAACyC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DG,KAAK,CAAC,sBAAsBE,QAAQ,KAAK,EAAAU,gBAAA,GAAAf,KAAK,CAACd,QAAQ,cAAA6B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhJ,IAAI,cAAAiJ,qBAAA,uBAApBA,qBAAA,CAAsBjB,OAAO,KAAIC,KAAK,CAACD,OAAO,EAAE,CAAC;IAC5F,CAAC,SAAS;MACRvH,iBAAiB,CAAC0F,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACa,SAAS,GAAG;MAAM,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMkC,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IAC7C,MAAMC,WAAW,GAAG,sEAAsE;IAE1F,IAAID,SAAS,EAAE;MACb,OAAO,GAAGC,WAAW,4CAA4C;IACnE;IAEA,MAAMC,MAAM,GAAG;MACbC,OAAO,EAAE,0CAA0C;MACnDC,SAAS,EAAE,0CAA0C;MACrDjC,OAAO,EAAE,4CAA4C;MACrDkC,OAAO,EAAE,8CAA8C;MACvDC,MAAM,EAAE;IACV,CAAC;IAED,OAAO,GAAGL,WAAW,IAAIC,MAAM,CAACH,KAAK,CAAC,IAAIG,MAAM,CAACC,OAAO,EAAE;EAC5D,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAIzF,KAAK,IAAK;IAClC,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAO,GAAG;IAC7B,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE,OAAOA,KAAK,GAAG,KAAK,GAAG,IAAI;IAC3D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOjB,IAAI,CAACC,SAAS,CAACgB,KAAK,CAAC;IAC3D,OAAOA,KAAK,CAAChD,QAAQ,CAAC,CAAC;EACzB,CAAC;;EAED;EACA,MAAM0I,mBAAmB,GAAGA,CAACrG,MAAM,EAAEuB,WAAW,KAAK;IACnD,IAAI,EAAC5E,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEqC,OAAO,KAAI,CAACrC,iBAAiB,CAACoE,aAAa,EAAE,OAAO,IAAI;IAEhF,oBACE1E,OAAA;MAAKiK,SAAS,EAAC,sBAAsB;MAAAC,QAAA,EAClC5J,iBAAiB,CAACoE,aAAa,CAACyF,GAAG,CAAC,CAACtF,MAAM,EAAEC,WAAW,KAAK;QAAA,IAAAsF,oBAAA;QAC5D,IAAI,CAAC1G,qBAAqB,CAACC,MAAM,EAAEkB,MAAM,CAACjB,UAAU,CAAC,EAAE,OAAO,IAAI;QAElE,MAAMwD,SAAS,GAAG,GAAG/D,IAAI,CAACC,SAAS,CAACK,MAAM,CAAC,IAAImB,WAAW,EAAE;QAC5D,MAAMuF,QAAQ,GAAG,GAAGnF,WAAW,IAAIJ,WAAW,EAAE;QAChD,MAAM0E,SAAS,GAAG5I,cAAc,CAACwG,SAAS,CAAC;QAC3C,MAAMkD,YAAY,GAAGxJ,gBAAgB,CAACuJ,QAAQ,CAAC;QAC/C,MAAMrF,iBAAiB,GAAG,EAAAoF,oBAAA,GAAAvF,MAAM,CAACI,WAAW,cAAAmF,oBAAA,uBAAlBA,oBAAA,CAAoBzH,OAAO,KAAIZ,iBAAiB,CAACY,OAAO;QAElF,oBACE3C,OAAA;UAAuBiK,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GAEvD,CAAClF,iBAAiB,iBACjBhF,OAAA;YACEuK,OAAO,EAAEA,CAAA,KAAM9D,gBAAgB,CAAC9C,MAAM,EAAEmB,WAAW,CAAE;YACrD0F,QAAQ,EAAEhB,SAAU;YACpBS,SAAS,EAAEX,gBAAgB,CAACzE,MAAM,CAACqD,WAAW,EAAEsB,SAAS,CAAE;YAAAU,QAAA,EAE1DV,SAAS,gBACRxJ,OAAA;cAAMiK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjClK,OAAA;gBAAKiK,SAAS,EAAC,4CAA4C;gBAACQ,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,gBAC5HlK,OAAA;kBAAQiK,SAAS,EAAC,YAAY;kBAACW,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACrGpL,OAAA;kBAAMiK,SAAS,EAAC,YAAY;kBAACS,IAAI,EAAC,cAAc;kBAACW,CAAC,EAAC;gBAAiH;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK,CAAC,cAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GAEPvG,MAAM,CAACyC,UAAU,IAAI;UACtB;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CACT,EAGA,CAAAd,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE5D,SAAS,IAAG,CAAC,IAAI3E,iBAAiB,CAACc,aAAa,iBAC7D7C,OAAA;YAAKiK,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnFlK,OAAA;cAAKiK,SAAS,EAAC,cAAc;cAACS,IAAI,EAAC,MAAM;cAACK,MAAM,EAAC,cAAc;cAACJ,OAAO,EAAC,WAAW;cAAAT,QAAA,eACjFlK,OAAA;gBAAMsL,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACP,WAAW,EAAE,CAAE;gBAACK,CAAC,EAAC;cAA6C;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC,uBACa,EAACd,YAAY,CAAC5D,SAAS,EAAC,GAC7C;UAAA;YAAAuE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA,GA9BOtG,WAAW;UAAAmG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+BhB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMI,YAAY,GAAGA,CAAC7H,MAAM,EAAE8H,KAAK,kBACjCzL,OAAA;IAAiBiK,SAAS,EAAC,MAAM;IAAAC,QAAA,GAC9B,CAACzJ,eAAe,iBACfT,OAAA,CAAAE,SAAA;MAAAgK,QAAA,gBACElK,OAAA;QAAKiK,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBlK,OAAA;UAAMiK,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GAAC,SAAO,EAACuB,KAAK,GAAG,CAAC,EAAC,GAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAENpL,OAAA;QAAKiK,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,MAAM;UACN,MAAM1H,WAAW,GAAGmB,MAAM,CAAC7B,cAAc,CAAC,eAAe,CAAC,IAAI6B,MAAM,CAAC7B,cAAc,CAAC,SAAS,CAAC;UAC9F,MAAM4J,eAAe,GAAGlJ,WAAW,GAC/B,CAAC,CAAC,eAAe,EAAEmB,MAAM,CAACgI,aAAa,CAAC,EAAE,CAAC,SAAS,EAAEhI,MAAM,CAACiI,OAAO,CAAC,CAAC,GACtEpG,MAAM,CAACqG,OAAO,CAAClI,MAAM,CAAC;UAE1B,OAAO+H,eAAe,CAACvB,GAAG,CAAC,CAAC,CAAC/E,GAAG,EAAEd,KAAK,CAAC,EAAEwH,UAAU,kBAClD9L,OAAA;YAAAkK,QAAA,GACG4B,UAAU,GAAG,CAAC,IAAI,GAAG,eACtB9L,OAAA;cAAMiK,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAE9E,GAAG,EAAC,GAAC;YAAA;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAACrB,gBAAgB,CAACzF,KAAK,CAAC,EACnEwH,UAAU,GAAGJ,eAAe,CAAC7H,MAAM,GAAG,CAAC,IAAI,GAAG;UAAA,GAHtCuB,GAAG;YAAA6F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIR,CACP,CAAC;QACJ,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,eACN,CACH,EAEApB,mBAAmB,CAACrG,MAAM,EAAE8H,KAAK,CAAC;EAAA,GA1B3BA,KAAK;IAAAR,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA2BV,CACN;;EAED;EACA,IAAI5K,iBAAiB,EAAE,OAAO,IAAI;;EAElC;EACA,IAAI,CAAC+C,gBAAgB,CAACM,MAAM,EAAE;IAC5B,oBACE7D,OAAA;MAAKiK,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAAC;IAEhD;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACEpL,OAAA;IAAKiK,SAAS,EAAC,WAAW;IAAAC,QAAA,EACvB3G,gBAAgB,CAAC4G,GAAG,CAACqB,YAAY;EAAC;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChC,CAAC;AAEV,CAAC;AAACzK,EAAA,CApeIR,wBAAwB;AAAA4L,EAAA,GAAxB5L,wBAAwB;AAse9B,eAAeA,wBAAwB;AAAC,IAAA4L,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}