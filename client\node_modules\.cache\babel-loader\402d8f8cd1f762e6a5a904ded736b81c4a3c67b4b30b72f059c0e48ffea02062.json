{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatMessage.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatMessage = ({\n  message,\n  onOptionSelect,\n  onFormLinkTriggered\n}) => {\n  _s();\n  const isUser = message.role === 'user';\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\n\n  // Handle checkbox selection\n  const handleCheckboxToggle = option => {\n    const newSelected = selectedCheckboxes.includes(option) ? selectedCheckboxes.filter(item => item !== option) : [...selectedCheckboxes, option];\n    setSelectedCheckboxes(newSelected);\n  };\n\n  // Handle checkbox submission\n  const handleCheckboxSubmit = () => {\n    if (selectedCheckboxes.length > 0) {\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\n      setSelectedCheckboxes([]);\n    }\n  };\n\n  // Handle single option selection (for radio/select)\n  const handleSingleOptionSelect = option => {\n    onOptionSelect && onOptionSelect(option);\n  };\n  // Helper function to check if data is empty (comprehensive check)\n  const checkIfDataIsEmpty = data => {\n    if (!data) return true;\n    if (Array.isArray(data)) return data.length === 0;\n    if (typeof data === 'object' && data !== null) {\n      if (Object.keys(data).length === 0) return true;\n      // Check nested data structure\n      if (data.data) return checkIfDataIsEmpty(data.data);\n      // Check common array fields\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n      for (const field of arrayFields) {\n        if (data[field] && Array.isArray(data[field])) {\n          return data[field].length === 0;\n        }\n      }\n      // Check if all values are empty\n      return Object.values(data).every(val => val === null || val === undefined || val === '' || Array.isArray(val) && val.length === 0 || typeof val === 'object' && val !== null && Object.keys(val).length === 0);\n    }\n    return false;\n  };\n\n  // Helper function to render \"No data available\" message\n  const renderNoDataMessage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\",\n    children: /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-blue-800 text-sm font-medium\",\n      children: \"No data available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 5\n  }, this);\n\n  // Helper function to check if data is leave balance related\n  const isLeaveBalanceData = data => {\n    if (!data) return false;\n\n    // Check if data contains leave balance information\n    if (Array.isArray(data)) {\n      return data.some(item => item.hasOwnProperty('leaveTypeName') || item.hasOwnProperty('leaveType') || item.hasOwnProperty('balance'));\n    }\n    if (typeof data === 'object') {\n      return data.hasOwnProperty('leaveTypeName') || data.hasOwnProperty('leaveType') || data.hasOwnProperty('balance') || data.data && isLeaveBalanceData(data.data);\n    }\n    return false;\n  };\n\n  // Format API response for display\n  const formatApiResponse = apiResponse => {\n    var _message$formData, _message$formData$for, _message$formConfig, _message$formData2, _message$formConfig2, _message$formConfig2$, _formLinkingConfig$re4;\n    if (!apiResponse) return null;\n\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\n    const formLinkingConfig = ((_message$formData = message.formData) === null || _message$formData === void 0 ? void 0 : (_message$formData$for = _message$formData.formConfig) === null || _message$formData$for === void 0 ? void 0 : _message$formData$for.formLinking) || ((_message$formConfig = message.formConfig) === null || _message$formConfig === void 0 ? void 0 : _message$formConfig.formLinking) || ((_message$formData2 = message.formData) === null || _message$formData2 === void 0 ? void 0 : _message$formData2.formLinking) || ((_message$formConfig2 = message.formConfig) === null || _message$formConfig2 === void 0 ? void 0 : (_message$formConfig2$ = _message$formConfig2.formConfig) === null || _message$formConfig2$ === void 0 ? void 0 : _message$formConfig2$.formLinking);\n\n    // For successful responses with data, check if we should display records with actions\n    if (apiResponse.success && apiResponse.data) {\n      var _message$conversation, _message$hybridFlow, _formLinkingConfig$re, _formLinkingConfig$re3;\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\n      const hasActiveConversationalFlow = message.isConversationalPhase || message.isHybridFlow || ((_message$conversation = message.conversationalFlow) === null || _message$conversation === void 0 ? void 0 : _message$conversation.isActive) || ((_message$hybridFlow = message.hybridFlow) === null || _message$hybridFlow === void 0 ? void 0 : _message$hybridFlow.isActive) || message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow);\n\n      // Check if this is leave balance data\n      const isLeaveData = isLeaveBalanceData(apiResponse.data);\n\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\n      if (formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re = formLinkingConfig.recordActions) === null || _formLinkingConfig$re === void 0 ? void 0 : _formLinkingConfig$re.length) > 0 && !hasActiveConversationalFlow) {\n        var _apiResponse$data, _formLinkingConfig$re2, _message$formData3, _message$formConfig3;\n        // Extract the actual data from the API response\n        const actualData = ((_apiResponse$data = apiResponse.data) === null || _apiResponse$data === void 0 ? void 0 : _apiResponse$data.data) || apiResponse.data;\n\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\n        const hasDisabledAutoTrigger = (_formLinkingConfig$re2 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re2 === void 0 ? void 0 : _formLinkingConfig$re2.some(action => {\n          var _action$autoTrigger;\n          return !((_action$autoTrigger = action.autoTrigger) !== null && _action$autoTrigger !== void 0 && _action$autoTrigger.enabled);\n        });\n        const formId = ((_message$formData3 = message.formData) === null || _message$formData3 === void 0 ? void 0 : _message$formData3._id) || ((_message$formConfig3 = message.formConfig) === null || _message$formConfig3 === void 0 ? void 0 : _message$formConfig3._id);\n\n        // Function to render records with interleaved apply buttons\n        const renderRecordsWithButtons = () => {\n          let records = [];\n\n          // Extract records from actualData\n          if (Array.isArray(actualData)) {\n            records = actualData;\n          } else if (actualData && typeof actualData === 'object') {\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n            for (const field of arrayFields) {\n              if (actualData[field] && Array.isArray(actualData[field])) {\n                records = actualData[field];\n                break;\n              }\n            }\n            if (records.length === 0) {\n              records = [actualData];\n            }\n          }\n\n          // Check if records are empty using helper function\n          if (checkIfDataIsEmpty(records)) {\n            return renderNoDataMessage();\n          }\n\n          // Split formatted response by \"Record N:\" pattern\n          let formattedRecords = [];\n          if (apiResponse.formattedResponse) {\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\n            formattedRecords = recordSections.filter(section => section.trim());\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [records.map((record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [formattedRecords[index] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: formattedRecords[index].trim()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 157,\n                columnNumber: 21\n              }, this), (formLinkingConfig === null || formLinkingConfig === void 0 ? void 0 : formLinkingConfig.enabled) && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2 ml-3\",\n                children: /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                  data: [record] // Pass only this specific record\n                  ,\n                  formId: formId,\n                  formLinkingConfig: formLinkingConfig,\n                  onFormLinkTriggered: onFormLinkTriggered,\n                  showOnlyButtons: true,\n                  autoTriggerOptions: {\n                    enabled: false,\n                    // Disable auto-trigger for leave balance data\n                    skipLeaveBalanceAutoTrigger: true\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 167,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 166,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this)), formattedRecords.length === 0 && apiResponse.formattedResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: apiResponse.formattedResponse\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                data: actualData,\n                formId: formId,\n                formLinkingConfig: formLinkingConfig,\n                onFormLinkTriggered: onFormLinkTriggered,\n                showOnlyButtons: true,\n                autoTriggerOptions: {\n                  enabled: false,\n                  // Disable auto-trigger for leave balance data\n                  skipLeaveBalanceAutoTrigger: true\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this);\n        };\n        return renderRecordsWithButtons();\n      }\n\n      // If we have a formatted response but no form linking, display just the formatted response\n      if (apiResponse.formattedResponse && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re3 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re3 === void 0 ? void 0 : _formLinkingConfig$re3.length) > 0)) {\n        // Check if the underlying data is empty using helper function\n        if (checkIfDataIsEmpty(apiResponse.data)) {\n          return renderNoDataMessage();\n        }\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 p-3 bg-blue-50 rounded-md\",\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: \"whitespace-pre-wrap text-sm text-blue-800\",\n            children: apiResponse.formattedResponse\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this);\n      }\n    }\n\n    // Check if data is empty (array with no items or empty object)\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re4 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re4 === void 0 ? void 0 : _formLinkingConfig$re4.length) > 0)) {\n      // Check if data is empty using helper function\n      if (checkIfDataIsEmpty(apiResponse.data)) {\n        return renderNoDataMessage();\n      }\n\n      // Display data if not empty\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"whitespace-pre-wrap text-sm text-gray-700\",\n          children: JSON.stringify(apiResponse.data, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Handle successful response but no data property at all\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\n      return renderNoDataMessage();\n    }\n\n    // Error display\n    if (apiResponse.error) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-red-50 rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-sm\",\n          children: apiResponse.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-[80%] p-3 rounded-lg ${isUser ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-200 text-gray-800 rounded-bl-none'}`,\n      children: [!(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"whitespace-pre-wrap\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this), !isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 308,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 307,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 301,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 318,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 327,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-gray-500\",\n          children: [\"Step \", message.currentStep, \" of \", message.totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 285,\n        columnNumber: 11\n      }, this), !isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 377,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 361,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 388,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 405,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 403,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalConversationalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-blue-600\",\n          children: [\"Conversational Step \", message.currentStep, \" of \", message.totalConversationalSteps, message.totalFormSteps > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-500\",\n            children: [\"(\", message.totalFormSteps, \" form field\", message.totalFormSteps !== 1 ? 's' : '', \" remaining)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 15\n          }, this), \"Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 432,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-3 h-3 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 13l4 4L19 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), \"Leave Application Submitted Successfully\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 13\n        }, this), message.updatedLeaveBalance && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 bg-blue-50 rounded-lg border border-blue-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2 text-blue-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 457,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 456,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-blue-800\",\n              children: \"Updated Leave Balance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-blue-700 whitespace-pre-wrap\",\n            children: message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 15\n          }, this), \"Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 471,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_cancelled' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 15\n          }, this), \"Form Cancelled\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 498,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 495,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Hybrid Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 507,\n        columnNumber: 11\n      }, this), message.apiResponse && !message.apiResponse.leaveBalance && formatApiResponse(message.apiResponse)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 271,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatMessage, \"cE9qr9Un/J6bIavRReyYdfaIaEo=\");\n_c = ChatMessage;\nexport default ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "RecordDisplayWithActions", "jsxDEV", "_jsxDEV", "ChatMessage", "message", "onOptionSelect", "onFormLinkTriggered", "_s", "isUser", "role", "selectedCheckboxes", "setSelectedCheckboxes", "handleCheckboxToggle", "option", "newSelected", "includes", "filter", "item", "handleCheckboxSubmit", "length", "join", "handleSingleOptionSelect", "checkIfDataIsEmpty", "data", "Array", "isArray", "Object", "keys", "arrayFields", "field", "values", "every", "val", "undefined", "renderNoDataMessage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "isLeaveBalanceData", "some", "hasOwnProperty", "formatApiResponse", "apiResponse", "_message$formData", "_message$formData$for", "_message$formConfig", "_message$formData2", "_message$formConfig2", "_message$formConfig2$", "_formLinkingConfig$re4", "formLinkingConfig", "formData", "formConfig", "formLinking", "success", "_message$conversation", "_message$hybridFlow", "_formLinkingConfig$re", "_formLinkingConfig$re3", "hasActiveConversationalFlow", "isConversationalPhase", "isHybridFlow", "conversationalFlow", "isActive", "hybridFlow", "options", "isLeaveData", "enabled", "recordActions", "_apiResponse$data", "_formLinkingConfig$re2", "_message$formData3", "_message$formConfig3", "actualData", "hasDisabledAutoTrigger", "action", "_action$autoTrigger", "autoTrigger", "formId", "_id", "renderRecordsWithButtons", "records", "formattedRecords", "formattedResponse", "recordSections", "split", "section", "trim", "map", "record", "index", "showOnlyButtons", "autoTriggerOptions", "skipLeaveBalanceAutoTrigger", "JSON", "stringify", "error", "content", "fieldType", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "currentStep", "totalSteps", "totalConversationalSteps", "totalFormSteps", "queryIntent", "updatedLeaveBalance", "replace", "leaveBalance", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatMessage.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\r\n\r\nconst ChatMessage = ({ message, onOptionSelect, onFormLinkTriggered }) => {\r\n  const isUser = message.role === 'user';\r\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\r\n\r\n  // Handle checkbox selection\r\n  const handleCheckboxToggle = (option) => {\r\n    const newSelected = selectedCheckboxes.includes(option)\r\n      ? selectedCheckboxes.filter(item => item !== option)\r\n      : [...selectedCheckboxes, option];\r\n    setSelectedCheckboxes(newSelected);\r\n  };\r\n\r\n  // Handle checkbox submission\r\n  const handleCheckboxSubmit = () => {\r\n    if (selectedCheckboxes.length > 0) {\r\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\r\n      setSelectedCheckboxes([]);\r\n    }\r\n  };\r\n\r\n  // Handle single option selection (for radio/select)\r\n  const handleSingleOptionSelect = (option) => {\r\n    onOptionSelect && onOptionSelect(option);\r\n  };\r\n  // Helper function to check if data is empty (comprehensive check)\r\n  const checkIfDataIsEmpty = (data) => {\r\n    if (!data) return true;\r\n    if (Array.isArray(data)) return data.length === 0;\r\n    if (typeof data === 'object' && data !== null) {\r\n      if (Object.keys(data).length === 0) return true;\r\n      // Check nested data structure\r\n      if (data.data) return checkIfDataIsEmpty(data.data);\r\n      // Check common array fields\r\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n      for (const field of arrayFields) {\r\n        if (data[field] && Array.isArray(data[field])) {\r\n          return data[field].length === 0;\r\n        }\r\n      }\r\n      // Check if all values are empty\r\n      return Object.values(data).every(val => \r\n        val === null || val === undefined || val === '' || \r\n        (Array.isArray(val) && val.length === 0) ||\r\n        (typeof val === 'object' && val !== null && Object.keys(val).length === 0)\r\n      );\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // Helper function to render \"No data available\" message\r\n  const renderNoDataMessage = () => (\r\n    <div className=\"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\">\r\n      <p className=\"text-blue-800 text-sm font-medium\">No data available</p>\r\n    </div>\r\n  );\r\n\r\n  // Helper function to check if data is leave balance related\r\n  const isLeaveBalanceData = (data) => {\r\n    if (!data) return false;\r\n\r\n    // Check if data contains leave balance information\r\n    if (Array.isArray(data)) {\r\n      return data.some(item =>\r\n        item.hasOwnProperty('leaveTypeName') ||\r\n        item.hasOwnProperty('leaveType') ||\r\n        item.hasOwnProperty('balance')\r\n      );\r\n    }\r\n\r\n    if (typeof data === 'object') {\r\n      return data.hasOwnProperty('leaveTypeName') ||\r\n             data.hasOwnProperty('leaveType') ||\r\n             data.hasOwnProperty('balance') ||\r\n             (data.data && isLeaveBalanceData(data.data));\r\n    }\r\n\r\n    return false;\r\n  };\r\n\r\n  // Format API response for display\r\n  const formatApiResponse = (apiResponse) => {\r\n    if (!apiResponse) return null;\r\n\r\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\r\n    const formLinkingConfig = message.formData?.formConfig?.formLinking ||\r\n                             message.formConfig?.formLinking ||\r\n                             message.formData?.formLinking ||\r\n                             message.formConfig?.formConfig?.formLinking;\r\n\r\n    // For successful responses with data, check if we should display records with actions\r\n    if (apiResponse.success && apiResponse.data) {\r\n\r\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\r\n      const hasActiveConversationalFlow = message.isConversationalPhase ||\r\n                                         message.isHybridFlow ||\r\n                                         message.conversationalFlow?.isActive ||\r\n                                         message.hybridFlow?.isActive ||\r\n                                         (message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow));\r\n\r\n      // Check if this is leave balance data\r\n      const isLeaveData = isLeaveBalanceData(apiResponse.data);\r\n\r\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\r\n      if (formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0 && !hasActiveConversationalFlow) {\r\n       \r\n        // Extract the actual data from the API response\r\n        const actualData = apiResponse.data?.data || apiResponse.data;\r\n        \r\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\r\n        const hasDisabledAutoTrigger = formLinkingConfig.recordActions?.some(action => \r\n          !action.autoTrigger?.enabled\r\n        );\r\n        \r\n        const formId = message.formData?._id || message.formConfig?._id;\r\n        \r\n        // Function to render records with interleaved apply buttons\r\n        const renderRecordsWithButtons = () => {\r\n          let records = [];\r\n          \r\n          // Extract records from actualData\r\n          if (Array.isArray(actualData)) {\r\n            records = actualData;\r\n          } else if (actualData && typeof actualData === 'object') {\r\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n            for (const field of arrayFields) {\r\n              if (actualData[field] && Array.isArray(actualData[field])) {\r\n                records = actualData[field];\r\n                break;\r\n              }\r\n            }\r\n            if (records.length === 0) {\r\n              records = [actualData];\r\n            }\r\n          }\r\n          \r\n          // Check if records are empty using helper function\r\n          if (checkIfDataIsEmpty(records)) {\r\n            return renderNoDataMessage();\r\n          }\r\n\r\n          // Split formatted response by \"Record N:\" pattern\r\n          let formattedRecords = [];\r\n          if (apiResponse.formattedResponse) {\r\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\r\n            formattedRecords = recordSections.filter(section => section.trim());\r\n          }\r\n\r\n          return (\r\n            <div className=\"mt-3\">\r\n              {records.map((record, index) => (\r\n                <div key={index} className=\"mb-4\">\r\n                  {/* Display the formatted response for this record */}\r\n                  {formattedRecords[index] && (\r\n                    <div className=\"mb-2 p-3 bg-blue-50 rounded-md\">\r\n                      <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                        {formattedRecords[index].trim()}\r\n                      </pre>\r\n                    </div>\r\n                  )}\r\n                  \r\n                  {/* Apply button for this specific record */}\r\n                  {formLinkingConfig?.enabled && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && (\r\n                    <div className=\"flex flex-wrap gap-2 ml-3\">\r\n                      <RecordDisplayWithActions\r\n                        data={[record]} // Pass only this specific record\r\n                        formId={formId}\r\n                        formLinkingConfig={formLinkingConfig}\r\n                        onFormLinkTriggered={onFormLinkTriggered}\r\n                        showOnlyButtons={true}\r\n                        autoTriggerOptions={{\r\n                          enabled: false, // Disable auto-trigger for leave balance data\r\n                          skipLeaveBalanceAutoTrigger: true\r\n                        }}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n              \r\n              {/* Fallback: if we can't split the formatted response properly */}\r\n              {formattedRecords.length === 0 && apiResponse.formattedResponse && (\r\n                <div>\r\n                  <div className=\"mb-3 p-3 bg-blue-50 rounded-md\">\r\n                    <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                      {apiResponse.formattedResponse}\r\n                    </pre>\r\n                  </div>\r\n                  {/* Only show Apply buttons if the response is not \"No data available\" */}\r\n                  {!apiResponse.formattedResponse.includes('No data available') && (\r\n                    <RecordDisplayWithActions\r\n                      data={actualData}\r\n                      formId={formId}\r\n                      formLinkingConfig={formLinkingConfig}\r\n                      onFormLinkTriggered={onFormLinkTriggered}\r\n                      showOnlyButtons={true}\r\n                      autoTriggerOptions={{\r\n                        enabled: false, // Disable auto-trigger for leave balance data\r\n                        skipLeaveBalanceAutoTrigger: true\r\n                      }}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          );\r\n        };\r\n\r\n        return renderRecordsWithButtons();\r\n      }\r\n        \r\n      // If we have a formatted response but no form linking, display just the formatted response\r\n      if (apiResponse.formattedResponse && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n        // Check if the underlying data is empty using helper function\r\n        if (checkIfDataIsEmpty(apiResponse.data)) {\r\n          return renderNoDataMessage();\r\n        }\r\n        \r\n        return (\r\n          <div className=\"mt-3 p-3 bg-blue-50 rounded-md\">\r\n            <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n              {apiResponse.formattedResponse}\r\n            </pre>\r\n          </div>\r\n        );\r\n      }\r\n    }\r\n    \r\n    // Check if data is empty (array with no items or empty object)\r\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n      // Check if data is empty using helper function\r\n      if (checkIfDataIsEmpty(apiResponse.data)) {\r\n        return renderNoDataMessage();\r\n      }\r\n      \r\n      // Display data if not empty\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\">\r\n          <pre className=\"whitespace-pre-wrap text-sm text-gray-700\">\r\n            {JSON.stringify(apiResponse.data, null, 2)}\r\n          </pre>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    // Handle successful response but no data property at all\r\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\r\n      return renderNoDataMessage();\r\n    }\r\n    \r\n    // Error display\r\n    if (apiResponse.error) {\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-red-50 rounded-md\">\r\n          <p className=\"text-red-600 text-sm\">{apiResponse.error}</p>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`flex ${\r\n        isUser ? 'justify-end' : 'justify-start'\r\n      } mb-4`}\r\n    >\r\n      <div\r\n        className={`max-w-[80%] p-3 rounded-lg ${\r\n          isUser\r\n            ? 'bg-blue-500 text-white rounded-br-none'\r\n            : 'bg-gray-200 text-gray-800 rounded-bl-none'\r\n        }`}\r\n      >\r\n        {/* Display message content only if we don't have a formatted API response */}\r\n        {!(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <p className=\"whitespace-pre-wrap\">{message.content}</p>\r\n        )}\r\n        \r\n        {/* Display conversational form options as buttons */}\r\n        {!isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalSteps && (\r\n              <div className=\"mt-2 text-xs text-gray-500\">\r\n                Step {message.currentStep} of {message.totalSteps}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form options for conversational phase */}\r\n        {!isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalConversationalSteps && (\r\n              <div className=\"mt-2 text-xs text-blue-600\">\r\n                Conversational Step {message.currentStep} of {message.totalConversationalSteps}\r\n                {message.totalFormSteps > 0 && (\r\n                  <span className=\"ml-2 text-gray-500\">\r\n                    ({message.totalFormSteps} form field{message.totalFormSteps !== 1 ? 's' : ''} remaining)\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n\r\n              \r\n        {/* Display form completion indicator - only show success if form was actually submitted successfully */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display leave form completion with updated balance */}\r\n        {!isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2\">\r\n            <div className=\"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\">\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n                Leave Application Submitted Successfully\r\n              </div>\r\n            </div>\r\n            {message.updatedLeaveBalance && (\r\n              <div className=\"p-3 bg-blue-50 rounded-lg border border-blue-200\">\r\n                <div className=\"flex items-center mb-2\">\r\n                  <svg className=\"w-4 h-4 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                  </svg>\r\n                  <h4 className=\"text-sm font-medium text-blue-800\">Updated Leave Balance</h4>\r\n                </div>\r\n                <div className=\"text-sm text-blue-700 whitespace-pre-wrap\">\r\n                  {message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Display form cancellation indicator */}\r\n        {!isUser && message.queryIntent === 'form_cancelled' && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Cancelled\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form completion indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              🔄 Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              🔄 Hybrid Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n\r\n        \r\n        {/* Leave balance is now included in the message content, no separate display needed */}\r\n\r\n        {/* Display API response if available (but not for leave balance) */}\r\n        {message.apiResponse && !message.apiResponse.leaveBalance && formatApiResponse(message.apiResponse)}\r\n        \r\n        {/* Display form data if available (for debugging) */}\r\n        {/* {message.formData && process.env.NODE_ENV === 'development' && (\r\n          <div className=\"mt-2 p-2 bg-gray-100 rounded text-xs text-gray-500\">\r\n            <p>Form data submitted</p>\r\n          </div>\r\n        )} */}\r\n        \r\n        {/* Display query intent if available (for debugging) */}\r\n        {/* {message.queryIntent && process.env.NODE_ENV === 'development' && (\r\n          <div className={`mt-2 p-2 rounded text-xs ${\r\n            message.queryIntent === 'form' \r\n              ? 'bg-blue-100 text-blue-700' \r\n              : 'bg-green-100 text-green-700'\r\n          }`}>\r\n            <p>Query intent: <strong>{message.queryIntent}</strong></p>\r\n            {message.formData && message.queryIntent === 'form' && (\r\n              <p>Form detected: {message.formData.name}</p>\r\n            )}\r\n          </div>\r\n        )} */}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatMessage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,wBAAwB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,cAAc;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,IAAI,KAAK,MAAM;EACtC,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAMe,oBAAoB,GAAIC,MAAM,IAAK;IACvC,MAAMC,WAAW,GAAGJ,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GACnDH,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKJ,MAAM,CAAC,GAClD,CAAC,GAAGH,kBAAkB,EAAEG,MAAM,CAAC;IACnCF,qBAAqB,CAACG,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIR,kBAAkB,CAACS,MAAM,GAAG,CAAC,EAAE;MACjCd,cAAc,IAAIA,cAAc,CAACK,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/DT,qBAAqB,CAAC,EAAE,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMU,wBAAwB,GAAIR,MAAM,IAAK;IAC3CR,cAAc,IAAIA,cAAc,CAACQ,MAAM,CAAC;EAC1C,CAAC;EACD;EACA,MAAMS,kBAAkB,GAAIC,IAAI,IAAK;IACnC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IACtB,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE,OAAOA,IAAI,CAACJ,MAAM,KAAK,CAAC;IACjD,IAAI,OAAOI,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;MAC7C,IAAIG,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAACJ,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC/C;MACA,IAAII,IAAI,CAACA,IAAI,EAAE,OAAOD,kBAAkB,CAACC,IAAI,CAACA,IAAI,CAAC;MACnD;MACA,MAAMK,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;MAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;QAC/B,IAAIL,IAAI,CAACM,KAAK,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACF,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE;UAC7C,OAAON,IAAI,CAACM,KAAK,CAAC,CAACV,MAAM,KAAK,CAAC;QACjC;MACF;MACA;MACA,OAAOO,MAAM,CAACI,MAAM,CAACP,IAAI,CAAC,CAACQ,KAAK,CAACC,GAAG,IAClCA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,IAC9CR,KAAK,CAACC,OAAO,CAACO,GAAG,CAAC,IAAIA,GAAG,CAACb,MAAM,KAAK,CAAE,IACvC,OAAOa,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIN,MAAM,CAACC,IAAI,CAACK,GAAG,CAAC,CAACb,MAAM,KAAK,CAC1E,CAAC;IACH;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMe,mBAAmB,GAAGA,CAAA,kBAC1BhC,OAAA;IAAKiC,SAAS,EAAC,uDAAuD;IAAAC,QAAA,eACpElC,OAAA;MAAGiC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAiB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnE,CACN;;EAED;EACA,MAAMC,kBAAkB,GAAIlB,IAAI,IAAK;IACnC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;;IAEvB;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MACvB,OAAOA,IAAI,CAACmB,IAAI,CAACzB,IAAI,IACnBA,IAAI,CAAC0B,cAAc,CAAC,eAAe,CAAC,IACpC1B,IAAI,CAAC0B,cAAc,CAAC,WAAW,CAAC,IAChC1B,IAAI,CAAC0B,cAAc,CAAC,SAAS,CAC/B,CAAC;IACH;IAEA,IAAI,OAAOpB,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOA,IAAI,CAACoB,cAAc,CAAC,eAAe,CAAC,IACpCpB,IAAI,CAACoB,cAAc,CAAC,WAAW,CAAC,IAChCpB,IAAI,CAACoB,cAAc,CAAC,SAAS,CAAC,IAC7BpB,IAAI,CAACA,IAAI,IAAIkB,kBAAkB,CAAClB,IAAI,CAACA,IAAI,CAAE;IACrD;IAEA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAIC,WAAW,IAAK;IAAA,IAAAC,iBAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACzC,IAAI,CAACP,WAAW,EAAE,OAAO,IAAI;;IAE7B;IACA,MAAMQ,iBAAiB,GAAG,EAAAP,iBAAA,GAAA1C,OAAO,CAACkD,QAAQ,cAAAR,iBAAA,wBAAAC,qBAAA,GAAhBD,iBAAA,CAAkBS,UAAU,cAAAR,qBAAA,uBAA5BA,qBAAA,CAA8BS,WAAW,OAAAR,mBAAA,GAC1C5C,OAAO,CAACmD,UAAU,cAAAP,mBAAA,uBAAlBA,mBAAA,CAAoBQ,WAAW,OAAAP,kBAAA,GAC/B7C,OAAO,CAACkD,QAAQ,cAAAL,kBAAA,uBAAhBA,kBAAA,CAAkBO,WAAW,OAAAN,oBAAA,GAC7B9C,OAAO,CAACmD,UAAU,cAAAL,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBK,UAAU,cAAAJ,qBAAA,uBAA9BA,qBAAA,CAAgCK,WAAW;;IAEpE;IACA,IAAIX,WAAW,CAACY,OAAO,IAAIZ,WAAW,CAACtB,IAAI,EAAE;MAAA,IAAAmC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAE3C;MACA,MAAMC,2BAA2B,GAAG1D,OAAO,CAAC2D,qBAAqB,IAC9B3D,OAAO,CAAC4D,YAAY,MAAAN,qBAAA,GACpBtD,OAAO,CAAC6D,kBAAkB,cAAAP,qBAAA,uBAA1BA,qBAAA,CAA4BQ,QAAQ,OAAAP,mBAAA,GACpCvD,OAAO,CAAC+D,UAAU,cAAAR,mBAAA,uBAAlBA,mBAAA,CAAoBO,QAAQ,KAC3B9D,OAAO,CAACgE,OAAO,IAAIhE,OAAO,CAACgE,OAAO,CAACjD,MAAM,GAAG,CAAC,KAAKf,OAAO,CAAC2D,qBAAqB,IAAI3D,OAAO,CAAC4D,YAAY,CAAE;;MAE7I;MACA,MAAMK,WAAW,GAAG5B,kBAAkB,CAACI,WAAW,CAACtB,IAAI,CAAC;;MAExD;MACA,IAAI8B,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEiB,OAAO,IAAI,EAAAV,qBAAA,GAAAP,iBAAiB,CAACkB,aAAa,cAAAX,qBAAA,uBAA/BA,qBAAA,CAAiCzC,MAAM,IAAG,CAAC,IAAI,CAAC2C,2BAA2B,EAAE;QAAA,IAAAU,iBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,oBAAA;QAE7G;QACA,MAAMC,UAAU,GAAG,EAAAJ,iBAAA,GAAA3B,WAAW,CAACtB,IAAI,cAAAiD,iBAAA,uBAAhBA,iBAAA,CAAkBjD,IAAI,KAAIsB,WAAW,CAACtB,IAAI;;QAE7D;QACA,MAAMsD,sBAAsB,IAAAJ,sBAAA,GAAGpB,iBAAiB,CAACkB,aAAa,cAAAE,sBAAA,uBAA/BA,sBAAA,CAAiC/B,IAAI,CAACoC,MAAM;UAAA,IAAAC,mBAAA;UAAA,OACzE,GAAAA,mBAAA,GAACD,MAAM,CAACE,WAAW,cAAAD,mBAAA,eAAlBA,mBAAA,CAAoBT,OAAO;QAAA,CAC9B,CAAC;QAED,MAAMW,MAAM,GAAG,EAAAP,kBAAA,GAAAtE,OAAO,CAACkD,QAAQ,cAAAoB,kBAAA,uBAAhBA,kBAAA,CAAkBQ,GAAG,OAAAP,oBAAA,GAAIvE,OAAO,CAACmD,UAAU,cAAAoB,oBAAA,uBAAlBA,oBAAA,CAAoBO,GAAG;;QAE/D;QACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;UACrC,IAAIC,OAAO,GAAG,EAAE;;UAEhB;UACA,IAAI5D,KAAK,CAACC,OAAO,CAACmD,UAAU,CAAC,EAAE;YAC7BQ,OAAO,GAAGR,UAAU;UACtB,CAAC,MAAM,IAAIA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;YACvD,MAAMhD,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;YAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;cAC/B,IAAIgD,UAAU,CAAC/C,KAAK,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACmD,UAAU,CAAC/C,KAAK,CAAC,CAAC,EAAE;gBACzDuD,OAAO,GAAGR,UAAU,CAAC/C,KAAK,CAAC;gBAC3B;cACF;YACF;YACA,IAAIuD,OAAO,CAACjE,MAAM,KAAK,CAAC,EAAE;cACxBiE,OAAO,GAAG,CAACR,UAAU,CAAC;YACxB;UACF;;UAEA;UACA,IAAItD,kBAAkB,CAAC8D,OAAO,CAAC,EAAE;YAC/B,OAAOlD,mBAAmB,CAAC,CAAC;UAC9B;;UAEA;UACA,IAAImD,gBAAgB,GAAG,EAAE;UACzB,IAAIxC,WAAW,CAACyC,iBAAiB,EAAE;YACjC,MAAMC,cAAc,GAAG1C,WAAW,CAACyC,iBAAiB,CAACE,KAAK,CAAC,iBAAiB,CAAC;YAC7EH,gBAAgB,GAAGE,cAAc,CAACvE,MAAM,CAACyE,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;UACrE;UAEA,oBACExF,OAAA;YAAKiC,SAAS,EAAC,MAAM;YAAAC,QAAA,GAClBgD,OAAO,CAACO,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB3F,OAAA;cAAiBiC,SAAS,EAAC,MAAM;cAAAC,QAAA,GAE9BiD,gBAAgB,CAACQ,KAAK,CAAC,iBACtB3F,OAAA;gBAAKiC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7ClC,OAAA;kBAAKiC,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvDiD,gBAAgB,CAACQ,KAAK,CAAC,CAACH,IAAI,CAAC;gBAAC;kBAAArD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAAAa,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEiB,OAAO,KAAIjB,iBAAiB,CAACkB,aAAa,IAAI,CAAC1B,WAAW,CAACyC,iBAAiB,CAACvE,QAAQ,CAAC,mBAAmB,CAAC,iBAC5Hb,OAAA;gBAAKiC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxClC,OAAA,CAACF,wBAAwB;kBACvBuB,IAAI,EAAE,CAACqE,MAAM,CAAE,CAAC;kBAAA;kBAChBX,MAAM,EAAEA,MAAO;kBACf5B,iBAAiB,EAAEA,iBAAkB;kBACrC/C,mBAAmB,EAAEA,mBAAoB;kBACzCwF,eAAe,EAAE,IAAK;kBACtBC,kBAAkB,EAAE;oBAClBzB,OAAO,EAAE,KAAK;oBAAE;oBAChB0B,2BAA2B,EAAE;kBAC/B;gBAAE;kBAAA3D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA,GAzBOqD,KAAK;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA0BV,CACN,CAAC,EAGD6C,gBAAgB,CAAClE,MAAM,KAAK,CAAC,IAAI0B,WAAW,CAACyC,iBAAiB,iBAC7DpF,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAKiC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7ClC,OAAA;kBAAKiC,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvDS,WAAW,CAACyC;gBAAiB;kBAAAjD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,CAACK,WAAW,CAACyC,iBAAiB,CAACvE,QAAQ,CAAC,mBAAmB,CAAC,iBAC3Db,OAAA,CAACF,wBAAwB;gBACvBuB,IAAI,EAAEqD,UAAW;gBACjBK,MAAM,EAAEA,MAAO;gBACf5B,iBAAiB,EAAEA,iBAAkB;gBACrC/C,mBAAmB,EAAEA,mBAAoB;gBACzCwF,eAAe,EAAE,IAAK;gBACtBC,kBAAkB,EAAE;kBAClBzB,OAAO,EAAE,KAAK;kBAAE;kBAChB0B,2BAA2B,EAAE;gBAC/B;cAAE;gBAAA3D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC;QAED,OAAO2C,wBAAwB,CAAC,CAAC;MACnC;;MAEA;MACA,IAAItC,WAAW,CAACyC,iBAAiB,IAAI,EAAEjC,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEiB,OAAO,IAAI,EAAAT,sBAAA,GAAAR,iBAAiB,CAACkB,aAAa,cAAAV,sBAAA,uBAA/BA,sBAAA,CAAiC1C,MAAM,IAAG,CAAC,CAAC,EAAE;QACjH;QACA,IAAIG,kBAAkB,CAACuB,WAAW,CAACtB,IAAI,CAAC,EAAE;UACxC,OAAOW,mBAAmB,CAAC,CAAC;QAC9B;QAEA,oBACEhC,OAAA;UAAKiC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7ClC,OAAA;YAAKiC,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACvDS,WAAW,CAACyC;UAAiB;YAAAjD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;IACF;;IAEA;IACA,IAAIK,WAAW,CAACY,OAAO,IAAIZ,WAAW,CAACtB,IAAI,KAAKU,SAAS,IAAI,EAAEoB,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEiB,OAAO,IAAI,EAAAlB,sBAAA,GAAAC,iBAAiB,CAACkB,aAAa,cAAAnB,sBAAA,uBAA/BA,sBAAA,CAAiCjC,MAAM,IAAG,CAAC,CAAC,EAAE;MACzI;MACA,IAAIG,kBAAkB,CAACuB,WAAW,CAACtB,IAAI,CAAC,EAAE;QACxC,OAAOW,mBAAmB,CAAC,CAAC;MAC9B;;MAEA;MACA,oBACEhC,OAAA;QAAKiC,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpElC,OAAA;UAAKiC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvD6D,IAAI,CAACC,SAAS,CAACrD,WAAW,CAACtB,IAAI,EAAE,IAAI,EAAE,CAAC;QAAC;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;;IAEA;IACA,IAAIK,WAAW,CAACY,OAAO,IAAI,CAACZ,WAAW,CAACtB,IAAI,IAAI,CAACsB,WAAW,CAACyC,iBAAiB,IAAI,CAACzC,WAAW,CAACsD,KAAK,EAAE;MACpG,OAAOjE,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;IACA,IAAIW,WAAW,CAACsD,KAAK,EAAE;MACrB,oBACEjG,OAAA;QAAKiC,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5ClC,OAAA;UAAGiC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAES,WAAW,CAACsD;QAAK;UAAA9D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEV;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEtC,OAAA;IACEiC,SAAS,EAAE,QACT3B,MAAM,GAAG,aAAa,GAAG,eAAe,OAClC;IAAA4B,QAAA,eAERlC,OAAA;MACEiC,SAAS,EAAE,8BACT3B,MAAM,GACF,wCAAwC,GACxC,2CAA2C,EAC9C;MAAA4B,QAAA,GAGF,EAAEhC,OAAO,CAACyC,WAAW,IAAIzC,OAAO,CAACyC,WAAW,CAACyC,iBAAiB,CAAC,iBAC9DpF,OAAA;QAAGiC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAEhC,OAAO,CAACgG;MAAO;QAAA/D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CACxD,EAGA,CAAChC,MAAM,IAAIJ,OAAO,CAACgE,OAAO,IAAI5C,KAAK,CAACC,OAAO,CAACrB,OAAO,CAACgE,OAAO,CAAC,IAAIhE,OAAO,CAACgE,OAAO,CAACjD,MAAM,GAAG,CAAC,IAAI,CAACf,OAAO,CAAC4D,YAAY,IAAI,CAAC5D,OAAO,CAAC2D,qBAAqB,IAAI,EAAE3D,OAAO,CAACyC,WAAW,IAAIzC,OAAO,CAACyC,WAAW,CAACyC,iBAAiB,CAAC,iBACvNpF,OAAA;QAAKiC,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClBhC,OAAO,CAACiG,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAnG,OAAA;UAAAkC,QAAA,gBACElC,OAAA;YAAKiC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDhC,OAAO,CAACgE,OAAO,CAACuB,GAAG,CAAC,CAAC9E,MAAM,EAAEgF,KAAK,kBACjC3F,OAAA;cAEEoG,OAAO,EAAEA,CAAA,KAAM1F,oBAAoB,CAACC,MAAM,CAAE;cAC5CsB,SAAS,EAAE,kHACTzB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAAuB,QAAA,eAEHlC,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClC,OAAA;kBAAKiC,SAAS,EAAE,kEACdzB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAAuB,QAAA,EACA1B,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,iBAClCX,OAAA;oBAAKiC,SAAS,EAAC,wBAAwB;oBAACoE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAArE,QAAA,eAC3FlC,OAAA;sBAAMwG,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACL3B,MAAM;cAAA;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDqD,KAAK;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACL9B,kBAAkB,CAACS,MAAM,GAAG,CAAC,iBAC5BjB,OAAA;YAAKiC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBlC,OAAA;cACEoG,OAAO,EAAEpF,oBAAqB;cAC9BiB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAAC1B,kBAAkB,CAACS,MAAM,EAAC,GAC/C;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDtC,OAAA;YAAKiC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACAtC,OAAA;UAAKiC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7ChC,OAAO,CAACgE,OAAO,CAACuB,GAAG,CAAC,CAAC9E,MAAM,EAAEgF,KAAK,kBACjC3F,OAAA;YAEEoG,OAAO,EAAEA,CAAA,KAAMjF,wBAAwB,CAACR,MAAM,CAAE;YAChDsB,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/MvB;UAAM,GAJFgF,KAAK;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACApC,OAAO,CAAC0G,WAAW,IAAI1G,OAAO,CAAC2G,UAAU,iBACxC7G,OAAA;UAAKiC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,OACrC,EAAChC,OAAO,CAAC0G,WAAW,EAAC,MAAI,EAAC1G,OAAO,CAAC2G,UAAU;QAAA;UAAA1E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAAChC,MAAM,IAAIJ,OAAO,CAAC4D,YAAY,IAAI5D,OAAO,CAAC2D,qBAAqB,IAAI3D,OAAO,CAACgE,OAAO,IAAI5C,KAAK,CAACC,OAAO,CAACrB,OAAO,CAACgE,OAAO,CAAC,IAAIhE,OAAO,CAACgE,OAAO,CAACjD,MAAM,GAAG,CAAC,IAAI,EAAEf,OAAO,CAACyC,WAAW,IAAIzC,OAAO,CAACyC,WAAW,CAACyC,iBAAiB,CAAC,iBACrNpF,OAAA;QAAKiC,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClBhC,OAAO,CAACiG,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAnG,OAAA;UAAAkC,QAAA,gBACElC,OAAA;YAAKiC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDhC,OAAO,CAACgE,OAAO,CAACuB,GAAG,CAAC,CAAC9E,MAAM,EAAEgF,KAAK,kBACjC3F,OAAA;cAEEoG,OAAO,EAAEA,CAAA,KAAM1F,oBAAoB,CAACC,MAAM,CAAE;cAC5CsB,SAAS,EAAE,kHACTzB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAAuB,QAAA,eAEHlC,OAAA;gBAAKiC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChClC,OAAA;kBAAKiC,SAAS,EAAE,kEACdzB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAAuB,QAAA,EACA1B,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,iBAClCX,OAAA;oBAAKiC,SAAS,EAAC,wBAAwB;oBAACoE,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAArE,QAAA,eAC3FlC,OAAA;sBAAMwG,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAxE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACL3B,MAAM;cAAA;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDqD,KAAK;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACL9B,kBAAkB,CAACS,MAAM,GAAG,CAAC,iBAC5BjB,OAAA;YAAKiC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBlC,OAAA;cACEoG,OAAO,EAAEpF,oBAAqB;cAC9BiB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAAC1B,kBAAkB,CAACS,MAAM,EAAC,GAC/C;YAAA;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDtC,OAAA;YAAKiC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACAtC,OAAA;UAAKiC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7ChC,OAAO,CAACgE,OAAO,CAACuB,GAAG,CAAC,CAAC9E,MAAM,EAAEgF,KAAK,kBACjC3F,OAAA;YAEEoG,OAAO,EAAEA,CAAA,KAAMjF,wBAAwB,CAACR,MAAM,CAAE;YAChDsB,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/MvB;UAAM,GAJFgF,KAAK;YAAAxD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACApC,OAAO,CAAC0G,WAAW,IAAI1G,OAAO,CAAC4G,wBAAwB,iBACtD9G,OAAA;UAAKiC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,sBACtB,EAAChC,OAAO,CAAC0G,WAAW,EAAC,MAAI,EAAC1G,OAAO,CAAC4G,wBAAwB,EAC7E5G,OAAO,CAAC6G,cAAc,GAAG,CAAC,iBACzB/G,OAAA;YAAMiC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,GAClC,EAAChC,OAAO,CAAC6G,cAAc,EAAC,aAAW,EAAC7G,OAAO,CAAC6G,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,aAC/E;UAAA;YAAA5E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAKA,CAAChC,MAAM,IAAIJ,OAAO,CAAC8G,WAAW,KAAK,gBAAgB,IAAI9G,OAAO,CAACyC,WAAW,IAAIzC,OAAO,CAACyC,WAAW,CAACY,OAAO,iBACxGvD,OAAA;QAAKiC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnElC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAACoE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAArE,QAAA,eACjFlC,OAAA;cAAMwG,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,+BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAChC,MAAM,IAAIJ,OAAO,CAAC8G,WAAW,KAAK,sBAAsB,IAAI9G,OAAO,CAACyC,WAAW,IAAIzC,OAAO,CAACyC,WAAW,CAACY,OAAO,iBAC9GvD,OAAA;QAAKiC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnBlC,OAAA;UAAKiC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnElC,OAAA;YAAKiC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChClC,OAAA;cAAKiC,SAAS,EAAC,cAAc;cAACoE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAArE,QAAA,eACjFlC,OAAA;gBAAMwG,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgB;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,4CAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLpC,OAAO,CAAC+G,mBAAmB,iBAC1BjH,OAAA;UAAKiC,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/DlC,OAAA;YAAKiC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrClC,OAAA;cAAKiC,SAAS,EAAC,4BAA4B;cAACoE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAArE,QAAA,eAC/FlC,OAAA;gBAAMwG,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAAxE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL,CAAC,eACNtC,OAAA;cAAIiC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACNtC,OAAA;YAAKiC,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACvDhC,OAAO,CAAC+G,mBAAmB,CAACC,OAAO,CAAC,6CAA6C,EAAE,EAAE;UAAC;YAAA/E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAAChC,MAAM,IAAIJ,OAAO,CAAC8G,WAAW,KAAK,gBAAgB,IAAI9G,OAAO,CAACyC,WAAW,IAAI,CAACzC,OAAO,CAACyC,WAAW,CAACY,OAAO,iBACzGvD,OAAA;QAAKiC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/DlC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAACoE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAArE,QAAA,eACjFlC,OAAA;cAAMwG,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,0BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAChC,MAAM,IAAIJ,OAAO,CAAC8G,WAAW,KAAK,gBAAgB,iBAClDhH,OAAA;QAAKiC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/DlC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAACoE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAArE,QAAA,eACjFlC,OAAA;cAAMwG,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,kBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAChC,MAAM,IAAIJ,OAAO,CAAC8G,WAAW,KAAK,uBAAuB,IAAI9G,OAAO,CAACyC,WAAW,IAAIzC,OAAO,CAACyC,WAAW,CAACY,OAAO,iBAC/GvD,OAAA;QAAKiC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnElC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAACoE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAArE,QAAA,eACjFlC,OAAA;cAAMwG,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,4CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAChC,MAAM,IAAIJ,OAAO,CAAC8G,WAAW,KAAK,uBAAuB,IAAI9G,OAAO,CAACyC,WAAW,IAAI,CAACzC,OAAO,CAACyC,WAAW,CAACY,OAAO,iBAChHvD,OAAA;QAAKiC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/DlC,OAAA;UAAKiC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChClC,OAAA;YAAKiC,SAAS,EAAC,cAAc;YAACoE,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAArE,QAAA,eACjFlC,OAAA;cAAMwG,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAxE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,8CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAOApC,OAAO,CAACyC,WAAW,IAAI,CAACzC,OAAO,CAACyC,WAAW,CAACwE,YAAY,IAAIzE,iBAAiB,CAACxC,OAAO,CAACyC,WAAW,CAAC;IAAA;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsBhG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjC,EAAA,CA/hBIJ,WAAW;AAAAmH,EAAA,GAAXnH,WAAW;AAiiBjB,eAAeA,WAAW;AAAC,IAAAmH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}