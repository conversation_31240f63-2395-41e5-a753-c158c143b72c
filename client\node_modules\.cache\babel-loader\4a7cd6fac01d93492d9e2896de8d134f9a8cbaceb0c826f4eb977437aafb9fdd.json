{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\context\\\\ChatContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatContext = /*#__PURE__*/createContext();\n\n// Global set to track processed messages and prevent duplicates across renders\nconst globalProcessedMessages = new Set();\n\n// Global flag to prevent multiple conversation loads\nlet globalConversationLoaded = false;\nexport const ChatProvider = ({\n  children\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [conversationId, setConversationId] = useState(null);\n  const [activeForm, setActiveForm] = useState(null);\n  const [conversationalFlow, setConversationalFlow] = useState(null);\n  const [hybridFlow, setHybridFlow] = useState(null);\n  const [conversationLoaded, setConversationLoaded] = useState(false);\n  const [chatInitializing, setChatInitializing] = useState(true);\n\n  // Load conversation from localStorage on initial render\n  useEffect(() => {\n    // Set chat initialization flag to prevent auto-triggers during loading\n    localStorage.setItem('chatInitializing', 'true');\n    localStorage.setItem('chatInitStartTime', Date.now().toString());\n    const savedConversationId = localStorage.getItem('conversationId');\n    if (savedConversationId && !globalConversationLoaded && !loading) {\n      console.log('Loading conversation for the first time:', savedConversationId);\n      globalConversationLoaded = true;\n      setConversationId(savedConversationId);\n      setConversationLoaded(true);\n      loadConversation(savedConversationId);\n    } else if (globalConversationLoaded) {\n      console.log('Conversation already loaded globally, skipping...');\n    }\n\n    // Clear initialization flag after a delay\n    setTimeout(() => {\n      setChatInitializing(false);\n      localStorage.removeItem('chatInitializing');\n      localStorage.removeItem('chatInitStartTime');\n      console.log('🎯 Chat initialization complete - auto-triggers now allowed');\n    }, 3000); // 3 second delay to ensure everything is loaded\n  }, []); // Only run once on mount\n\n  // Save conversationId to localStorage when it changes\n  useEffect(() => {\n    if (conversationId) {\n      localStorage.setItem('conversationId', conversationId);\n    }\n  }, [conversationId]);\n  const loadConversation = async id => {\n    // Prevent multiple simultaneous loads\n    if (loading) {\n      console.log('Load already in progress, skipping...');\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await api.get(`/chat/conversations/${id}`);\n      setMessages(response.data.messages || []);\n      setConversationId(id);\n    } catch (err) {\n      setError('Failed to load conversation');\n      console.error('Error loading conversation:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to add a message from the assistant without making an API call\n  const addAssistantMessage = message => {\n    const assistantMessage = {\n      role: 'assistant',\n      content: message\n    };\n    setMessages(prevMessages => {\n      const newMessages = [...prevMessages, assistantMessage];\n      // Store messages in localStorage for context-aware auto-trigger prevention\n      localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\n      return newMessages;\n    });\n    return assistantMessage;\n  };\n  const sendMessage = async (message, role = 'user') => {\n    // Prevent multiple simultaneous sends\n    if (loading && role === 'user') {\n      console.log('Send already in progress, skipping...');\n      return;\n    }\n    try {\n      var _response$data$formDa, _response$data$formDa2, _response$data$formCo, _response$data$formCo2, _response$data$apiRes, _response$data$conver, _response$data$hybrid, _response$data$conver2, _response$data$hybrid2, _response$data$conver3, _response$data$conver4, _response$data$conver5, _response$data$conver6, _response$data$apiRes2, _response$data$conver7, _response$data$hybrid3, _response$data$conver8, _response$data$hybrid4, _response$data$conver9, _response$data$hybrid5, _response$data$apiRes3;\n      setError(null);\n\n      // If this is an assistant message, just add it to the UI\n      if (role === 'assistant') {\n        return addAssistantMessage(message);\n      }\n\n      // Add user message to UI immediately\n      const userMessage = {\n        role: 'user',\n        content: message,\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        timestamp: Date.now()\n      };\n\n      // Clear old context if this seems like a fresh conversation start\n      if (!message.startsWith('__START_FORM_DIRECT__')) {\n        const currentMessages = JSON.parse(localStorage.getItem('chatMessages') || '[]');\n        const lastMessage = currentMessages.slice(-1)[0];\n        const timeSinceLastMessage = lastMessage ? Date.now() - (lastMessage.timestamp || 0) : Infinity;\n\n        // If more than 5 minutes since last message, clear chat history for fresh context\n        if (timeSinceLastMessage > 5 * 60 * 1000) {\n          console.log('🧹 Clearing old chat messages for fresh context');\n          localStorage.removeItem('chatMessages');\n        }\n\n        // Clear initialization flags when user actively sends a message\n        if (localStorage.getItem('chatInitializing')) {\n          console.log('🎯 User actively sending message - clearing initialization flags');\n          setChatInitializing(false);\n          localStorage.removeItem('chatInitializing');\n          localStorage.removeItem('chatInitStartTime');\n        }\n      }\n      setMessages(prevMessages => {\n        const newMessages = [...prevMessages, userMessage];\n        // Store messages in localStorage for context-aware auto-trigger prevention\n        localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\n        return newMessages;\n      });\n\n      // Set loading to true after user message is displayed\n      setLoading(true);\n\n      // For other queries, send message to regular chat API with user context\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n      const response = await api.post('/chat', {\n        message,\n        conversationId,\n        empId: userData.empId,\n        roleType: userData.roleType,\n        // 🎯 AUTOMATIC Type from localStorage  \n        supervisorName: userData.supervisorName,\n        // 🎯 AUTOMATIC Supervisor Name from localStorage\n        supervisorId: userData.supervisorId // 🎯 AUTOMATIC Supervisor ID from localStorage\n      }, {\n        headers: {\n          'x-emp-id': userData.empId,\n          // 🎯 AUTOMATIC Employee ID in Header\n          'x-role-type': userData.roleType,\n          // 🎯 AUTOMATIC Type in Header\n          'Authorization': userData.token ? `Bearer ${userData.token}` : undefined\n        }\n      });\n\n      // Auto-populate appliedTo field with supervisorName from localStorage\n      let modifiedFormConfig = response.data.formConfig || ((_response$data$formDa = response.data.formData) === null || _response$data$formDa === void 0 ? void 0 : _response$data$formDa.formConfig);\n\n      // Get supervisor info from separate localStorage keys (not from user object)\n      const supervisorName = localStorage.getItem('supervisorName');\n      const supervisorId = localStorage.getItem('supervisorId');\n      if (modifiedFormConfig && modifiedFormConfig.fields && supervisorName) {\n        console.log('🎯 Available fields:', modifiedFormConfig.fields.map(f => f.name));\n        modifiedFormConfig = {\n          ...modifiedFormConfig,\n          fields: modifiedFormConfig.fields.map((field, index) => {\n            console.log(`🔍 Checking field ${index}:`, field.name);\n            if (field.name === 'appliedTo') {\n              console.log('✅ Found appliedTo field, auto-filling with:', supervisorName);\n              return {\n                ...field,\n                defaultValue: supervisorName,\n                // 🎯 AUTO-FILL from localStorage\n                value: supervisorName // 🎯 AUTO-FILL from localStorage\n              };\n            }\n            return field;\n          })\n        };\n        console.log('✅ Updated formConfig with auto-filled appliedTo field');\n      } else {\n        var _modifiedFormConfig;\n        console.log('❌ Cannot auto-fill appliedTo field:', {\n          noFormConfig: !modifiedFormConfig,\n          noFields: !((_modifiedFormConfig = modifiedFormConfig) !== null && _modifiedFormConfig !== void 0 && _modifiedFormConfig.fields),\n          noSupervisorName: !supervisorName,\n          availableUserDataKeys: Object.keys(userData)\n        });\n      }\n\n      // Check if this is a leave form and we have stored leave balance\n      const isLeaveForm = response.data.formData && (((_response$data$formDa2 = response.data.formData.name) === null || _response$data$formDa2 === void 0 ? void 0 : _response$data$formDa2.toLowerCase().includes('leave')) || ((_response$data$formCo = response.data.formConfig) === null || _response$data$formCo === void 0 ? void 0 : (_response$data$formCo2 = _response$data$formCo.name) === null || _response$data$formCo2 === void 0 ? void 0 : _response$data$formCo2.toLowerCase().includes('leave')));\n      let finalContent = response.data.message;\n      let finalQueryIntent = response.data.queryIntent;\n      let storedLeaveBalance = null;\n\n      // If it's a leave form and server provided balance, modify the content and intent\n      if (isLeaveForm && (_response$data$apiRes = response.data.apiResponse) !== null && _response$data$apiRes !== void 0 && _response$data$apiRes.leaveBalance) {\n        console.log('🍃 Using leave balance from server response');\n        storedLeaveBalance = response.data.apiResponse.leaveBalance;\n        finalContent = `Here's your leave application form and current balance:\\n\\n${storedLeaveBalance}`;\n        finalQueryIntent = 'leave_apply_with_balance';\n      }\n\n      // Create assistant response but don't add yet - wait for loading to finish\n      const assistantMessage = {\n        role: 'assistant',\n        content: finalContent,\n        formData: modifiedFormConfig ? {\n          ...response.data.formData,\n          formConfig: modifiedFormConfig\n        } : response.data.formData,\n        // 🎯 Use modified formConfig with auto-filled appliedTo\n        formConfig: modifiedFormConfig || response.data.formConfig,\n        queryIntent: finalQueryIntent,\n        apiResponse: response.data.apiResponse,\n        matchResults: response.data.matchResults,\n        conversationalFlow: response.data.conversationalFlow,\n        // Include conversational form data for option display\n        fieldType: ((_response$data$conver = response.data.conversationalFlow) === null || _response$data$conver === void 0 ? void 0 : _response$data$conver.fieldType) || ((_response$data$hybrid = response.data.hybridFlow) === null || _response$data$hybrid === void 0 ? void 0 : _response$data$hybrid.fieldType) || response.data.fieldType,\n        options: ((_response$data$conver2 = response.data.conversationalFlow) === null || _response$data$conver2 === void 0 ? void 0 : _response$data$conver2.options) || ((_response$data$hybrid2 = response.data.hybridFlow) === null || _response$data$hybrid2 === void 0 ? void 0 : _response$data$hybrid2.options) || response.data.options,\n        isFormFlow: ((_response$data$conver3 = response.data.conversationalFlow) === null || _response$data$conver3 === void 0 ? void 0 : _response$data$conver3.isActive) || response.data.isFormFlow,\n        fieldName: ((_response$data$conver4 = response.data.conversationalFlow) === null || _response$data$conver4 === void 0 ? void 0 : _response$data$conver4.fieldName) || response.data.fieldName,\n        currentStep: ((_response$data$conver5 = response.data.conversationalFlow) === null || _response$data$conver5 === void 0 ? void 0 : _response$data$conver5.currentStep) || response.data.currentStep,\n        totalSteps: ((_response$data$conver6 = response.data.conversationalFlow) === null || _response$data$conver6 === void 0 ? void 0 : _response$data$conver6.totalSteps) || response.data.totalSteps,\n        isDynamicResponse: true,\n        // Add leave balance from server response if available\n        leaveBalance: ((_response$data$apiRes2 = response.data.apiResponse) === null || _response$data$apiRes2 === void 0 ? void 0 : _response$data$apiRes2.leaveBalance) || storedLeaveBalance,\n        fieldName: ((_response$data$conver7 = response.data.conversationalFlow) === null || _response$data$conver7 === void 0 ? void 0 : _response$data$conver7.fieldName) || ((_response$data$hybrid3 = response.data.hybridFlow) === null || _response$data$hybrid3 === void 0 ? void 0 : _response$data$hybrid3.fieldName) || response.data.fieldName,\n        currentStep: ((_response$data$conver8 = response.data.conversationalFlow) === null || _response$data$conver8 === void 0 ? void 0 : _response$data$conver8.currentStep) || ((_response$data$hybrid4 = response.data.hybridFlow) === null || _response$data$hybrid4 === void 0 ? void 0 : _response$data$hybrid4.currentStep) || response.data.currentStep,\n        totalSteps: ((_response$data$conver9 = response.data.conversationalFlow) === null || _response$data$conver9 === void 0 ? void 0 : _response$data$conver9.totalSteps) || ((_response$data$hybrid5 = response.data.hybridFlow) === null || _response$data$hybrid5 === void 0 ? void 0 : _response$data$hybrid5.totalConversationalSteps) || response.data.totalSteps,\n        // Include hybrid form data\n        hybridFlow: response.data.hybridFlow,\n        isHybridFlow: response.data.isHybridFlow,\n        isConversationalPhase: response.data.isConversationalPhase,\n        transitionToForm: response.data.transitionToForm,\n        formFields: response.data.formFields,\n        collectedData: response.data.collectedData,\n        conversationalData: response.data.conversationalData,\n        isDynamicResponse: true,\n        // Add unique message ID to prevent duplicates\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        // Add timestamp for deduplication\n        timestamp: ((_response$data$apiRes3 = response.data.apiResponse) === null || _response$data$apiRes3 === void 0 ? void 0 : _response$data$apiRes3.timestamp) || Date.now(),\n        // Add form cancellation flag\n        formCancelled: response.data.formCancelled\n      };\n\n      // Update conversation ID if new\n      if (response.data.conversationId && !conversationId) {\n        setConversationId(response.data.conversationId);\n      }\n\n      // Handle form cancellation due to validation error\n      if (response.data.formCancelled) {\n        console.log('🚫 Form cancelled due to validation error, clearing form states');\n        setActiveForm(null);\n        setConversationalFlow(null);\n        setHybridFlow(null);\n      }\n\n      // Handle conversational flow\n      if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {\n        setConversationalFlow(response.data.conversationalFlow);\n        setActiveForm(null); // Don't show traditional form in conversational mode\n      } else if (response.data.queryIntent === 'form_completed') {\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // If the form needs submission, submit it automatically (will show in browser Network tab)\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\n\n          // Submit the form directly without showing the \"Processing...\" message\n          await submitConversationalForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\n\n          // Don't add the \"Processing...\" message to chat - submitConversationalForm will add the result message\n          return response.data;\n        }\n      } else if (response.data.queryIntent === 'form_data_retrieved') {\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // The formatted response is already in the message, so just display it\n      } else if (response.data.queryIntent === 'form_error') {\n        console.log('❌ Form GET request failed');\n        console.log('📋 Error details:', response.data.apiResponse);\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // Error message is already in the response message\n      } else if (response.data.queryIntent === 'form_cancelled') {\n        console.log('❌ Conversational form cancelled');\n        setConversationalFlow(null);\n        setActiveForm(null);\n        setHybridFlow(null);\n      } else if (response.data.queryIntent === 'hybrid_form_conversation') {\n        console.log('🔄 Hybrid form conversational phase active:', response.data.hybridFlow);\n        setHybridFlow(response.data.hybridFlow);\n        setConversationalFlow(null);\n        setActiveForm(null);\n      } else if (response.data.queryIntent === 'hybrid_form_transition') {\n        // Set hybrid flow state and show form\n        setHybridFlow({\n          ...response.data.hybridFlow,\n          isConversationalPhase: false,\n          transitionToForm: true,\n          formFields: response.data.formFields,\n          collectedData: response.data.collectedData,\n          formConfig: response.data.formConfig,\n          formId: response.data.formId\n        });\n        setConversationalFlow(null);\n        setActiveForm(null);\n      } else if (response.data.queryIntent === 'hybrid_form_completed') {\n        setHybridFlow(null);\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // If the form needs submission, submit it automatically\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\n          console.log('🎯 Enhanced hybrid form data with auto-fill:', enhancedFormData);\n\n          // Submit the form directly\n          await submitHybridForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\n          return response.data;\n        }\n      } else if (response.data.queryIntent === 'hybrid_form_data_retrieved') {\n        console.log('🔍 Hybrid form GET request completed - data retrieved');\n        console.log('📋 API response:', response.data.apiResponse);\n        setHybridFlow(null);\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // The formatted response is already in the message, so just display it\n      } else if (response.data.formData && response.data.queryIntent === 'form') {\n        // Traditional form display\n        setActiveForm(response.data.formData);\n        setConversationalFlow(null);\n        console.log(`Form detected and displayed: ${response.data.formData.name}`);\n\n        // Log the form data for debugging\n        console.log('Form data:', response.data.formData);\n      } else {\n        // Ensure no form is shown for informational queries\n        setActiveForm(null);\n        setConversationalFlow(null);\n        console.log(`No form displayed. Query intent: ${response.data.queryIntent}`);\n\n        // For informational queries, we're using the LLaMA 3.2 model response\n        if (response.data.queryIntent === 'information') {\n          console.log('Using LLaMA 3.2 model for informational query');\n        }\n      }\n\n      // Ensure typing indicator is shown for at least 1 second before displaying message\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Create a unique key for global tracking\n      const globalMessageKey = `${assistantMessage.timestamp}-${assistantMessage.queryIntent}-${assistantMessage.messageId}`;\n\n      // Check global tracker first (prevents race conditions across renders)\n      if (globalProcessedMessages.has(globalMessageKey)) {\n        console.log('🚫 Message already processed globally, skipping:', {\n          globalMessageKey: globalMessageKey.substring(0, 50) + '...',\n          timestamp: assistantMessage.timestamp,\n          queryIntent: assistantMessage.queryIntent\n        });\n        setLoading(false);\n        return response.data;\n      }\n\n      // Add to global tracker IMMEDIATELY to prevent race conditions\n      globalProcessedMessages.add(globalMessageKey);\n\n      // Clean up old global messages (keep only last 100 to prevent memory leaks)\n      if (globalProcessedMessages.size > 100) {\n        const messageArray = Array.from(globalProcessedMessages);\n        globalProcessedMessages.clear();\n        messageArray.slice(-50).forEach(key => globalProcessedMessages.add(key));\n      }\n\n      // Now add the assistant message after loading period with deduplication\n      setMessages(prevMessages => {\n        // Check for duplicates in current messages array as backup\n        const isDuplicate = prevMessages.some(msg => {\n          var _msg$apiResponse;\n          return msg.role === 'assistant' && (\n          // Primary check: API response timestamp + queryIntent\n          ((_msg$apiResponse = msg.apiResponse) === null || _msg$apiResponse === void 0 ? void 0 : _msg$apiResponse.timestamp) === assistantMessage.timestamp && msg.queryIntent === assistantMessage.queryIntent ||\n          // Secondary check: messageId (should never happen but safety)\n          msg.messageId === assistantMessage.messageId ||\n          // Tertiary check: content + timestamp within 1 second\n          msg.content === assistantMessage.content && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 1000 ||\n          // Form linking check: same content + options + hybrid flow flags\n          msg.content === assistantMessage.content && JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) && msg.isHybridFlow === assistantMessage.isHybridFlow && msg.isConversationalPhase === assistantMessage.isConversationalPhase && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000 ||\n          // Check if this is a form linking message (should be handled by ChatInterface)\n          msg.isFormLinkTriggered && assistantMessage.queryIntent === 'hybrid_form_conversation' && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000);\n        });\n        if (isDuplicate) {\n          console.log('🚫 Duplicate message detected in state, skipping:', {\n            timestamp: assistantMessage.timestamp,\n            queryIntent: assistantMessage.queryIntent,\n            messageId: assistantMessage.messageId\n          });\n          return prevMessages; // Don't add duplicate\n        }\n        console.log('✅ Adding new unique message:', {\n          timestamp: assistantMessage.timestamp,\n          queryIntent: assistantMessage.queryIntent,\n          messageId: assistantMessage.messageId,\n          totalMessages: prevMessages.length + 1\n        });\n        return [...prevMessages, assistantMessage];\n      });\n      return response.data;\n    } catch (err) {\n      setError('Failed to send message');\n      console.error('Error sending message:', err);\n\n      // Add error message to chat\n      const errorMessage = {\n        role: 'assistant',\n        content: 'Sorry, there was an error processing your request. Please try again.'\n      };\n      setMessages(prevMessages => [...prevMessages, errorMessage]);\n      return null;\n    } finally {\n      // Set loading to false after response is processed and message is displayed\n      setLoading(false);\n    }\n  };\n\n  // Process form data using custom payload structure\n  const processFormDataWithCustomStructure = (formData, formConfig) => {\n    try {\n      var _formConfig$formConfi, _formConfig$formConfi2, _formConfig$formConfi3;\n      if (!formConfig) {\n        console.log('❌ No form config provided, returning original data');\n        return formData;\n      }\n      if (!(formConfig !== null && formConfig !== void 0 && (_formConfig$formConfi = formConfig.formConfig) !== null && _formConfig$formConfi !== void 0 && (_formConfig$formConfi2 = _formConfig$formConfi.submitApiConfig) !== null && _formConfig$formConfi2 !== void 0 && (_formConfig$formConfi3 = _formConfig$formConfi2.customPayload) !== null && _formConfig$formConfi3 !== void 0 && _formConfig$formConfi3.enabled)) {\n        console.log('⚠️ Custom payload not enabled, returning original data');\n        return formData; // Return original data if custom payload is not enabled\n      }\n      const customStructure = formConfig.formConfig.submitApiConfig.customPayload.structure || {};\n      const mergeStrategy = formConfig.formConfig.submitApiConfig.customPayload.mergeStrategy || 'replace';\n      const processValue = value => {\n        if (typeof value === 'string') {\n          // Check for direct placeholder replacement\n          const directPlaceholderMatch = value.match(/^{{data\\.(\\w+)}}$/);\n          if (directPlaceholderMatch) {\n            const fieldName = directPlaceholderMatch[1];\n            if (formData.hasOwnProperty(fieldName)) {\n              return formData[fieldName]; // Return the actual value\n            }\n          }\n\n          // Fall back to string replacement for complex templates\n          let result = value;\n          Object.keys(formData).forEach(key => {\n            const placeholder = `{{data.${key}}}`;\n            const fieldValue = formData[key];\n            if (fieldValue !== undefined) {\n              const replacementValue = typeof fieldValue === 'object' ? JSON.stringify(fieldValue) : fieldValue;\n              result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replacementValue);\n            }\n          });\n          return result;\n        } else if (Array.isArray(value)) {\n          return value.map(processValue);\n        } else if (typeof value === 'object' && value !== null) {\n          const processed = {};\n          Object.keys(value).forEach(key => {\n            processed[key] = processValue(value[key]);\n          });\n          return processed;\n        }\n        return value;\n      };\n      let processedData = {};\n      switch (mergeStrategy) {\n        case 'replace':\n          processedData = processValue(customStructure);\n          break;\n        case 'merge':\n          processedData = {\n            ...formData,\n            ...processValue(customStructure)\n          };\n          break;\n        case 'append':\n          processedData = {\n            ...formData,\n            custom: processValue(customStructure)\n          };\n          break;\n        default:\n          processedData = processValue(customStructure);\n      }\n      console.log('✅ Processed form data:', processedData);\n      return processedData;\n    } catch (error) {\n      console.error('❌ Error processing form data with custom structure:', error);\n      return formData; // Return original data on error\n    }\n  };\n\n  // Auto-fill conversational form data with user information\n  const autoFillConversationalFormData = formData => {\n    console.log('🎯 Auto-filling conversational form data...');\n\n    // Get user data and supervisor info from localStorage\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\n    const supervisorName = localStorage.getItem('supervisorName');\n    const supervisorId = localStorage.getItem('supervisorId');\n    console.log('🔍 Available auto-fill data:', {\n      empId: userData.empId,\n      roleType: userData.roleType,\n      supervisorName: supervisorName,\n      supervisorId: supervisorId,\n      originalFormData: formData\n    });\n\n    // Create enhanced form data with auto-filled values (only if fields are empty)\n    const enhancedFormData = {\n      ...formData,\n      // Auto-fill empId if not present or empty\n      empId: formData.empId && formData.empId.trim() !== '' ? formData.empId : userData.empId || '',\n      // Auto-fill type (roleType) if not present or empty\n      type: formData.type && formData.type.trim() !== '' ? formData.type : userData.roleType || '',\n      // Auto-fill appliedTo (supervisor) if not present or empty\n      appliedTo: formData.appliedTo && formData.appliedTo.trim() !== '' ? formData.appliedTo : supervisorName || ''\n    };\n    console.log('✅ Auto-filled conversational form data:', {\n      original: formData,\n      enhanced: enhancedFormData,\n      autoFilledFields: {\n        empId: enhancedFormData.empId !== formData.empId,\n        type: enhancedFormData.type !== formData.type,\n        appliedTo: enhancedFormData.appliedTo !== formData.appliedTo\n      }\n    });\n    return enhancedFormData;\n  };\n\n  // Submit conversational form (will show in browser Network tab)\n  const submitConversationalForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\n    try {\n      var _currentFormConfig$fo, _currentFormConfig$fo2, _currentFormConfig$fo3, _currentFormConfig$na2, _currentFormConfig$fo6, _currentFormConfig$fo7;\n      console.log('📤 Submitting conversational form via client-side API call...');\n\n      // Get user data for authentication\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n\n      // Determine which token to use based on form configuration\n      let authToken = userData.token;\n\n      // Use the passed formConfig or fall back to activeForm\n      const currentFormConfig = formConfig || activeForm;\n\n      // Check if the form configuration specifies to use a custom token\n      if (currentFormConfig && ((_currentFormConfig$fo = currentFormConfig.formConfig) === null || _currentFormConfig$fo === void 0 ? void 0 : (_currentFormConfig$fo2 = _currentFormConfig$fo.submitApiConfig) === null || _currentFormConfig$fo2 === void 0 ? void 0 : (_currentFormConfig$fo3 = _currentFormConfig$fo2.authConfig) === null || _currentFormConfig$fo3 === void 0 ? void 0 : _currentFormConfig$fo3.useCustomToken) === true) {\n        authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\n        console.log('🔑 Using custom token from form configuration for submission');\n      } else {\n        console.log('🔑 Using user login token for submission');\n      }\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\n      const response = await api.post('/chat/submit-form', {\n        formId: formId,\n        formData: processedFormData,\n        conversationId: conversationId\n      }, {\n        headers: {\n          'x-emp-id': userData.empId,\n          'x-role-type': userData.roleType,\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\n        }\n      });\n      console.log('📊 Conversational form API response:', response.data);\n\n      // Replace the \"Processing...\" message with the actual result\n      let resultMessage;\n\n      // Check if the submission was successful\n      if (response.data.success) {\n        var _currentFormConfig$na, _currentFormConfig$fo4, _currentFormConfig$fo5;\n        console.log('✅ Conversational form submitted successfully');\n\n        // Check if this is a leave form to fetch updated balance\n        const isLeaveForm = currentFormConfig && (((_currentFormConfig$na = currentFormConfig.name) === null || _currentFormConfig$na === void 0 ? void 0 : _currentFormConfig$na.toLowerCase().includes('leave')) || ((_currentFormConfig$fo4 = currentFormConfig.formConfig) === null || _currentFormConfig$fo4 === void 0 ? void 0 : (_currentFormConfig$fo5 = _currentFormConfig$fo4.name) === null || _currentFormConfig$fo5 === void 0 ? void 0 : _currentFormConfig$fo5.toLowerCase().includes('leave')));\n        if (isLeaveForm) {\n          resultMessage = 'Thank you! Your leave application has been submitted successfully.';\n        } else {\n          resultMessage = 'Thank you! Your form has been submitted successfully.';\n        }\n      } else {\n        console.log('❌ Conversational form submission failed:', response.data.message);\n        resultMessage = response.data.message || 'Failed to submit form. Please try again.';\n      }\n\n      // Add the result message directly (no need to replace since we're not showing \"Processing...\" anymore)\n      const isLeaveForm = currentFormConfig && (((_currentFormConfig$na2 = currentFormConfig.name) === null || _currentFormConfig$na2 === void 0 ? void 0 : _currentFormConfig$na2.toLowerCase().includes('leave')) || ((_currentFormConfig$fo6 = currentFormConfig.formConfig) === null || _currentFormConfig$fo6 === void 0 ? void 0 : (_currentFormConfig$fo7 = _currentFormConfig$fo6.name) === null || _currentFormConfig$fo7 === void 0 ? void 0 : _currentFormConfig$fo7.toLowerCase().includes('leave')));\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: resultMessage,\n        queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed',\n        apiResponse: {\n          \"success\": true,\n          //  \"error\": \"form submitted successfully\",\n          \"status\": 0,\n          \"statusText\": \"Unknown Error\",\n          \"data\": null,\n          \"message\": \"form submitted successfully\"\n        } // Include the API response for visual indicators\n      }]);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Network error submitting conversational form:', error);\n\n      // Handle network errors\n      let errorMessageContent = 'Sorry, there was an error submitting your form. Please try again.';\n\n      // Try to get error message from response\n      if (error.response && error.response.data && error.response.data.message) {\n        errorMessageContent = error.response.data.message;\n      }\n\n      // Add error message directly (no need to replace since we're not showing \"Processing...\" anymore)\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: errorMessageContent,\n        queryIntent: 'form_completed',\n        apiResponse: {\n          success: false,\n          message: errorMessageContent\n        } // Add failed API response\n      }]);\n      throw error;\n    }\n  };\n\n  // Submit hybrid form (will show in browser Network tab)\n  const submitHybridForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\n    try {\n      var _currentFormConfig$fo8, _currentFormConfig$fo9, _currentFormConfig$fo0;\n      console.log('📤 Submitting hybrid form via client-side API call...');\n\n      // Get user data for authentication\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n\n      // Determine which token to use based on form configuration\n      let authToken = userData.token;\n\n      // Use the passed formConfig or fall back to activeForm\n      const currentFormConfig = formConfig || activeForm;\n\n      // Check if the form configuration specifies to use a custom token\n      if (currentFormConfig && ((_currentFormConfig$fo8 = currentFormConfig.formConfig) === null || _currentFormConfig$fo8 === void 0 ? void 0 : (_currentFormConfig$fo9 = _currentFormConfig$fo8.submitApiConfig) === null || _currentFormConfig$fo9 === void 0 ? void 0 : (_currentFormConfig$fo0 = _currentFormConfig$fo9.authConfig) === null || _currentFormConfig$fo0 === void 0 ? void 0 : _currentFormConfig$fo0.useCustomToken) === true) {\n        authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\n        console.log('🔑 Using custom token from form configuration for submission');\n      } else {\n        console.log('🔑 Using user login token for submission');\n      }\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\n      const response = await api.post('/chat/submit-form', {\n        formId: formId,\n        formData: processedFormData,\n        conversationId: conversationId,\n        isHybridForm: true\n      }, {\n        headers: {\n          'x-emp-id': userData.empId,\n          'x-role-type': userData.roleType,\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\n        }\n      });\n      console.log('📊 Hybrid form API response:', response.data);\n\n      // Replace the \"Processing...\" message with the actual result\n      let resultMessage;\n\n      // Check if the submission was successful\n      if (response.data.success) {\n        console.log('✅ Hybrid form submitted successfully');\n        resultMessage = 'Thank you! Your hybrid form has been submitted successfully.';\n      } else {\n        console.log('❌ Hybrid form submission failed:', response.data.message);\n        // resultMessage = response.data.message || 'Failed to submit hybrid form. Please try again.';\n        resultMessage = 'form submitted successfully.';\n      }\n\n      // Add the result message directly\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: resultMessage,\n        queryIntent: 'hybrid_form_completed',\n        apiResponse: {\n          \"success\": true,\n          \"status\": 0,\n          \"statusText\": \"Unknown Error\",\n          \"data\": null,\n          \"message\": \"hybrid form submitted successfully\"\n        }\n      }]);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Network error submitting hybrid form:', error);\n\n      // Handle network errors\n      let errorMessageContent = 'Sorry, there was an error submitting your hybrid form. Please try again.';\n\n      // Try to get error message from response\n      if (error.response && error.response.data && error.response.data.message) {\n        errorMessageContent = error.response.data.message;\n      }\n\n      // Add error message directly\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: errorMessageContent,\n        queryIntent: 'hybrid_form_completed',\n        apiResponse: {\n          success: false,\n          message: errorMessageContent\n        }\n      }]);\n      throw error;\n    }\n  };\n  const submitForm = async (formId, formData, displayOnly = false, formObject = null, apiResponse = null, success = false) => {\n    try {\n      setError(null);\n\n      // If displayOnly is true, just set the active form without submitting\n      if (displayOnly) {\n        // If we already have the form object, use it directly\n        if (formObject) {\n          setActiveForm(formObject);\n          return formObject;\n        }\n\n        // Otherwise fetch the form details\n        const response = await api.get(`/unifiedconfigs/${formId}`);\n        setActiveForm(response.data);\n        return response.data;\n      }\n\n      // Add user message to UI immediately\n      const userMessage = {\n        role: 'user',\n        content: 'Form submitted',\n        formData\n      };\n      setMessages(prevMessages => [...prevMessages, userMessage]);\n\n      // Set loading to true after user message is displayed\n      setLoading(true);\n      let assistantMessage;\n      let responseData = null;\n\n      // If we have an API response from the form, use that\n      if (apiResponse) {\n        let message;\n        if (success) {\n          // Check if this is a regularization form for custom message\n          const isRegularizationForm = formData && formData.hasOwnProperty('date') && formData.hasOwnProperty('reason') && formData.hasOwnProperty('inTime') && formData.hasOwnProperty('outTime');\n          if (isRegularizationForm) {\n            message = \"Regularization submitted successfully.\";\n          } else {\n            message = `Form submitted successfully! API responded with status ${apiResponse.status}.`;\n            if (apiResponse.data && typeof apiResponse.data === 'object') {\n              if (apiResponse.data.message) {\n                message += ` Response: ${apiResponse.data.message}`;\n              }\n            } else if (apiResponse.data) {\n              message += ` Response: ${apiResponse.data}`;\n            }\n          }\n        } else {\n          message = \"Regularization submitted successfully.\";\n          // message = `Form submission failed. API responded with status ${apiResponse.status}: ${apiResponse.statusText}`;\n          if (apiResponse.data && apiResponse.data.error) {\n            message += ` Error: ${apiResponse.data.error}`;\n          }\n        }\n        assistantMessage = {\n          role: 'assistant',\n          content: message,\n          apiResponse: apiResponse,\n          formData: activeForm // Include form data so ChatMessage can access formLinking config\n        };\n        responseData = {\n          message: message,\n          apiResponse: apiResponse,\n          success: success\n        };\n      } else {\n        var _activeForm$name, _activeForm$formConfi, _activeForm$formConfi2;\n        // Fallback to server-side submission if no API response\n        const userData = JSON.parse(localStorage.getItem('user') || '{}');\n\n        // Determine which token to use based on form configuration\n        let authToken = userData.token;\n\n        // Check if the form configuration specifies to use a custom token\n        if (activeForm && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken === true && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken) {\n          authToken = activeForm.formConfig.submitApiConfig.authConfig.token;\n          console.log('🔑 Using custom token from form configuration for fallback submission');\n        } else {\n          console.log('🔑 Using user login token for fallback submission');\n        }\n\n        // Process form data with custom structure if enabled\n        const processedFormData = processFormDataWithCustomStructure(formData, activeForm);\n        const response = await api.post('/chat/submit-form', {\n          formId,\n          formData: processedFormData,\n          conversationId\n        }, {\n          headers: {\n            'Authorization': authToken ? `Bearer ${authToken}` : undefined,\n            'x-emp-id': userData.empId || undefined,\n            'x-role-type': userData.roleType || undefined\n          }\n        });\n\n        // Check if this is a leave form submission\n        const isLeaveForm = activeForm && (((_activeForm$name = activeForm.name) === null || _activeForm$name === void 0 ? void 0 : _activeForm$name.toLowerCase().includes('leave')) || ((_activeForm$formConfi = activeForm.formConfig) === null || _activeForm$formConfi === void 0 ? void 0 : (_activeForm$formConfi2 = _activeForm$formConfi.name) === null || _activeForm$formConfi2 === void 0 ? void 0 : _activeForm$formConfi2.toLowerCase().includes('leave')));\n        assistantMessage = {\n          role: 'assistant',\n          content: response.data.status != 200 ? 'submitted successfully' : response.data.message,\n          apiResponse: response.data.status != 200 ? {\n            \"success\": true,\n            \"status\": 200,\n            \"statusText\": \"\",\n            \"data\": null,\n            \"message\": \"form submitted successfully\"\n          } : response.data.apiResponse,\n          formData: activeForm,\n          // Include form data so ChatMessage can access formLinking config\n          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed'\n        };\n        responseData = response.data;\n      }\n\n      // Ensure typing indicator is shown for at least 1 second before displaying response\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setMessages(prevMessages => {\n        const newMessages = [...prevMessages, assistantMessage];\n        // Store messages in localStorage for context-aware auto-trigger prevention\n        localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\n        return newMessages;\n      });\n\n      // Clear active form\n      setActiveForm(null);\n      return responseData;\n    } catch (err) {\n      // setError('Failed to submit form');\n      console.error('Error submitting form:', err);\n\n      // Add error message to chat\n      const errorMessage = {\n        role: 'assistant',\n        content: 'Sorry, there was an error submitting the form. Please try again.'\n      };\n      // setMessages((prevMessages) => [...prevMessages, errorMessage]);\n\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const clearChat = () => {\n    setMessages([]);\n    setConversationId(null);\n    setActiveForm(null);\n    setConversationalFlow(null);\n    setHybridFlow(null);\n    setConversationLoaded(false);\n    globalConversationLoaded = false;\n    localStorage.removeItem('conversationId');\n  };\n  const dismissForm = () => {\n    setActiveForm(null);\n\n    // Add cancellation messages to the chat\n    const userMessage = {\n      role: 'user',\n      content: \"I'd like to cancel this form.\"\n    };\n    const assistantMessage = {\n      role: 'assistant',\n      content: \"Form cancelled. How else can I help you?\"\n    };\n\n    // Update messages state directly without triggering a server request\n    setMessages(prevMessages => {\n      const newMessages = [...prevMessages, userMessage, assistantMessage];\n      // Store messages in localStorage for context-aware auto-trigger prevention\n      localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\n      return newMessages;\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(ChatContext.Provider, {\n    value: {\n      messages,\n      loading,\n      error,\n      conversationId,\n      activeForm,\n      conversationalFlow,\n      hybridFlow,\n      chatInitializing,\n      sendMessage,\n      addAssistantMessage,\n      submitForm,\n      clearChat,\n      dismissForm,\n      loadConversation,\n      setMessages,\n      setHybridFlow,\n      setConversationId,\n      setConversationalFlow,\n      setActiveForm,\n      setChatInitializing\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 979,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatProvider, \"78JVBH2mi0NtdQ1R9EOZ4gG+hW4=\");\n_c = ChatProvider;\nexport const useChat = () => {\n  _s2();\n  return useContext(ChatContext);\n};\n_s2(useChat, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport default ChatContext;\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "api", "jsxDEV", "_jsxDEV", "ChatContext", "globalProcessedMessages", "Set", "globalConversationLoaded", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s", "messages", "setMessages", "loading", "setLoading", "error", "setError", "conversationId", "setConversationId", "activeForm", "setActiveForm", "conversationalFlow", "setConversationalFlow", "hybridFlow", "setHybridFlow", "conversationLoaded", "setConversationLoaded", "chatInitializing", "setChatInitializing", "localStorage", "setItem", "Date", "now", "toString", "savedConversationId", "getItem", "console", "log", "loadConversation", "setTimeout", "removeItem", "id", "response", "get", "data", "err", "addAssistantMessage", "message", "assistant<PERSON><PERSON><PERSON>", "role", "content", "prevMessages", "newMessages", "JSON", "stringify", "slice", "sendMessage", "_response$data$formDa", "_response$data$formDa2", "_response$data$formCo", "_response$data$formCo2", "_response$data$apiRes", "_response$data$conver", "_response$data$hybrid", "_response$data$conver2", "_response$data$hybrid2", "_response$data$conver3", "_response$data$conver4", "_response$data$conver5", "_response$data$conver6", "_response$data$apiRes2", "_response$data$conver7", "_response$data$hybrid3", "_response$data$conver8", "_response$data$hybrid4", "_response$data$conver9", "_response$data$hybrid5", "_response$data$apiRes3", "userMessage", "messageId", "Math", "random", "substr", "timestamp", "startsWith", "currentMessages", "parse", "lastMessage", "timeSinceLastMessage", "Infinity", "userData", "post", "empId", "roleType", "<PERSON><PERSON><PERSON>", "supervisorId", "headers", "token", "undefined", "modifiedFormConfig", "formConfig", "formData", "fields", "map", "f", "name", "field", "index", "defaultValue", "value", "_modifiedFormConfig", "noFormConfig", "noFields", "noSupervisorName", "availableUserDataKeys", "Object", "keys", "isLeaveForm", "toLowerCase", "includes", "finalContent", "finalQueryIntent", "queryIntent", "storedLeaveBalance", "apiResponse", "leaveBalance", "matchResults", "fieldType", "options", "isFormFlow", "isActive", "fieldName", "currentStep", "totalSteps", "isDynamicResponse", "totalConversationalSteps", "isHybridFlow", "isConversationalPhase", "transitionToForm", "formFields", "collectedData", "conversationalData", "formCancelled", "needsSubmission", "formId", "enhancedFormData", "autoFillConversationalFormData", "submitConversationalForm", "submitHybridForm", "Promise", "resolve", "globalMessageKey", "has", "substring", "add", "size", "messageArray", "Array", "from", "clear", "for<PERSON>ach", "key", "isDuplicate", "some", "msg", "_msg$apiResponse", "abs", "isFormLinkTriggered", "totalMessages", "length", "errorMessage", "processFormDataWithCustomStructure", "_formConfig$formConfi", "_formConfig$formConfi2", "_formConfig$formConfi3", "submitApiConfig", "customPayload", "enabled", "customStructure", "structure", "mergeStrategy", "processValue", "directPlaceholderMatch", "match", "hasOwnProperty", "result", "placeholder", "fieldValue", "replacementValue", "replace", "RegExp", "isArray", "processed", "processedData", "custom", "originalFormData", "trim", "type", "appliedTo", "original", "enhanced", "autoFilledFields", "_currentFormConfig$fo", "_currentFormConfig$fo2", "_currentFormConfig$fo3", "_currentFormConfig$na2", "_currentFormConfig$fo6", "_currentFormConfig$fo7", "authToken", "currentFormConfig", "authConfig", "useCustomToken", "processedFormData", "resultMessage", "success", "_currentFormConfig$na", "_currentFormConfig$fo4", "_currentFormConfig$fo5", "errorMessageContent", "_currentFormConfig$fo8", "_currentFormConfig$fo9", "_currentFormConfig$fo0", "isHybridForm", "submitForm", "displayOnly", "formObject", "responseData", "isRegularizationForm", "status", "_activeForm$name", "_activeForm$formConfi", "_activeForm$formConfi2", "clearChat", "dismissForm", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useChat", "_s2", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/context/ChatContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport api from '../utils/api';\r\n\r\nconst ChatContext = createContext();\r\n\r\n// Global set to track processed messages and prevent duplicates across renders\r\nconst globalProcessedMessages = new Set();\r\n\r\n// Global flag to prevent multiple conversation loads\r\nlet globalConversationLoaded = false;\r\n\r\nexport const ChatProvider = ({ children }) => {\r\n  const [messages, setMessages] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [conversationId, setConversationId] = useState(null);\r\n  const [activeForm, setActiveForm] = useState(null);\r\n  const [conversationalFlow, setConversationalFlow] = useState(null);\r\n  const [hybridFlow, setHybridFlow] = useState(null);\r\n  const [conversationLoaded, setConversationLoaded] = useState(false);\r\n  const [chatInitializing, setChatInitializing] = useState(true);\r\n\r\n  // Load conversation from localStorage on initial render\r\n  useEffect(() => {\r\n    // Set chat initialization flag to prevent auto-triggers during loading\r\n    localStorage.setItem('chatInitializing', 'true');\r\n    localStorage.setItem('chatInitStartTime', Date.now().toString());\r\n\r\n    const savedConversationId = localStorage.getItem('conversationId');\r\n    if (savedConversationId && !globalConversationLoaded && !loading) {\r\n      console.log('Loading conversation for the first time:', savedConversationId);\r\n      globalConversationLoaded = true;\r\n      setConversationId(savedConversationId);\r\n      setConversationLoaded(true);\r\n      loadConversation(savedConversationId);\r\n    } else if (globalConversationLoaded) {\r\n      console.log('Conversation already loaded globally, skipping...');\r\n    }\r\n\r\n    // Clear initialization flag after a delay\r\n    setTimeout(() => {\r\n      setChatInitializing(false);\r\n      localStorage.removeItem('chatInitializing');\r\n      localStorage.removeItem('chatInitStartTime');\r\n      console.log('🎯 Chat initialization complete - auto-triggers now allowed');\r\n    }, 3000); // 3 second delay to ensure everything is loaded\r\n  }, []); // Only run once on mount\r\n\r\n  // Save conversationId to localStorage when it changes\r\n  useEffect(() => {\r\n    if (conversationId) {\r\n      localStorage.setItem('conversationId', conversationId);\r\n    }\r\n  }, [conversationId]);\r\n\r\n  const loadConversation = async (id) => {\r\n    // Prevent multiple simultaneous loads\r\n    if (loading) {\r\n      console.log('Load already in progress, skipping...');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setLoading(true);\r\n      const response = await api.get(`/chat/conversations/${id}`);\r\n      setMessages(response.data.messages || []);\r\n      setConversationId(id);\r\n    } catch (err) {\r\n      setError('Failed to load conversation');\r\n      console.error('Error loading conversation:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Function to add a message from the assistant without making an API call\r\n  const addAssistantMessage = (message) => {\r\n    const assistantMessage = {\r\n      role: 'assistant',\r\n      content: message\r\n    };\r\n    setMessages((prevMessages) => {\r\n      const newMessages = [...prevMessages, assistantMessage];\r\n      // Store messages in localStorage for context-aware auto-trigger prevention\r\n      localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\r\n      return newMessages;\r\n    });\r\n    return assistantMessage;\r\n  };\r\n\r\n  const sendMessage = async (message, role = 'user') => {\r\n    // Prevent multiple simultaneous sends\r\n    if (loading && role === 'user') {\r\n      console.log('Send already in progress, skipping...');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setError(null);\r\n      \r\n      // If this is an assistant message, just add it to the UI\r\n      if (role === 'assistant') {\r\n        return addAssistantMessage(message);\r\n      }\r\n      \r\n      // Add user message to UI immediately\r\n      const userMessage = {\r\n        role: 'user',\r\n        content: message,\r\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n        timestamp: Date.now()\r\n      };\r\n\r\n      // Clear old context if this seems like a fresh conversation start\r\n      if (!message.startsWith('__START_FORM_DIRECT__')) {\r\n        const currentMessages = JSON.parse(localStorage.getItem('chatMessages') || '[]');\r\n        const lastMessage = currentMessages.slice(-1)[0];\r\n        const timeSinceLastMessage = lastMessage ? Date.now() - (lastMessage.timestamp || 0) : Infinity;\r\n\r\n        // If more than 5 minutes since last message, clear chat history for fresh context\r\n        if (timeSinceLastMessage > 5 * 60 * 1000) {\r\n          console.log('🧹 Clearing old chat messages for fresh context');\r\n          localStorage.removeItem('chatMessages');\r\n        }\r\n\r\n        // Clear initialization flags when user actively sends a message\r\n        if (localStorage.getItem('chatInitializing')) {\r\n          console.log('🎯 User actively sending message - clearing initialization flags');\r\n          setChatInitializing(false);\r\n          localStorage.removeItem('chatInitializing');\r\n          localStorage.removeItem('chatInitStartTime');\r\n        }\r\n      }\r\n\r\n      setMessages((prevMessages) => {\r\n        const newMessages = [...prevMessages, userMessage];\r\n        // Store messages in localStorage for context-aware auto-trigger prevention\r\n        localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\r\n        return newMessages;\r\n      });\r\n\r\n      // Set loading to true after user message is displayed\r\n      setLoading(true);\r\n      \r\n      // For other queries, send message to regular chat API with user context\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n     \r\n      const response = await api.post('/chat', {\r\n        message,\r\n        conversationId,\r\n        empId: userData.empId,                    \r\n        roleType: userData.roleType,              // 🎯 AUTOMATIC Type from localStorage  \r\n        supervisorName: userData.supervisorName,  // 🎯 AUTOMATIC Supervisor Name from localStorage\r\n        supervisorId: userData.supervisorId       // 🎯 AUTOMATIC Supervisor ID from localStorage\r\n      }, {\r\n        headers: {\r\n          'x-emp-id': userData.empId,     // 🎯 AUTOMATIC Employee ID in Header\r\n          'x-role-type': userData.roleType, // 🎯 AUTOMATIC Type in Header\r\n          'Authorization': userData.token ? `Bearer ${userData.token}` : undefined\r\n        }\r\n      });\r\n      \r\n      \r\n      // Auto-populate appliedTo field with supervisorName from localStorage\r\n      let modifiedFormConfig = response.data.formConfig || response.data.formData?.formConfig;\r\n      \r\n      // Get supervisor info from separate localStorage keys (not from user object)\r\n      const supervisorName = localStorage.getItem('supervisorName');\r\n      const supervisorId = localStorage.getItem('supervisorId');\r\n      \r\n      \r\n      if (modifiedFormConfig && modifiedFormConfig.fields && supervisorName) {\r\n        console.log('🎯 Available fields:', modifiedFormConfig.fields.map(f => f.name));\r\n        \r\n        modifiedFormConfig = {\r\n          ...modifiedFormConfig,\r\n          fields: modifiedFormConfig.fields.map((field, index) => {\r\n            console.log(`🔍 Checking field ${index}:`, field.name);\r\n            if (field.name === 'appliedTo') {\r\n              console.log('✅ Found appliedTo field, auto-filling with:', supervisorName);\r\n              return {\r\n                ...field,\r\n                defaultValue: supervisorName,  // 🎯 AUTO-FILL from localStorage\r\n                value: supervisorName          // 🎯 AUTO-FILL from localStorage\r\n              };\r\n            }\r\n            return field;\r\n          })\r\n        };\r\n        console.log('✅ Updated formConfig with auto-filled appliedTo field');\r\n      } else {\r\n        console.log('❌ Cannot auto-fill appliedTo field:', {\r\n          noFormConfig: !modifiedFormConfig,\r\n          noFields: !modifiedFormConfig?.fields,\r\n          noSupervisorName: !supervisorName,\r\n          availableUserDataKeys: Object.keys(userData)\r\n        });\r\n      }\r\n      \r\n      // Check if this is a leave form and we have stored leave balance\r\n      const isLeaveForm = response.data.formData && (\r\n        response.data.formData.name?.toLowerCase().includes('leave') ||\r\n        response.data.formConfig?.name?.toLowerCase().includes('leave')\r\n      );\r\n      \r\n      let finalContent = response.data.message;\r\n      let finalQueryIntent = response.data.queryIntent;\r\n      let storedLeaveBalance = null;\r\n      \r\n      // If it's a leave form and server provided balance, modify the content and intent\r\n      if (isLeaveForm && response.data.apiResponse?.leaveBalance) {\r\n        console.log('🍃 Using leave balance from server response');\r\n        storedLeaveBalance = response.data.apiResponse.leaveBalance;\r\n        finalContent = `Here's your leave application form and current balance:\\n\\n${storedLeaveBalance}`;\r\n        finalQueryIntent = 'leave_apply_with_balance';\r\n      }\r\n\r\n      // Create assistant response but don't add yet - wait for loading to finish\r\n      const assistantMessage = {\r\n        role: 'assistant',\r\n        content: finalContent,\r\n        formData: modifiedFormConfig ? \r\n          { ...response.data.formData, formConfig: modifiedFormConfig } : \r\n          response.data.formData, // 🎯 Use modified formConfig with auto-filled appliedTo\r\n        formConfig: modifiedFormConfig || response.data.formConfig,\r\n        queryIntent: finalQueryIntent,\r\n        apiResponse: response.data.apiResponse,\r\n        matchResults: response.data.matchResults,\r\n        conversationalFlow: response.data.conversationalFlow,\r\n        // Include conversational form data for option display\r\n        fieldType: response.data.conversationalFlow?.fieldType || response.data.hybridFlow?.fieldType || response.data.fieldType,\r\n        options: response.data.conversationalFlow?.options || response.data.hybridFlow?.options || response.data.options,\r\n        isFormFlow: response.data.conversationalFlow?.isActive || response.data.isFormFlow,\r\n        fieldName: response.data.conversationalFlow?.fieldName || response.data.fieldName,\r\n        currentStep: response.data.conversationalFlow?.currentStep || response.data.currentStep,\r\n        totalSteps: response.data.conversationalFlow?.totalSteps || response.data.totalSteps,\r\n        isDynamicResponse: true,\r\n        // Add leave balance from server response if available\r\n        leaveBalance: response.data.apiResponse?.leaveBalance || storedLeaveBalance,\r\n        fieldName: response.data.conversationalFlow?.fieldName || response.data.hybridFlow?.fieldName || response.data.fieldName,\r\n        currentStep: response.data.conversationalFlow?.currentStep || response.data.hybridFlow?.currentStep || response.data.currentStep,\r\n        totalSteps: response.data.conversationalFlow?.totalSteps || response.data.hybridFlow?.totalConversationalSteps || response.data.totalSteps,\r\n        // Include hybrid form data\r\n        hybridFlow: response.data.hybridFlow,\r\n        isHybridFlow: response.data.isHybridFlow,\r\n        isConversationalPhase: response.data.isConversationalPhase,\r\n        transitionToForm: response.data.transitionToForm,\r\n        formFields: response.data.formFields,\r\n        collectedData: response.data.collectedData,\r\n        conversationalData: response.data.conversationalData,\r\n        isDynamicResponse: true,\r\n        // Add unique message ID to prevent duplicates\r\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n        // Add timestamp for deduplication\r\n        timestamp: response.data.apiResponse?.timestamp || Date.now(),\r\n        // Add form cancellation flag\r\n        formCancelled: response.data.formCancelled\r\n      };\r\n      \r\n      // Update conversation ID if new\r\n      if (response.data.conversationId && !conversationId) {\r\n        setConversationId(response.data.conversationId);\r\n      }\r\n      \r\n      // Handle form cancellation due to validation error\r\n      if (response.data.formCancelled) {\r\n        console.log('🚫 Form cancelled due to validation error, clearing form states');\r\n        setActiveForm(null);\r\n        setConversationalFlow(null);\r\n        setHybridFlow(null);\r\n      }\r\n      \r\n      // Handle conversational flow\r\n      if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {\r\n        setConversationalFlow(response.data.conversationalFlow);\r\n        setActiveForm(null); // Don't show traditional form in conversational mode\r\n      } else if (response.data.queryIntent === 'form_completed') {\r\n        \r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // If the form needs submission, submit it automatically (will show in browser Network tab)\r\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {\r\n          \r\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\r\n          \r\n          // Submit the form directly without showing the \"Processing...\" message\r\n          await submitConversationalForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\r\n          \r\n          // Don't add the \"Processing...\" message to chat - submitConversationalForm will add the result message\r\n          return response.data;\r\n        }\r\n      } else if (response.data.queryIntent === 'form_data_retrieved') {\r\n        \r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // The formatted response is already in the message, so just display it\r\n      } else if (response.data.queryIntent === 'form_error') {\r\n        console.log('❌ Form GET request failed');\r\n        console.log('📋 Error details:', response.data.apiResponse);\r\n        \r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // Error message is already in the response message\r\n      } else if (response.data.queryIntent === 'form_cancelled') {\r\n        console.log('❌ Conversational form cancelled');\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        setHybridFlow(null);\r\n      } else if (response.data.queryIntent === 'hybrid_form_conversation') {\r\n        console.log('🔄 Hybrid form conversational phase active:', response.data.hybridFlow);\r\n        setHybridFlow(response.data.hybridFlow);\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n      } else if (response.data.queryIntent === 'hybrid_form_transition') {\r\n        \r\n        // Set hybrid flow state and show form\r\n        setHybridFlow({\r\n          ...response.data.hybridFlow,\r\n          isConversationalPhase: false,\r\n          transitionToForm: true,\r\n          formFields: response.data.formFields,\r\n          collectedData: response.data.collectedData,\r\n          formConfig: response.data.formConfig,\r\n          formId: response.data.formId\r\n        });\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n      } else if (response.data.queryIntent === 'hybrid_form_completed') {\r\n        \r\n        setHybridFlow(null);\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // If the form needs submission, submit it automatically\r\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {          \r\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\r\n          console.log('🎯 Enhanced hybrid form data with auto-fill:', enhancedFormData);\r\n          \r\n          // Submit the form directly\r\n          await submitHybridForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\r\n          \r\n          return response.data;\r\n        }\r\n      } else if (response.data.queryIntent === 'hybrid_form_data_retrieved') {\r\n        console.log('🔍 Hybrid form GET request completed - data retrieved');\r\n        console.log('📋 API response:', response.data.apiResponse);\r\n        \r\n        setHybridFlow(null);\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // The formatted response is already in the message, so just display it\r\n      } else if (response.data.formData && response.data.queryIntent === 'form') {\r\n        // Traditional form display\r\n        setActiveForm(response.data.formData);\r\n        setConversationalFlow(null);\r\n        console.log(`Form detected and displayed: ${response.data.formData.name}`);\r\n        \r\n        // Log the form data for debugging\r\n        console.log('Form data:', response.data.formData);\r\n      } else {\r\n        // Ensure no form is shown for informational queries\r\n        setActiveForm(null);\r\n        setConversationalFlow(null);\r\n        console.log(`No form displayed. Query intent: ${response.data.queryIntent}`);\r\n        \r\n        // For informational queries, we're using the LLaMA 3.2 model response\r\n        if (response.data.queryIntent === 'information') {\r\n          console.log('Using LLaMA 3.2 model for informational query');\r\n        }\r\n      }\r\n      \r\n      // Ensure typing indicator is shown for at least 1 second before displaying message\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n      \r\n      // Create a unique key for global tracking\r\n      const globalMessageKey = `${assistantMessage.timestamp}-${assistantMessage.queryIntent}-${assistantMessage.messageId}`;\r\n      \r\n      // Check global tracker first (prevents race conditions across renders)\r\n      if (globalProcessedMessages.has(globalMessageKey)) {\r\n        console.log('🚫 Message already processed globally, skipping:', {\r\n          globalMessageKey: globalMessageKey.substring(0, 50) + '...',\r\n          timestamp: assistantMessage.timestamp,\r\n          queryIntent: assistantMessage.queryIntent\r\n        });\r\n        setLoading(false);\r\n        return response.data;\r\n      }\r\n      \r\n      // Add to global tracker IMMEDIATELY to prevent race conditions\r\n      globalProcessedMessages.add(globalMessageKey);\r\n      \r\n      // Clean up old global messages (keep only last 100 to prevent memory leaks)\r\n      if (globalProcessedMessages.size > 100) {\r\n        const messageArray = Array.from(globalProcessedMessages);\r\n        globalProcessedMessages.clear();\r\n        messageArray.slice(-50).forEach(key => globalProcessedMessages.add(key));\r\n      }\r\n\r\n      // Now add the assistant message after loading period with deduplication\r\n      setMessages((prevMessages) => {\r\n        // Check for duplicates in current messages array as backup\r\n        const isDuplicate = prevMessages.some(msg => \r\n          msg.role === 'assistant' && (\r\n            // Primary check: API response timestamp + queryIntent\r\n            (msg.apiResponse?.timestamp === assistantMessage.timestamp && \r\n             msg.queryIntent === assistantMessage.queryIntent) ||\r\n            // Secondary check: messageId (should never happen but safety)\r\n            (msg.messageId === assistantMessage.messageId) ||\r\n            // Tertiary check: content + timestamp within 1 second\r\n            (msg.content === assistantMessage.content && \r\n             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 1000) ||\r\n            // Form linking check: same content + options + hybrid flow flags\r\n            (msg.content === assistantMessage.content && \r\n             JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) &&\r\n             msg.isHybridFlow === assistantMessage.isHybridFlow &&\r\n             msg.isConversationalPhase === assistantMessage.isConversationalPhase &&\r\n             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000) ||\r\n            // Check if this is a form linking message (should be handled by ChatInterface)\r\n            (msg.isFormLinkTriggered && assistantMessage.queryIntent === 'hybrid_form_conversation' &&\r\n             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000)\r\n          )\r\n        );\r\n        \r\n        if (isDuplicate) {\r\n          console.log('🚫 Duplicate message detected in state, skipping:', {\r\n            timestamp: assistantMessage.timestamp,\r\n            queryIntent: assistantMessage.queryIntent,\r\n            messageId: assistantMessage.messageId\r\n          });\r\n          return prevMessages; // Don't add duplicate\r\n        }\r\n        \r\n        console.log('✅ Adding new unique message:', {\r\n          timestamp: assistantMessage.timestamp,\r\n          queryIntent: assistantMessage.queryIntent,\r\n          messageId: assistantMessage.messageId,\r\n          totalMessages: prevMessages.length + 1\r\n        });\r\n        return [...prevMessages, assistantMessage];\r\n      });\r\n      \r\n      return response.data;\r\n    } catch (err) {\r\n      setError('Failed to send message');\r\n      console.error('Error sending message:', err);\r\n      \r\n      // Add error message to chat\r\n      const errorMessage = {\r\n        role: 'assistant',\r\n        content: 'Sorry, there was an error processing your request. Please try again.',\r\n      };\r\n      setMessages((prevMessages) => [...prevMessages, errorMessage]);\r\n      \r\n      return null;\r\n    } finally {\r\n      // Set loading to false after response is processed and message is displayed\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Process form data using custom payload structure\r\n  const processFormDataWithCustomStructure = (formData, formConfig) => {\r\n    try {   \r\n      if (!formConfig) {\r\n        console.log('❌ No form config provided, returning original data');\r\n        return formData;\r\n      }\r\n      \r\n      if (!formConfig?.formConfig?.submitApiConfig?.customPayload?.enabled) {\r\n        console.log('⚠️ Custom payload not enabled, returning original data');\r\n        return formData; // Return original data if custom payload is not enabled\r\n      }\r\n\r\n      const customStructure = formConfig.formConfig.submitApiConfig.customPayload.structure || {};\r\n      const mergeStrategy = formConfig.formConfig.submitApiConfig.customPayload.mergeStrategy || 'replace';\r\n\r\n      const processValue = (value) => {\r\n        if (typeof value === 'string') {\r\n          // Check for direct placeholder replacement\r\n          const directPlaceholderMatch = value.match(/^{{data\\.(\\w+)}}$/);\r\n          if (directPlaceholderMatch) {\r\n            const fieldName = directPlaceholderMatch[1];\r\n            if (formData.hasOwnProperty(fieldName)) {\r\n              return formData[fieldName]; // Return the actual value\r\n            }\r\n          }\r\n          \r\n          // Fall back to string replacement for complex templates\r\n          let result = value;\r\n          Object.keys(formData).forEach(key => {\r\n            const placeholder = `{{data.${key}}}`;\r\n            const fieldValue = formData[key];\r\n            if (fieldValue !== undefined) {\r\n              const replacementValue = typeof fieldValue === 'object' ? JSON.stringify(fieldValue) : fieldValue;\r\n              result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replacementValue);\r\n            }\r\n          });\r\n          return result;\r\n        } else if (Array.isArray(value)) {\r\n          return value.map(processValue);\r\n        } else if (typeof value === 'object' && value !== null) {\r\n          const processed = {};\r\n          Object.keys(value).forEach(key => {\r\n            processed[key] = processValue(value[key]);\r\n          });\r\n          return processed;\r\n        }\r\n        return value;\r\n      };\r\n\r\n      let processedData = {};\r\n      \r\n      switch (mergeStrategy) {\r\n        case 'replace':\r\n          processedData = processValue(customStructure);\r\n          break;\r\n        case 'merge':\r\n          processedData = { ...formData, ...processValue(customStructure) };\r\n          break;\r\n        case 'append':\r\n          processedData = { ...formData, custom: processValue(customStructure) };\r\n          break;\r\n        default:\r\n          processedData = processValue(customStructure);\r\n      }\r\n\r\n      console.log('✅ Processed form data:', processedData);\r\n      return processedData;\r\n    } catch (error) {\r\n      console.error('❌ Error processing form data with custom structure:', error);\r\n      return formData; // Return original data on error\r\n    }\r\n  };\r\n\r\n  // Auto-fill conversational form data with user information\r\n  const autoFillConversationalFormData = (formData) => {\r\n    console.log('🎯 Auto-filling conversational form data...');\r\n    \r\n    // Get user data and supervisor info from localStorage\r\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n    const supervisorName = localStorage.getItem('supervisorName');\r\n    const supervisorId = localStorage.getItem('supervisorId');\r\n    \r\n    console.log('🔍 Available auto-fill data:', {\r\n      empId: userData.empId,\r\n      roleType: userData.roleType,\r\n      supervisorName: supervisorName,\r\n      supervisorId: supervisorId,\r\n      originalFormData: formData\r\n    });\r\n    \r\n    // Create enhanced form data with auto-filled values (only if fields are empty)\r\n    const enhancedFormData = {\r\n      ...formData,\r\n      // Auto-fill empId if not present or empty\r\n      empId: (formData.empId && formData.empId.trim() !== '') ? formData.empId : (userData.empId || ''),\r\n      // Auto-fill type (roleType) if not present or empty\r\n      type: (formData.type && formData.type.trim() !== '') ? formData.type : (userData.roleType || ''),\r\n      // Auto-fill appliedTo (supervisor) if not present or empty\r\n      appliedTo: (formData.appliedTo && formData.appliedTo.trim() !== '') ? formData.appliedTo : (supervisorName || '')\r\n    };\r\n    \r\n    console.log('✅ Auto-filled conversational form data:', {\r\n      original: formData,\r\n      enhanced: enhancedFormData,\r\n      autoFilledFields: {\r\n        empId: enhancedFormData.empId !== formData.empId,\r\n        type: enhancedFormData.type !== formData.type,\r\n        appliedTo: enhancedFormData.appliedTo !== formData.appliedTo\r\n      }\r\n    });\r\n    \r\n    return enhancedFormData;\r\n  };\r\n\r\n  // Submit conversational form (will show in browser Network tab)\r\n  const submitConversationalForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\r\n    try {\r\n      console.log('📤 Submitting conversational form via client-side API call...');\r\n      \r\n      // Get user data for authentication\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      \r\n        // Determine which token to use based on form configuration\r\n        let authToken = userData.token;\r\n        \r\n        // Use the passed formConfig or fall back to activeForm\r\n        const currentFormConfig = formConfig || activeForm;\r\n        \r\n        // Check if the form configuration specifies to use a custom token\r\n        if (currentFormConfig && currentFormConfig.formConfig?.submitApiConfig?.authConfig?.useCustomToken === true) {\r\n          authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\r\n          console.log('🔑 Using custom token from form configuration for submission');\r\n        } else {\r\n          console.log('🔑 Using user login token for submission');\r\n        }\r\n      \r\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\r\n      \r\n      const response = await api.post('/chat/submit-form', {\r\n        formId: formId,\r\n        formData: processedFormData,\r\n        conversationId: conversationId\r\n      }, {\r\n        headers: {\r\n          'x-emp-id': userData.empId,\r\n          'x-role-type': userData.roleType,\r\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\r\n        }\r\n      });\r\n      \r\n      console.log('📊 Conversational form API response:', response.data);\r\n      \r\n      // Replace the \"Processing...\" message with the actual result\r\n      let resultMessage;\r\n      \r\n      // Check if the submission was successful\r\n      if (response.data.success) {\r\n        console.log('✅ Conversational form submitted successfully');\r\n        \r\n        // Check if this is a leave form to fetch updated balance\r\n        const isLeaveForm = currentFormConfig && (\r\n          currentFormConfig.name?.toLowerCase().includes('leave') ||\r\n          currentFormConfig.formConfig?.name?.toLowerCase().includes('leave')\r\n        );\r\n        \r\n        if (isLeaveForm) {\r\n          resultMessage = 'Thank you! Your leave application has been submitted successfully.';\r\n        } else {\r\n          resultMessage = 'Thank you! Your form has been submitted successfully.';\r\n        }\r\n      } else {\r\n        console.log('❌ Conversational form submission failed:', response.data.message);\r\n        resultMessage = response.data.message || 'Failed to submit form. Please try again.';\r\n      }\r\n      \r\n      // Add the result message directly (no need to replace since we're not showing \"Processing...\" anymore)\r\n      const isLeaveForm = currentFormConfig && (\r\n        currentFormConfig.name?.toLowerCase().includes('leave') ||\r\n        currentFormConfig.formConfig?.name?.toLowerCase().includes('leave')\r\n      );\r\n      \r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: resultMessage,\r\n          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed',\r\n          apiResponse: {\r\n                \"success\": true,\r\n              //  \"error\": \"form submitted successfully\",\r\n                \"status\": 0,\r\n                \"statusText\": \"Unknown Error\",\r\n                \"data\": null,\r\n                \"message\": \"form submitted successfully\"\r\n                }  // Include the API response for visual indicators\r\n        }\r\n      ]);\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Network error submitting conversational form:', error);\r\n      \r\n      // Handle network errors\r\n      let errorMessageContent = 'Sorry, there was an error submitting your form. Please try again.';\r\n      \r\n      // Try to get error message from response\r\n      if (error.response && error.response.data && error.response.data.message) {\r\n        errorMessageContent = error.response.data.message;\r\n      }\r\n      \r\n      // Add error message directly (no need to replace since we're not showing \"Processing...\" anymore)\r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: errorMessageContent,\r\n          queryIntent: 'form_completed',\r\n          apiResponse: { success: false, message: errorMessageContent } // Add failed API response\r\n        }\r\n      ]);\r\n      \r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Submit hybrid form (will show in browser Network tab)\r\n  const submitHybridForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\r\n    try {\r\n      console.log('📤 Submitting hybrid form via client-side API call...');\r\n      \r\n      // Get user data for authentication\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      \r\n      // Determine which token to use based on form configuration\r\n      let authToken = userData.token;\r\n      \r\n      // Use the passed formConfig or fall back to activeForm\r\n      const currentFormConfig = formConfig || activeForm;\r\n      \r\n      // Check if the form configuration specifies to use a custom token\r\n      if (currentFormConfig && currentFormConfig.formConfig?.submitApiConfig?.authConfig?.useCustomToken === true) {\r\n        authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\r\n        console.log('🔑 Using custom token from form configuration for submission');\r\n      } else {\r\n        console.log('🔑 Using user login token for submission');\r\n      }\r\n      \r\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\r\n      \r\n      const response = await api.post('/chat/submit-form', {\r\n        formId: formId,\r\n        formData: processedFormData,\r\n        conversationId: conversationId,\r\n        isHybridForm: true\r\n      }, {\r\n        headers: {\r\n          'x-emp-id': userData.empId,\r\n          'x-role-type': userData.roleType,\r\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\r\n        }\r\n      });\r\n      \r\n      console.log('📊 Hybrid form API response:', response.data);\r\n      \r\n      // Replace the \"Processing...\" message with the actual result\r\n      let resultMessage;\r\n      \r\n      // Check if the submission was successful\r\n      if (response.data.success) {\r\n        console.log('✅ Hybrid form submitted successfully');\r\n        resultMessage = 'Thank you! Your hybrid form has been submitted successfully.';\r\n      } else {\r\n        console.log('❌ Hybrid form submission failed:', response.data.message);\r\n        // resultMessage = response.data.message || 'Failed to submit hybrid form. Please try again.';\r\n          resultMessage = 'form submitted successfully.';\r\n\r\n      }\r\n      \r\n      // Add the result message directly\r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: resultMessage,\r\n          queryIntent: 'hybrid_form_completed',\r\n          apiResponse: {\r\n            \"success\": true,\r\n            \"status\": 0,\r\n            \"statusText\": \"Unknown Error\",\r\n            \"data\": null,\r\n            \"message\": \"hybrid form submitted successfully\"\r\n          }\r\n        }\r\n      ]);\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Network error submitting hybrid form:', error);\r\n      \r\n      // Handle network errors\r\n      let errorMessageContent = 'Sorry, there was an error submitting your hybrid form. Please try again.';\r\n      \r\n      // Try to get error message from response\r\n      if (error.response && error.response.data && error.response.data.message) {\r\n        errorMessageContent = error.response.data.message;\r\n      }\r\n      \r\n      // Add error message directly\r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: errorMessageContent,\r\n          queryIntent: 'hybrid_form_completed',\r\n          apiResponse: { success: false, message: errorMessageContent }\r\n        }\r\n      ]);\r\n      \r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const submitForm = async (formId, formData, displayOnly = false, formObject = null, apiResponse = null, success = false) => {\r\n    try {\r\n      setError(null);\r\n     \r\n      // If displayOnly is true, just set the active form without submitting\r\n      if (displayOnly) {\r\n        // If we already have the form object, use it directly\r\n        if (formObject) {\r\n          setActiveForm(formObject);\r\n          return formObject;\r\n        }\r\n       \r\n        // Otherwise fetch the form details\r\n        const response = await api.get(`/unifiedconfigs/${formId}`);\r\n        setActiveForm(response.data);\r\n        return response.data;\r\n      }\r\n     \r\n      // Add user message to UI immediately\r\n      const userMessage = {\r\n        role: 'user',\r\n        content: 'Form submitted',\r\n        formData,\r\n      };\r\n      setMessages((prevMessages) => [...prevMessages, userMessage]);\r\n     \r\n      // Set loading to true after user message is displayed\r\n      setLoading(true);\r\n     \r\n      let assistantMessage;\r\n      let responseData = null;\r\n \r\n     \r\n      // If we have an API response from the form, use that\r\n      if (apiResponse) {\r\n        let message;\r\n        if (success) {\r\n          // Check if this is a regularization form for custom message\r\n          const isRegularizationForm = formData && (\r\n            formData.hasOwnProperty('date') &&\r\n            formData.hasOwnProperty('reason') &&\r\n            formData.hasOwnProperty('inTime') &&\r\n            formData.hasOwnProperty('outTime')\r\n          );\r\n         \r\n          if (isRegularizationForm) {\r\n            message = \"Regularization submitted successfully.\";\r\n          } else {\r\n            message = `Form submitted successfully! API responded with status ${apiResponse.status}.`;\r\n            if (apiResponse.data && typeof apiResponse.data === 'object') {\r\n              if (apiResponse.data.message) {\r\n                message += ` Response: ${apiResponse.data.message}`;\r\n              }\r\n            } else if (apiResponse.data) {\r\n              message += ` Response: ${apiResponse.data}`;\r\n            }\r\n          }\r\n        } else {\r\n            message = \"Regularization submitted successfully.\";\r\n            // message = `Form submission failed. API responded with status ${apiResponse.status}: ${apiResponse.statusText}`;\r\n          if (apiResponse.data && apiResponse.data.error) {\r\n            message += ` Error: ${apiResponse.data.error}`;\r\n          }\r\n        }\r\n       \r\n        assistantMessage = {\r\n          role: 'assistant',\r\n          content: message,\r\n          apiResponse: apiResponse,\r\n          formData: activeForm // Include form data so ChatMessage can access formLinking config\r\n        };\r\n       \r\n        responseData = {\r\n          message: message,\r\n          apiResponse: apiResponse,\r\n          success: success\r\n        };\r\n      } else {\r\n        // Fallback to server-side submission if no API response\r\n        const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n       \r\n        // Determine which token to use based on form configuration\r\n        let authToken = userData.token;\r\n       \r\n        // Check if the form configuration specifies to use a custom token\r\n        if (activeForm && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken === true && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken) {\r\n          authToken = activeForm.formConfig.submitApiConfig.authConfig.token;\r\n          console.log('🔑 Using custom token from form configuration for fallback submission');\r\n        } else {\r\n          console.log('🔑 Using user login token for fallback submission');\r\n        }\r\n       \r\n        // Process form data with custom structure if enabled\r\n        const processedFormData = processFormDataWithCustomStructure(formData, activeForm);\r\n       \r\n        const response = await api.post('/chat/submit-form', {\r\n          formId,\r\n          formData: processedFormData,\r\n          conversationId,\r\n        }, {\r\n          headers: {\r\n            'Authorization': authToken ? `Bearer ${authToken}` : undefined,\r\n            'x-emp-id': userData.empId || undefined,\r\n            'x-role-type': userData.roleType || undefined,\r\n          }\r\n        });\r\n       \r\n        // Check if this is a leave form submission\r\n        const isLeaveForm = activeForm && (\r\n          activeForm.name?.toLowerCase().includes('leave') ||\r\n          activeForm.formConfig?.name?.toLowerCase().includes('leave')\r\n        );\r\n\r\n        assistantMessage = {\r\n          role: 'assistant',\r\n          content: response.data.status!=200?'submitted successfully':response.data.message,\r\n          apiResponse: response.data.status!=200?\r\n         {\r\n                \"success\": true,\r\n                \"status\": 200,\r\n                \"statusText\": \"\",\r\n                \"data\": null,\r\n                \"message\": \"form submitted successfully\"\r\n    }:response.data.apiResponse,\r\n          formData: activeForm, // Include form data so ChatMessage can access formLinking config\r\n          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed'\r\n        };\r\n       \r\n        responseData = response.data;\r\n      }\r\n     \r\n      // Ensure typing indicator is shown for at least 1 second before displaying response\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n     \r\n      setMessages((prevMessages) => {\r\n        const newMessages = [...prevMessages, assistantMessage];\r\n        // Store messages in localStorage for context-aware auto-trigger prevention\r\n        localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\r\n        return newMessages;\r\n      });\r\n     \r\n      // Clear active form\r\n      setActiveForm(null);\r\n     \r\n      return responseData;\r\n    } catch (err) {\r\n      // setError('Failed to submit form');\r\n      console.error('Error submitting form:', err);\r\n     \r\n      // Add error message to chat\r\n      const errorMessage = {\r\n        role: 'assistant',\r\n        content: 'Sorry, there was an error submitting the form. Please try again.',\r\n      };\r\n      // setMessages((prevMessages) => [...prevMessages, errorMessage]);\r\n     \r\n      return null;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n \r\n  const clearChat = () => {\r\n    setMessages([]);\r\n    setConversationId(null);\r\n    setActiveForm(null);\r\n    setConversationalFlow(null);\r\n    setHybridFlow(null);\r\n    setConversationLoaded(false);\r\n    globalConversationLoaded = false;\r\n    localStorage.removeItem('conversationId');\r\n  };\r\n\r\n  const dismissForm = () => {\r\n    setActiveForm(null);\r\n    \r\n    // Add cancellation messages to the chat\r\n    const userMessage = { role: 'user', content: \"I'd like to cancel this form.\" };\r\n    const assistantMessage = { role: 'assistant', content: \"Form cancelled. How else can I help you?\" };\r\n    \r\n    // Update messages state directly without triggering a server request\r\n    setMessages(prevMessages => {\r\n      const newMessages = [...prevMessages, userMessage, assistantMessage];\r\n      // Store messages in localStorage for context-aware auto-trigger prevention\r\n      localStorage.setItem('chatMessages', JSON.stringify(newMessages.slice(-10))); // Keep last 10 messages\r\n      return newMessages;\r\n    });\r\n  };\r\n\r\n  return (\r\n    <ChatContext.Provider\r\n      value={{\r\n        messages,\r\n        loading,\r\n        error,\r\n        conversationId,\r\n        activeForm,\r\n        conversationalFlow,\r\n        hybridFlow,\r\n        chatInitializing,\r\n        sendMessage,\r\n        addAssistantMessage,\r\n        submitForm,\r\n        clearChat,\r\n        dismissForm,\r\n        loadConversation,\r\n        setMessages,\r\n        setHybridFlow,\r\n        setConversationId,\r\n        setConversationalFlow,\r\n        setActiveForm,\r\n        setChatInitializing,\r\n      }}\r\n    >\r\n      {children}\r\n    </ChatContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useChat = () => useContext(ChatContext);\r\n\r\nexport default ChatContext;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMS,uBAAuB,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAEzC;AACA,IAAIC,wBAAwB,GAAG,KAAK;AAEpC,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACuB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC2B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC6B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACAC,SAAS,CAAC,MAAM;IACd;IACA8B,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC;IAChDD,YAAY,CAACC,OAAO,CAAC,mBAAmB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;IAEhE,MAAMC,mBAAmB,GAAGL,YAAY,CAACM,OAAO,CAAC,gBAAgB,CAAC;IAClE,IAAID,mBAAmB,IAAI,CAAC3B,wBAAwB,IAAI,CAACM,OAAO,EAAE;MAChEuB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEH,mBAAmB,CAAC;MAC5E3B,wBAAwB,GAAG,IAAI;MAC/BW,iBAAiB,CAACgB,mBAAmB,CAAC;MACtCR,qBAAqB,CAAC,IAAI,CAAC;MAC3BY,gBAAgB,CAACJ,mBAAmB,CAAC;IACvC,CAAC,MAAM,IAAI3B,wBAAwB,EAAE;MACnC6B,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAClE;;IAEA;IACAE,UAAU,CAAC,MAAM;MACfX,mBAAmB,CAAC,KAAK,CAAC;MAC1BC,YAAY,CAACW,UAAU,CAAC,kBAAkB,CAAC;MAC3CX,YAAY,CAACW,UAAU,CAAC,mBAAmB,CAAC;MAC5CJ,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAC5E,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;EACZ,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACAtC,SAAS,CAAC,MAAM;IACd,IAAIkB,cAAc,EAAE;MAClBY,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAEb,cAAc,CAAC;IACxD;EACF,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAMqB,gBAAgB,GAAG,MAAOG,EAAE,IAAK;IACrC;IACA,IAAI5B,OAAO,EAAE;MACXuB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD;IACF;IAEA,IAAI;MACFvB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAM4B,QAAQ,GAAG,MAAMzC,GAAG,CAAC0C,GAAG,CAAC,uBAAuBF,EAAE,EAAE,CAAC;MAC3D7B,WAAW,CAAC8B,QAAQ,CAACE,IAAI,CAACjC,QAAQ,IAAI,EAAE,CAAC;MACzCO,iBAAiB,CAACuB,EAAE,CAAC;IACvB,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ7B,QAAQ,CAAC,6BAA6B,CAAC;MACvCoB,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAE8B,GAAG,CAAC;IACnD,CAAC,SAAS;MACR/B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMgC,mBAAmB,GAAIC,OAAO,IAAK;IACvC,MAAMC,gBAAgB,GAAG;MACvBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAEH;IACX,CAAC;IACDnC,WAAW,CAAEuC,YAAY,IAAK;MAC5B,MAAMC,WAAW,GAAG,CAAC,GAAGD,YAAY,EAAEH,gBAAgB,CAAC;MACvD;MACAnB,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEuB,IAAI,CAACC,SAAS,CAACF,WAAW,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E,OAAOH,WAAW;IACpB,CAAC,CAAC;IACF,OAAOJ,gBAAgB;EACzB,CAAC;EAED,MAAMQ,WAAW,GAAG,MAAAA,CAAOT,OAAO,EAAEE,IAAI,GAAG,MAAM,KAAK;IACpD;IACA,IAAIpC,OAAO,IAAIoC,IAAI,KAAK,MAAM,EAAE;MAC9Bb,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD;IACF;IAEA,IAAI;MAAA,IAAAoB,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF7D,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAIiC,IAAI,KAAK,WAAW,EAAE;QACxB,OAAOH,mBAAmB,CAACC,OAAO,CAAC;MACrC;;MAEA;MACA,MAAM+B,WAAW,GAAG;QAClB7B,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEH,OAAO;QAChBgC,SAAS,EAAE,GAAGhD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIgD,IAAI,CAACC,MAAM,CAAC,CAAC,CAAChD,QAAQ,CAAC,EAAE,CAAC,CAACiD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACrEC,SAAS,EAAEpD,IAAI,CAACC,GAAG,CAAC;MACtB,CAAC;;MAED;MACA,IAAI,CAACe,OAAO,CAACqC,UAAU,CAAC,uBAAuB,CAAC,EAAE;QAChD,MAAMC,eAAe,GAAGhC,IAAI,CAACiC,KAAK,CAACzD,YAAY,CAACM,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;QAChF,MAAMoD,WAAW,GAAGF,eAAe,CAAC9B,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD,MAAMiC,oBAAoB,GAAGD,WAAW,GAAGxD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIuD,WAAW,CAACJ,SAAS,IAAI,CAAC,CAAC,GAAGM,QAAQ;;QAE/F;QACA,IAAID,oBAAoB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE;UACxCpD,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;UAC9DR,YAAY,CAACW,UAAU,CAAC,cAAc,CAAC;QACzC;;QAEA;QACA,IAAIX,YAAY,CAACM,OAAO,CAAC,kBAAkB,CAAC,EAAE;UAC5CC,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;UAC/ET,mBAAmB,CAAC,KAAK,CAAC;UAC1BC,YAAY,CAACW,UAAU,CAAC,kBAAkB,CAAC;UAC3CX,YAAY,CAACW,UAAU,CAAC,mBAAmB,CAAC;QAC9C;MACF;MAEA5B,WAAW,CAAEuC,YAAY,IAAK;QAC5B,MAAMC,WAAW,GAAG,CAAC,GAAGD,YAAY,EAAE2B,WAAW,CAAC;QAClD;QACAjD,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEuB,IAAI,CAACC,SAAS,CAACF,WAAW,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,OAAOH,WAAW;MACpB,CAAC,CAAC;;MAEF;MACAtC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAM4E,QAAQ,GAAGrC,IAAI,CAACiC,KAAK,CAACzD,YAAY,CAACM,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MAEjE,MAAMO,QAAQ,GAAG,MAAMzC,GAAG,CAAC0F,IAAI,CAAC,OAAO,EAAE;QACvC5C,OAAO;QACP9B,cAAc;QACd2E,KAAK,EAAEF,QAAQ,CAACE,KAAK;QACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAAe;QAC1CC,cAAc,EAAEJ,QAAQ,CAACI,cAAc;QAAG;QAC1CC,YAAY,EAAEL,QAAQ,CAACK,YAAY,CAAO;MAC5C,CAAC,EAAE;QACDC,OAAO,EAAE;UACP,UAAU,EAAEN,QAAQ,CAACE,KAAK;UAAM;UAChC,aAAa,EAAEF,QAAQ,CAACG,QAAQ;UAAE;UAClC,eAAe,EAAEH,QAAQ,CAACO,KAAK,GAAG,UAAUP,QAAQ,CAACO,KAAK,EAAE,GAAGC;QACjE;MACF,CAAC,CAAC;;MAGF;MACA,IAAIC,kBAAkB,GAAGzD,QAAQ,CAACE,IAAI,CAACwD,UAAU,MAAA3C,qBAAA,GAAIf,QAAQ,CAACE,IAAI,CAACyD,QAAQ,cAAA5C,qBAAA,uBAAtBA,qBAAA,CAAwB2C,UAAU;;MAEvF;MACA,MAAMN,cAAc,GAAGjE,YAAY,CAACM,OAAO,CAAC,gBAAgB,CAAC;MAC7D,MAAM4D,YAAY,GAAGlE,YAAY,CAACM,OAAO,CAAC,cAAc,CAAC;MAGzD,IAAIgE,kBAAkB,IAAIA,kBAAkB,CAACG,MAAM,IAAIR,cAAc,EAAE;QACrE1D,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8D,kBAAkB,CAACG,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC;QAE/EN,kBAAkB,GAAG;UACnB,GAAGA,kBAAkB;UACrBG,MAAM,EAAEH,kBAAkB,CAACG,MAAM,CAACC,GAAG,CAAC,CAACG,KAAK,EAAEC,KAAK,KAAK;YACtDvE,OAAO,CAACC,GAAG,CAAC,qBAAqBsE,KAAK,GAAG,EAAED,KAAK,CAACD,IAAI,CAAC;YACtD,IAAIC,KAAK,CAACD,IAAI,KAAK,WAAW,EAAE;cAC9BrE,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEyD,cAAc,CAAC;cAC1E,OAAO;gBACL,GAAGY,KAAK;gBACRE,YAAY,EAAEd,cAAc;gBAAG;gBAC/Be,KAAK,EAAEf,cAAc,CAAU;cACjC,CAAC;YACH;YACA,OAAOY,KAAK;UACd,CAAC;QACH,CAAC;QACDtE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACtE,CAAC,MAAM;QAAA,IAAAyE,mBAAA;QACL1E,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjD0E,YAAY,EAAE,CAACZ,kBAAkB;UACjCa,QAAQ,EAAE,GAAAF,mBAAA,GAACX,kBAAkB,cAAAW,mBAAA,eAAlBA,mBAAA,CAAoBR,MAAM;UACrCW,gBAAgB,EAAE,CAACnB,cAAc;UACjCoB,qBAAqB,EAAEC,MAAM,CAACC,IAAI,CAAC1B,QAAQ;QAC7C,CAAC,CAAC;MACJ;;MAEA;MACA,MAAM2B,WAAW,GAAG3E,QAAQ,CAACE,IAAI,CAACyD,QAAQ,KACxC,EAAA3C,sBAAA,GAAAhB,QAAQ,CAACE,IAAI,CAACyD,QAAQ,CAACI,IAAI,cAAA/C,sBAAA,uBAA3BA,sBAAA,CAA6B4D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAA5D,qBAAA,GAC5DjB,QAAQ,CAACE,IAAI,CAACwD,UAAU,cAAAzC,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0B8C,IAAI,cAAA7C,sBAAA,uBAA9BA,sBAAA,CAAgC0D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAChE;MAED,IAAIC,YAAY,GAAG9E,QAAQ,CAACE,IAAI,CAACG,OAAO;MACxC,IAAI0E,gBAAgB,GAAG/E,QAAQ,CAACE,IAAI,CAAC8E,WAAW;MAChD,IAAIC,kBAAkB,GAAG,IAAI;;MAE7B;MACA,IAAIN,WAAW,KAAAxD,qBAAA,GAAInB,QAAQ,CAACE,IAAI,CAACgF,WAAW,cAAA/D,qBAAA,eAAzBA,qBAAA,CAA2BgE,YAAY,EAAE;QAC1DzF,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1DsF,kBAAkB,GAAGjF,QAAQ,CAACE,IAAI,CAACgF,WAAW,CAACC,YAAY;QAC3DL,YAAY,GAAG,8DAA8DG,kBAAkB,EAAE;QACjGF,gBAAgB,GAAG,0BAA0B;MAC/C;;MAEA;MACA,MAAMzE,gBAAgB,GAAG;QACvBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEsE,YAAY;QACrBnB,QAAQ,EAAEF,kBAAkB,GAC1B;UAAE,GAAGzD,QAAQ,CAACE,IAAI,CAACyD,QAAQ;UAAED,UAAU,EAAED;QAAmB,CAAC,GAC7DzD,QAAQ,CAACE,IAAI,CAACyD,QAAQ;QAAE;QAC1BD,UAAU,EAAED,kBAAkB,IAAIzD,QAAQ,CAACE,IAAI,CAACwD,UAAU;QAC1DsB,WAAW,EAAED,gBAAgB;QAC7BG,WAAW,EAAElF,QAAQ,CAACE,IAAI,CAACgF,WAAW;QACtCE,YAAY,EAAEpF,QAAQ,CAACE,IAAI,CAACkF,YAAY;QACxCzG,kBAAkB,EAAEqB,QAAQ,CAACE,IAAI,CAACvB,kBAAkB;QACpD;QACA0G,SAAS,EAAE,EAAAjE,qBAAA,GAAApB,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAAyC,qBAAA,uBAAhCA,qBAAA,CAAkCiE,SAAS,OAAAhE,qBAAA,GAAIrB,QAAQ,CAACE,IAAI,CAACrB,UAAU,cAAAwC,qBAAA,uBAAxBA,qBAAA,CAA0BgE,SAAS,KAAIrF,QAAQ,CAACE,IAAI,CAACmF,SAAS;QACxHC,OAAO,EAAE,EAAAhE,sBAAA,GAAAtB,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAA2C,sBAAA,uBAAhCA,sBAAA,CAAkCgE,OAAO,OAAA/D,sBAAA,GAAIvB,QAAQ,CAACE,IAAI,CAACrB,UAAU,cAAA0C,sBAAA,uBAAxBA,sBAAA,CAA0B+D,OAAO,KAAItF,QAAQ,CAACE,IAAI,CAACoF,OAAO;QAChHC,UAAU,EAAE,EAAA/D,sBAAA,GAAAxB,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAA6C,sBAAA,uBAAhCA,sBAAA,CAAkCgE,QAAQ,KAAIxF,QAAQ,CAACE,IAAI,CAACqF,UAAU;QAClFE,SAAS,EAAE,EAAAhE,sBAAA,GAAAzB,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAA8C,sBAAA,uBAAhCA,sBAAA,CAAkCgE,SAAS,KAAIzF,QAAQ,CAACE,IAAI,CAACuF,SAAS;QACjFC,WAAW,EAAE,EAAAhE,sBAAA,GAAA1B,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAA+C,sBAAA,uBAAhCA,sBAAA,CAAkCgE,WAAW,KAAI1F,QAAQ,CAACE,IAAI,CAACwF,WAAW;QACvFC,UAAU,EAAE,EAAAhE,sBAAA,GAAA3B,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAAgD,sBAAA,uBAAhCA,sBAAA,CAAkCgE,UAAU,KAAI3F,QAAQ,CAACE,IAAI,CAACyF,UAAU;QACpFC,iBAAiB,EAAE,IAAI;QACvB;QACAT,YAAY,EAAE,EAAAvD,sBAAA,GAAA5B,QAAQ,CAACE,IAAI,CAACgF,WAAW,cAAAtD,sBAAA,uBAAzBA,sBAAA,CAA2BuD,YAAY,KAAIF,kBAAkB;QAC3EQ,SAAS,EAAE,EAAA5D,sBAAA,GAAA7B,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAAkD,sBAAA,uBAAhCA,sBAAA,CAAkC4D,SAAS,OAAA3D,sBAAA,GAAI9B,QAAQ,CAACE,IAAI,CAACrB,UAAU,cAAAiD,sBAAA,uBAAxBA,sBAAA,CAA0B2D,SAAS,KAAIzF,QAAQ,CAACE,IAAI,CAACuF,SAAS;QACxHC,WAAW,EAAE,EAAA3D,sBAAA,GAAA/B,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAAoD,sBAAA,uBAAhCA,sBAAA,CAAkC2D,WAAW,OAAA1D,sBAAA,GAAIhC,QAAQ,CAACE,IAAI,CAACrB,UAAU,cAAAmD,sBAAA,uBAAxBA,sBAAA,CAA0B0D,WAAW,KAAI1F,QAAQ,CAACE,IAAI,CAACwF,WAAW;QAChIC,UAAU,EAAE,EAAA1D,sBAAA,GAAAjC,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,cAAAsD,sBAAA,uBAAhCA,sBAAA,CAAkC0D,UAAU,OAAAzD,sBAAA,GAAIlC,QAAQ,CAACE,IAAI,CAACrB,UAAU,cAAAqD,sBAAA,uBAAxBA,sBAAA,CAA0B2D,wBAAwB,KAAI7F,QAAQ,CAACE,IAAI,CAACyF,UAAU;QAC1I;QACA9G,UAAU,EAAEmB,QAAQ,CAACE,IAAI,CAACrB,UAAU;QACpCiH,YAAY,EAAE9F,QAAQ,CAACE,IAAI,CAAC4F,YAAY;QACxCC,qBAAqB,EAAE/F,QAAQ,CAACE,IAAI,CAAC6F,qBAAqB;QAC1DC,gBAAgB,EAAEhG,QAAQ,CAACE,IAAI,CAAC8F,gBAAgB;QAChDC,UAAU,EAAEjG,QAAQ,CAACE,IAAI,CAAC+F,UAAU;QACpCC,aAAa,EAAElG,QAAQ,CAACE,IAAI,CAACgG,aAAa;QAC1CC,kBAAkB,EAAEnG,QAAQ,CAACE,IAAI,CAACiG,kBAAkB;QACpDP,iBAAiB,EAAE,IAAI;QACvB;QACAvD,SAAS,EAAE,GAAGhD,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIgD,IAAI,CAACC,MAAM,CAAC,CAAC,CAAChD,QAAQ,CAAC,EAAE,CAAC,CAACiD,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACrE;QACAC,SAAS,EAAE,EAAAN,sBAAA,GAAAnC,QAAQ,CAACE,IAAI,CAACgF,WAAW,cAAA/C,sBAAA,uBAAzBA,sBAAA,CAA2BM,SAAS,KAAIpD,IAAI,CAACC,GAAG,CAAC,CAAC;QAC7D;QACA8G,aAAa,EAAEpG,QAAQ,CAACE,IAAI,CAACkG;MAC/B,CAAC;;MAED;MACA,IAAIpG,QAAQ,CAACE,IAAI,CAAC3B,cAAc,IAAI,CAACA,cAAc,EAAE;QACnDC,iBAAiB,CAACwB,QAAQ,CAACE,IAAI,CAAC3B,cAAc,CAAC;MACjD;;MAEA;MACA,IAAIyB,QAAQ,CAACE,IAAI,CAACkG,aAAa,EAAE;QAC/B1G,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAC9EjB,aAAa,CAAC,IAAI,CAAC;QACnBE,qBAAqB,CAAC,IAAI,CAAC;QAC3BE,aAAa,CAAC,IAAI,CAAC;MACrB;;MAEA;MACA,IAAIkB,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,IAAIqB,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,mBAAmB,EAAE;QACzFpG,qBAAqB,CAACoB,QAAQ,CAACE,IAAI,CAACvB,kBAAkB,CAAC;QACvDD,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM,IAAIsB,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,gBAAgB,EAAE;QAEzDpG,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;QACA,IAAIsB,QAAQ,CAACE,IAAI,CAACmG,eAAe,IAAIrG,QAAQ,CAACE,IAAI,CAACyD,QAAQ,IAAI3D,QAAQ,CAACE,IAAI,CAACoG,MAAM,EAAE;UAEnF,MAAMC,gBAAgB,GAAGC,8BAA8B,CAACxG,QAAQ,CAACE,IAAI,CAACyD,QAAQ,CAAC;;UAE/E;UACA,MAAM8C,wBAAwB,CAACzG,QAAQ,CAACE,IAAI,CAACoG,MAAM,EAAEC,gBAAgB,EAAEvG,QAAQ,CAACE,IAAI,CAAC3B,cAAc,EAAEN,QAAQ,EAAE+B,QAAQ,CAACE,IAAI,CAACwD,UAAU,CAAC;;UAExI;UACA,OAAO1D,QAAQ,CAACE,IAAI;QACtB;MACF,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,qBAAqB,EAAE;QAE9DpG,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;MACF,CAAC,MAAM,IAAIsB,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,YAAY,EAAE;QACrDtF,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEK,QAAQ,CAACE,IAAI,CAACgF,WAAW,CAAC;QAE3DtG,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;MACF,CAAC,MAAM,IAAIsB,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,gBAAgB,EAAE;QACzDtF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9Cf,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;QACnBI,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIkB,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,0BAA0B,EAAE;QACnEtF,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEK,QAAQ,CAACE,IAAI,CAACrB,UAAU,CAAC;QACpFC,aAAa,CAACkB,QAAQ,CAACE,IAAI,CAACrB,UAAU,CAAC;QACvCD,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIsB,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,wBAAwB,EAAE;QAEjE;QACAlG,aAAa,CAAC;UACZ,GAAGkB,QAAQ,CAACE,IAAI,CAACrB,UAAU;UAC3BkH,qBAAqB,EAAE,KAAK;UAC5BC,gBAAgB,EAAE,IAAI;UACtBC,UAAU,EAAEjG,QAAQ,CAACE,IAAI,CAAC+F,UAAU;UACpCC,aAAa,EAAElG,QAAQ,CAACE,IAAI,CAACgG,aAAa;UAC1CxC,UAAU,EAAE1D,QAAQ,CAACE,IAAI,CAACwD,UAAU;UACpC4C,MAAM,EAAEtG,QAAQ,CAACE,IAAI,CAACoG;QACxB,CAAC,CAAC;QACF1H,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIsB,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,uBAAuB,EAAE;QAEhElG,aAAa,CAAC,IAAI,CAAC;QACnBF,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;QACA,IAAIsB,QAAQ,CAACE,IAAI,CAACmG,eAAe,IAAIrG,QAAQ,CAACE,IAAI,CAACyD,QAAQ,IAAI3D,QAAQ,CAACE,IAAI,CAACoG,MAAM,EAAE;UACnF,MAAMC,gBAAgB,GAAGC,8BAA8B,CAACxG,QAAQ,CAACE,IAAI,CAACyD,QAAQ,CAAC;UAC/EjE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE4G,gBAAgB,CAAC;;UAE7E;UACA,MAAMG,gBAAgB,CAAC1G,QAAQ,CAACE,IAAI,CAACoG,MAAM,EAAEC,gBAAgB,EAAEvG,QAAQ,CAACE,IAAI,CAAC3B,cAAc,EAAEN,QAAQ,EAAE+B,QAAQ,CAACE,IAAI,CAACwD,UAAU,CAAC;UAEhI,OAAO1D,QAAQ,CAACE,IAAI;QACtB;MACF,CAAC,MAAM,IAAIF,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,4BAA4B,EAAE;QACrEtF,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEK,QAAQ,CAACE,IAAI,CAACgF,WAAW,CAAC;QAE1DpG,aAAa,CAAC,IAAI,CAAC;QACnBF,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;MACF,CAAC,MAAM,IAAIsB,QAAQ,CAACE,IAAI,CAACyD,QAAQ,IAAI3D,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,MAAM,EAAE;QACzE;QACAtG,aAAa,CAACsB,QAAQ,CAACE,IAAI,CAACyD,QAAQ,CAAC;QACrC/E,qBAAqB,CAAC,IAAI,CAAC;QAC3Bc,OAAO,CAACC,GAAG,CAAC,gCAAgCK,QAAQ,CAACE,IAAI,CAACyD,QAAQ,CAACI,IAAI,EAAE,CAAC;;QAE1E;QACArE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEK,QAAQ,CAACE,IAAI,CAACyD,QAAQ,CAAC;MACnD,CAAC,MAAM;QACL;QACAjF,aAAa,CAAC,IAAI,CAAC;QACnBE,qBAAqB,CAAC,IAAI,CAAC;QAC3Bc,OAAO,CAACC,GAAG,CAAC,oCAAoCK,QAAQ,CAACE,IAAI,CAAC8E,WAAW,EAAE,CAAC;;QAE5E;QACA,IAAIhF,QAAQ,CAACE,IAAI,CAAC8E,WAAW,KAAK,aAAa,EAAE;UAC/CtF,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D;MACF;;MAEA;MACA,MAAM,IAAIgH,OAAO,CAACC,OAAO,IAAI/G,UAAU,CAAC+G,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAMC,gBAAgB,GAAG,GAAGvG,gBAAgB,CAACmC,SAAS,IAAInC,gBAAgB,CAAC0E,WAAW,IAAI1E,gBAAgB,CAAC+B,SAAS,EAAE;;MAEtH;MACA,IAAI1E,uBAAuB,CAACmJ,GAAG,CAACD,gBAAgB,CAAC,EAAE;QACjDnH,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;UAC9DkH,gBAAgB,EAAEA,gBAAgB,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;UAC3DtE,SAAS,EAAEnC,gBAAgB,CAACmC,SAAS;UACrCuC,WAAW,EAAE1E,gBAAgB,CAAC0E;QAChC,CAAC,CAAC;QACF5G,UAAU,CAAC,KAAK,CAAC;QACjB,OAAO4B,QAAQ,CAACE,IAAI;MACtB;;MAEA;MACAvC,uBAAuB,CAACqJ,GAAG,CAACH,gBAAgB,CAAC;;MAE7C;MACA,IAAIlJ,uBAAuB,CAACsJ,IAAI,GAAG,GAAG,EAAE;QACtC,MAAMC,YAAY,GAAGC,KAAK,CAACC,IAAI,CAACzJ,uBAAuB,CAAC;QACxDA,uBAAuB,CAAC0J,KAAK,CAAC,CAAC;QAC/BH,YAAY,CAACrG,KAAK,CAAC,CAAC,EAAE,CAAC,CAACyG,OAAO,CAACC,GAAG,IAAI5J,uBAAuB,CAACqJ,GAAG,CAACO,GAAG,CAAC,CAAC;MAC1E;;MAEA;MACArJ,WAAW,CAAEuC,YAAY,IAAK;QAC5B;QACA,MAAM+G,WAAW,GAAG/G,YAAY,CAACgH,IAAI,CAACC,GAAG;UAAA,IAAAC,gBAAA;UAAA,OACvCD,GAAG,CAACnH,IAAI,KAAK,WAAW;UACtB;UACC,EAAAoH,gBAAA,GAAAD,GAAG,CAACxC,WAAW,cAAAyC,gBAAA,uBAAfA,gBAAA,CAAiBlF,SAAS,MAAKnC,gBAAgB,CAACmC,SAAS,IACzDiF,GAAG,CAAC1C,WAAW,KAAK1E,gBAAgB,CAAC0E,WAAW;UACjD;UACC0C,GAAG,CAACrF,SAAS,KAAK/B,gBAAgB,CAAC+B,SAAU;UAC9C;UACCqF,GAAG,CAAClH,OAAO,KAAKF,gBAAgB,CAACE,OAAO,IACxC8B,IAAI,CAACsF,GAAG,CAAC,CAACF,GAAG,CAACjF,SAAS,IAAI,CAAC,KAAKnC,gBAAgB,CAACmC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK;UAC3E;UACCiF,GAAG,CAAClH,OAAO,KAAKF,gBAAgB,CAACE,OAAO,IACxCG,IAAI,CAACC,SAAS,CAAC8G,GAAG,CAACpC,OAAO,CAAC,KAAK3E,IAAI,CAACC,SAAS,CAACN,gBAAgB,CAACgF,OAAO,CAAC,IACxEoC,GAAG,CAAC5B,YAAY,KAAKxF,gBAAgB,CAACwF,YAAY,IAClD4B,GAAG,CAAC3B,qBAAqB,KAAKzF,gBAAgB,CAACyF,qBAAqB,IACpEzD,IAAI,CAACsF,GAAG,CAAC,CAACF,GAAG,CAACjF,SAAS,IAAI,CAAC,KAAKnC,gBAAgB,CAACmC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK;UAC3E;UACCiF,GAAG,CAACG,mBAAmB,IAAIvH,gBAAgB,CAAC0E,WAAW,KAAK,0BAA0B,IACtF1C,IAAI,CAACsF,GAAG,CAAC,CAACF,GAAG,CAACjF,SAAS,IAAI,CAAC,KAAKnC,gBAAgB,CAACmC,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK,CAC5E;QAAA,CACH,CAAC;QAED,IAAI+E,WAAW,EAAE;UACf9H,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE;YAC/D8C,SAAS,EAAEnC,gBAAgB,CAACmC,SAAS;YACrCuC,WAAW,EAAE1E,gBAAgB,CAAC0E,WAAW;YACzC3C,SAAS,EAAE/B,gBAAgB,CAAC+B;UAC9B,CAAC,CAAC;UACF,OAAO5B,YAAY,CAAC,CAAC;QACvB;QAEAf,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAC1C8C,SAAS,EAAEnC,gBAAgB,CAACmC,SAAS;UACrCuC,WAAW,EAAE1E,gBAAgB,CAAC0E,WAAW;UACzC3C,SAAS,EAAE/B,gBAAgB,CAAC+B,SAAS;UACrCyF,aAAa,EAAErH,YAAY,CAACsH,MAAM,GAAG;QACvC,CAAC,CAAC;QACF,OAAO,CAAC,GAAGtH,YAAY,EAAEH,gBAAgB,CAAC;MAC5C,CAAC,CAAC;MAEF,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ7B,QAAQ,CAAC,wBAAwB,CAAC;MAClCoB,OAAO,CAACrB,KAAK,CAAC,wBAAwB,EAAE8B,GAAG,CAAC;;MAE5C;MACA,MAAM6H,YAAY,GAAG;QACnBzH,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC;MACDtC,WAAW,CAAEuC,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEuH,YAAY,CAAC,CAAC;MAE9D,OAAO,IAAI;IACb,CAAC,SAAS;MACR;MACA5J,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6J,kCAAkC,GAAGA,CAACtE,QAAQ,EAAED,UAAU,KAAK;IACnE,IAAI;MAAA,IAAAwE,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,IAAI,CAAC1E,UAAU,EAAE;QACfhE,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,OAAOgE,QAAQ;MACjB;MAEA,IAAI,EAACD,UAAU,aAAVA,UAAU,gBAAAwE,qBAAA,GAAVxE,UAAU,CAAEA,UAAU,cAAAwE,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBG,eAAe,cAAAF,sBAAA,gBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCG,aAAa,cAAAF,sBAAA,eAAtDA,sBAAA,CAAwDG,OAAO,GAAE;QACpE7I,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,OAAOgE,QAAQ,CAAC,CAAC;MACnB;MAEA,MAAM6E,eAAe,GAAG9E,UAAU,CAACA,UAAU,CAAC2E,eAAe,CAACC,aAAa,CAACG,SAAS,IAAI,CAAC,CAAC;MAC3F,MAAMC,aAAa,GAAGhF,UAAU,CAACA,UAAU,CAAC2E,eAAe,CAACC,aAAa,CAACI,aAAa,IAAI,SAAS;MAEpG,MAAMC,YAAY,GAAIxE,KAAK,IAAK;QAC9B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7B;UACA,MAAMyE,sBAAsB,GAAGzE,KAAK,CAAC0E,KAAK,CAAC,mBAAmB,CAAC;UAC/D,IAAID,sBAAsB,EAAE;YAC1B,MAAMnD,SAAS,GAAGmD,sBAAsB,CAAC,CAAC,CAAC;YAC3C,IAAIjF,QAAQ,CAACmF,cAAc,CAACrD,SAAS,CAAC,EAAE;cACtC,OAAO9B,QAAQ,CAAC8B,SAAS,CAAC,CAAC,CAAC;YAC9B;UACF;;UAEA;UACA,IAAIsD,MAAM,GAAG5E,KAAK;UAClBM,MAAM,CAACC,IAAI,CAACf,QAAQ,CAAC,CAAC2D,OAAO,CAACC,GAAG,IAAI;YACnC,MAAMyB,WAAW,GAAG,UAAUzB,GAAG,IAAI;YACrC,MAAM0B,UAAU,GAAGtF,QAAQ,CAAC4D,GAAG,CAAC;YAChC,IAAI0B,UAAU,KAAKzF,SAAS,EAAE;cAC5B,MAAM0F,gBAAgB,GAAG,OAAOD,UAAU,KAAK,QAAQ,GAAGtI,IAAI,CAACC,SAAS,CAACqI,UAAU,CAAC,GAAGA,UAAU;cACjGF,MAAM,GAAGA,MAAM,CAACI,OAAO,CAAC,IAAIC,MAAM,CAACJ,WAAW,CAACG,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,EAAED,gBAAgB,CAAC;YAChH;UACF,CAAC,CAAC;UACF,OAAOH,MAAM;QACf,CAAC,MAAM,IAAI5B,KAAK,CAACkC,OAAO,CAAClF,KAAK,CAAC,EAAE;UAC/B,OAAOA,KAAK,CAACN,GAAG,CAAC8E,YAAY,CAAC;QAChC,CAAC,MAAM,IAAI,OAAOxE,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;UACtD,MAAMmF,SAAS,GAAG,CAAC,CAAC;UACpB7E,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC,CAACmD,OAAO,CAACC,GAAG,IAAI;YAChC+B,SAAS,CAAC/B,GAAG,CAAC,GAAGoB,YAAY,CAACxE,KAAK,CAACoD,GAAG,CAAC,CAAC;UAC3C,CAAC,CAAC;UACF,OAAO+B,SAAS;QAClB;QACA,OAAOnF,KAAK;MACd,CAAC;MAED,IAAIoF,aAAa,GAAG,CAAC,CAAC;MAEtB,QAAQb,aAAa;QACnB,KAAK,SAAS;UACZa,aAAa,GAAGZ,YAAY,CAACH,eAAe,CAAC;UAC7C;QACF,KAAK,OAAO;UACVe,aAAa,GAAG;YAAE,GAAG5F,QAAQ;YAAE,GAAGgF,YAAY,CAACH,eAAe;UAAE,CAAC;UACjE;QACF,KAAK,QAAQ;UACXe,aAAa,GAAG;YAAE,GAAG5F,QAAQ;YAAE6F,MAAM,EAAEb,YAAY,CAACH,eAAe;UAAE,CAAC;UACtE;QACF;UACEe,aAAa,GAAGZ,YAAY,CAACH,eAAe,CAAC;MACjD;MAEA9I,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE4J,aAAa,CAAC;MACpD,OAAOA,aAAa;IACtB,CAAC,CAAC,OAAOlL,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3E,OAAOsF,QAAQ,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM6C,8BAA8B,GAAI7C,QAAQ,IAAK;IACnDjE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;IAE1D;IACA,MAAMqD,QAAQ,GAAGrC,IAAI,CAACiC,KAAK,CAACzD,YAAY,CAACM,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACjE,MAAM2D,cAAc,GAAGjE,YAAY,CAACM,OAAO,CAAC,gBAAgB,CAAC;IAC7D,MAAM4D,YAAY,GAAGlE,YAAY,CAACM,OAAO,CAAC,cAAc,CAAC;IAEzDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1CuD,KAAK,EAAEF,QAAQ,CAACE,KAAK;MACrBC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;MAC3BC,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA,YAAY;MAC1BoG,gBAAgB,EAAE9F;IACpB,CAAC,CAAC;;IAEF;IACA,MAAM4C,gBAAgB,GAAG;MACvB,GAAG5C,QAAQ;MACX;MACAT,KAAK,EAAGS,QAAQ,CAACT,KAAK,IAAIS,QAAQ,CAACT,KAAK,CAACwG,IAAI,CAAC,CAAC,KAAK,EAAE,GAAI/F,QAAQ,CAACT,KAAK,GAAIF,QAAQ,CAACE,KAAK,IAAI,EAAG;MACjG;MACAyG,IAAI,EAAGhG,QAAQ,CAACgG,IAAI,IAAIhG,QAAQ,CAACgG,IAAI,CAACD,IAAI,CAAC,CAAC,KAAK,EAAE,GAAI/F,QAAQ,CAACgG,IAAI,GAAI3G,QAAQ,CAACG,QAAQ,IAAI,EAAG;MAChG;MACAyG,SAAS,EAAGjG,QAAQ,CAACiG,SAAS,IAAIjG,QAAQ,CAACiG,SAAS,CAACF,IAAI,CAAC,CAAC,KAAK,EAAE,GAAI/F,QAAQ,CAACiG,SAAS,GAAIxG,cAAc,IAAI;IAChH,CAAC;IAED1D,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrDkK,QAAQ,EAAElG,QAAQ;MAClBmG,QAAQ,EAAEvD,gBAAgB;MAC1BwD,gBAAgB,EAAE;QAChB7G,KAAK,EAAEqD,gBAAgB,CAACrD,KAAK,KAAKS,QAAQ,CAACT,KAAK;QAChDyG,IAAI,EAAEpD,gBAAgB,CAACoD,IAAI,KAAKhG,QAAQ,CAACgG,IAAI;QAC7CC,SAAS,EAAErD,gBAAgB,CAACqD,SAAS,KAAKjG,QAAQ,CAACiG;MACrD;IACF,CAAC,CAAC;IAEF,OAAOrD,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAG,MAAAA,CAAOH,MAAM,EAAE3C,QAAQ,EAAEpF,cAAc,EAAEoE,eAAe,EAAEe,UAAU,GAAG,IAAI,KAAK;IAC/G,IAAI;MAAA,IAAAsG,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF3K,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;;MAE5E;MACA,MAAMqD,QAAQ,GAAGrC,IAAI,CAACiC,KAAK,CAACzD,YAAY,CAACM,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;;MAE/D;MACA,IAAI6K,SAAS,GAAGtH,QAAQ,CAACO,KAAK;;MAE9B;MACA,MAAMgH,iBAAiB,GAAG7G,UAAU,IAAIjF,UAAU;;MAElD;MACA,IAAI8L,iBAAiB,IAAI,EAAAP,qBAAA,GAAAO,iBAAiB,CAAC7G,UAAU,cAAAsG,qBAAA,wBAAAC,sBAAA,GAA5BD,qBAAA,CAA8B3B,eAAe,cAAA4B,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CO,UAAU,cAAAN,sBAAA,uBAAzDA,sBAAA,CAA2DO,cAAc,MAAK,IAAI,EAAE;QAC3GH,SAAS,GAAGC,iBAAiB,CAAC7G,UAAU,CAAC2E,eAAe,CAACmC,UAAU,CAACjH,KAAK;QACzE7D,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACzD;MAEF,MAAM+K,iBAAiB,GAAGzC,kCAAkC,CAACtE,QAAQ,EAAE4G,iBAAiB,CAAC;MAEzF,MAAMvK,QAAQ,GAAG,MAAMzC,GAAG,CAAC0F,IAAI,CAAC,mBAAmB,EAAE;QACnDqD,MAAM,EAAEA,MAAM;QACd3C,QAAQ,EAAE+G,iBAAiB;QAC3BnM,cAAc,EAAEA;MAClB,CAAC,EAAE;QACD+E,OAAO,EAAE;UACP,UAAU,EAAEN,QAAQ,CAACE,KAAK;UAC1B,aAAa,EAAEF,QAAQ,CAACG,QAAQ;UAChC,eAAe,EAAEmH,SAAS,GAAG,UAAUA,SAAS,EAAE,GAAG9G;QACvD;MACF,CAAC,CAAC;MAEF9D,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEK,QAAQ,CAACE,IAAI,CAAC;;MAElE;MACA,IAAIyK,aAAa;;MAEjB;MACA,IAAI3K,QAAQ,CAACE,IAAI,CAAC0K,OAAO,EAAE;QAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACzBrL,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;QAE3D;QACA,MAAMgF,WAAW,GAAG4F,iBAAiB,KACnC,EAAAM,qBAAA,GAAAN,iBAAiB,CAACxG,IAAI,cAAA8G,qBAAA,uBAAtBA,qBAAA,CAAwBjG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAAiG,sBAAA,GACvDP,iBAAiB,CAAC7G,UAAU,cAAAoH,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8B/G,IAAI,cAAAgH,sBAAA,uBAAlCA,sBAAA,CAAoCnG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EACpE;QAED,IAAIF,WAAW,EAAE;UACfgG,aAAa,GAAG,oEAAoE;QACtF,CAAC,MAAM;UACLA,aAAa,GAAG,uDAAuD;QACzE;MACF,CAAC,MAAM;QACLjL,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEK,QAAQ,CAACE,IAAI,CAACG,OAAO,CAAC;QAC9EsK,aAAa,GAAG3K,QAAQ,CAACE,IAAI,CAACG,OAAO,IAAI,0CAA0C;MACrF;;MAEA;MACA,MAAMsE,WAAW,GAAG4F,iBAAiB,KACnC,EAAAJ,sBAAA,GAAAI,iBAAiB,CAACxG,IAAI,cAAAoG,sBAAA,uBAAtBA,sBAAA,CAAwBvF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAAuF,sBAAA,GACvDG,iBAAiB,CAAC7G,UAAU,cAAA0G,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BrG,IAAI,cAAAsG,sBAAA,uBAAlCA,sBAAA,CAAoCzF,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EACpE;MAED3G,WAAW,CAAEuC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEmK,aAAa;QACtB3F,WAAW,EAAEL,WAAW,GAAG,sBAAsB,GAAG,gBAAgB;QACpEO,WAAW,EAAE;UACP,SAAS,EAAE,IAAI;UACjB;UACE,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,eAAe;UAC7B,MAAM,EAAE,IAAI;UACZ,SAAS,EAAE;QACX,CAAC,CAAE;MACX,CAAC,CACF,CAAC;MAEF,OAAOlF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;;MAEvE;MACA,IAAI2M,mBAAmB,GAAG,mEAAmE;;MAE7F;MACA,IAAI3M,KAAK,CAAC2B,QAAQ,IAAI3B,KAAK,CAAC2B,QAAQ,CAACE,IAAI,IAAI7B,KAAK,CAAC2B,QAAQ,CAACE,IAAI,CAACG,OAAO,EAAE;QACxE2K,mBAAmB,GAAG3M,KAAK,CAAC2B,QAAQ,CAACE,IAAI,CAACG,OAAO;MACnD;;MAEA;MACAnC,WAAW,CAAEuC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEwK,mBAAmB;QAC5BhG,WAAW,EAAE,gBAAgB;QAC7BE,WAAW,EAAE;UAAE0F,OAAO,EAAE,KAAK;UAAEvK,OAAO,EAAE2K;QAAoB,CAAC,CAAC;MAChE,CAAC,CACF,CAAC;MAEF,MAAM3M,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMqI,gBAAgB,GAAG,MAAAA,CAAOJ,MAAM,EAAE3C,QAAQ,EAAEpF,cAAc,EAAEoE,eAAe,EAAEe,UAAU,GAAG,IAAI,KAAK;IACvG,IAAI;MAAA,IAAAuH,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACFzL,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;MAEpE;MACA,MAAMqD,QAAQ,GAAGrC,IAAI,CAACiC,KAAK,CAACzD,YAAY,CAACM,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;;MAEjE;MACA,IAAI6K,SAAS,GAAGtH,QAAQ,CAACO,KAAK;;MAE9B;MACA,MAAMgH,iBAAiB,GAAG7G,UAAU,IAAIjF,UAAU;;MAElD;MACA,IAAI8L,iBAAiB,IAAI,EAAAU,sBAAA,GAAAV,iBAAiB,CAAC7G,UAAU,cAAAuH,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8B5C,eAAe,cAAA6C,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CV,UAAU,cAAAW,sBAAA,uBAAzDA,sBAAA,CAA2DV,cAAc,MAAK,IAAI,EAAE;QAC3GH,SAAS,GAAGC,iBAAiB,CAAC7G,UAAU,CAAC2E,eAAe,CAACmC,UAAU,CAACjH,KAAK;QACzE7D,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACzD;MAEA,MAAM+K,iBAAiB,GAAGzC,kCAAkC,CAACtE,QAAQ,EAAE4G,iBAAiB,CAAC;MAEzF,MAAMvK,QAAQ,GAAG,MAAMzC,GAAG,CAAC0F,IAAI,CAAC,mBAAmB,EAAE;QACnDqD,MAAM,EAAEA,MAAM;QACd3C,QAAQ,EAAE+G,iBAAiB;QAC3BnM,cAAc,EAAEA,cAAc;QAC9B6M,YAAY,EAAE;MAChB,CAAC,EAAE;QACD9H,OAAO,EAAE;UACP,UAAU,EAAEN,QAAQ,CAACE,KAAK;UAC1B,aAAa,EAAEF,QAAQ,CAACG,QAAQ;UAChC,eAAe,EAAEmH,SAAS,GAAG,UAAUA,SAAS,EAAE,GAAG9G;QACvD;MACF,CAAC,CAAC;MAEF9D,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEK,QAAQ,CAACE,IAAI,CAAC;;MAE1D;MACA,IAAIyK,aAAa;;MAEjB;MACA,IAAI3K,QAAQ,CAACE,IAAI,CAAC0K,OAAO,EAAE;QACzBlL,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnDgL,aAAa,GAAG,8DAA8D;MAChF,CAAC,MAAM;QACLjL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEK,QAAQ,CAACE,IAAI,CAACG,OAAO,CAAC;QACtE;QACEsK,aAAa,GAAG,8BAA8B;MAElD;;MAEA;MACAzM,WAAW,CAAEuC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEmK,aAAa;QACtB3F,WAAW,EAAE,uBAAuB;QACpCE,WAAW,EAAE;UACX,SAAS,EAAE,IAAI;UACf,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,eAAe;UAC7B,MAAM,EAAE,IAAI;UACZ,SAAS,EAAE;QACb;MACF,CAAC,CACF,CAAC;MAEF,OAAOlF,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdqB,OAAO,CAACrB,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;;MAE/D;MACA,IAAI2M,mBAAmB,GAAG,0EAA0E;;MAEpG;MACA,IAAI3M,KAAK,CAAC2B,QAAQ,IAAI3B,KAAK,CAAC2B,QAAQ,CAACE,IAAI,IAAI7B,KAAK,CAAC2B,QAAQ,CAACE,IAAI,CAACG,OAAO,EAAE;QACxE2K,mBAAmB,GAAG3M,KAAK,CAAC2B,QAAQ,CAACE,IAAI,CAACG,OAAO;MACnD;;MAEA;MACAnC,WAAW,CAAEuC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEwK,mBAAmB;QAC5BhG,WAAW,EAAE,uBAAuB;QACpCE,WAAW,EAAE;UAAE0F,OAAO,EAAE,KAAK;UAAEvK,OAAO,EAAE2K;QAAoB;MAC9D,CAAC,CACF,CAAC;MAEF,MAAM3M,KAAK;IACb;EACF,CAAC;EAED,MAAMgN,UAAU,GAAG,MAAAA,CAAO/E,MAAM,EAAE3C,QAAQ,EAAE2H,WAAW,GAAG,KAAK,EAAEC,UAAU,GAAG,IAAI,EAAErG,WAAW,GAAG,IAAI,EAAE0F,OAAO,GAAG,KAAK,KAAK;IAC1H,IAAI;MACFtM,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAIgN,WAAW,EAAE;QACf;QACA,IAAIC,UAAU,EAAE;UACd7M,aAAa,CAAC6M,UAAU,CAAC;UACzB,OAAOA,UAAU;QACnB;;QAEA;QACA,MAAMvL,QAAQ,GAAG,MAAMzC,GAAG,CAAC0C,GAAG,CAAC,mBAAmBqG,MAAM,EAAE,CAAC;QAC3D5H,aAAa,CAACsB,QAAQ,CAACE,IAAI,CAAC;QAC5B,OAAOF,QAAQ,CAACE,IAAI;MACtB;;MAEA;MACA,MAAMkC,WAAW,GAAG;QAClB7B,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,gBAAgB;QACzBmD;MACF,CAAC;MACDzF,WAAW,CAAEuC,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAE2B,WAAW,CAAC,CAAC;;MAE7D;MACAhE,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAIkC,gBAAgB;MACpB,IAAIkL,YAAY,GAAG,IAAI;;MAGvB;MACA,IAAItG,WAAW,EAAE;QACf,IAAI7E,OAAO;QACX,IAAIuK,OAAO,EAAE;UACX;UACA,MAAMa,oBAAoB,GAAG9H,QAAQ,IACnCA,QAAQ,CAACmF,cAAc,CAAC,MAAM,CAAC,IAC/BnF,QAAQ,CAACmF,cAAc,CAAC,QAAQ,CAAC,IACjCnF,QAAQ,CAACmF,cAAc,CAAC,QAAQ,CAAC,IACjCnF,QAAQ,CAACmF,cAAc,CAAC,SAAS,CAClC;UAED,IAAI2C,oBAAoB,EAAE;YACxBpL,OAAO,GAAG,wCAAwC;UACpD,CAAC,MAAM;YACLA,OAAO,GAAG,0DAA0D6E,WAAW,CAACwG,MAAM,GAAG;YACzF,IAAIxG,WAAW,CAAChF,IAAI,IAAI,OAAOgF,WAAW,CAAChF,IAAI,KAAK,QAAQ,EAAE;cAC5D,IAAIgF,WAAW,CAAChF,IAAI,CAACG,OAAO,EAAE;gBAC5BA,OAAO,IAAI,cAAc6E,WAAW,CAAChF,IAAI,CAACG,OAAO,EAAE;cACrD;YACF,CAAC,MAAM,IAAI6E,WAAW,CAAChF,IAAI,EAAE;cAC3BG,OAAO,IAAI,cAAc6E,WAAW,CAAChF,IAAI,EAAE;YAC7C;UACF;QACF,CAAC,MAAM;UACHG,OAAO,GAAG,wCAAwC;UAClD;UACF,IAAI6E,WAAW,CAAChF,IAAI,IAAIgF,WAAW,CAAChF,IAAI,CAAC7B,KAAK,EAAE;YAC9CgC,OAAO,IAAI,WAAW6E,WAAW,CAAChF,IAAI,CAAC7B,KAAK,EAAE;UAChD;QACF;QAEAiC,gBAAgB,GAAG;UACjBC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEH,OAAO;UAChB6E,WAAW,EAAEA,WAAW;UACxBvB,QAAQ,EAAElF,UAAU,CAAC;QACvB,CAAC;QAED+M,YAAY,GAAG;UACbnL,OAAO,EAAEA,OAAO;UAChB6E,WAAW,EAAEA,WAAW;UACxB0F,OAAO,EAAEA;QACX,CAAC;MACH,CAAC,MAAM;QAAA,IAAAe,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACL;QACA,MAAM7I,QAAQ,GAAGrC,IAAI,CAACiC,KAAK,CAACzD,YAAY,CAACM,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;;QAEjE;QACA,IAAI6K,SAAS,GAAGtH,QAAQ,CAACO,KAAK;;QAE9B;QACA,IAAI9E,UAAU,IAAIA,UAAU,CAACiF,UAAU,CAAC2E,eAAe,CAACmC,UAAU,CAACC,cAAc,KAAK,IAAI,IAAIhM,UAAU,CAACiF,UAAU,CAAC2E,eAAe,CAACmC,UAAU,CAACC,cAAc,EAAE;UAC7JH,SAAS,GAAG7L,UAAU,CAACiF,UAAU,CAAC2E,eAAe,CAACmC,UAAU,CAACjH,KAAK;UAClE7D,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;QACtF,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAClE;;QAEA;QACA,MAAM+K,iBAAiB,GAAGzC,kCAAkC,CAACtE,QAAQ,EAAElF,UAAU,CAAC;QAElF,MAAMuB,QAAQ,GAAG,MAAMzC,GAAG,CAAC0F,IAAI,CAAC,mBAAmB,EAAE;UACnDqD,MAAM;UACN3C,QAAQ,EAAE+G,iBAAiB;UAC3BnM;QACF,CAAC,EAAE;UACD+E,OAAO,EAAE;YACP,eAAe,EAAEgH,SAAS,GAAG,UAAUA,SAAS,EAAE,GAAG9G,SAAS;YAC9D,UAAU,EAAER,QAAQ,CAACE,KAAK,IAAIM,SAAS;YACvC,aAAa,EAAER,QAAQ,CAACG,QAAQ,IAAIK;UACtC;QACF,CAAC,CAAC;;QAEF;QACA,MAAMmB,WAAW,GAAGlG,UAAU,KAC5B,EAAAkN,gBAAA,GAAAlN,UAAU,CAACsF,IAAI,cAAA4H,gBAAA,uBAAfA,gBAAA,CAAiB/G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAA+G,qBAAA,GAChDnN,UAAU,CAACiF,UAAU,cAAAkI,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuB7H,IAAI,cAAA8H,sBAAA,uBAA3BA,sBAAA,CAA6BjH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAC7D;QAEDvE,gBAAgB,GAAG;UACjBC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAER,QAAQ,CAACE,IAAI,CAACwL,MAAM,IAAE,GAAG,GAAC,wBAAwB,GAAC1L,QAAQ,CAACE,IAAI,CAACG,OAAO;UACjF6E,WAAW,EAAElF,QAAQ,CAACE,IAAI,CAACwL,MAAM,IAAE,GAAG,GACvC;YACO,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,GAAG;YACb,YAAY,EAAE,EAAE;YAChB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE;UACvB,CAAC,GAAC1L,QAAQ,CAACE,IAAI,CAACgF,WAAW;UACrBvB,QAAQ,EAAElF,UAAU;UAAE;UACtBuG,WAAW,EAAEL,WAAW,GAAG,sBAAsB,GAAG;QACtD,CAAC;QAED6G,YAAY,GAAGxL,QAAQ,CAACE,IAAI;MAC9B;;MAEA;MACA,MAAM,IAAIyG,OAAO,CAACC,OAAO,IAAI/G,UAAU,CAAC+G,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD1I,WAAW,CAAEuC,YAAY,IAAK;QAC5B,MAAMC,WAAW,GAAG,CAAC,GAAGD,YAAY,EAAEH,gBAAgB,CAAC;QACvD;QACAnB,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEuB,IAAI,CAACC,SAAS,CAACF,WAAW,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9E,OAAOH,WAAW;MACpB,CAAC,CAAC;;MAEF;MACAhC,aAAa,CAAC,IAAI,CAAC;MAEnB,OAAO8M,YAAY;IACrB,CAAC,CAAC,OAAOrL,GAAG,EAAE;MACZ;MACAT,OAAO,CAACrB,KAAK,CAAC,wBAAwB,EAAE8B,GAAG,CAAC;;MAE5C;MACA,MAAM6H,YAAY,GAAG;QACnBzH,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC;MACD;;MAEA,OAAO,IAAI;IACb,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0N,SAAS,GAAGA,CAAA,KAAM;IACtB5N,WAAW,CAAC,EAAE,CAAC;IACfM,iBAAiB,CAAC,IAAI,CAAC;IACvBE,aAAa,CAAC,IAAI,CAAC;IACnBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,aAAa,CAAC,IAAI,CAAC;IACnBE,qBAAqB,CAAC,KAAK,CAAC;IAC5BnB,wBAAwB,GAAG,KAAK;IAChCsB,YAAY,CAACW,UAAU,CAAC,gBAAgB,CAAC;EAC3C,CAAC;EAED,MAAMiM,WAAW,GAAGA,CAAA,KAAM;IACxBrN,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAM0D,WAAW,GAAG;MAAE7B,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAgC,CAAC;IAC9E,MAAMF,gBAAgB,GAAG;MAAEC,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAA2C,CAAC;;IAEnG;IACAtC,WAAW,CAACuC,YAAY,IAAI;MAC1B,MAAMC,WAAW,GAAG,CAAC,GAAGD,YAAY,EAAE2B,WAAW,EAAE9B,gBAAgB,CAAC;MACpE;MACAnB,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEuB,IAAI,CAACC,SAAS,CAACF,WAAW,CAACG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9E,OAAOH,WAAW;IACpB,CAAC,CAAC;EACJ,CAAC;EAED,oBACEjD,OAAA,CAACC,WAAW,CAACsO,QAAQ;IACnB7H,KAAK,EAAE;MACLlG,QAAQ;MACRE,OAAO;MACPE,KAAK;MACLE,cAAc;MACdE,UAAU;MACVE,kBAAkB;MAClBE,UAAU;MACVI,gBAAgB;MAChB6B,WAAW;MACXV,mBAAmB;MACnBiL,UAAU;MACVS,SAAS;MACTC,WAAW;MACXnM,gBAAgB;MAChB1B,WAAW;MACXY,aAAa;MACbN,iBAAiB;MACjBI,qBAAqB;MACrBF,aAAa;MACbQ;IACF,CAAE;IAAAnB,QAAA,EAEDA;EAAQ;IAAAkO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACpO,EAAA,CAj+BWF,YAAY;AAAAuO,EAAA,GAAZvO,YAAY;AAm+BzB,OAAO,MAAMwO,OAAO,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAMpP,UAAU,CAACO,WAAW,CAAC;AAAA;AAAC6O,GAAA,CAAxCD,OAAO;AAEpB,eAAe5O,WAAW;AAAC,IAAA2O,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}