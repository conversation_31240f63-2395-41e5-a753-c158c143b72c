{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatMessage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatMessage = ({\n  message,\n  onOptionSelect,\n  onFormLinkTriggered\n}) => {\n  _s();\n  var _message$apiResponse4, _message$apiResponse5;\n  const isUser = message.role === 'user';\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\n\n  // Check if message content contains leave balance information\n  const hasLeaveBalanceInContent = () => {\n    var _message$apiResponse, _message$apiResponse2;\n    const content = message.content || '';\n    const apiResponseData = ((_message$apiResponse = message.apiResponse) === null || _message$apiResponse === void 0 ? void 0 : _message$apiResponse.data) || {};\n    const formattedResponse = ((_message$apiResponse2 = message.apiResponse) === null || _message$apiResponse2 === void 0 ? void 0 : _message$apiResponse2.formattedResponse) || '';\n\n    // Check in message content\n    const contentHasLeave = content.toLowerCase().includes('leave balance') || content.toLowerCase().includes('leave type') || content.toLowerCase().includes('sick leave') || content.toLowerCase().includes('casual leave') || content.toLowerCase().includes('privilege leave') || content.includes('balance:') || content.includes('days remaining') || content.includes('leaveTypeName:') || content.includes('Record 1:') && content.includes('balance:');\n\n    // Check in API response formatted response\n    const formattedHasLeave = formattedResponse.toLowerCase().includes('leave') || formattedResponse.includes('balance:') || formattedResponse.includes('leaveTypeName:');\n\n    // Check if API response data contains leave-related fields\n    const dataHasLeave = Array.isArray(apiResponseData) ? apiResponseData.some(item => item.hasOwnProperty('leaveTypeName') || item.hasOwnProperty('balance')) : typeof apiResponseData === 'object' && (apiResponseData.hasOwnProperty('leaveTypeName') || apiResponseData.hasOwnProperty('balance'));\n    return contentHasLeave || formattedHasLeave || dataHasLeave;\n  };\n\n  // Handle checkbox selection\n  const handleCheckboxToggle = option => {\n    const newSelected = selectedCheckboxes.includes(option) ? selectedCheckboxes.filter(item => item !== option) : [...selectedCheckboxes, option];\n    setSelectedCheckboxes(newSelected);\n  };\n\n  // Handle checkbox submission\n  const handleCheckboxSubmit = () => {\n    if (selectedCheckboxes.length > 0) {\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\n      setSelectedCheckboxes([]);\n    }\n  };\n\n  // Render leave balance with leave type buttons\n  const renderLeaveBalanceWithButtons = (apiResponse, formLinkingConfig = null) => {\n    var _message$formData, _message$formConfig;\n    const formId = ((_message$formData = message.formData) === null || _message$formData === void 0 ? void 0 : _message$formData._id) || ((_message$formConfig = message.formConfig) === null || _message$formConfig === void 0 ? void 0 : _message$formConfig._id);\n\n    // Default leave type configuration if none provided\n    const defaultLeaveConfig = {\n      enabled: true,\n      recordActions: [{\n        buttonText: 'Sick leave',\n        targetFormName: 'Leave Application',\n        fieldMapping: {\n          leaveType: 'sick leave'\n        },\n        conditions: [],\n        buttonStyle: 'primary',\n        autoSubmitOnClick: false,\n        autoTrigger: {\n          enabled: false,\n          delaySeconds: 2\n        }\n      }, {\n        buttonText: 'Casual leave',\n        targetFormName: 'Leave Application',\n        fieldMapping: {\n          leaveType: 'casual leave'\n        },\n        conditions: [],\n        buttonStyle: 'primary',\n        autoSubmitOnClick: false,\n        autoTrigger: {\n          enabled: false,\n          delaySeconds: 2\n        }\n      }, {\n        buttonText: 'Privilege Leave',\n        targetFormName: 'Leave Application',\n        fieldMapping: {\n          leaveType: 'Privilege Leave'\n        },\n        conditions: [],\n        buttonStyle: 'primary',\n        autoSubmitOnClick: false,\n        autoTrigger: {\n          enabled: false,\n          delaySeconds: 2\n        }\n      }]\n    };\n\n    // Use provided config or default\n    const activeConfig = formLinkingConfig || defaultLeaveConfig;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 bg-blue-50 rounded-md mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"whitespace-pre-wrap text-sm text-blue-800\",\n          children: apiResponse.leaveBalance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: activeConfig.recordActions.map((action, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleLeaveTypeClick(action),\n          className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n          children: action.buttonText\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 107,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Handle leave type button clicks\n  const handleLeaveTypeClick = action => {\n    console.log('🍃 Leave type clicked:', action.buttonText);\n\n    // Trigger the leave application form with pre-filled leave type\n    if (onFormLinkTriggered) {\n      onFormLinkTriggered({\n        targetForm: {\n          name: action.targetFormName\n        },\n        prefillData: action.fieldMapping,\n        buttonText: action.buttonText,\n        buttonStyle: action.buttonStyle\n      });\n    }\n  };\n\n  // Handle single option selection (for radio/select)\n  const handleSingleOptionSelect = option => {\n    onOptionSelect && onOptionSelect(option);\n  };\n  // Helper function to check if data is empty (comprehensive check)\n  const checkIfDataIsEmpty = data => {\n    if (!data) return true;\n    if (Array.isArray(data)) return data.length === 0;\n    if (typeof data === 'object' && data !== null) {\n      if (Object.keys(data).length === 0) return true;\n      // Check nested data structure\n      if (data.data) return checkIfDataIsEmpty(data.data);\n      // Check common array fields\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n      for (const field of arrayFields) {\n        if (data[field] && Array.isArray(data[field])) {\n          return data[field].length === 0;\n        }\n      }\n      // Check if all values are empty\n      return Object.values(data).every(val => val === null || val === undefined || val === '' || Array.isArray(val) && val.length === 0 || typeof val === 'object' && val !== null && Object.keys(val).length === 0);\n    }\n    return false;\n  };\n\n  // Helper function to render \"No data available\" message\n  const renderNoDataMessage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\",\n    children: /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-blue-800 text-sm font-medium\",\n      children: \"No data available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 166,\n    columnNumber: 5\n  }, this);\n\n  // Format API response for display\n  const formatApiResponse = apiResponse => {\n    var _message$formData2, _message$formData2$fo, _message$formConfig2, _message$formData3, _message$formConfig3, _message$formConfig3$, _message$apiResponse3, _formLinkingConfig$re4;\n    if (!apiResponse) return null;\n\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\n    const formLinkingConfig = ((_message$formData2 = message.formData) === null || _message$formData2 === void 0 ? void 0 : (_message$formData2$fo = _message$formData2.formConfig) === null || _message$formData2$fo === void 0 ? void 0 : _message$formData2$fo.formLinking) || ((_message$formConfig2 = message.formConfig) === null || _message$formConfig2 === void 0 ? void 0 : _message$formConfig2.formLinking) || ((_message$formData3 = message.formData) === null || _message$formData3 === void 0 ? void 0 : _message$formData3.formLinking) || ((_message$formConfig3 = message.formConfig) === null || _message$formConfig3 === void 0 ? void 0 : (_message$formConfig3$ = _message$formConfig3.formConfig) === null || _message$formConfig3$ === void 0 ? void 0 : _message$formConfig3$.formLinking);\n\n    // Special handling for leave balance API responses - ALWAYS show leave types\n    if ((_message$apiResponse3 = message.apiResponse) !== null && _message$apiResponse3 !== void 0 && _message$apiResponse3.leaveBalance) {\n      var _message$formConfig4;\n      console.log('🍃 Leave balance detected in apiResponse.leaveBalance, showing with leave type buttons');\n      // Try to find form linking config in the message structure, but use default if not found\n      const leaveFormLinkingConfig = (_message$formConfig4 = message.formConfig) === null || _message$formConfig4 === void 0 ? void 0 : _message$formConfig4.formLinking;\n      return renderLeaveBalanceWithButtons(message.apiResponse, leaveFormLinkingConfig);\n    }\n\n    // Also check if this is a leave balance response in the API data or formatted response\n    if (message.apiResponse && hasLeaveBalanceInContent()) {\n      var _message$formConfig5;\n      console.log('🍃 Leave balance detected in API response data/content, showing with leave type buttons');\n      // Create a synthetic leave balance response\n      const syntheticResponse = {\n        leaveBalance: message.apiResponse.formattedResponse || message.content || 'Leave balance information',\n        data: message.apiResponse.data\n      };\n      const leaveFormLinkingConfig = (_message$formConfig5 = message.formConfig) === null || _message$formConfig5 === void 0 ? void 0 : _message$formConfig5.formLinking;\n      return renderLeaveBalanceWithButtons(syntheticResponse, leaveFormLinkingConfig);\n    }\n\n    // For successful responses with data, check if we should display records with actions\n    if (apiResponse.success && apiResponse.data) {\n      var _message$conversation, _message$hybridFlow, _formLinkingConfig$re, _formLinkingConfig$re3;\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\n      const hasActiveConversationalFlow = message.isConversationalPhase || message.isHybridFlow || ((_message$conversation = message.conversationalFlow) === null || _message$conversation === void 0 ? void 0 : _message$conversation.isActive) || ((_message$hybridFlow = message.hybridFlow) === null || _message$hybridFlow === void 0 ? void 0 : _message$hybridFlow.isActive) || message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow);\n\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\n      if (formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re = formLinkingConfig.recordActions) === null || _formLinkingConfig$re === void 0 ? void 0 : _formLinkingConfig$re.length) > 0 && !hasActiveConversationalFlow) {\n        var _apiResponse$data, _formLinkingConfig$re2, _message$formData4, _message$formConfig6;\n        // Extract the actual data from the API response\n        const actualData = ((_apiResponse$data = apiResponse.data) === null || _apiResponse$data === void 0 ? void 0 : _apiResponse$data.data) || apiResponse.data;\n\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\n        const hasDisabledAutoTrigger = (_formLinkingConfig$re2 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re2 === void 0 ? void 0 : _formLinkingConfig$re2.some(action => {\n          var _action$autoTrigger;\n          return !((_action$autoTrigger = action.autoTrigger) !== null && _action$autoTrigger !== void 0 && _action$autoTrigger.enabled);\n        });\n        const formId = ((_message$formData4 = message.formData) === null || _message$formData4 === void 0 ? void 0 : _message$formData4._id) || ((_message$formConfig6 = message.formConfig) === null || _message$formConfig6 === void 0 ? void 0 : _message$formConfig6._id);\n\n        // Function to render records with interleaved apply buttons\n        const renderRecordsWithButtons = () => {\n          let records = [];\n\n          // Extract records from actualData\n          if (Array.isArray(actualData)) {\n            records = actualData;\n          } else if (actualData && typeof actualData === 'object') {\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n            for (const field of arrayFields) {\n              if (actualData[field] && Array.isArray(actualData[field])) {\n                records = actualData[field];\n                break;\n              }\n            }\n            if (records.length === 0) {\n              records = [actualData];\n            }\n          }\n\n          // Check if records are empty using helper function\n          if (checkIfDataIsEmpty(records)) {\n            return renderNoDataMessage();\n          }\n\n          // Split formatted response by \"Record N:\" pattern\n          let formattedRecords = [];\n          if (apiResponse.formattedResponse) {\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\n            formattedRecords = recordSections.filter(section => section.trim());\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [records.map((record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [formattedRecords[index] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: formattedRecords[index].trim()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 263,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 21\n              }, this), (formLinkingConfig === null || formLinkingConfig === void 0 ? void 0 : formLinkingConfig.enabled) && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2 ml-3\",\n                children: /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                  data: [record] // Pass only this specific record\n                  ,\n                  formId: formId,\n                  formLinkingConfig: formLinkingConfig,\n                  onFormLinkTriggered: onFormLinkTriggered,\n                  showOnlyButtons: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 272,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this)), formattedRecords.length === 0 && apiResponse.formattedResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: apiResponse.formattedResponse\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this), !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                data: actualData,\n                formId: formId,\n                formLinkingConfig: formLinkingConfig,\n                onFormLinkTriggered: onFormLinkTriggered,\n                showOnlyButtons: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 294,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this);\n        };\n        return renderRecordsWithButtons();\n      }\n\n      // If we have a formatted response but no form linking, display just the formatted response\n      if (apiResponse.formattedResponse && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re3 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re3 === void 0 ? void 0 : _formLinkingConfig$re3.length) > 0)) {\n        // Check if the underlying data is empty using helper function\n        if (checkIfDataIsEmpty(apiResponse.data)) {\n          return renderNoDataMessage();\n        }\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 p-3 bg-blue-50 rounded-md\",\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: \"whitespace-pre-wrap text-sm text-blue-800\",\n            children: apiResponse.formattedResponse\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this);\n      }\n    }\n\n    // Check if data is empty (array with no items or empty object)\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re4 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re4 === void 0 ? void 0 : _formLinkingConfig$re4.length) > 0)) {\n      // Check if data is empty using helper function\n      if (checkIfDataIsEmpty(apiResponse.data)) {\n        return renderNoDataMessage();\n      }\n\n      // Display data if not empty\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"whitespace-pre-wrap text-sm text-gray-700\",\n          children: JSON.stringify(apiResponse.data, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 338,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 337,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Handle successful response but no data property at all\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\n      return renderNoDataMessage();\n    }\n\n    // Error display\n    if (apiResponse.error) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-red-50 rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-sm\",\n          children: apiResponse.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 354,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-[80%] p-3 rounded-lg ${isUser ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-200 text-gray-800 rounded-bl-none'}`,\n      children: [!(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"whitespace-pre-wrap\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 377,\n        columnNumber: 11\n      }, this), !isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 405,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 404,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-gray-500\",\n          children: [\"Step \", message.currentStep, \" of \", message.totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 11\n      }, this), !isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 475,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 474,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 467,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 494,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalConversationalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-blue-600\",\n          children: [\"Conversational Step \", message.currentStep, \" of \", message.totalConversationalSteps, message.totalFormSteps > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-500\",\n            children: [\"(\", message.totalFormSteps, \" form field\", message.totalFormSteps !== 1 ? 's' : '', \" remaining)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 532,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 15\n          }, this), \"Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-3 h-3 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 13l4 4L19 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 545,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 17\n            }, this), \"Leave Application Submitted Successfully\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 543,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this), message.updatedLeaveBalance && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-blue-50 rounded-lg border border-blue-200 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-4 h-4 mr-2 text-blue-600\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 555,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 554,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-medium text-blue-800\",\n                children: \"Updated Leave Balance\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 557,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-blue-700 whitespace-pre-wrap\",\n              children: message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleLeaveTypeClick({\n                buttonText: 'Sick leave',\n                targetFormName: 'Leave Application',\n                fieldMapping: {\n                  leaveType: 'sick leave'\n                }\n              }),\n              className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n              children: \"Sick leave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 566,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleLeaveTypeClick({\n                buttonText: 'Casual leave',\n                targetFormName: 'Leave Application',\n                fieldMapping: {\n                  leaveType: 'casual leave'\n                }\n              }),\n              className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n              children: \"Casual leave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleLeaveTypeClick({\n                buttonText: 'Privilege Leave',\n                targetFormName: 'Leave Application',\n                fieldMapping: {\n                  leaveType: 'Privilege Leave'\n                }\n              }),\n              className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n              children: \"Privilege Leave\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 595,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 594,\n            columnNumber: 15\n          }, this), \"Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_cancelled' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 607,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 606,\n            columnNumber: 15\n          }, this), \"Form Cancelled\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 604,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 619,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 618,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 616,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 631,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 630,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Hybrid Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 629,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 11\n      }, this), message.apiResponse && formatApiResponse(message.apiResponse), !isUser && hasLeaveBalanceInContent() && !((_message$apiResponse4 = message.apiResponse) !== null && _message$apiResponse4 !== void 0 && _message$apiResponse4.leaveBalance) && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600 mb-2\",\n          children: \"Apply for leave:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleLeaveTypeClick({\n              buttonText: 'Sick leave',\n              targetFormName: 'Leave Application',\n              fieldMapping: {\n                leaveType: 'sick leave'\n              }\n            }),\n            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: \"Sick leave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleLeaveTypeClick({\n              buttonText: 'Casual leave',\n              targetFormName: 'Leave Application',\n              fieldMapping: {\n                leaveType: 'casual leave'\n              }\n            }),\n            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: \"Casual leave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleLeaveTypeClick({\n              buttonText: 'Privilege Leave',\n              targetFormName: 'Leave Application',\n              fieldMapping: {\n                leaveType: 'Privilege Leave'\n              }\n            }),\n            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: \"Privilege Leave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 649,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 647,\n        columnNumber: 11\n      }, this), !isUser && hasLeaveBalanceInContent() && !((_message$apiResponse5 = message.apiResponse) !== null && _message$apiResponse5 !== void 0 && _message$apiResponse5.leaveBalance) && !(message.apiResponse && hasLeaveBalanceInContent()) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-600 mb-2\",\n          children: \"Apply for leave:\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 675,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap gap-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleLeaveTypeClick({\n              buttonText: 'Sick leave',\n              targetFormName: 'Leave Application',\n              fieldMapping: {\n                leaveType: 'sick leave'\n              }\n            }),\n            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: \"Sick leave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 677,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleLeaveTypeClick({\n              buttonText: 'Casual leave',\n              targetFormName: 'Leave Application',\n              fieldMapping: {\n                leaveType: 'casual leave'\n              }\n            }),\n            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: \"Casual leave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 683,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleLeaveTypeClick({\n              buttonText: 'Privilege Leave',\n              targetFormName: 'Leave Application',\n              fieldMapping: {\n                leaveType: 'Privilege Leave'\n              }\n            }),\n            className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n            children: \"Privilege Leave\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 689,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 676,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 674,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 368,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 363,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatMessage, \"cE9qr9Un/J6bIavRReyYdfaIaEo=\");\n_c = ChatMessage;\nexport default ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");", "map": {"version": 3, "names": ["React", "useState", "RecordDisplayWithActions", "jsxDEV", "_jsxDEV", "ChatMessage", "message", "onOptionSelect", "onFormLinkTriggered", "_s", "_message$apiResponse4", "_message$apiResponse5", "isUser", "role", "selectedCheckboxes", "setSelectedCheckboxes", "hasLeaveBalanceInContent", "_message$apiResponse", "_message$apiResponse2", "content", "apiResponseData", "apiResponse", "data", "formattedResponse", "contentHasLeave", "toLowerCase", "includes", "formattedHasLeave", "dataHasLeave", "Array", "isArray", "some", "item", "hasOwnProperty", "handleCheckboxToggle", "option", "newSelected", "filter", "handleCheckboxSubmit", "length", "join", "renderLeaveBalanceWithButtons", "formLinkingConfig", "_message$formData", "_message$formConfig", "formId", "formData", "_id", "formConfig", "defaultLeaveConfig", "enabled", "recordActions", "buttonText", "targetFormName", "fieldMapping", "leaveType", "conditions", "buttonStyle", "autoSubmitOnClick", "autoTrigger", "delaySeconds", "activeConfig", "className", "children", "leaveBalance", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "action", "index", "onClick", "handleLeaveTypeClick", "console", "log", "targetForm", "name", "prefillData", "handleSingleOptionSelect", "checkIfDataIsEmpty", "Object", "keys", "arrayFields", "field", "values", "every", "val", "undefined", "renderNoDataMessage", "formatApiResponse", "_message$formData2", "_message$formData2$fo", "_message$formConfig2", "_message$formData3", "_message$formConfig3", "_message$formConfig3$", "_message$apiResponse3", "_formLinkingConfig$re4", "formLinking", "_message$formConfig4", "leaveFormLinkingConfig", "_message$formConfig5", "syntheticResponse", "success", "_message$conversation", "_message$hybridFlow", "_formLinkingConfig$re", "_formLinkingConfig$re3", "hasActiveConversationalFlow", "isConversationalPhase", "isHybridFlow", "conversationalFlow", "isActive", "hybridFlow", "options", "_apiResponse$data", "_formLinkingConfig$re2", "_message$formData4", "_message$formConfig6", "actualData", "hasDisabledAutoTrigger", "_action$autoTrigger", "renderRecordsWithButtons", "records", "formattedRecords", "recordSections", "split", "section", "trim", "record", "showOnlyButtons", "JSON", "stringify", "error", "fieldType", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "currentStep", "totalSteps", "totalConversationalSteps", "totalFormSteps", "queryIntent", "updatedLeaveBalance", "replace", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatMessage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\r\n\r\nconst ChatMessage = ({ message, onOptionSelect, onFormLinkTriggered }) => {\r\n  const isUser = message.role === 'user';\r\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\r\n\r\n  // Check if message content contains leave balance information\r\n  const hasLeaveBalanceInContent = () => {\r\n    const content = message.content || '';\r\n    const apiResponseData = message.apiResponse?.data || {};\r\n    const formattedResponse = message.apiResponse?.formattedResponse || '';\r\n\r\n    // Check in message content\r\n    const contentHasLeave = content.toLowerCase().includes('leave balance') ||\r\n           content.toLowerCase().includes('leave type') ||\r\n           content.toLowerCase().includes('sick leave') ||\r\n           content.toLowerCase().includes('casual leave') ||\r\n           content.toLowerCase().includes('privilege leave') ||\r\n           content.includes('balance:') ||\r\n           content.includes('days remaining') ||\r\n           content.includes('leaveTypeName:') ||\r\n           content.includes('Record 1:') && content.includes('balance:');\r\n\r\n    // Check in API response formatted response\r\n    const formattedHasLeave = formattedResponse.toLowerCase().includes('leave') ||\r\n           formattedResponse.includes('balance:') ||\r\n           formattedResponse.includes('leaveTypeName:');\r\n\r\n    // Check if API response data contains leave-related fields\r\n    const dataHasLeave = Array.isArray(apiResponseData) ?\r\n      apiResponseData.some(item => item.hasOwnProperty('leaveTypeName') || item.hasOwnProperty('balance')) :\r\n      (typeof apiResponseData === 'object' && (apiResponseData.hasOwnProperty('leaveTypeName') || apiResponseData.hasOwnProperty('balance')));\r\n\r\n    return contentHasLeave || formattedHasLeave || dataHasLeave;\r\n  };\r\n\r\n  // Handle checkbox selection\r\n  const handleCheckboxToggle = (option) => {\r\n    const newSelected = selectedCheckboxes.includes(option)\r\n      ? selectedCheckboxes.filter(item => item !== option)\r\n      : [...selectedCheckboxes, option];\r\n    setSelectedCheckboxes(newSelected);\r\n  };\r\n\r\n  // Handle checkbox submission\r\n  const handleCheckboxSubmit = () => {\r\n    if (selectedCheckboxes.length > 0) {\r\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\r\n      setSelectedCheckboxes([]);\r\n    }\r\n  };\r\n\r\n  // Render leave balance with leave type buttons\r\n  const renderLeaveBalanceWithButtons = (apiResponse, formLinkingConfig = null) => {\r\n    const formId = message.formData?._id || message.formConfig?._id;\r\n\r\n    // Default leave type configuration if none provided\r\n    const defaultLeaveConfig = {\r\n      enabled: true,\r\n      recordActions: [\r\n        {\r\n          buttonText: 'Sick leave',\r\n          targetFormName: 'Leave Application',\r\n          fieldMapping: { leaveType: 'sick leave' },\r\n          conditions: [],\r\n          buttonStyle: 'primary',\r\n          autoSubmitOnClick: false,\r\n          autoTrigger: { enabled: false, delaySeconds: 2 }\r\n        },\r\n        {\r\n          buttonText: 'Casual leave',\r\n          targetFormName: 'Leave Application',\r\n          fieldMapping: { leaveType: 'casual leave' },\r\n          conditions: [],\r\n          buttonStyle: 'primary',\r\n          autoSubmitOnClick: false,\r\n          autoTrigger: { enabled: false, delaySeconds: 2 }\r\n        },\r\n        {\r\n          buttonText: 'Privilege Leave',\r\n          targetFormName: 'Leave Application',\r\n          fieldMapping: { leaveType: 'Privilege Leave' },\r\n          conditions: [],\r\n          buttonStyle: 'primary',\r\n          autoSubmitOnClick: false,\r\n          autoTrigger: { enabled: false, delaySeconds: 2 }\r\n        }\r\n      ]\r\n    };\r\n\r\n    // Use provided config or default\r\n    const activeConfig = formLinkingConfig || defaultLeaveConfig;\r\n\r\n    return (\r\n      <div className=\"mt-3\">\r\n        {/* Display the leave balance content */}\r\n        <div className=\"p-3 bg-blue-50 rounded-md mb-3\">\r\n          <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n            {apiResponse.leaveBalance}\r\n          </pre>\r\n        </div>\r\n\r\n        {/* Always display leave type buttons */}\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {activeConfig.recordActions.map((action, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => handleLeaveTypeClick(action)}\r\n              className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n            >\r\n              {action.buttonText}\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Handle leave type button clicks\r\n  const handleLeaveTypeClick = (action) => {\r\n    console.log('🍃 Leave type clicked:', action.buttonText);\r\n\r\n    // Trigger the leave application form with pre-filled leave type\r\n    if (onFormLinkTriggered) {\r\n      onFormLinkTriggered({\r\n        targetForm: { name: action.targetFormName },\r\n        prefillData: action.fieldMapping,\r\n        buttonText: action.buttonText,\r\n        buttonStyle: action.buttonStyle\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle single option selection (for radio/select)\r\n  const handleSingleOptionSelect = (option) => {\r\n    onOptionSelect && onOptionSelect(option);\r\n  };\r\n  // Helper function to check if data is empty (comprehensive check)\r\n  const checkIfDataIsEmpty = (data) => {\r\n    if (!data) return true;\r\n    if (Array.isArray(data)) return data.length === 0;\r\n    if (typeof data === 'object' && data !== null) {\r\n      if (Object.keys(data).length === 0) return true;\r\n      // Check nested data structure\r\n      if (data.data) return checkIfDataIsEmpty(data.data);\r\n      // Check common array fields\r\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n      for (const field of arrayFields) {\r\n        if (data[field] && Array.isArray(data[field])) {\r\n          return data[field].length === 0;\r\n        }\r\n      }\r\n      // Check if all values are empty\r\n      return Object.values(data).every(val => \r\n        val === null || val === undefined || val === '' || \r\n        (Array.isArray(val) && val.length === 0) ||\r\n        (typeof val === 'object' && val !== null && Object.keys(val).length === 0)\r\n      );\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // Helper function to render \"No data available\" message\r\n  const renderNoDataMessage = () => (\r\n    <div className=\"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\">\r\n      <p className=\"text-blue-800 text-sm font-medium\">No data available</p>\r\n    </div>\r\n  );\r\n\r\n  // Format API response for display\r\n  const formatApiResponse = (apiResponse) => {\r\n    if (!apiResponse) return null;\r\n    \r\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\r\n    const formLinkingConfig = message.formData?.formConfig?.formLinking ||\r\n                             message.formConfig?.formLinking ||\r\n                             message.formData?.formLinking ||\r\n                             message.formConfig?.formConfig?.formLinking;\r\n\r\n    // Special handling for leave balance API responses - ALWAYS show leave types\r\n    if (message.apiResponse?.leaveBalance) {\r\n      console.log('🍃 Leave balance detected in apiResponse.leaveBalance, showing with leave type buttons');\r\n      // Try to find form linking config in the message structure, but use default if not found\r\n      const leaveFormLinkingConfig = message.formConfig?.formLinking;\r\n      return renderLeaveBalanceWithButtons(message.apiResponse, leaveFormLinkingConfig);\r\n    }\r\n\r\n    // Also check if this is a leave balance response in the API data or formatted response\r\n    if (message.apiResponse && hasLeaveBalanceInContent()) {\r\n      console.log('🍃 Leave balance detected in API response data/content, showing with leave type buttons');\r\n      // Create a synthetic leave balance response\r\n      const syntheticResponse = {\r\n        leaveBalance: message.apiResponse.formattedResponse || message.content || 'Leave balance information',\r\n        data: message.apiResponse.data\r\n      };\r\n      const leaveFormLinkingConfig = message.formConfig?.formLinking;\r\n      return renderLeaveBalanceWithButtons(syntheticResponse, leaveFormLinkingConfig);\r\n    }\r\n    \r\n    // For successful responses with data, check if we should display records with actions\r\n    if (apiResponse.success && apiResponse.data) {\r\n      \r\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\r\n      const hasActiveConversationalFlow = message.isConversationalPhase || \r\n                                         message.isHybridFlow || \r\n                                         message.conversationalFlow?.isActive ||\r\n                                         message.hybridFlow?.isActive ||\r\n                                         (message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow));\r\n      \r\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\r\n      if (formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0 && !hasActiveConversationalFlow) {\r\n       \r\n        // Extract the actual data from the API response\r\n        const actualData = apiResponse.data?.data || apiResponse.data;\r\n        \r\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\r\n        const hasDisabledAutoTrigger = formLinkingConfig.recordActions?.some(action => \r\n          !action.autoTrigger?.enabled\r\n        );\r\n        \r\n        const formId = message.formData?._id || message.formConfig?._id;\r\n        \r\n        // Function to render records with interleaved apply buttons\r\n        const renderRecordsWithButtons = () => {\r\n          let records = [];\r\n          \r\n          // Extract records from actualData\r\n          if (Array.isArray(actualData)) {\r\n            records = actualData;\r\n          } else if (actualData && typeof actualData === 'object') {\r\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n            for (const field of arrayFields) {\r\n              if (actualData[field] && Array.isArray(actualData[field])) {\r\n                records = actualData[field];\r\n                break;\r\n              }\r\n            }\r\n            if (records.length === 0) {\r\n              records = [actualData];\r\n            }\r\n          }\r\n          \r\n          // Check if records are empty using helper function\r\n          if (checkIfDataIsEmpty(records)) {\r\n            return renderNoDataMessage();\r\n          }\r\n\r\n          // Split formatted response by \"Record N:\" pattern\r\n          let formattedRecords = [];\r\n          if (apiResponse.formattedResponse) {\r\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\r\n            formattedRecords = recordSections.filter(section => section.trim());\r\n          }\r\n\r\n          return (\r\n            <div className=\"mt-3\">\r\n              {records.map((record, index) => (\r\n                <div key={index} className=\"mb-4\">\r\n                  {/* Display the formatted response for this record */}\r\n                  {formattedRecords[index] && (\r\n                    <div className=\"mb-2 p-3 bg-blue-50 rounded-md\">\r\n                      <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                        {formattedRecords[index].trim()}\r\n                      </pre>\r\n                    </div>\r\n                  )}\r\n                  \r\n                  {/* Apply button for this specific record */}\r\n                  {formLinkingConfig?.enabled && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && (\r\n                    <div className=\"flex flex-wrap gap-2 ml-3\">\r\n                      <RecordDisplayWithActions\r\n                        data={[record]} // Pass only this specific record\r\n                        formId={formId}\r\n                        formLinkingConfig={formLinkingConfig}\r\n                        onFormLinkTriggered={onFormLinkTriggered}\r\n                        showOnlyButtons={true}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n              \r\n              {/* Fallback: if we can't split the formatted response properly */}\r\n              {formattedRecords.length === 0 && apiResponse.formattedResponse && (\r\n                <div>\r\n                  <div className=\"mb-3 p-3 bg-blue-50 rounded-md\">\r\n                    <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                      {apiResponse.formattedResponse}\r\n                    </pre>\r\n                  </div>\r\n                  {/* Only show Apply buttons if the response is not \"No data available\" */}\r\n                  {!apiResponse.formattedResponse.includes('No data available') && (\r\n                    <RecordDisplayWithActions\r\n                      data={actualData}\r\n                      formId={formId}\r\n                      formLinkingConfig={formLinkingConfig}\r\n                      onFormLinkTriggered={onFormLinkTriggered}\r\n                      showOnlyButtons={true}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          );\r\n        };\r\n\r\n        return renderRecordsWithButtons();\r\n      }\r\n        \r\n      // If we have a formatted response but no form linking, display just the formatted response\r\n      if (apiResponse.formattedResponse && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n        // Check if the underlying data is empty using helper function\r\n        if (checkIfDataIsEmpty(apiResponse.data)) {\r\n          return renderNoDataMessage();\r\n        }\r\n        \r\n        return (\r\n          <div className=\"mt-3 p-3 bg-blue-50 rounded-md\">\r\n            <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n              {apiResponse.formattedResponse}\r\n            </pre>\r\n          </div>\r\n        );\r\n      }\r\n    }\r\n    \r\n    // Check if data is empty (array with no items or empty object)\r\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n      // Check if data is empty using helper function\r\n      if (checkIfDataIsEmpty(apiResponse.data)) {\r\n        return renderNoDataMessage();\r\n      }\r\n      \r\n      // Display data if not empty\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\">\r\n          <pre className=\"whitespace-pre-wrap text-sm text-gray-700\">\r\n            {JSON.stringify(apiResponse.data, null, 2)}\r\n          </pre>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    // Handle successful response but no data property at all\r\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\r\n      return renderNoDataMessage();\r\n    }\r\n    \r\n    // Error display\r\n    if (apiResponse.error) {\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-red-50 rounded-md\">\r\n          <p className=\"text-red-600 text-sm\">{apiResponse.error}</p>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`flex ${\r\n        isUser ? 'justify-end' : 'justify-start'\r\n      } mb-4`}\r\n    >\r\n      <div\r\n        className={`max-w-[80%] p-3 rounded-lg ${\r\n          isUser\r\n            ? 'bg-blue-500 text-white rounded-br-none'\r\n            : 'bg-gray-200 text-gray-800 rounded-bl-none'\r\n        }`}\r\n      >\r\n        {/* Display message content only if we don't have a formatted API response */}\r\n        {!(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <p className=\"whitespace-pre-wrap\">{message.content}</p>\r\n        )}\r\n        \r\n        {/* Display conversational form options as buttons */}\r\n        {!isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalSteps && (\r\n              <div className=\"mt-2 text-xs text-gray-500\">\r\n                Step {message.currentStep} of {message.totalSteps}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form options for conversational phase */}\r\n        {!isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalConversationalSteps && (\r\n              <div className=\"mt-2 text-xs text-blue-600\">\r\n                Conversational Step {message.currentStep} of {message.totalConversationalSteps}\r\n                {message.totalFormSteps > 0 && (\r\n                  <span className=\"ml-2 text-gray-500\">\r\n                    ({message.totalFormSteps} form field{message.totalFormSteps !== 1 ? 's' : ''} remaining)\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n\r\n              \r\n        {/* Display form completion indicator - only show success if form was actually submitted successfully */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display leave form completion with updated balance */}\r\n        {!isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2\">\r\n            <div className=\"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\">\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n                Leave Application Submitted Successfully\r\n              </div>\r\n            </div>\r\n            {message.updatedLeaveBalance && (\r\n              <div>\r\n                <div className=\"p-3 bg-blue-50 rounded-lg border border-blue-200 mb-3\">\r\n                  <div className=\"flex items-center mb-2\">\r\n                    <svg className=\"w-4 h-4 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                    </svg>\r\n                    <h4 className=\"text-sm font-medium text-blue-800\">Updated Leave Balance</h4>\r\n                  </div>\r\n                  <div className=\"text-sm text-blue-700 whitespace-pre-wrap\">\r\n                    {message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Add leave type buttons for updated balance too */}\r\n                <div className=\"flex flex-wrap gap-2\">\r\n                  <button\r\n                    onClick={() => handleLeaveTypeClick({ buttonText: 'Sick leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'sick leave' } })}\r\n                    className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n                  >\r\n                    Sick leave\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleLeaveTypeClick({ buttonText: 'Casual leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'casual leave' } })}\r\n                    className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n                  >\r\n                    Casual leave\r\n                  </button>\r\n                  <button\r\n                    onClick={() => handleLeaveTypeClick({ buttonText: 'Privilege Leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'Privilege Leave' } })}\r\n                    className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n                  >\r\n                    Privilege Leave\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Display form cancellation indicator */}\r\n        {!isUser && message.queryIntent === 'form_cancelled' && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Cancelled\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form completion indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              🔄 Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              🔄 Hybrid Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n\r\n        \r\n        {/* Leave balance is now included in the message content, no separate display needed */}\r\n\r\n        {/* Display API response if available */}\r\n        {message.apiResponse && formatApiResponse(message.apiResponse)}\r\n\r\n        {/* Fallback: Always show leave type buttons if leave balance is detected anywhere */}\r\n        {!isUser && hasLeaveBalanceInContent() && !message.apiResponse?.leaveBalance && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            <div className=\"text-sm text-gray-600 mb-2\">Apply for leave:</div>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              <button\r\n                onClick={() => handleLeaveTypeClick({ buttonText: 'Sick leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'sick leave' } })}\r\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n              >\r\n                Sick leave\r\n              </button>\r\n              <button\r\n                onClick={() => handleLeaveTypeClick({ buttonText: 'Casual leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'casual leave' } })}\r\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n              >\r\n                Casual leave\r\n              </button>\r\n              <button\r\n                onClick={() => handleLeaveTypeClick({ buttonText: 'Privilege Leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'Privilege Leave' } })}\r\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n              >\r\n                Privilege Leave\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Show leave type buttons if leave balance is detected in message content */}\r\n        {!isUser && hasLeaveBalanceInContent() && !message.apiResponse?.leaveBalance && !(message.apiResponse && hasLeaveBalanceInContent()) && (\r\n          <div className=\"mt-3\">\r\n            <div className=\"text-sm text-gray-600 mb-2\">Apply for leave:</div>\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              <button\r\n                onClick={() => handleLeaveTypeClick({ buttonText: 'Sick leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'sick leave' } })}\r\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n              >\r\n                Sick leave\r\n              </button>\r\n              <button\r\n                onClick={() => handleLeaveTypeClick({ buttonText: 'Casual leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'casual leave' } })}\r\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n              >\r\n                Casual leave\r\n              </button>\r\n              <button\r\n                onClick={() => handleLeaveTypeClick({ buttonText: 'Privilege Leave', targetFormName: 'Leave Application', fieldMapping: { leaveType: 'Privilege Leave' } })}\r\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n              >\r\n                Privilege Leave\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Display form data if available (for debugging) */}\r\n        {/* {message.formData && process.env.NODE_ENV === 'development' && (\r\n          <div className=\"mt-2 p-2 bg-gray-100 rounded text-xs text-gray-500\">\r\n            <p>Form data submitted</p>\r\n          </div>\r\n        )} */}\r\n        \r\n        {/* Display query intent if available (for debugging) */}\r\n        {/* {message.queryIntent && process.env.NODE_ENV === 'development' && (\r\n          <div className={`mt-2 p-2 rounded text-xs ${\r\n            message.queryIntent === 'form' \r\n              ? 'bg-blue-100 text-blue-700' \r\n              : 'bg-green-100 text-green-700'\r\n          }`}>\r\n            <p>Query intent: <strong>{message.queryIntent}</strong></p>\r\n            {message.formData && message.queryIntent === 'form' && (\r\n              <p>Form detected: {message.formData.name}</p>\r\n            )}\r\n          </div>\r\n        )} */}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatMessage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,wBAAwB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,cAAc;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,qBAAA;EACxE,MAAMC,MAAM,GAAGN,OAAO,CAACO,IAAI,KAAK,MAAM;EACtC,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAMe,wBAAwB,GAAGA,CAAA,KAAM;IAAA,IAAAC,oBAAA,EAAAC,qBAAA;IACrC,MAAMC,OAAO,GAAGb,OAAO,CAACa,OAAO,IAAI,EAAE;IACrC,MAAMC,eAAe,GAAG,EAAAH,oBAAA,GAAAX,OAAO,CAACe,WAAW,cAAAJ,oBAAA,uBAAnBA,oBAAA,CAAqBK,IAAI,KAAI,CAAC,CAAC;IACvD,MAAMC,iBAAiB,GAAG,EAAAL,qBAAA,GAAAZ,OAAO,CAACe,WAAW,cAAAH,qBAAA,uBAAnBA,qBAAA,CAAqBK,iBAAiB,KAAI,EAAE;;IAEtE;IACA,MAAMC,eAAe,GAAGL,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,eAAe,CAAC,IAChEP,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,IAC5CP,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,YAAY,CAAC,IAC5CP,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,cAAc,CAAC,IAC9CP,OAAO,CAACM,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,iBAAiB,CAAC,IACjDP,OAAO,CAACO,QAAQ,CAAC,UAAU,CAAC,IAC5BP,OAAO,CAACO,QAAQ,CAAC,gBAAgB,CAAC,IAClCP,OAAO,CAACO,QAAQ,CAAC,gBAAgB,CAAC,IAClCP,OAAO,CAACO,QAAQ,CAAC,WAAW,CAAC,IAAIP,OAAO,CAACO,QAAQ,CAAC,UAAU,CAAC;;IAEpE;IACA,MAAMC,iBAAiB,GAAGJ,iBAAiB,CAACE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,IACpEH,iBAAiB,CAACG,QAAQ,CAAC,UAAU,CAAC,IACtCH,iBAAiB,CAACG,QAAQ,CAAC,gBAAgB,CAAC;;IAEnD;IACA,MAAME,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACV,eAAe,CAAC,GACjDA,eAAe,CAACW,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,cAAc,CAAC,eAAe,CAAC,IAAID,IAAI,CAACC,cAAc,CAAC,SAAS,CAAC,CAAC,GACnG,OAAOb,eAAe,KAAK,QAAQ,KAAKA,eAAe,CAACa,cAAc,CAAC,eAAe,CAAC,IAAIb,eAAe,CAACa,cAAc,CAAC,SAAS,CAAC,CAAE;IAEzI,OAAOT,eAAe,IAAIG,iBAAiB,IAAIC,YAAY;EAC7D,CAAC;;EAED;EACA,MAAMM,oBAAoB,GAAIC,MAAM,IAAK;IACvC,MAAMC,WAAW,GAAGtB,kBAAkB,CAACY,QAAQ,CAACS,MAAM,CAAC,GACnDrB,kBAAkB,CAACuB,MAAM,CAACL,IAAI,IAAIA,IAAI,KAAKG,MAAM,CAAC,GAClD,CAAC,GAAGrB,kBAAkB,EAAEqB,MAAM,CAAC;IACnCpB,qBAAqB,CAACqB,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,MAAME,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIxB,kBAAkB,CAACyB,MAAM,GAAG,CAAC,EAAE;MACjChC,cAAc,IAAIA,cAAc,CAACO,kBAAkB,CAAC0B,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/DzB,qBAAqB,CAAC,EAAE,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM0B,6BAA6B,GAAGA,CAACpB,WAAW,EAAEqB,iBAAiB,GAAG,IAAI,KAAK;IAAA,IAAAC,iBAAA,EAAAC,mBAAA;IAC/E,MAAMC,MAAM,GAAG,EAAAF,iBAAA,GAAArC,OAAO,CAACwC,QAAQ,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBI,GAAG,OAAAH,mBAAA,GAAItC,OAAO,CAAC0C,UAAU,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBG,GAAG;;IAE/D;IACA,MAAME,kBAAkB,GAAG;MACzBC,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,CACb;QACEC,UAAU,EAAE,YAAY;QACxBC,cAAc,EAAE,mBAAmB;QACnCC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAa,CAAC;QACzCC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,SAAS;QACtBC,iBAAiB,EAAE,KAAK;QACxBC,WAAW,EAAE;UAAET,OAAO,EAAE,KAAK;UAAEU,YAAY,EAAE;QAAE;MACjD,CAAC,EACD;QACER,UAAU,EAAE,cAAc;QAC1BC,cAAc,EAAE,mBAAmB;QACnCC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAe,CAAC;QAC3CC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,SAAS;QACtBC,iBAAiB,EAAE,KAAK;QACxBC,WAAW,EAAE;UAAET,OAAO,EAAE,KAAK;UAAEU,YAAY,EAAE;QAAE;MACjD,CAAC,EACD;QACER,UAAU,EAAE,iBAAiB;QAC7BC,cAAc,EAAE,mBAAmB;QACnCC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAkB,CAAC;QAC9CC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,SAAS;QACtBC,iBAAiB,EAAE,KAAK;QACxBC,WAAW,EAAE;UAAET,OAAO,EAAE,KAAK;UAAEU,YAAY,EAAE;QAAE;MACjD,CAAC;IAEL,CAAC;;IAED;IACA,MAAMC,YAAY,GAAGnB,iBAAiB,IAAIO,kBAAkB;IAE5D,oBACE7C,OAAA;MAAK0D,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnB3D,OAAA;QAAK0D,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C3D,OAAA;UAAK0D,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvD1C,WAAW,CAAC2C;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhE,OAAA;QAAK0D,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClCF,YAAY,CAACV,aAAa,CAACkB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC5CnE,OAAA;UAEEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAACH,MAAM,CAAE;UAC5CR,SAAS,EAAC,yJAAyJ;UAAAC,QAAA,EAElKO,MAAM,CAAClB;QAAU,GAJbmB,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMK,oBAAoB,GAAIH,MAAM,IAAK;IACvCI,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEL,MAAM,CAAClB,UAAU,CAAC;;IAExD;IACA,IAAI5C,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC;QAClBoE,UAAU,EAAE;UAAEC,IAAI,EAAEP,MAAM,CAACjB;QAAe,CAAC;QAC3CyB,WAAW,EAAER,MAAM,CAAChB,YAAY;QAChCF,UAAU,EAAEkB,MAAM,CAAClB,UAAU;QAC7BK,WAAW,EAAEa,MAAM,CAACb;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMsB,wBAAwB,GAAI5C,MAAM,IAAK;IAC3C5B,cAAc,IAAIA,cAAc,CAAC4B,MAAM,CAAC;EAC1C,CAAC;EACD;EACA,MAAM6C,kBAAkB,GAAI1D,IAAI,IAAK;IACnC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IACtB,IAAIO,KAAK,CAACC,OAAO,CAACR,IAAI,CAAC,EAAE,OAAOA,IAAI,CAACiB,MAAM,KAAK,CAAC;IACjD,IAAI,OAAOjB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;MAC7C,IAAI2D,MAAM,CAACC,IAAI,CAAC5D,IAAI,CAAC,CAACiB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC/C;MACA,IAAIjB,IAAI,CAACA,IAAI,EAAE,OAAO0D,kBAAkB,CAAC1D,IAAI,CAACA,IAAI,CAAC;MACnD;MACA,MAAM6D,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;MAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;QAC/B,IAAI7D,IAAI,CAAC8D,KAAK,CAAC,IAAIvD,KAAK,CAACC,OAAO,CAACR,IAAI,CAAC8D,KAAK,CAAC,CAAC,EAAE;UAC7C,OAAO9D,IAAI,CAAC8D,KAAK,CAAC,CAAC7C,MAAM,KAAK,CAAC;QACjC;MACF;MACA;MACA,OAAO0C,MAAM,CAACI,MAAM,CAAC/D,IAAI,CAAC,CAACgE,KAAK,CAACC,GAAG,IAClCA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,IAC9C1D,KAAK,CAACC,OAAO,CAACyD,GAAG,CAAC,IAAIA,GAAG,CAAChD,MAAM,KAAK,CAAE,IACvC,OAAOgD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIN,MAAM,CAACC,IAAI,CAACK,GAAG,CAAC,CAAChD,MAAM,KAAK,CAC1E,CAAC;IACH;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMkD,mBAAmB,GAAGA,CAAA,kBAC1BrF,OAAA;IAAK0D,SAAS,EAAC,uDAAuD;IAAAC,QAAA,eACpE3D,OAAA;MAAG0D,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnE,CACN;;EAED;EACA,MAAMsB,iBAAiB,GAAIrE,WAAW,IAAK;IAAA,IAAAsE,kBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,kBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IACzC,IAAI,CAAC7E,WAAW,EAAE,OAAO,IAAI;;IAE7B;IACA,MAAMqB,iBAAiB,GAAG,EAAAiD,kBAAA,GAAArF,OAAO,CAACwC,QAAQ,cAAA6C,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkB3C,UAAU,cAAA4C,qBAAA,uBAA5BA,qBAAA,CAA8BO,WAAW,OAAAN,oBAAA,GAC1CvF,OAAO,CAAC0C,UAAU,cAAA6C,oBAAA,uBAAlBA,oBAAA,CAAoBM,WAAW,OAAAL,kBAAA,GAC/BxF,OAAO,CAACwC,QAAQ,cAAAgD,kBAAA,uBAAhBA,kBAAA,CAAkBK,WAAW,OAAAJ,oBAAA,GAC7BzF,OAAO,CAAC0C,UAAU,cAAA+C,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoB/C,UAAU,cAAAgD,qBAAA,uBAA9BA,qBAAA,CAAgCG,WAAW;;IAEpE;IACA,KAAAF,qBAAA,GAAI3F,OAAO,CAACe,WAAW,cAAA4E,qBAAA,eAAnBA,qBAAA,CAAqBjC,YAAY,EAAE;MAAA,IAAAoC,oBAAA;MACrC1B,OAAO,CAACC,GAAG,CAAC,wFAAwF,CAAC;MACrG;MACA,MAAM0B,sBAAsB,IAAAD,oBAAA,GAAG9F,OAAO,CAAC0C,UAAU,cAAAoD,oBAAA,uBAAlBA,oBAAA,CAAoBD,WAAW;MAC9D,OAAO1D,6BAA6B,CAACnC,OAAO,CAACe,WAAW,EAAEgF,sBAAsB,CAAC;IACnF;;IAEA;IACA,IAAI/F,OAAO,CAACe,WAAW,IAAIL,wBAAwB,CAAC,CAAC,EAAE;MAAA,IAAAsF,oBAAA;MACrD5B,OAAO,CAACC,GAAG,CAAC,yFAAyF,CAAC;MACtG;MACA,MAAM4B,iBAAiB,GAAG;QACxBvC,YAAY,EAAE1D,OAAO,CAACe,WAAW,CAACE,iBAAiB,IAAIjB,OAAO,CAACa,OAAO,IAAI,2BAA2B;QACrGG,IAAI,EAAEhB,OAAO,CAACe,WAAW,CAACC;MAC5B,CAAC;MACD,MAAM+E,sBAAsB,IAAAC,oBAAA,GAAGhG,OAAO,CAAC0C,UAAU,cAAAsD,oBAAA,uBAAlBA,oBAAA,CAAoBH,WAAW;MAC9D,OAAO1D,6BAA6B,CAAC8D,iBAAiB,EAAEF,sBAAsB,CAAC;IACjF;;IAEA;IACA,IAAIhF,WAAW,CAACmF,OAAO,IAAInF,WAAW,CAACC,IAAI,EAAE;MAAA,IAAAmF,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAE3C;MACA,MAAMC,2BAA2B,GAAGvG,OAAO,CAACwG,qBAAqB,IAC9BxG,OAAO,CAACyG,YAAY,MAAAN,qBAAA,GACpBnG,OAAO,CAAC0G,kBAAkB,cAAAP,qBAAA,uBAA1BA,qBAAA,CAA4BQ,QAAQ,OAAAP,mBAAA,GACpCpG,OAAO,CAAC4G,UAAU,cAAAR,mBAAA,uBAAlBA,mBAAA,CAAoBO,QAAQ,KAC3B3G,OAAO,CAAC6G,OAAO,IAAI7G,OAAO,CAAC6G,OAAO,CAAC5E,MAAM,GAAG,CAAC,KAAKjC,OAAO,CAACwG,qBAAqB,IAAIxG,OAAO,CAACyG,YAAY,CAAE;;MAE7I;MACA,IAAIrE,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEQ,OAAO,IAAI,EAAAyD,qBAAA,GAAAjE,iBAAiB,CAACS,aAAa,cAAAwD,qBAAA,uBAA/BA,qBAAA,CAAiCpE,MAAM,IAAG,CAAC,IAAI,CAACsE,2BAA2B,EAAE;QAAA,IAAAO,iBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,oBAAA;QAE7G;QACA,MAAMC,UAAU,GAAG,EAAAJ,iBAAA,GAAA/F,WAAW,CAACC,IAAI,cAAA8F,iBAAA,uBAAhBA,iBAAA,CAAkB9F,IAAI,KAAID,WAAW,CAACC,IAAI;;QAE7D;QACA,MAAMmG,sBAAsB,IAAAJ,sBAAA,GAAG3E,iBAAiB,CAACS,aAAa,cAAAkE,sBAAA,uBAA/BA,sBAAA,CAAiCtF,IAAI,CAACuC,MAAM;UAAA,IAAAoD,mBAAA;UAAA,OACzE,GAAAA,mBAAA,GAACpD,MAAM,CAACX,WAAW,cAAA+D,mBAAA,eAAlBA,mBAAA,CAAoBxE,OAAO;QAAA,CAC9B,CAAC;QAED,MAAML,MAAM,GAAG,EAAAyE,kBAAA,GAAAhH,OAAO,CAACwC,QAAQ,cAAAwE,kBAAA,uBAAhBA,kBAAA,CAAkBvE,GAAG,OAAAwE,oBAAA,GAAIjH,OAAO,CAAC0C,UAAU,cAAAuE,oBAAA,uBAAlBA,oBAAA,CAAoBxE,GAAG;;QAE/D;QACA,MAAM4E,wBAAwB,GAAGA,CAAA,KAAM;UACrC,IAAIC,OAAO,GAAG,EAAE;;UAEhB;UACA,IAAI/F,KAAK,CAACC,OAAO,CAAC0F,UAAU,CAAC,EAAE;YAC7BI,OAAO,GAAGJ,UAAU;UACtB,CAAC,MAAM,IAAIA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;YACvD,MAAMrC,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;YAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;cAC/B,IAAIqC,UAAU,CAACpC,KAAK,CAAC,IAAIvD,KAAK,CAACC,OAAO,CAAC0F,UAAU,CAACpC,KAAK,CAAC,CAAC,EAAE;gBACzDwC,OAAO,GAAGJ,UAAU,CAACpC,KAAK,CAAC;gBAC3B;cACF;YACF;YACA,IAAIwC,OAAO,CAACrF,MAAM,KAAK,CAAC,EAAE;cACxBqF,OAAO,GAAG,CAACJ,UAAU,CAAC;YACxB;UACF;;UAEA;UACA,IAAIxC,kBAAkB,CAAC4C,OAAO,CAAC,EAAE;YAC/B,OAAOnC,mBAAmB,CAAC,CAAC;UAC9B;;UAEA;UACA,IAAIoC,gBAAgB,GAAG,EAAE;UACzB,IAAIxG,WAAW,CAACE,iBAAiB,EAAE;YACjC,MAAMuG,cAAc,GAAGzG,WAAW,CAACE,iBAAiB,CAACwG,KAAK,CAAC,iBAAiB,CAAC;YAC7EF,gBAAgB,GAAGC,cAAc,CAACzF,MAAM,CAAC2F,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;UACrE;UAEA,oBACE7H,OAAA;YAAK0D,SAAS,EAAC,MAAM;YAAAC,QAAA,GAClB6D,OAAO,CAACvD,GAAG,CAAC,CAAC6D,MAAM,EAAE3D,KAAK,kBACzBnE,OAAA;cAAiB0D,SAAS,EAAC,MAAM;cAAAC,QAAA,GAE9B8D,gBAAgB,CAACtD,KAAK,CAAC,iBACtBnE,OAAA;gBAAK0D,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7C3D,OAAA;kBAAK0D,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvD8D,gBAAgB,CAACtD,KAAK,CAAC,CAAC0D,IAAI,CAAC;gBAAC;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAAA1B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEQ,OAAO,KAAIR,iBAAiB,CAACS,aAAa,IAAI,CAAC9B,WAAW,CAACE,iBAAiB,CAACG,QAAQ,CAAC,mBAAmB,CAAC,iBAC5HtB,OAAA;gBAAK0D,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxC3D,OAAA,CAACF,wBAAwB;kBACvBoB,IAAI,EAAE,CAAC4G,MAAM,CAAE,CAAC;kBAAA;kBAChBrF,MAAM,EAAEA,MAAO;kBACfH,iBAAiB,EAAEA,iBAAkB;kBACrClC,mBAAmB,EAAEA,mBAAoB;kBACzC2H,eAAe,EAAE;gBAAK;kBAAAlE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA,GArBOG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBV,CACN,CAAC,EAGDyD,gBAAgB,CAACtF,MAAM,KAAK,CAAC,IAAIlB,WAAW,CAACE,iBAAiB,iBAC7DnB,OAAA;cAAA2D,QAAA,gBACE3D,OAAA;gBAAK0D,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7C3D,OAAA;kBAAK0D,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvD1C,WAAW,CAACE;gBAAiB;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,CAAC/C,WAAW,CAACE,iBAAiB,CAACG,QAAQ,CAAC,mBAAmB,CAAC,iBAC3DtB,OAAA,CAACF,wBAAwB;gBACvBoB,IAAI,EAAEkG,UAAW;gBACjB3E,MAAM,EAAEA,MAAO;gBACfH,iBAAiB,EAAEA,iBAAkB;gBACrClC,mBAAmB,EAAEA,mBAAoB;gBACzC2H,eAAe,EAAE;cAAK;gBAAAlE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC;QAED,OAAOuD,wBAAwB,CAAC,CAAC;MACnC;;MAEA;MACA,IAAItG,WAAW,CAACE,iBAAiB,IAAI,EAAEmB,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEQ,OAAO,IAAI,EAAA0D,sBAAA,GAAAlE,iBAAiB,CAACS,aAAa,cAAAyD,sBAAA,uBAA/BA,sBAAA,CAAiCrE,MAAM,IAAG,CAAC,CAAC,EAAE;QACjH;QACA,IAAIyC,kBAAkB,CAAC3D,WAAW,CAACC,IAAI,CAAC,EAAE;UACxC,OAAOmE,mBAAmB,CAAC,CAAC;QAC9B;QAEA,oBACErF,OAAA;UAAK0D,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7C3D,OAAA;YAAK0D,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACvD1C,WAAW,CAACE;UAAiB;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;IACF;;IAEA;IACA,IAAI/C,WAAW,CAACmF,OAAO,IAAInF,WAAW,CAACC,IAAI,KAAKkE,SAAS,IAAI,EAAE9C,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEQ,OAAO,IAAI,EAAAgD,sBAAA,GAAAxD,iBAAiB,CAACS,aAAa,cAAA+C,sBAAA,uBAA/BA,sBAAA,CAAiC3D,MAAM,IAAG,CAAC,CAAC,EAAE;MACzI;MACA,IAAIyC,kBAAkB,CAAC3D,WAAW,CAACC,IAAI,CAAC,EAAE;QACxC,OAAOmE,mBAAmB,CAAC,CAAC;MAC9B;;MAEA;MACA,oBACErF,OAAA;QAAK0D,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpE3D,OAAA;UAAK0D,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvDqE,IAAI,CAACC,SAAS,CAAChH,WAAW,CAACC,IAAI,EAAE,IAAI,EAAE,CAAC;QAAC;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;;IAEA;IACA,IAAI/C,WAAW,CAACmF,OAAO,IAAI,CAACnF,WAAW,CAACC,IAAI,IAAI,CAACD,WAAW,CAACE,iBAAiB,IAAI,CAACF,WAAW,CAACiH,KAAK,EAAE;MACpG,OAAO7C,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;IACA,IAAIpE,WAAW,CAACiH,KAAK,EAAE;MACrB,oBACElI,OAAA;QAAK0D,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5C3D,OAAA;UAAG0D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAE1C,WAAW,CAACiH;QAAK;UAAArE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEV;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACEhE,OAAA;IACE0D,SAAS,EAAE,QACTlD,MAAM,GAAG,aAAa,GAAG,eAAe,OAClC;IAAAmD,QAAA,eAER3D,OAAA;MACE0D,SAAS,EAAE,8BACTlD,MAAM,GACF,wCAAwC,GACxC,2CAA2C,EAC9C;MAAAmD,QAAA,GAGF,EAAEzD,OAAO,CAACe,WAAW,IAAIf,OAAO,CAACe,WAAW,CAACE,iBAAiB,CAAC,iBAC9DnB,OAAA;QAAG0D,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAEzD,OAAO,CAACa;MAAO;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CACxD,EAGA,CAACxD,MAAM,IAAIN,OAAO,CAAC6G,OAAO,IAAItF,KAAK,CAACC,OAAO,CAACxB,OAAO,CAAC6G,OAAO,CAAC,IAAI7G,OAAO,CAAC6G,OAAO,CAAC5E,MAAM,GAAG,CAAC,IAAI,CAACjC,OAAO,CAACyG,YAAY,IAAI,CAACzG,OAAO,CAACwG,qBAAqB,IAAI,EAAExG,OAAO,CAACe,WAAW,IAAIf,OAAO,CAACe,WAAW,CAACE,iBAAiB,CAAC,iBACvNnB,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClBzD,OAAO,CAACiI,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAnI,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAK0D,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDzD,OAAO,CAAC6G,OAAO,CAAC9C,GAAG,CAAC,CAAClC,MAAM,EAAEoC,KAAK,kBACjCnE,OAAA;cAEEoE,OAAO,EAAEA,CAAA,KAAMtC,oBAAoB,CAACC,MAAM,CAAE;cAC5C2B,SAAS,EAAE,kHACThD,kBAAkB,CAACY,QAAQ,CAACS,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAA4B,QAAA,eAEH3D,OAAA;gBAAK0D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC3D,OAAA;kBAAK0D,SAAS,EAAE,kEACdhD,kBAAkB,CAACY,QAAQ,CAACS,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAA4B,QAAA,EACAjD,kBAAkB,CAACY,QAAQ,CAACS,MAAM,CAAC,iBAClC/B,OAAA;oBAAK0D,SAAS,EAAC,wBAAwB;oBAAC0E,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAA3E,QAAA,eAC3F3D,OAAA;sBAAMuI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACLjC,MAAM;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLtD,kBAAkB,CAACyB,MAAM,GAAG,CAAC,iBAC5BnC,OAAA;YAAK0D,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB3D,OAAA;cACEoE,OAAO,EAAElC,oBAAqB;cAC9BwB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAACjD,kBAAkB,CAACyB,MAAM,EAAC,GAC/C;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDhE,OAAA;YAAK0D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACAhE,OAAA;UAAK0D,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7CzD,OAAO,CAAC6G,OAAO,CAAC9C,GAAG,CAAC,CAAClC,MAAM,EAAEoC,KAAK,kBACjCnE,OAAA;YAEEoE,OAAO,EAAEA,CAAA,KAAMO,wBAAwB,CAAC5C,MAAM,CAAE;YAChD2B,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/M5B;UAAM,GAJFoC,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACA9D,OAAO,CAACyI,WAAW,IAAIzI,OAAO,CAAC0I,UAAU,iBACxC5I,OAAA;UAAK0D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,OACrC,EAACzD,OAAO,CAACyI,WAAW,EAAC,MAAI,EAACzI,OAAO,CAAC0I,UAAU;QAAA;UAAA/E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAACxD,MAAM,IAAIN,OAAO,CAACyG,YAAY,IAAIzG,OAAO,CAACwG,qBAAqB,IAAIxG,OAAO,CAAC6G,OAAO,IAAItF,KAAK,CAACC,OAAO,CAACxB,OAAO,CAAC6G,OAAO,CAAC,IAAI7G,OAAO,CAAC6G,OAAO,CAAC5E,MAAM,GAAG,CAAC,IAAI,EAAEjC,OAAO,CAACe,WAAW,IAAIf,OAAO,CAACe,WAAW,CAACE,iBAAiB,CAAC,iBACrNnB,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClBzD,OAAO,CAACiI,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAnI,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAK0D,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDzD,OAAO,CAAC6G,OAAO,CAAC9C,GAAG,CAAC,CAAClC,MAAM,EAAEoC,KAAK,kBACjCnE,OAAA;cAEEoE,OAAO,EAAEA,CAAA,KAAMtC,oBAAoB,CAACC,MAAM,CAAE;cAC5C2B,SAAS,EAAE,kHACThD,kBAAkB,CAACY,QAAQ,CAACS,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAA4B,QAAA,eAEH3D,OAAA;gBAAK0D,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC3D,OAAA;kBAAK0D,SAAS,EAAE,kEACdhD,kBAAkB,CAACY,QAAQ,CAACS,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAA4B,QAAA,EACAjD,kBAAkB,CAACY,QAAQ,CAACS,MAAM,CAAC,iBAClC/B,OAAA;oBAAK0D,SAAS,EAAC,wBAAwB;oBAAC0E,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAA3E,QAAA,eAC3F3D,OAAA;sBAAMuI,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAA7E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACLjC,MAAM;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLtD,kBAAkB,CAACyB,MAAM,GAAG,CAAC,iBAC5BnC,OAAA;YAAK0D,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB3D,OAAA;cACEoE,OAAO,EAAElC,oBAAqB;cAC9BwB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAACjD,kBAAkB,CAACyB,MAAM,EAAC,GAC/C;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDhE,OAAA;YAAK0D,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACAhE,OAAA;UAAK0D,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7CzD,OAAO,CAAC6G,OAAO,CAAC9C,GAAG,CAAC,CAAClC,MAAM,EAAEoC,KAAK,kBACjCnE,OAAA;YAEEoE,OAAO,EAAEA,CAAA,KAAMO,wBAAwB,CAAC5C,MAAM,CAAE;YAChD2B,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/M5B;UAAM,GAJFoC,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACA9D,OAAO,CAACyI,WAAW,IAAIzI,OAAO,CAAC2I,wBAAwB,iBACtD7I,OAAA;UAAK0D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,sBACtB,EAACzD,OAAO,CAACyI,WAAW,EAAC,MAAI,EAACzI,OAAO,CAAC2I,wBAAwB,EAC7E3I,OAAO,CAAC4I,cAAc,GAAG,CAAC,iBACzB9I,OAAA;YAAM0D,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,GAClC,EAACzD,OAAO,CAAC4I,cAAc,EAAC,aAAW,EAAC5I,OAAO,CAAC4I,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,aAC/E;UAAA;YAAAjF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAKA,CAACxD,MAAM,IAAIN,OAAO,CAAC6I,WAAW,KAAK,gBAAgB,IAAI7I,OAAO,CAACe,WAAW,IAAIf,OAAO,CAACe,WAAW,CAACmF,OAAO,iBACxGpG,OAAA;QAAK0D,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE3D,OAAA;UAAK0D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3D,OAAA;YAAK0D,SAAS,EAAC,cAAc;YAAC0E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA3E,QAAA,eACjF3D,OAAA;cAAMuI,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,+BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACxD,MAAM,IAAIN,OAAO,CAAC6I,WAAW,KAAK,sBAAsB,IAAI7I,OAAO,CAACe,WAAW,IAAIf,OAAO,CAACe,WAAW,CAACmF,OAAO,iBAC9GpG,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3D,OAAA;UAAK0D,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnE3D,OAAA;YAAK0D,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC3D,OAAA;cAAK0D,SAAS,EAAC,cAAc;cAAC0E,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3E,QAAA,eACjF3D,OAAA;gBAAMuI,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgB;gBAAA7E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,4CAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACL9D,OAAO,CAAC8I,mBAAmB,iBAC1BhJ,OAAA;UAAA2D,QAAA,gBACE3D,OAAA;YAAK0D,SAAS,EAAC,uDAAuD;YAAAC,QAAA,gBACpE3D,OAAA;cAAK0D,SAAS,EAAC,wBAAwB;cAAAC,QAAA,gBACrC3D,OAAA;gBAAK0D,SAAS,EAAC,4BAA4B;gBAAC0E,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAA3E,QAAA,eAC/F3D,OAAA;kBAAMuI,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA6G;kBAAA7E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClL,CAAC,eACNhE,OAAA;gBAAI0D,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAC;cAAqB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzE,CAAC,eACNhE,OAAA;cAAK0D,SAAS,EAAC,2CAA2C;cAAAC,QAAA,EACvDzD,OAAO,CAAC8I,mBAAmB,CAACC,OAAO,CAAC,6CAA6C,EAAE,EAAE;YAAC;cAAApF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhE,OAAA;YAAK0D,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnC3D,OAAA;cACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;gBAAErB,UAAU,EAAE,YAAY;gBAAEC,cAAc,EAAE,mBAAmB;gBAAEC,YAAY,EAAE;kBAAEC,SAAS,EAAE;gBAAa;cAAE,CAAC,CAAE;cAClJO,SAAS,EAAC,yJAAyJ;cAAAC,QAAA,EACpK;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThE,OAAA;cACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;gBAAErB,UAAU,EAAE,cAAc;gBAAEC,cAAc,EAAE,mBAAmB;gBAAEC,YAAY,EAAE;kBAAEC,SAAS,EAAE;gBAAe;cAAE,CAAC,CAAE;cACtJO,SAAS,EAAC,yJAAyJ;cAAAC,QAAA,EACpK;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACThE,OAAA;cACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;gBAAErB,UAAU,EAAE,iBAAiB;gBAAEC,cAAc,EAAE,mBAAmB;gBAAEC,YAAY,EAAE;kBAAEC,SAAS,EAAE;gBAAkB;cAAE,CAAC,CAAE;cAC5JO,SAAS,EAAC,yJAAyJ;cAAAC,QAAA,EACpK;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAACxD,MAAM,IAAIN,OAAO,CAAC6I,WAAW,KAAK,gBAAgB,IAAI7I,OAAO,CAACe,WAAW,IAAI,CAACf,OAAO,CAACe,WAAW,CAACmF,OAAO,iBACzGpG,OAAA;QAAK0D,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D3D,OAAA;UAAK0D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3D,OAAA;YAAK0D,SAAS,EAAC,cAAc;YAAC0E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA3E,QAAA,eACjF3D,OAAA;cAAMuI,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,0BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACxD,MAAM,IAAIN,OAAO,CAAC6I,WAAW,KAAK,gBAAgB,iBAClD/I,OAAA;QAAK0D,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D3D,OAAA;UAAK0D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3D,OAAA;YAAK0D,SAAS,EAAC,cAAc;YAAC0E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA3E,QAAA,eACjF3D,OAAA;cAAMuI,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,kBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACxD,MAAM,IAAIN,OAAO,CAAC6I,WAAW,KAAK,uBAAuB,IAAI7I,OAAO,CAACe,WAAW,IAAIf,OAAO,CAACe,WAAW,CAACmF,OAAO,iBAC/GpG,OAAA;QAAK0D,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE3D,OAAA;UAAK0D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3D,OAAA;YAAK0D,SAAS,EAAC,cAAc;YAAC0E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA3E,QAAA,eACjF3D,OAAA;cAAMuI,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,4CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACxD,MAAM,IAAIN,OAAO,CAAC6I,WAAW,KAAK,uBAAuB,IAAI7I,OAAO,CAACe,WAAW,IAAI,CAACf,OAAO,CAACe,WAAW,CAACmF,OAAO,iBAChHpG,OAAA;QAAK0D,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D3D,OAAA;UAAK0D,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC3D,OAAA;YAAK0D,SAAS,EAAC,cAAc;YAAC0E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA3E,QAAA,eACjF3D,OAAA;cAAMuI,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAA7E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,8CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAOA9D,OAAO,CAACe,WAAW,IAAIqE,iBAAiB,CAACpF,OAAO,CAACe,WAAW,CAAC,EAG7D,CAACT,MAAM,IAAII,wBAAwB,CAAC,CAAC,IAAI,GAAAN,qBAAA,GAACJ,OAAO,CAACe,WAAW,cAAAX,qBAAA,eAAnBA,qBAAA,CAAqBsD,YAAY,KAAI,EAAE1D,OAAO,CAACe,WAAW,IAAIf,OAAO,CAACe,WAAW,CAACE,iBAAiB,CAAC,iBAC7InB,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3D,OAAA;UAAK0D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClEhE,OAAA;UAAK0D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC3D,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;cAAErB,UAAU,EAAE,YAAY;cAAEC,cAAc,EAAE,mBAAmB;cAAEC,YAAY,EAAE;gBAAEC,SAAS,EAAE;cAAa;YAAE,CAAC,CAAE;YAClJO,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;cAAErB,UAAU,EAAE,cAAc;cAAEC,cAAc,EAAE,mBAAmB;cAAEC,YAAY,EAAE;gBAAEC,SAAS,EAAE;cAAe;YAAE,CAAC,CAAE;YACtJO,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;cAAErB,UAAU,EAAE,iBAAiB;cAAEC,cAAc,EAAE,mBAAmB;cAAEC,YAAY,EAAE;gBAAEC,SAAS,EAAE;cAAkB;YAAE,CAAC,CAAE;YAC5JO,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACxD,MAAM,IAAII,wBAAwB,CAAC,CAAC,IAAI,GAAAL,qBAAA,GAACL,OAAO,CAACe,WAAW,cAAAV,qBAAA,eAAnBA,qBAAA,CAAqBqD,YAAY,KAAI,EAAE1D,OAAO,CAACe,WAAW,IAAIL,wBAAwB,CAAC,CAAC,CAAC,iBAClIZ,OAAA;QAAK0D,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB3D,OAAA;UAAK0D,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAAgB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAClEhE,OAAA;UAAK0D,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBACnC3D,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;cAAErB,UAAU,EAAE,YAAY;cAAEC,cAAc,EAAE,mBAAmB;cAAEC,YAAY,EAAE;gBAAEC,SAAS,EAAE;cAAa;YAAE,CAAC,CAAE;YAClJO,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;cAAErB,UAAU,EAAE,cAAc;cAAEC,cAAc,EAAE,mBAAmB;cAAEC,YAAY,EAAE;gBAAEC,SAAS,EAAE;cAAe;YAAE,CAAC,CAAE;YACtJO,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACThE,OAAA;YACEoE,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAAC;cAAErB,UAAU,EAAE,iBAAiB;cAAEC,cAAc,EAAE,mBAAmB;cAAEC,YAAY,EAAE;gBAAEC,SAAS,EAAE;cAAkB;YAAE,CAAC,CAAE;YAC5JO,SAAS,EAAC,yJAAyJ;YAAAC,QAAA,EACpK;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsBE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3D,EAAA,CA9sBIJ,WAAW;AAAAiJ,EAAA,GAAXjJ,WAAW;AAgtBjB,eAAeA,WAAW;AAAC,IAAAiJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}