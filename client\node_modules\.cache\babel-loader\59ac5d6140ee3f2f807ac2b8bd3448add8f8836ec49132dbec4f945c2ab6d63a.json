{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\FormLinkingConfig.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormLinkingConfig = ({\n  formLinking,\n  onFormLinkingChange,\n  isFormType = false\n}) => {\n  _s();\n  const [availableForms, setAvailableForms] = useState([]);\n  const [loading, setLoading] = useState(false);\n\n  // Fetch available forms for linking\n  useEffect(() => {\n    const fetchAvailableForms = async () => {\n      if (!isFormType) return;\n      setLoading(true);\n      try {\n        const response = await api.get('/unifiedconfigs/forms-for-linking');\n        if (response.data.success) {\n          setAvailableForms(response.data.forms);\n        }\n      } catch (error) {\n        console.error('Error fetching available forms:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchAvailableForms();\n  }, [isFormType]);\n\n  // Handle form linking enabled/disabled\n  const handleFormLinkingToggle = enabled => {\n    onFormLinkingChange({\n      ...formLinking,\n      enabled: enabled\n    });\n  };\n\n  // Handle adding a new record action\n  const handleAddRecordAction = () => {\n    const newAction = {\n      buttonText: 'Apply',\n      targetFormId: '',\n      targetFormName: '',\n      fieldMapping: {},\n      conditions: [],\n      buttonStyle: 'primary'\n    };\n    onFormLinkingChange({\n      ...formLinking,\n      recordActions: [...formLinking.recordActions, newAction]\n    });\n  };\n\n  // Handle removing a record action\n  const handleRemoveRecordAction = index => {\n    const updatedActions = formLinking.recordActions.filter((_, i) => i !== index);\n    onFormLinkingChange({\n      ...formLinking,\n      recordActions: updatedActions\n    });\n  };\n\n  // Handle updating a record action\n  const handleUpdateRecordAction = (index, updatedAction) => {\n    const updatedActions = formLinking.recordActions.map((action, i) => i === index ? updatedAction : action);\n    onFormLinkingChange({\n      ...formLinking,\n      recordActions: updatedActions\n    });\n  };\n\n  // Handle field mapping changes\n  const handleFieldMappingChange = (actionIndex, sourceField, targetField) => {\n    const updatedActions = formLinking.recordActions.map((action, i) => {\n      if (i === actionIndex) {\n        const newFieldMapping = {\n          ...action.fieldMapping\n        };\n        if (targetField) {\n          newFieldMapping[targetField] = sourceField;\n        } else {\n          delete newFieldMapping[sourceField];\n        }\n        return {\n          ...action,\n          fieldMapping: newFieldMapping\n        };\n      }\n      return action;\n    });\n    onFormLinkingChange({\n      ...formLinking,\n      recordActions: updatedActions\n    });\n  };\n\n  // Handle condition changes\n  const handleConditionChange = (actionIndex, conditionIndex, updatedCondition) => {\n    const updatedActions = formLinking.recordActions.map((action, i) => {\n      if (i === actionIndex) {\n        const newConditions = action.conditions.map((condition, j) => j === conditionIndex ? updatedCondition : condition);\n        return {\n          ...action,\n          conditions: newConditions\n        };\n      }\n      return action;\n    });\n    onFormLinkingChange({\n      ...formLinking,\n      recordActions: updatedActions\n    });\n  };\n\n  // Handle adding a condition\n  const handleAddCondition = actionIndex => {\n    const newCondition = {\n      field: '',\n      operator: 'equals',\n      value: ''\n    };\n    const updatedActions = formLinking.recordActions.map((action, i) => {\n      if (i === actionIndex) {\n        return {\n          ...action,\n          conditions: [...action.conditions, newCondition]\n        };\n      }\n      return action;\n    });\n    onFormLinkingChange({\n      ...formLinking,\n      recordActions: updatedActions\n    });\n  };\n\n  // Handle removing a condition\n  const handleRemoveCondition = (actionIndex, conditionIndex) => {\n    const updatedActions = formLinking.recordActions.map((action, i) => {\n      if (i === actionIndex) {\n        const newConditions = action.conditions.filter((_, j) => j !== conditionIndex);\n        return {\n          ...action,\n          conditions: newConditions\n        };\n      }\n      return action;\n    });\n    onFormLinkingChange({\n      ...formLinking,\n      recordActions: updatedActions\n    });\n  };\n  if (!isFormType) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-lg font-medium text-gray-900\",\n      children: \"Form Linking Configuration\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 160,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-sm text-gray-600\",\n      children: \"Configure action buttons that appear on data records to link to other forms.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center space-x-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        type: \"checkbox\",\n        id: \"formLinkingEnabled\",\n        checked: formLinking.enabled,\n        onChange: e => handleFormLinkingToggle(e.target.checked),\n        className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 167,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n        htmlFor: \"formLinkingEnabled\",\n        className: \"text-sm font-medium text-gray-700\",\n        children: \"Enable Form Linking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 166,\n      columnNumber: 7\n    }, this), formLinking.enabled && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-md font-medium text-gray-800\",\n            children: \"Record Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: handleAddRecordAction,\n            className: \"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\",\n            children: \"Add Action\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), loading && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-sm text-gray-600\",\n            children: \"Loading available forms...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 197,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 15\n        }, this), formLinking.recordActions.map((action, actionIndex) => {\n          var _action$autoTrigger, _action$autoTrigger3, _action$autoTrigger4, _action$autoTrigger5;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"border border-gray-200 rounded-lg p-4 mb-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                className: \"text-sm font-medium text-gray-700\",\n                children: [\"Action \", actionIndex + 1]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                type: \"button\",\n                onClick: () => handleRemoveRecordAction(actionIndex),\n                className: \"text-red-600 hover:text-red-800 text-sm\",\n                children: \"Remove\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Button Text\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: action.buttonText,\n                  onChange: e => handleUpdateRecordAction(actionIndex, {\n                    ...action,\n                    buttonText: e.target.value\n                  }),\n                  className: \"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                  placeholder: \"Apply\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700 mb-1\",\n                  children: \"Button Style\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: action.buttonStyle,\n                  onChange: e => handleUpdateRecordAction(actionIndex, {\n                    ...action,\n                    buttonStyle: e.target.value\n                  }),\n                  className: \"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"primary\",\n                    children: \"Primary (Blue)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"secondary\",\n                    children: \"Secondary (Gray)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 246,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"success\",\n                    children: \"Success (Green)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 247,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"warning\",\n                    children: \"Warning (Yellow)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"danger\",\n                    children: \"Danger (Red)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                children: \"Target Form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: action.targetFormId,\n                onChange: e => {\n                  const selectedForm = availableForms.find(f => f._id === e.target.value);\n                  handleUpdateRecordAction(actionIndex, {\n                    ...action,\n                    targetFormId: e.target.value,\n                    targetFormName: selectedForm ? selectedForm.name : ''\n                  });\n                },\n                className: \"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"Select a form...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 21\n                }, this), availableForms.map(form => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: form._id,\n                  children: [form.name, \" (\", form.category, \")\"]\n                }, form._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 23\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 259,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-medium text-gray-700 mb-2\",\n                children: \"Field Mapping\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 mb-2\",\n                children: \"Map fields from record data to target form fields\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: [Object.entries(action.fieldMapping).map(([targetField, sourceField], mappingIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: sourceField,\n                    onChange: e => handleFieldMappingChange(actionIndex, e.target.value, targetField),\n                    placeholder: \"Source field\",\n                    className: \"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-500\",\n                    children: \"\\u2192\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 299,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"text\",\n                    value: targetField,\n                    onChange: e => {\n                      const oldMapping = {\n                        ...action.fieldMapping\n                      };\n                      delete oldMapping[targetField];\n                      if (e.target.value) {\n                        oldMapping[e.target.value] = sourceField;\n                      }\n                      handleUpdateRecordAction(actionIndex, {\n                        ...action,\n                        fieldMapping: oldMapping\n                      });\n                    },\n                    placeholder: \"Target field\",\n                    className: \"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    type: \"button\",\n                    onClick: () => handleFieldMappingChange(actionIndex, sourceField, null),\n                    className: \"text-red-600 hover:text-red-800 text-sm\",\n                    children: \"Remove\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 25\n                  }, this)]\n                }, mappingIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 23\n                }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleFieldMappingChange(actionIndex, '', 'newField'),\n                  className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                  children: \"+ Add Field Mapping\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 326,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 289,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: /*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Auto-Trigger Configuration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 339,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 mb-2\",\n                children: \"Configure automatic opening of target form when conditions are met\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"border border-gray-200 rounded-lg p-4 bg-gray-50\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 mb-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                    type: \"checkbox\",\n                    id: `autoTrigger-${actionIndex}`,\n                    checked: ((_action$autoTrigger = action.autoTrigger) === null || _action$autoTrigger === void 0 ? void 0 : _action$autoTrigger.enabled) || false,\n                    onChange: e => {\n                      var _action$autoTrigger2;\n                      return handleUpdateRecordAction(actionIndex, {\n                        ...action,\n                        autoTrigger: {\n                          ...action.autoTrigger,\n                          enabled: e.target.checked,\n                          delaySeconds: ((_action$autoTrigger2 = action.autoTrigger) === null || _action$autoTrigger2 === void 0 ? void 0 : _action$autoTrigger2.delaySeconds) || 0\n                        }\n                      });\n                    },\n                    className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                    htmlFor: `autoTrigger-${actionIndex}`,\n                    className: \"text-sm font-medium text-gray-700\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 363,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 348,\n                  columnNumber: 21\n                }, this), ((_action$autoTrigger3 = action.autoTrigger) === null || _action$autoTrigger3 === void 0 ? void 0 : _action$autoTrigger3.enabled) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"space-y-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                      className: \"block text-sm font-medium text-gray-700 mb-1\",\n                      children: \"Delay (seconds)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 371,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"number\",\n                      min: \"0\",\n                      max: \"60\",\n                      value: ((_action$autoTrigger4 = action.autoTrigger) === null || _action$autoTrigger4 === void 0 ? void 0 : _action$autoTrigger4.delaySeconds) || 0,\n                      onChange: e => handleUpdateRecordAction(actionIndex, {\n                        ...action,\n                        autoTrigger: {\n                          ...action.autoTrigger,\n                          enabled: true,\n                          delaySeconds: parseInt(e.target.value) || 0\n                        }\n                      }),\n                      className: \"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\",\n                      placeholder: \"0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 374,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"Delay in seconds before auto-triggering (0 = immediate)\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 390,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 370,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-blue-50 border border-blue-200 rounded-md p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"h-5 w-5 text-blue-400\",\n                          viewBox: \"0 0 20 20\",\n                          fill: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 399,\n                            columnNumber: 33\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 398,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 397,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"ml-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-sm font-medium text-blue-800\",\n                          children: \"Auto-Trigger Enabled\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 403,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-1 text-sm text-blue-700\",\n                          children: \"When enabled, the target form will automatically open after the specified delay when data is displayed and conditions are met.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 404,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 402,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 396,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 395,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 369,\n                  columnNumber: 23\n                }, this), !((_action$autoTrigger5 = action.autoTrigger) !== null && _action$autoTrigger5 !== void 0 && _action$autoTrigger5.enabled) && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-3 mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                      type: \"checkbox\",\n                      id: `autoSubmitOnClick-${actionIndex}`,\n                      checked: action.autoSubmitOnClick || false,\n                      onChange: e => handleUpdateRecordAction(actionIndex, {\n                        ...action,\n                        autoSubmitOnClick: e.target.checked\n                      }),\n                      className: \"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 417,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                      htmlFor: `autoSubmitOnClick-${actionIndex}`,\n                      className: \"text-sm font-medium text-gray-700\",\n                      children: \"Auto-Submit on Click\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 427,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 416,\n                    columnNumber: 25\n                  }, this), action.autoSubmitOnClick && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-green-50 border border-green-200 rounded-md p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"h-5 w-5 text-green-400\",\n                          viewBox: \"0 0 20 20\",\n                          fill: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 437,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 436,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 435,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"ml-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-sm font-medium text-green-800\",\n                          children: \"Auto-Submit Enabled\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 441,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-1 text-sm text-green-700\",\n                          children: \"When clicked, the button will automatically submit the form data to the API without displaying the form to the user.\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 442,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 440,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 434,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 27\n                  }, this), !action.autoSubmitOnClick && /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-gray-50 border border-gray-200 rounded-md p-3\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-start\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                          className: \"h-5 w-5 text-gray-400\",\n                          viewBox: \"0 0 20 20\",\n                          fill: \"currentColor\",\n                          children: /*#__PURE__*/_jsxDEV(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                            clipRule: \"evenodd\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 455,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 454,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 453,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"ml-3\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                          className: \"text-sm font-medium text-gray-800\",\n                          children: \"Display Form\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 459,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-1 text-sm text-gray-700\",\n                          children: \"When clicked, the button will display the form for user input (default behavior).\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 460,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 458,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 452,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 451,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 415,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 347,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-medium text-gray-700\",\n                  children: \"Show Conditions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 475,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleAddCondition(actionIndex),\n                  className: \"text-blue-600 hover:text-blue-800 text-sm\",\n                  children: \"+ Add Condition\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 478,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-xs text-gray-500 mb-2\",\n                children: \"Define when this button should be visible (also affects auto-trigger)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 19\n              }, this), action.conditions.map((condition, conditionIndex) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center space-x-2 mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: condition.field,\n                  onChange: e => handleConditionChange(actionIndex, conditionIndex, {\n                    ...condition,\n                    field: e.target.value\n                  }),\n                  placeholder: \"Field name\",\n                  className: \"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: condition.operator,\n                  onChange: e => handleConditionChange(actionIndex, conditionIndex, {\n                    ...condition,\n                    operator: e.target.value\n                  }),\n                  className: \"p-2 border border-gray-300 rounded-md text-sm\",\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"equals\",\n                    children: \"Equals\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"not_equals\",\n                    children: \"Not Equals\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 511,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"contains\",\n                    children: \"Contains\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 512,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"not_contains\",\n                    children: \"Not Contains\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 513,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"exists\",\n                    children: \"Exists\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"not_exists\",\n                    children: \"Not Exists\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 515,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  value: condition.value,\n                  onChange: e => handleConditionChange(actionIndex, conditionIndex, {\n                    ...condition,\n                    value: e.target.value\n                  }),\n                  placeholder: \"Value\",\n                  className: \"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 517,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  type: \"button\",\n                  onClick: () => handleRemoveCondition(actionIndex, conditionIndex),\n                  className: \"text-red-600 hover:text-red-800 text-sm\",\n                  children: \"Remove\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 527,\n                  columnNumber: 23\n                }, this)]\n              }, conditionIndex, true, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 21\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 473,\n              columnNumber: 17\n            }, this)]\n          }, actionIndex, true, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 15\n          }, this);\n        })]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n_s(FormLinkingConfig, \"kvlyqyweLxXxxyvm+juxCmnk77U=\");\n_c = FormLinkingConfig;\nexport default FormLinkingConfig;\nvar _c;\n$RefreshReg$(_c, \"FormLinkingConfig\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "api", "jsxDEV", "_jsxDEV", "FormLinkingConfig", "formLinking", "onFormLinkingChange", "isFormType", "_s", "availableForms", "setAvailableForms", "loading", "setLoading", "fetchAvailableForms", "response", "get", "data", "success", "forms", "error", "console", "handleFormLinkingToggle", "enabled", "handleAddRecordAction", "newAction", "buttonText", "targetFormId", "targetFormName", "fieldMapping", "conditions", "buttonStyle", "recordActions", "handleRemoveRecordAction", "index", "updatedActions", "filter", "_", "i", "handleUpdateRecordAction", "updatedAction", "map", "action", "handleFieldMappingChange", "actionIndex", "sourceField", "targetField", "newFieldMapping", "handleConditionChange", "conditionIndex", "updatedCondition", "newConditions", "condition", "j", "handleAddCondition", "newCondition", "field", "operator", "value", "handleRemoveCondition", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "id", "checked", "onChange", "e", "target", "htmlFor", "onClick", "_action$autoTrigger", "_action$autoTrigger3", "_action$autoTrigger4", "_action$autoTrigger5", "placeholder", "selectedForm", "find", "f", "_id", "name", "form", "category", "Object", "entries", "mappingIndex", "oldMapping", "autoTrigger", "_action$autoTrigger2", "delaySeconds", "min", "max", "parseInt", "viewBox", "fill", "fillRule", "d", "clipRule", "autoSubmitOnClick", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/FormLinkingConfig.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport api from '../utils/api';\r\n\r\nconst FormLinkingConfig = ({ \r\n  formLinking, \r\n  onFormLinkingChange, \r\n  isFormType = false \r\n}) => {\r\n  const [availableForms, setAvailableForms] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  // Fetch available forms for linking\r\n  useEffect(() => {\r\n    const fetchAvailableForms = async () => {\r\n      if (!isFormType) return;\r\n      \r\n      setLoading(true);\r\n      try {\r\n        const response = await api.get('/unifiedconfigs/forms-for-linking');\r\n        if (response.data.success) {\r\n          setAvailableForms(response.data.forms);\r\n        }\r\n      } catch (error) {\r\n        console.error('Error fetching available forms:', error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    fetchAvailableForms();\r\n  }, [isFormType]);\r\n\r\n  // Handle form linking enabled/disabled\r\n  const handleFormLinkingToggle = (enabled) => {\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      enabled: enabled\r\n    });\r\n  };\r\n\r\n  // Handle adding a new record action\r\n  const handleAddRecordAction = () => {\r\n    const newAction = {\r\n      buttonText: 'Apply',\r\n      targetFormId: '',\r\n      targetFormName: '',\r\n      fieldMapping: {},\r\n      conditions: [],\r\n      buttonStyle: 'primary'\r\n    };\r\n\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      recordActions: [...formLinking.recordActions, newAction]\r\n    });\r\n  };\r\n\r\n  // Handle removing a record action\r\n  const handleRemoveRecordAction = (index) => {\r\n    const updatedActions = formLinking.recordActions.filter((_, i) => i !== index);\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      recordActions: updatedActions\r\n    });\r\n  };\r\n\r\n  // Handle updating a record action\r\n  const handleUpdateRecordAction = (index, updatedAction) => {\r\n    const updatedActions = formLinking.recordActions.map((action, i) => \r\n      i === index ? updatedAction : action\r\n    );\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      recordActions: updatedActions\r\n    });\r\n  };\r\n\r\n  // Handle field mapping changes\r\n  const handleFieldMappingChange = (actionIndex, sourceField, targetField) => {\r\n    const updatedActions = formLinking.recordActions.map((action, i) => {\r\n      if (i === actionIndex) {\r\n        const newFieldMapping = { ...action.fieldMapping };\r\n        if (targetField) {\r\n          newFieldMapping[targetField] = sourceField;\r\n        } else {\r\n          delete newFieldMapping[sourceField];\r\n        }\r\n        return { ...action, fieldMapping: newFieldMapping };\r\n      }\r\n      return action;\r\n    });\r\n\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      recordActions: updatedActions\r\n    });\r\n  };\r\n\r\n  // Handle condition changes\r\n  const handleConditionChange = (actionIndex, conditionIndex, updatedCondition) => {\r\n    const updatedActions = formLinking.recordActions.map((action, i) => {\r\n      if (i === actionIndex) {\r\n        const newConditions = action.conditions.map((condition, j) => \r\n          j === conditionIndex ? updatedCondition : condition\r\n        );\r\n        return { ...action, conditions: newConditions };\r\n      }\r\n      return action;\r\n    });\r\n\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      recordActions: updatedActions\r\n    });\r\n  };\r\n\r\n  // Handle adding a condition\r\n  const handleAddCondition = (actionIndex) => {\r\n    const newCondition = {\r\n      field: '',\r\n      operator: 'equals',\r\n      value: ''\r\n    };\r\n\r\n    const updatedActions = formLinking.recordActions.map((action, i) => {\r\n      if (i === actionIndex) {\r\n        return { ...action, conditions: [...action.conditions, newCondition] };\r\n      }\r\n      return action;\r\n    });\r\n\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      recordActions: updatedActions\r\n    });\r\n  };\r\n\r\n  // Handle removing a condition\r\n  const handleRemoveCondition = (actionIndex, conditionIndex) => {\r\n    const updatedActions = formLinking.recordActions.map((action, i) => {\r\n      if (i === actionIndex) {\r\n        const newConditions = action.conditions.filter((_, j) => j !== conditionIndex);\r\n        return { ...action, conditions: newConditions };\r\n      }\r\n      return action;\r\n    });\r\n\r\n    onFormLinkingChange({\r\n      ...formLinking,\r\n      recordActions: updatedActions\r\n    });\r\n  };\r\n\r\n  if (!isFormType) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <h3 className=\"text-lg font-medium text-gray-900\">Form Linking Configuration</h3>\r\n      <p className=\"text-sm text-gray-600\">\r\n        Configure action buttons that appear on data records to link to other forms.\r\n      </p>\r\n      \r\n      {/* Enable/Disable Form Linking */}\r\n      <div className=\"flex items-center space-x-3\">\r\n        <input\r\n          type=\"checkbox\"\r\n          id=\"formLinkingEnabled\"\r\n          checked={formLinking.enabled}\r\n          onChange={(e) => handleFormLinkingToggle(e.target.checked)}\r\n          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n        />\r\n        <label htmlFor=\"formLinkingEnabled\" className=\"text-sm font-medium text-gray-700\">\r\n          Enable Form Linking\r\n        </label>\r\n      </div>\r\n\r\n      {formLinking.enabled && (\r\n        <div className=\"space-y-6\">\r\n          {/* Record Actions */}\r\n          <div>\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <h4 className=\"text-md font-medium text-gray-800\">Record Actions</h4>\r\n              <button\r\n                type=\"button\"\r\n                onClick={handleAddRecordAction}\r\n                className=\"px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600\"\r\n              >\r\n                Add Action\r\n              </button>\r\n            </div>\r\n\r\n            {loading && (\r\n              <div className=\"text-center py-4\">\r\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto\"></div>\r\n                <p className=\"mt-2 text-sm text-gray-600\">Loading available forms...</p>\r\n              </div>\r\n            )}\r\n\r\n            {formLinking.recordActions.map((action, actionIndex) => (\r\n              <div key={actionIndex} className=\"border border-gray-200 rounded-lg p-4 mb-4\">\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <h5 className=\"text-sm font-medium text-gray-700\">Action {actionIndex + 1}</h5>\r\n                  <button\r\n                    type=\"button\"\r\n                    onClick={() => handleRemoveRecordAction(actionIndex)}\r\n                    className=\"text-red-600 hover:text-red-800 text-sm\"\r\n                  >\r\n                    Remove\r\n                  </button>\r\n                </div>\r\n\r\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n                  {/* Button Text */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Button Text\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={action.buttonText}\r\n                      onChange={(e) => handleUpdateRecordAction(actionIndex, { \r\n                        ...action, \r\n                        buttonText: e.target.value \r\n                      })}\r\n                      className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\r\n                      placeholder=\"Apply\"\r\n                    />\r\n                  </div>\r\n\r\n                  {/* Button Style */}\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Button Style\r\n                    </label>\r\n                    <select\r\n                      value={action.buttonStyle}\r\n                      onChange={(e) => handleUpdateRecordAction(actionIndex, { \r\n                        ...action, \r\n                        buttonStyle: e.target.value \r\n                      })}\r\n                      className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\r\n                    >\r\n                      <option value=\"primary\">Primary (Blue)</option>\r\n                      <option value=\"secondary\">Secondary (Gray)</option>\r\n                      <option value=\"success\">Success (Green)</option>\r\n                      <option value=\"warning\">Warning (Yellow)</option>\r\n                      <option value=\"danger\">Danger (Red)</option>\r\n                    </select>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Target Form Selection */}\r\n                <div className=\"mt-4\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                    Target Form\r\n                  </label>\r\n                  <select\r\n                    value={action.targetFormId}\r\n                    onChange={(e) => {\r\n                      const selectedForm = availableForms.find(f => f._id === e.target.value);\r\n                      handleUpdateRecordAction(actionIndex, { \r\n                        ...action, \r\n                        targetFormId: e.target.value,\r\n                        targetFormName: selectedForm ? selectedForm.name : ''\r\n                      });\r\n                    }}\r\n                    className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\r\n                  >\r\n                    <option value=\"\">Select a form...</option>\r\n                    {availableForms.map(form => (\r\n                      <option key={form._id} value={form._id}>\r\n                        {form.name} ({form.category})\r\n                      </option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n\r\n                {/* Field Mapping */}\r\n                <div className=\"mt-4\">\r\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\r\n                    Field Mapping\r\n                  </label>\r\n                  <div className=\"text-xs text-gray-500 mb-2\">\r\n                    Map fields from record data to target form fields\r\n                  </div>\r\n                  \r\n                  <div className=\"space-y-2\">\r\n                    {Object.entries(action.fieldMapping).map(([targetField, sourceField], mappingIndex) => (\r\n                      <div key={mappingIndex} className=\"flex items-center space-x-2\">\r\n                        <input\r\n                          type=\"text\"\r\n                          value={sourceField}\r\n                          onChange={(e) => handleFieldMappingChange(actionIndex, e.target.value, targetField)}\r\n                          placeholder=\"Source field\"\r\n                          className=\"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\r\n                        />\r\n                        <span className=\"text-gray-500\">→</span>\r\n                        <input\r\n                          type=\"text\"\r\n                          value={targetField}\r\n                          onChange={(e) => {\r\n                            const oldMapping = { ...action.fieldMapping };\r\n                            delete oldMapping[targetField];\r\n                            if (e.target.value) {\r\n                              oldMapping[e.target.value] = sourceField;\r\n                            }\r\n                            handleUpdateRecordAction(actionIndex, { \r\n                              ...action, \r\n                              fieldMapping: oldMapping \r\n                            });\r\n                          }}\r\n                          placeholder=\"Target field\"\r\n                          className=\"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\r\n                        />\r\n                        <button\r\n                          type=\"button\"\r\n                          onClick={() => handleFieldMappingChange(actionIndex, sourceField, null)}\r\n                          className=\"text-red-600 hover:text-red-800 text-sm\"\r\n                        >\r\n                          Remove\r\n                        </button>\r\n                      </div>\r\n                    ))}\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleFieldMappingChange(actionIndex, '', 'newField')}\r\n                      className=\"text-blue-600 hover:text-blue-800 text-sm\"\r\n                    >\r\n                      + Add Field Mapping\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Auto-Trigger Configuration */}\r\n                <div className=\"mt-4\">\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <label className=\"block text-sm font-medium text-gray-700\">\r\n                      Auto-Trigger Configuration\r\n                    </label>\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-500 mb-2\">\r\n                    Configure automatic opening of target form when conditions are met\r\n                  </div>\r\n                  \r\n                  <div className=\"border border-gray-200 rounded-lg p-4 bg-gray-50\">\r\n                    <div className=\"flex items-center space-x-3 mb-4\">\r\n                      <input\r\n                        type=\"checkbox\"\r\n                        id={`autoTrigger-${actionIndex}`}\r\n                        checked={action.autoTrigger?.enabled || false}\r\n                        onChange={(e) => handleUpdateRecordAction(actionIndex, { \r\n                          ...action, \r\n                          autoTrigger: {\r\n                            ...action.autoTrigger,\r\n                            enabled: e.target.checked,\r\n                            delaySeconds: action.autoTrigger?.delaySeconds || 0\r\n                          }\r\n                        })}\r\n                        className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n                      />\r\n                      <label htmlFor={`autoTrigger-${actionIndex}`} className=\"text-sm font-medium text-gray-700\">\r\n                        {/* Enable Auto-Trigger */}\r\n                      </label>\r\n                    </div>\r\n                    \r\n                    {action.autoTrigger?.enabled && (\r\n                      <div className=\"space-y-3\">\r\n                        <div>\r\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                            Delay (seconds)\r\n                          </label>\r\n                          <input\r\n                            type=\"number\"\r\n                            min=\"0\"\r\n                            max=\"60\"\r\n                            value={action.autoTrigger?.delaySeconds || 0}\r\n                            onChange={(e) => handleUpdateRecordAction(actionIndex, { \r\n                              ...action, \r\n                              autoTrigger: {\r\n                                ...action.autoTrigger,\r\n                                enabled: true,\r\n                                delaySeconds: parseInt(e.target.value) || 0\r\n                              }\r\n                            })}\r\n                            className=\"w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500\"\r\n                            placeholder=\"0\"\r\n                          />\r\n                          <div className=\"text-xs text-gray-500 mt-1\">\r\n                            Delay in seconds before auto-triggering (0 = immediate)\r\n                          </div>\r\n                        </div>\r\n                        \r\n                        <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3\">\r\n                          <div className=\"flex items-start\">\r\n                            <div className=\"flex-shrink-0\">\r\n                              <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                              </svg>\r\n                            </div>\r\n                            <div className=\"ml-3\">\r\n                              <h3 className=\"text-sm font-medium text-blue-800\">Auto-Trigger Enabled</h3>\r\n                              <div className=\"mt-1 text-sm text-blue-700\">\r\n                                When enabled, the target form will automatically open after the specified delay when data is displayed and conditions are met.\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    )}\r\n                    \r\n                    {/* Auto-Submit on Click (when auto-trigger is disabled) */}\r\n                    {!action.autoTrigger?.enabled && (\r\n                      <div className=\"mt-4\">\r\n                        <div className=\"flex items-center space-x-3 mb-2\">\r\n                          <input\r\n                            type=\"checkbox\"\r\n                            id={`autoSubmitOnClick-${actionIndex}`}\r\n                            checked={action.autoSubmitOnClick || false}\r\n                            onChange={(e) => handleUpdateRecordAction(actionIndex, { \r\n                              ...action, \r\n                              autoSubmitOnClick: e.target.checked\r\n                            })}\r\n                            className=\"h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded\"\r\n                          />\r\n                          <label htmlFor={`autoSubmitOnClick-${actionIndex}`} className=\"text-sm font-medium text-gray-700\">\r\n                            Auto-Submit on Click\r\n                          </label>\r\n                        </div>\r\n                        \r\n                        {action.autoSubmitOnClick && (\r\n                          <div className=\"bg-green-50 border border-green-200 rounded-md p-3\">\r\n                            <div className=\"flex items-start\">\r\n                              <div className=\"flex-shrink-0\">\r\n                                <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\r\n                                </svg>\r\n                              </div>\r\n                              <div className=\"ml-3\">\r\n                                <h3 className=\"text-sm font-medium text-green-800\">Auto-Submit Enabled</h3>\r\n                                <div className=\"mt-1 text-sm text-green-700\">\r\n                                  When clicked, the button will automatically submit the form data to the API without displaying the form to the user.\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                        \r\n                        {!action.autoSubmitOnClick && (\r\n                          <div className=\"bg-gray-50 border border-gray-200 rounded-md p-3\">\r\n                            <div className=\"flex items-start\">\r\n                              <div className=\"flex-shrink-0\">\r\n                                <svg className=\"h-5 w-5 text-gray-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                                </svg>\r\n                              </div>\r\n                              <div className=\"ml-3\">\r\n                                <h3 className=\"text-sm font-medium text-gray-800\">Display Form</h3>\r\n                                <div className=\"mt-1 text-sm text-gray-700\">\r\n                                  When clicked, the button will display the form for user input (default behavior).\r\n                                </div>\r\n                              </div>\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Conditions */}\r\n                <div className=\"mt-4\">\r\n                  <div className=\"flex items-center justify-between mb-2\">\r\n                    <label className=\"block text-sm font-medium text-gray-700\">\r\n                      Show Conditions\r\n                    </label>\r\n                    <button\r\n                      type=\"button\"\r\n                      onClick={() => handleAddCondition(actionIndex)}\r\n                      className=\"text-blue-600 hover:text-blue-800 text-sm\"\r\n                    >\r\n                      + Add Condition\r\n                    </button>\r\n                  </div>\r\n                  <div className=\"text-xs text-gray-500 mb-2\">\r\n                    Define when this button should be visible (also affects auto-trigger)\r\n                  </div>\r\n                  \r\n                  {action.conditions.map((condition, conditionIndex) => (\r\n                    <div key={conditionIndex} className=\"flex items-center space-x-2 mb-2\">\r\n                      <input\r\n                        type=\"text\"\r\n                        value={condition.field}\r\n                        onChange={(e) => handleConditionChange(actionIndex, conditionIndex, { \r\n                          ...condition, \r\n                          field: e.target.value \r\n                        })}\r\n                        placeholder=\"Field name\"\r\n                        className=\"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\r\n                      />\r\n                      <select\r\n                        value={condition.operator}\r\n                        onChange={(e) => handleConditionChange(actionIndex, conditionIndex, { \r\n                          ...condition, \r\n                          operator: e.target.value \r\n                        })}\r\n                        className=\"p-2 border border-gray-300 rounded-md text-sm\"\r\n                      >\r\n                        <option value=\"equals\">Equals</option>\r\n                        <option value=\"not_equals\">Not Equals</option>\r\n                        <option value=\"contains\">Contains</option>\r\n                        <option value=\"not_contains\">Not Contains</option>\r\n                        <option value=\"exists\">Exists</option>\r\n                        <option value=\"not_exists\">Not Exists</option>\r\n                      </select>\r\n                      <input\r\n                        type=\"text\"\r\n                        value={condition.value}\r\n                        onChange={(e) => handleConditionChange(actionIndex, conditionIndex, { \r\n                          ...condition, \r\n                          value: e.target.value \r\n                        })}\r\n                        placeholder=\"Value\"\r\n                        className=\"flex-1 p-2 border border-gray-300 rounded-md text-sm\"\r\n                      />\r\n                      <button\r\n                        type=\"button\"\r\n                        onClick={() => handleRemoveCondition(actionIndex, conditionIndex)}\r\n                        className=\"text-red-600 hover:text-red-800 text-sm\"\r\n                      >\r\n                        Remove\r\n                      </button>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormLinkingConfig;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,WAAW;EACXC,mBAAmB;EACnBC,UAAU,GAAG;AACf,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACY,OAAO,EAAEC,UAAU,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;;EAE7C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,mBAAmB,GAAG,MAAAA,CAAA,KAAY;MACtC,IAAI,CAACN,UAAU,EAAE;MAEjBK,UAAU,CAAC,IAAI,CAAC;MAChB,IAAI;QACF,MAAME,QAAQ,GAAG,MAAMb,GAAG,CAACc,GAAG,CAAC,mCAAmC,CAAC;QACnE,IAAID,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;UACzBP,iBAAiB,CAACI,QAAQ,CAACE,IAAI,CAACE,KAAK,CAAC;QACxC;MACF,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD,CAAC,SAAS;QACRP,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,CAACN,UAAU,CAAC,CAAC;;EAEhB;EACA,MAAMc,uBAAuB,GAAIC,OAAO,IAAK;IAC3ChB,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACdiB,OAAO,EAAEA;IACX,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,MAAMC,SAAS,GAAG;MAChBC,UAAU,EAAE,OAAO;MACnBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,EAAE;MAClBC,YAAY,EAAE,CAAC,CAAC;MAChBC,UAAU,EAAE,EAAE;MACdC,WAAW,EAAE;IACf,CAAC;IAEDxB,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACd0B,aAAa,EAAE,CAAC,GAAG1B,WAAW,CAAC0B,aAAa,EAAEP,SAAS;IACzD,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMQ,wBAAwB,GAAIC,KAAK,IAAK;IAC1C,MAAMC,cAAc,GAAG7B,WAAW,CAAC0B,aAAa,CAACI,MAAM,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,KAAKJ,KAAK,CAAC;IAC9E3B,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACd0B,aAAa,EAAEG;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,wBAAwB,GAAGA,CAACL,KAAK,EAAEM,aAAa,KAAK;IACzD,MAAML,cAAc,GAAG7B,WAAW,CAAC0B,aAAa,CAACS,GAAG,CAAC,CAACC,MAAM,EAAEJ,CAAC,KAC7DA,CAAC,KAAKJ,KAAK,GAAGM,aAAa,GAAGE,MAChC,CAAC;IACDnC,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACd0B,aAAa,EAAEG;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMQ,wBAAwB,GAAGA,CAACC,WAAW,EAAEC,WAAW,EAAEC,WAAW,KAAK;IAC1E,MAAMX,cAAc,GAAG7B,WAAW,CAAC0B,aAAa,CAACS,GAAG,CAAC,CAACC,MAAM,EAAEJ,CAAC,KAAK;MAClE,IAAIA,CAAC,KAAKM,WAAW,EAAE;QACrB,MAAMG,eAAe,GAAG;UAAE,GAAGL,MAAM,CAACb;QAAa,CAAC;QAClD,IAAIiB,WAAW,EAAE;UACfC,eAAe,CAACD,WAAW,CAAC,GAAGD,WAAW;QAC5C,CAAC,MAAM;UACL,OAAOE,eAAe,CAACF,WAAW,CAAC;QACrC;QACA,OAAO;UAAE,GAAGH,MAAM;UAAEb,YAAY,EAAEkB;QAAgB,CAAC;MACrD;MACA,OAAOL,MAAM;IACf,CAAC,CAAC;IAEFnC,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACd0B,aAAa,EAAEG;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMa,qBAAqB,GAAGA,CAACJ,WAAW,EAAEK,cAAc,EAAEC,gBAAgB,KAAK;IAC/E,MAAMf,cAAc,GAAG7B,WAAW,CAAC0B,aAAa,CAACS,GAAG,CAAC,CAACC,MAAM,EAAEJ,CAAC,KAAK;MAClE,IAAIA,CAAC,KAAKM,WAAW,EAAE;QACrB,MAAMO,aAAa,GAAGT,MAAM,CAACZ,UAAU,CAACW,GAAG,CAAC,CAACW,SAAS,EAAEC,CAAC,KACvDA,CAAC,KAAKJ,cAAc,GAAGC,gBAAgB,GAAGE,SAC5C,CAAC;QACD,OAAO;UAAE,GAAGV,MAAM;UAAEZ,UAAU,EAAEqB;QAAc,CAAC;MACjD;MACA,OAAOT,MAAM;IACf,CAAC,CAAC;IAEFnC,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACd0B,aAAa,EAAEG;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMmB,kBAAkB,GAAIV,WAAW,IAAK;IAC1C,MAAMW,YAAY,GAAG;MACnBC,KAAK,EAAE,EAAE;MACTC,QAAQ,EAAE,QAAQ;MAClBC,KAAK,EAAE;IACT,CAAC;IAED,MAAMvB,cAAc,GAAG7B,WAAW,CAAC0B,aAAa,CAACS,GAAG,CAAC,CAACC,MAAM,EAAEJ,CAAC,KAAK;MAClE,IAAIA,CAAC,KAAKM,WAAW,EAAE;QACrB,OAAO;UAAE,GAAGF,MAAM;UAAEZ,UAAU,EAAE,CAAC,GAAGY,MAAM,CAACZ,UAAU,EAAEyB,YAAY;QAAE,CAAC;MACxE;MACA,OAAOb,MAAM;IACf,CAAC,CAAC;IAEFnC,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACd0B,aAAa,EAAEG;IACjB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,qBAAqB,GAAGA,CAACf,WAAW,EAAEK,cAAc,KAAK;IAC7D,MAAMd,cAAc,GAAG7B,WAAW,CAAC0B,aAAa,CAACS,GAAG,CAAC,CAACC,MAAM,EAAEJ,CAAC,KAAK;MAClE,IAAIA,CAAC,KAAKM,WAAW,EAAE;QACrB,MAAMO,aAAa,GAAGT,MAAM,CAACZ,UAAU,CAACM,MAAM,CAAC,CAACC,CAAC,EAAEgB,CAAC,KAAKA,CAAC,KAAKJ,cAAc,CAAC;QAC9E,OAAO;UAAE,GAAGP,MAAM;UAAEZ,UAAU,EAAEqB;QAAc,CAAC;MACjD;MACA,OAAOT,MAAM;IACf,CAAC,CAAC;IAEFnC,mBAAmB,CAAC;MAClB,GAAGD,WAAW;MACd0B,aAAa,EAAEG;IACjB,CAAC,CAAC;EACJ,CAAC;EAED,IAAI,CAAC3B,UAAU,EAAE;IACf,OAAO,IAAI;EACb;EAEA,oBACEJ,OAAA;IAAKwD,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBzD,OAAA;MAAIwD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAA0B;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACjF7D,OAAA;MAAGwD,SAAS,EAAC,uBAAuB;MAAAC,QAAA,EAAC;IAErC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC,eAGJ7D,OAAA;MAAKwD,SAAS,EAAC,6BAA6B;MAAAC,QAAA,gBAC1CzD,OAAA;QACE8D,IAAI,EAAC,UAAU;QACfC,EAAE,EAAC,oBAAoB;QACvBC,OAAO,EAAE9D,WAAW,CAACiB,OAAQ;QAC7B8C,QAAQ,EAAGC,CAAC,IAAKhD,uBAAuB,CAACgD,CAAC,CAACC,MAAM,CAACH,OAAO,CAAE;QAC3DR,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eACF7D,OAAA;QAAOoE,OAAO,EAAC,oBAAoB;QAACZ,SAAS,EAAC,mCAAmC;QAAAC,QAAA,EAAC;MAElF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,EAEL3D,WAAW,CAACiB,OAAO,iBAClBnB,OAAA;MAAKwD,SAAS,EAAC,WAAW;MAAAC,QAAA,eAExBzD,OAAA;QAAAyD,QAAA,gBACEzD,OAAA;UAAKwD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDzD,OAAA;YAAIwD,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrE7D,OAAA;YACE8D,IAAI,EAAC,QAAQ;YACbO,OAAO,EAAEjD,qBAAsB;YAC/BoC,SAAS,EAAC,oEAAoE;YAAAC,QAAA,EAC/E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,EAELrD,OAAO,iBACNR,OAAA;UAAKwD,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BzD,OAAA;YAAKwD,SAAS,EAAC;UAAsE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC5F7D,OAAA;YAAGwD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,EAAC;UAA0B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CACN,EAEA3D,WAAW,CAAC0B,aAAa,CAACS,GAAG,CAAC,CAACC,MAAM,EAAEE,WAAW;UAAA,IAAA8B,mBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA;UAAA,oBACjDzE,OAAA;YAAuBwD,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBAC3EzD,OAAA;cAAKwD,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDzD,OAAA;gBAAIwD,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAC,SAAO,EAACjB,WAAW,GAAG,CAAC;cAAA;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/E7D,OAAA;gBACE8D,IAAI,EAAC,QAAQ;gBACbO,OAAO,EAAEA,CAAA,KAAMxC,wBAAwB,CAACW,WAAW,CAAE;gBACrDgB,SAAS,EAAC,yCAAyC;gBAAAC,QAAA,EACpD;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEN7D,OAAA;cAAKwD,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBAEpDzD,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAOwD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE8D,IAAI,EAAC,MAAM;kBACXR,KAAK,EAAEhB,MAAM,CAAChB,UAAW;kBACzB2C,QAAQ,EAAGC,CAAC,IAAK/B,wBAAwB,CAACK,WAAW,EAAE;oBACrD,GAAGF,MAAM;oBACThB,UAAU,EAAE4C,CAAC,CAACC,MAAM,CAACb;kBACvB,CAAC,CAAE;kBACHE,SAAS,EAAC,wFAAwF;kBAClGkB,WAAW,EAAC;gBAAO;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN7D,OAAA;gBAAAyD,QAAA,gBACEzD,OAAA;kBAAOwD,SAAS,EAAC,8CAA8C;kBAAAC,QAAA,EAAC;gBAEhE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACEsD,KAAK,EAAEhB,MAAM,CAACX,WAAY;kBAC1BsC,QAAQ,EAAGC,CAAC,IAAK/B,wBAAwB,CAACK,WAAW,EAAE;oBACrD,GAAGF,MAAM;oBACTX,WAAW,EAAEuC,CAAC,CAACC,MAAM,CAACb;kBACxB,CAAC,CAAE;kBACHE,SAAS,EAAC,wFAAwF;kBAAAC,QAAA,gBAElGzD,OAAA;oBAAQsD,KAAK,EAAC,SAAS;oBAAAG,QAAA,EAAC;kBAAc;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC/C7D,OAAA;oBAAQsD,KAAK,EAAC,WAAW;oBAAAG,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACnD7D,OAAA;oBAAQsD,KAAK,EAAC,SAAS;oBAAAG,QAAA,EAAC;kBAAe;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAChD7D,OAAA;oBAAQsD,KAAK,EAAC,SAAS;oBAAAG,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACjD7D,OAAA;oBAAQsD,KAAK,EAAC,QAAQ;oBAAAG,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOwD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBACEsD,KAAK,EAAEhB,MAAM,CAACf,YAAa;gBAC3B0C,QAAQ,EAAGC,CAAC,IAAK;kBACf,MAAMS,YAAY,GAAGrE,cAAc,CAACsE,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKZ,CAAC,CAACC,MAAM,CAACb,KAAK,CAAC;kBACvEnB,wBAAwB,CAACK,WAAW,EAAE;oBACpC,GAAGF,MAAM;oBACTf,YAAY,EAAE2C,CAAC,CAACC,MAAM,CAACb,KAAK;oBAC5B9B,cAAc,EAAEmD,YAAY,GAAGA,YAAY,CAACI,IAAI,GAAG;kBACrD,CAAC,CAAC;gBACJ,CAAE;gBACFvB,SAAS,EAAC,wFAAwF;gBAAAC,QAAA,gBAElGzD,OAAA;kBAAQsD,KAAK,EAAC,EAAE;kBAAAG,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACzCvD,cAAc,CAAC+B,GAAG,CAAC2C,IAAI,iBACtBhF,OAAA;kBAAuBsD,KAAK,EAAE0B,IAAI,CAACF,GAAI;kBAAArB,QAAA,GACpCuB,IAAI,CAACD,IAAI,EAAC,IAAE,EAACC,IAAI,CAACC,QAAQ,EAAC,GAC9B;gBAAA,GAFaD,IAAI,CAACF,GAAG;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEb,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAGN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAOwD,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7D,OAAA;gBAAKwD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN7D,OAAA;gBAAKwD,SAAS,EAAC,WAAW;gBAAAC,QAAA,GACvByB,MAAM,CAACC,OAAO,CAAC7C,MAAM,CAACb,YAAY,CAAC,CAACY,GAAG,CAAC,CAAC,CAACK,WAAW,EAAED,WAAW,CAAC,EAAE2C,YAAY,kBAChFpF,OAAA;kBAAwBwD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,gBAC7DzD,OAAA;oBACE8D,IAAI,EAAC,MAAM;oBACXR,KAAK,EAAEb,WAAY;oBACnBwB,QAAQ,EAAGC,CAAC,IAAK3B,wBAAwB,CAACC,WAAW,EAAE0B,CAAC,CAACC,MAAM,CAACb,KAAK,EAAEZ,WAAW,CAAE;oBACpFgC,WAAW,EAAC,cAAc;oBAC1BlB,SAAS,EAAC;kBAAsD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACF7D,OAAA;oBAAMwD,SAAS,EAAC,eAAe;oBAAAC,QAAA,EAAC;kBAAC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACxC7D,OAAA;oBACE8D,IAAI,EAAC,MAAM;oBACXR,KAAK,EAAEZ,WAAY;oBACnBuB,QAAQ,EAAGC,CAAC,IAAK;sBACf,MAAMmB,UAAU,GAAG;wBAAE,GAAG/C,MAAM,CAACb;sBAAa,CAAC;sBAC7C,OAAO4D,UAAU,CAAC3C,WAAW,CAAC;sBAC9B,IAAIwB,CAAC,CAACC,MAAM,CAACb,KAAK,EAAE;wBAClB+B,UAAU,CAACnB,CAAC,CAACC,MAAM,CAACb,KAAK,CAAC,GAAGb,WAAW;sBAC1C;sBACAN,wBAAwB,CAACK,WAAW,EAAE;wBACpC,GAAGF,MAAM;wBACTb,YAAY,EAAE4D;sBAChB,CAAC,CAAC;oBACJ,CAAE;oBACFX,WAAW,EAAC,cAAc;oBAC1BlB,SAAS,EAAC;kBAAsD;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC,eACF7D,OAAA;oBACE8D,IAAI,EAAC,QAAQ;oBACbO,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAACC,WAAW,EAAEC,WAAW,EAAE,IAAI,CAAE;oBACxEe,SAAS,EAAC,yCAAyC;oBAAAC,QAAA,EACpD;kBAED;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA,GAhCDuB,YAAY;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAiCjB,CACN,CAAC,eACF7D,OAAA;kBACE8D,IAAI,EAAC,QAAQ;kBACbO,OAAO,EAAEA,CAAA,KAAM9B,wBAAwB,CAACC,WAAW,EAAE,EAAE,EAAE,UAAU,CAAE;kBACrEgB,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACtD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAKwD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,eACrDzD,OAAA;kBAAOwD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eACN7D,OAAA;gBAAKwD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAEN7D,OAAA;gBAAKwD,SAAS,EAAC,kDAAkD;gBAAAC,QAAA,gBAC/DzD,OAAA;kBAAKwD,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,gBAC/CzD,OAAA;oBACE8D,IAAI,EAAC,UAAU;oBACfC,EAAE,EAAE,eAAevB,WAAW,EAAG;oBACjCwB,OAAO,EAAE,EAAAM,mBAAA,GAAAhC,MAAM,CAACgD,WAAW,cAAAhB,mBAAA,uBAAlBA,mBAAA,CAAoBnD,OAAO,KAAI,KAAM;oBAC9C8C,QAAQ,EAAGC,CAAC;sBAAA,IAAAqB,oBAAA;sBAAA,OAAKpD,wBAAwB,CAACK,WAAW,EAAE;wBACrD,GAAGF,MAAM;wBACTgD,WAAW,EAAE;0BACX,GAAGhD,MAAM,CAACgD,WAAW;0BACrBnE,OAAO,EAAE+C,CAAC,CAACC,MAAM,CAACH,OAAO;0BACzBwB,YAAY,EAAE,EAAAD,oBAAA,GAAAjD,MAAM,CAACgD,WAAW,cAAAC,oBAAA,uBAAlBA,oBAAA,CAAoBC,YAAY,KAAI;wBACpD;sBACF,CAAC,CAAC;oBAAA,CAAC;oBACHhC,SAAS,EAAC;kBAAmE;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC9E,CAAC,eACF7D,OAAA;oBAAOoE,OAAO,EAAE,eAAe5B,WAAW,EAAG;oBAACgB,SAAS,EAAC;kBAAmC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEpF,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,EAEL,EAAAU,oBAAA,GAAAjC,MAAM,CAACgD,WAAW,cAAAf,oBAAA,uBAAlBA,oBAAA,CAAoBpD,OAAO,kBAC1BnB,OAAA;kBAAKwD,SAAS,EAAC,WAAW;kBAAAC,QAAA,gBACxBzD,OAAA;oBAAAyD,QAAA,gBACEzD,OAAA;sBAAOwD,SAAS,EAAC,8CAA8C;sBAAAC,QAAA,EAAC;oBAEhE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACR7D,OAAA;sBACE8D,IAAI,EAAC,QAAQ;sBACb2B,GAAG,EAAC,GAAG;sBACPC,GAAG,EAAC,IAAI;sBACRpC,KAAK,EAAE,EAAAkB,oBAAA,GAAAlC,MAAM,CAACgD,WAAW,cAAAd,oBAAA,uBAAlBA,oBAAA,CAAoBgB,YAAY,KAAI,CAAE;sBAC7CvB,QAAQ,EAAGC,CAAC,IAAK/B,wBAAwB,CAACK,WAAW,EAAE;wBACrD,GAAGF,MAAM;wBACTgD,WAAW,EAAE;0BACX,GAAGhD,MAAM,CAACgD,WAAW;0BACrBnE,OAAO,EAAE,IAAI;0BACbqE,YAAY,EAAEG,QAAQ,CAACzB,CAAC,CAACC,MAAM,CAACb,KAAK,CAAC,IAAI;wBAC5C;sBACF,CAAC,CAAE;sBACHE,SAAS,EAAC,wFAAwF;sBAClGkB,WAAW,EAAC;oBAAG;sBAAAhB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChB,CAAC,eACF7D,OAAA;sBAAKwD,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAE5C;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAEN7D,OAAA;oBAAKwD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAC/DzD,OAAA;sBAAKwD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC/BzD,OAAA;wBAAKwD,SAAS,EAAC,eAAe;wBAAAC,QAAA,eAC5BzD,OAAA;0BAAKwD,SAAS,EAAC,uBAAuB;0BAACoC,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,cAAc;0BAAApC,QAAA,eAC5EzD,OAAA;4BAAM8F,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,kIAAkI;4BAACC,QAAQ,EAAC;0BAAS;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN7D,OAAA;wBAAKwD,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBzD,OAAA;0BAAIwD,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAoB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3E7D,OAAA;0BAAKwD,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAE5C;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN,EAGA,GAAAY,oBAAA,GAACnC,MAAM,CAACgD,WAAW,cAAAb,oBAAA,eAAlBA,oBAAA,CAAoBtD,OAAO,kBAC3BnB,OAAA;kBAAKwD,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBzD,OAAA;oBAAKwD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CzD,OAAA;sBACE8D,IAAI,EAAC,UAAU;sBACfC,EAAE,EAAE,qBAAqBvB,WAAW,EAAG;sBACvCwB,OAAO,EAAE1B,MAAM,CAAC2D,iBAAiB,IAAI,KAAM;sBAC3ChC,QAAQ,EAAGC,CAAC,IAAK/B,wBAAwB,CAACK,WAAW,EAAE;wBACrD,GAAGF,MAAM;wBACT2D,iBAAiB,EAAE/B,CAAC,CAACC,MAAM,CAACH;sBAC9B,CAAC,CAAE;sBACHR,SAAS,EAAC;oBAAqE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChF,CAAC,eACF7D,OAAA;sBAAOoE,OAAO,EAAE,qBAAqB5B,WAAW,EAAG;sBAACgB,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAC;oBAElG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC,EAELvB,MAAM,CAAC2D,iBAAiB,iBACvBjG,OAAA;oBAAKwD,SAAS,EAAC,oDAAoD;oBAAAC,QAAA,eACjEzD,OAAA;sBAAKwD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC/BzD,OAAA;wBAAKwD,SAAS,EAAC,eAAe;wBAAAC,QAAA,eAC5BzD,OAAA;0BAAKwD,SAAS,EAAC,wBAAwB;0BAACoC,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,cAAc;0BAAApC,QAAA,eAC7EzD,OAAA;4BAAM8F,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,uIAAuI;4BAACC,QAAQ,EAAC;0BAAS;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACrL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN7D,OAAA;wBAAKwD,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBzD,OAAA;0BAAIwD,SAAS,EAAC,oCAAoC;0BAAAC,QAAA,EAAC;wBAAmB;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC3E7D,OAAA;0BAAKwD,SAAS,EAAC,6BAA6B;0BAAAC,QAAA,EAAC;wBAE7C;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN,EAEA,CAACvB,MAAM,CAAC2D,iBAAiB,iBACxBjG,OAAA;oBAAKwD,SAAS,EAAC,kDAAkD;oBAAAC,QAAA,eAC/DzD,OAAA;sBAAKwD,SAAS,EAAC,kBAAkB;sBAAAC,QAAA,gBAC/BzD,OAAA;wBAAKwD,SAAS,EAAC,eAAe;wBAAAC,QAAA,eAC5BzD,OAAA;0BAAKwD,SAAS,EAAC,uBAAuB;0BAACoC,OAAO,EAAC,WAAW;0BAACC,IAAI,EAAC,cAAc;0BAAApC,QAAA,eAC5EzD,OAAA;4BAAM8F,QAAQ,EAAC,SAAS;4BAACC,CAAC,EAAC,kIAAkI;4BAACC,QAAQ,EAAC;0BAAS;4BAAAtC,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAChL;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACN7D,OAAA;wBAAKwD,SAAS,EAAC,MAAM;wBAAAC,QAAA,gBACnBzD,OAAA;0BAAIwD,SAAS,EAAC,mCAAmC;0BAAAC,QAAA,EAAC;wBAAY;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACnE7D,OAAA;0BAAKwD,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAE5C;0BAAAC,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBzD,OAAA;gBAAKwD,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDzD,OAAA;kBAAOwD,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EAAC;gBAE3D;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7D,OAAA;kBACE8D,IAAI,EAAC,QAAQ;kBACbO,OAAO,EAAEA,CAAA,KAAMnB,kBAAkB,CAACV,WAAW,CAAE;kBAC/CgB,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACtD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC,eACN7D,OAAA;gBAAKwD,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAE5C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,EAELvB,MAAM,CAACZ,UAAU,CAACW,GAAG,CAAC,CAACW,SAAS,EAAEH,cAAc,kBAC/C7C,OAAA;gBAA0BwD,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,gBACpEzD,OAAA;kBACE8D,IAAI,EAAC,MAAM;kBACXR,KAAK,EAAEN,SAAS,CAACI,KAAM;kBACvBa,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACJ,WAAW,EAAEK,cAAc,EAAE;oBAClE,GAAGG,SAAS;oBACZI,KAAK,EAAEc,CAAC,CAACC,MAAM,CAACb;kBAClB,CAAC,CAAE;kBACHoB,WAAW,EAAC,YAAY;kBACxBlB,SAAS,EAAC;gBAAsD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC,eACF7D,OAAA;kBACEsD,KAAK,EAAEN,SAAS,CAACK,QAAS;kBAC1BY,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACJ,WAAW,EAAEK,cAAc,EAAE;oBAClE,GAAGG,SAAS;oBACZK,QAAQ,EAAEa,CAAC,CAACC,MAAM,CAACb;kBACrB,CAAC,CAAE;kBACHE,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAEzDzD,OAAA;oBAAQsD,KAAK,EAAC,QAAQ;oBAAAG,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC7D,OAAA;oBAAQsD,KAAK,EAAC,YAAY;oBAAAG,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC9C7D,OAAA;oBAAQsD,KAAK,EAAC,UAAU;oBAAAG,QAAA,EAAC;kBAAQ;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAC1C7D,OAAA;oBAAQsD,KAAK,EAAC,cAAc;oBAAAG,QAAA,EAAC;kBAAY;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eAClD7D,OAAA;oBAAQsD,KAAK,EAAC,QAAQ;oBAAAG,QAAA,EAAC;kBAAM;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACtC7D,OAAA;oBAAQsD,KAAK,EAAC,YAAY;oBAAAG,QAAA,EAAC;kBAAU;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACT7D,OAAA;kBACE8D,IAAI,EAAC,MAAM;kBACXR,KAAK,EAAEN,SAAS,CAACM,KAAM;kBACvBW,QAAQ,EAAGC,CAAC,IAAKtB,qBAAqB,CAACJ,WAAW,EAAEK,cAAc,EAAE;oBAClE,GAAGG,SAAS;oBACZM,KAAK,EAAEY,CAAC,CAACC,MAAM,CAACb;kBAClB,CAAC,CAAE;kBACHoB,WAAW,EAAC,OAAO;kBACnBlB,SAAS,EAAC;gBAAsD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjE,CAAC,eACF7D,OAAA;kBACE8D,IAAI,EAAC,QAAQ;kBACbO,OAAO,EAAEA,CAAA,KAAMd,qBAAqB,CAACf,WAAW,EAAEK,cAAc,CAAE;kBAClEW,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,EACpD;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA,GA1CDhB,cAAc;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA2CnB,CACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GA9UErB,WAAW;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OA+UhB,CAAC;QAAA,CACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxD,EAAA,CA5hBIJ,iBAAiB;AAAAiG,EAAA,GAAjBjG,iBAAiB;AA8hBvB,eAAeA,iBAAiB;AAAC,IAAAiG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}