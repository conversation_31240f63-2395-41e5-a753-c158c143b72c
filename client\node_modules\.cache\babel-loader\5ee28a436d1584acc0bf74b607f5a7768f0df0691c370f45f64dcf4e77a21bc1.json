{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatbotWidget.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport ChatPage from '../pages/ChatPage';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChatbotWidget = () => {\n  _s();\n  const [isOpen, setIsOpen] = useState(false);\n\n  // Handle chat toggle with trigger prevention\n  const handleChatToggle = () => {\n    const newIsOpen = !isOpen;\n    setIsOpen(newIsOpen);\n\n    // Set timestamp when chat is opened to prevent auto-triggers\n    if (newIsOpen) {\n      console.log('🔓 Chat opened, setting lastChatLoadTime to prevent auto-triggers');\n      localStorage.setItem('lastChatLoadTime', Date.now().toString());\n    } else {\n      console.log('🔒 Chat closed');\n    }\n  };\n  const [messages, setMessages] = useState([{\n    type: 'bot',\n    text: '👋 Hi! I provide comprehensive details about:\\n• Employee Directory & Information\\n• Attendance Records & Analysis\\n• Leave Management & Balances\\n• Project Overview & Statistics\\n\\nAsk me anything for full detailed responses!'\n  }]);\n  const [inputValue, setInputValue] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const scrollToBottom = () => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: \"smooth\"\n    });\n  };\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      onClick: handleChatToggle,\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: '60px',\n        height: '60px',\n        backgroundColor: '#007bff',\n        borderRadius: '50%',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        cursor: 'pointer',\n        boxShadow: '0 4px 12px rgba(0,123,255,0.3)',\n        zIndex: 1000,\n        transition: 'all 0.3s ease'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"span\", {\n        style: {\n          color: 'white',\n          fontSize: '24px'\n        },\n        children: isOpen ? '✕' : '💬'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(ChatPage, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(ChatbotWidget, \"y/OuAHFiNuOkCqTjw2dAkmEPQVo=\");\n_c = ChatbotWidget;\nexport default ChatbotWidget;\nvar _c;\n$RefreshReg$(_c, \"ChatbotWidget\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "ChatPage", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Cha<PERSON>bot<PERSON><PERSON>t", "_s", "isOpen", "setIsOpen", "handleChatToggle", "newIsOpen", "console", "log", "localStorage", "setItem", "Date", "now", "toString", "messages", "setMessages", "type", "text", "inputValue", "setInputValue", "isLoading", "setIsLoading", "messagesEndRef", "scrollToBottom", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "children", "onClick", "style", "position", "bottom", "right", "width", "height", "backgroundColor", "borderRadius", "display", "alignItems", "justifyContent", "cursor", "boxShadow", "zIndex", "transition", "color", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatbotWidget.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport ChatPage from '../pages/ChatPage';\r\n\r\nconst ChatbotWidget = () => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n\r\n  // Handle chat toggle with trigger prevention\r\n  const handleChatToggle = () => {\r\n    const newIsOpen = !isOpen;\r\n    setIsOpen(newIsOpen);\r\n\r\n    // Set timestamp when chat is opened to prevent auto-triggers\r\n    if (newIsOpen) {\r\n      console.log('🔓 Chat opened, setting lastChatLoadTime to prevent auto-triggers');\r\n      localStorage.setItem('lastChatLoadTime', Date.now().toString());\r\n    } else {\r\n      console.log('🔒 Chat closed');\r\n    }\r\n  };\r\n  const [messages, setMessages] = useState([\r\n    {\r\n      type: 'bot',\r\n      text: '👋 Hi! I provide comprehensive details about:\\n• Employee Directory & Information\\n• Attendance Records & Analysis\\n• Leave Management & Balances\\n• Project Overview & Statistics\\n\\nAsk me anything for full detailed responses!'\r\n    }\r\n  ]);\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const messagesEndRef = useRef(null);\r\n\r\n  const scrollToBottom = () => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: \"smooth\" });\r\n  };\r\n\r\n  useEffect(() => {\r\n    scrollToBottom();\r\n  }, [messages]);\r\n\r\n  return (\r\n    <>\r\n      {/* Chat Button */}\r\n      <div\r\n        onClick={handleChatToggle}\r\n        style={{\r\n          position: 'fixed',\r\n          bottom: '20px',\r\n          right: '20px',\r\n          width: '60px',\r\n          height: '60px',\r\n          backgroundColor: '#007bff',\r\n          borderRadius: '50%',\r\n          display: 'flex',\r\n          alignItems: 'center',\r\n          justifyContent: 'center',\r\n          cursor: 'pointer',\r\n          boxShadow: '0 4px 12px rgba(0,123,255,0.3)',\r\n          zIndex: 1000,\r\n          transition: 'all 0.3s ease'\r\n        }}\r\n      >\r\n        <span style={{ color: 'white', fontSize: '24px' }}>\r\n          {isOpen ? '✕' : '💬'}\r\n        </span>\r\n      </div>\r\n      {isOpen && (\r\n        <ChatPage />\r\n      )}\r\n\r\n      \r\n    </>\r\n  );\r\n};\r\n\r\nexport default ChatbotWidget;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,QAAQ,MAAM,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;;EAE3C;EACA,MAAMY,gBAAgB,GAAGA,CAAA,KAAM;IAC7B,MAAMC,SAAS,GAAG,CAACH,MAAM;IACzBC,SAAS,CAACE,SAAS,CAAC;;IAEpB;IACA,IAAIA,SAAS,EAAE;MACbC,OAAO,CAACC,GAAG,CAAC,mEAAmE,CAAC;MAChFC,YAAY,CAACC,OAAO,CAAC,kBAAkB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC,MAAM;MACLN,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC/B;EACF,CAAC;EACD,MAAM,CAACM,QAAQ,EAAEC,WAAW,CAAC,GAAGtB,QAAQ,CAAC,CACvC;IACEuB,IAAI,EAAE,KAAK;IACXC,IAAI,EAAE;EACR,CAAC,CACF,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM6B,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAEnC,MAAM6B,cAAc,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA;IAC3B,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC;EAEDhC,SAAS,CAAC,MAAM;IACd4B,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;EAEd,oBACEhB,OAAA,CAAAE,SAAA;IAAA4B,QAAA,gBAEE9B,OAAA;MACE+B,OAAO,EAAExB,gBAAiB;MAC1ByB,KAAK,EAAE;QACLC,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,gCAAgC;QAC3CC,MAAM,EAAE,IAAI;QACZC,UAAU,EAAE;MACd,CAAE;MAAAhB,QAAA,eAEF9B,OAAA;QAAMgC,KAAK,EAAE;UAAEe,KAAK,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAlB,QAAA,EAC/CzB,MAAM,GAAG,GAAG,GAAG;MAAI;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EACL/C,MAAM,iBACLL,OAAA,CAACF,QAAQ;MAAAmD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACZ;EAAA,eAGD,CAAC;AAEP,CAAC;AAAChD,EAAA,CAnEID,aAAa;AAAAkD,EAAA,GAAblD,aAAa;AAqEnB,eAAeA,aAAa;AAAC,IAAAkD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}