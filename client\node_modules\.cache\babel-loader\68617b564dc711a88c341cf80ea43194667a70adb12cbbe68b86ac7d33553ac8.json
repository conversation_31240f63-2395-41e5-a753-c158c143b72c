{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatInterface.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { useForm } from '../context/FormContext';\nimport { useChat } from '../context/ChatContext';\nimport { useAuth } from '../context/AuthContext';\nimport api from '../utils/api';\nimport ChatInput from './ChatInput';\nimport ChatMessage from './ChatMessage';\nimport ChatFormDisplay from './ChatFormDisplay';\nimport HybridForm from './HybridForm';\nimport TypingIndicator from './TypingIndicator';\nimport AttendanceRegularizationDisplay from './AttendanceRegularizationDisplay';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ChatInterface = () => {\n  _s();\n  const {\n    forms,\n    getForms,\n    getFormByName\n  } = useForm();\n  const {\n    messages,\n    loading,\n    activeForm,\n    conversationalFlow,\n    hybridFlow,\n    conversationId,\n    sendMessage,\n    addAssistantMessage,\n    submitForm,\n    dismissForm,\n    clearChat,\n    setHybridFlow,\n    setMessages,\n    setConversationId,\n    setConversationalFlow,\n    setActiveForm,\n    clearLeaveBalanceCache\n  } = useChat();\n  const {\n    user,\n    login,\n    isLoggedIn,\n    logout\n  } = useAuth();\n  const [loginForm, setLoginForm] = useState(null);\n  const [loadingLoginForm, setLoadingLoginForm] = useState(true);\n  const [hybridFormId, setHybridFormId] = useState(null);\n  const messagesEndRef = useRef(null);\n\n  // Fetch login form on component mount - only once\n  useEffect(() => {\n    const fetchLoginForm = async () => {\n      // Only fetch if we don't already have the login form\n      if (!loginForm) {\n        try {\n          setLoadingLoginForm(true);\n          const form = await getFormByName('Login');\n          setLoginForm(form);\n        } catch (error) {\n          console.error('Error fetching login form:', error);\n        } finally {\n          setLoadingLoginForm(false);\n        }\n      }\n    };\n\n    // Only fetch if user is not logged in and we don't have the form yet\n    if (!isLoggedIn() && !loginForm) {\n      fetchLoginForm();\n    }\n  }, [isLoggedIn, loginForm, getFormByName]);\n\n  // Fetch forms on component mount\n  useEffect(() => {\n    if (isLoggedIn()) {\n      getForms();\n    }\n  }, [getForms, isLoggedIn]);\n\n  // Add welcome message when user logs in\n  useEffect(() => {\n    if (isLoggedIn() && messages.length === 0) {\n      // Use firstName if available, otherwise fall back to username or empId\n      const displayName = user.firstName || user.username || user.empId;\n      addAssistantMessage(`Hey ${displayName}, how can I help you today?`);\n    }\n  }, [isLoggedIn, messages.length, addAssistantMessage, user]);\n\n  // Scroll to bottom of messages\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n\n  // Process user message using the ChatContext\n  const processMessage = async message => {\n    // Use the ChatContext to send the message to the server\n    await sendMessage(message);\n  };\n\n  // Handle form submission using the ChatContext\n  const handleFormSubmit = async result => {\n    // If this is the login form, handle authentication\n    if (activeForm && activeForm.name === 'Login') {\n      try {\n        // Call the server-side login endpoint (no CORS issues)\n        const response = await api.post('/auth/login', {\n          empId: result.formData.empId,\n          password: result.formData.password\n        });\n\n        // Check if login was successful\n        if (response.data && response.data.success === true && response.data.data) {\n          const userData = response.data.data;\n\n          // Store roleType in localStorage for form submissions\n          if (userData.roleType) {\n            localStorage.setItem('roleType', userData.roleType);\n          }\n\n          // Store user in localStorage and update auth context\n          await login(userData);\n\n          // Clear chat and dismiss the login form\n          clearChat();\n          // dismissForm();\n\n          // Add welcome message using firstName\n          addAssistantMessage(`Hey ${userData.firstName}, how can I help you today?`);\n        } else {\n          // Show error message in chat\n          addAssistantMessage('Login failed. Please check your credentials and try again.');\n        }\n      } catch (error) {\n        console.error('Login error:', error);\n\n        // Show appropriate error message based on the error response\n        if (error.response && error.response.status === 401) {\n          addAssistantMessage('Invalid employee ID or password. Please try again.');\n        } else if (error.response && error.response.status === 503) {\n          addAssistantMessage('Login service is temporarily unavailable. Please try again later.');\n        } else {\n          addAssistantMessage('Login failed. Please try again later.');\n        }\n      }\n    } else {\n      // For other forms, use the normal submit handler with API response\n      submitForm(activeForm._id, result.formData, false, null, result.apiResponse, result.success);\n    }\n  };\n\n  // Handle form cancellation using the ChatContext\n  const handleFormCancel = () => {\n    // Use the ChatContext to dismiss the form\n    // This will also add cancellation messages to the chat\n    dismissForm();\n  };\n\n  // Handle conversational form option selection\n  const handleOptionSelect = option => {\n    // Send the selected option as a user message\n    processMessage(option);\n  };\n\n  // Handle user logout\n  const handleLogout = () => {\n    logout();\n    clearChat();\n    clearLeaveBalanceCache(); // Clear leave balance cache on logout\n  };\n\n  // Handle form linking (Apply button clicks)\n  const handleFormLinkTriggered = async linkData => {\n    try {\n      var _linkData$targetForm$, _linkData$targetForm$2;\n      // Check if the target form has hybrid flow enabled\n      const isHybridFlow = (_linkData$targetForm$ = linkData.targetForm.formConfig) === null || _linkData$targetForm$ === void 0 ? void 0 : (_linkData$targetForm$2 = _linkData$targetForm$.hybridFlow) === null || _linkData$targetForm$2 === void 0 ? void 0 : _linkData$targetForm$2.enabled;\n      if (isHybridFlow) {\n        var _response$data$conver, _response$data$hybrid, _response$data$conver2, _response$data$hybrid2, _response$data$conver3, _response$data$conver4, _response$data$hybrid3, _response$data$conver5, _response$data$hybrid4, _response$data$conver6, _response$data$hybrid5, _response$data$apiRes;\n        console.log('🔄 Starting hybrid flow for linked form without showing form name:', linkData.targetForm.name);\n\n        // Store prefill data temporarily for the form\n        localStorage.setItem('tempPrefillData', JSON.stringify(linkData.prefillData));\n\n        // Instead of sending the form name as a message, directly start the conversational phase\n        // by making a direct API call to start the form conversation\n        const userData = JSON.parse(localStorage.getItem('user') || '{}');\n        const response = await api.post('/chat', {\n          message: `__START_FORM_DIRECT__${linkData.targetForm.name}`,\n          // Special marker to start form directly\n          conversationId,\n          empId: userData.empId,\n          roleType: userData.roleType,\n          supervisorName: userData.supervisorName,\n          supervisorId: userData.supervisorId,\n          skipFormNameDisplay: true // Flag to skip form name display\n        }, {\n          headers: {\n            'x-emp-id': userData.empId,\n            'x-role-type': userData.roleType,\n            'Authorization': userData.token ? `Bearer ${userData.token}` : undefined\n          }\n        });\n\n        // Process the response similar to sendMessage but without adding the form name message\n        // Construct formData from hybridFlow if not present\n        const formData = response.data.formData || (response.data.hybridFlow ? {\n          _id: response.data.hybridFlow.formId,\n          name: response.data.hybridFlow.formName,\n          formConfig: response.data.formConfig\n        } : null);\n        const assistantMessage = {\n          role: 'assistant',\n          content: response.data.message,\n          formData: formData,\n          formConfig: response.data.formConfig,\n          queryIntent: response.data.queryIntent,\n          apiResponse: response.data.apiResponse,\n          matchResults: response.data.matchResults,\n          conversationalFlow: response.data.conversationalFlow,\n          fieldType: ((_response$data$conver = response.data.conversationalFlow) === null || _response$data$conver === void 0 ? void 0 : _response$data$conver.fieldType) || ((_response$data$hybrid = response.data.hybridFlow) === null || _response$data$hybrid === void 0 ? void 0 : _response$data$hybrid.fieldType) || response.data.fieldType,\n          options: ((_response$data$conver2 = response.data.conversationalFlow) === null || _response$data$conver2 === void 0 ? void 0 : _response$data$conver2.options) || ((_response$data$hybrid2 = response.data.hybridFlow) === null || _response$data$hybrid2 === void 0 ? void 0 : _response$data$hybrid2.options) || response.data.options,\n          isFormFlow: ((_response$data$conver3 = response.data.conversationalFlow) === null || _response$data$conver3 === void 0 ? void 0 : _response$data$conver3.isActive) || response.data.isFormFlow,\n          fieldName: ((_response$data$conver4 = response.data.conversationalFlow) === null || _response$data$conver4 === void 0 ? void 0 : _response$data$conver4.fieldName) || ((_response$data$hybrid3 = response.data.hybridFlow) === null || _response$data$hybrid3 === void 0 ? void 0 : _response$data$hybrid3.fieldName) || response.data.fieldName,\n          currentStep: ((_response$data$conver5 = response.data.conversationalFlow) === null || _response$data$conver5 === void 0 ? void 0 : _response$data$conver5.currentStep) || ((_response$data$hybrid4 = response.data.hybridFlow) === null || _response$data$hybrid4 === void 0 ? void 0 : _response$data$hybrid4.currentStep) || response.data.currentStep,\n          totalSteps: ((_response$data$conver6 = response.data.conversationalFlow) === null || _response$data$conver6 === void 0 ? void 0 : _response$data$conver6.totalSteps) || ((_response$data$hybrid5 = response.data.hybridFlow) === null || _response$data$hybrid5 === void 0 ? void 0 : _response$data$hybrid5.totalConversationalSteps) || response.data.totalSteps,\n          hybridFlow: response.data.hybridFlow,\n          isHybridFlow: response.data.isHybridFlow,\n          isConversationalPhase: response.data.isConversationalPhase,\n          transitionToForm: response.data.transitionToForm,\n          formFields: response.data.formFields,\n          collectedData: response.data.collectedData,\n          conversationalData: response.data.conversationalData,\n          isDynamicResponse: true,\n          isFormLinkTriggered: true,\n          // Flag to identify form linking messages\n          messageId: `form-link-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n          // Unique prefix for form linking\n          timestamp: ((_response$data$apiRes = response.data.apiResponse) === null || _response$data$apiRes === void 0 ? void 0 : _response$data$apiRes.timestamp) || Date.now()\n        };\n\n        // Update conversation ID if new\n        if (response.data.conversationId && !conversationId) {\n          setConversationId(response.data.conversationId);\n        }\n\n        // Handle conversational flow state\n        if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {\n          setConversationalFlow(response.data.conversationalFlow);\n          setActiveForm(null);\n        } else if (response.data.queryIntent === 'hybrid_form_conversation') {\n          setHybridFlow(response.data.hybridFlow);\n          setConversationalFlow(null);\n          setActiveForm(null);\n        } else if (response.data.queryIntent === 'hybrid_form_transition') {\n          // Handle hybrid form transition (conversational to form phase)\n          setHybridFlow(response.data.hybridFlow);\n          setConversationalFlow(null);\n          setActiveForm(null);\n        }\n\n        // Add the assistant message to chat (this will show the questions directly)\n        // Use the same deduplication logic as in ChatContext\n        setMessages(prevMessages => {\n          // For form linking messages, use a more specific duplicate check\n          const isDuplicate = prevMessages.some(msg => {\n            var _msg$formData, _assistantMessage$for;\n            return msg.role === 'assistant' && (\n            // Check for same messageId (most specific)\n            msg.messageId === assistantMessage.messageId ||\n            // For form linking messages, check for same form ID and transition state\n            msg.isFormLinkTriggered && assistantMessage.isFormLinkTriggered && ((_msg$formData = msg.formData) === null || _msg$formData === void 0 ? void 0 : _msg$formData._id) === ((_assistantMessage$for = assistantMessage.formData) === null || _assistantMessage$for === void 0 ? void 0 : _assistantMessage$for._id) && msg.transitionToForm === assistantMessage.transitionToForm && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 2000 ||\n            // Check for same content + options + timestamp within 2 seconds (for regular messages)\n            !msg.isFormLinkTriggered && !assistantMessage.isFormLinkTriggered && msg.content === assistantMessage.content && JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 2000);\n          });\n          if (isDuplicate) {\n            return prevMessages;\n          }\n          return [...prevMessages, assistantMessage];\n        });\n\n        // Clean up temporary storage\n        localStorage.removeItem('tempPrefillData');\n      } else {\n        // For traditional forms, also skip the form name display\n        // Create a form with prefilled data and show it directly\n        const formWithPrefillData = {\n          ...linkData.targetForm,\n          prefillData: linkData.prefillData\n        };\n\n        // Show the target form with pre-filled data without the opening message\n        submitForm(linkData.targetForm._id, {}, true, formWithPrefillData);\n      }\n    } catch (error) {\n      console.error('Error handling form link:', error);\n      addAssistantMessage('❌ Error opening linked form. Please try again.');\n    }\n  };\n\n  // Handle regularization apply button click\n  const handleRegularizationApply = async (attendanceRecord, index) => {\n    try {\n      // If forms are not loaded yet, try to load them first\n      if (!forms || forms.length === 0) {\n        console.log('⏳ Forms not loaded yet, attempting to fetch...');\n        try {\n          const fetchedForms = await getForms(); // This should fetch forms if not already loaded\n          console.log('📋 Fetched forms:', fetchedForms);\n        } catch (error) {\n          console.error('❌ Failed to fetch forms:', error);\n          addAssistantMessage('❌ Unable to load forms. Please try again or contact your administrator.');\n          return;\n        }\n      }\n\n      // Find the regularization form in the forms list\n      let regularizationForm = null;\n\n      // Try to find form by different possible names\n      const possibleFormNames = ['Regularization', 'regularization', 'Attendance Regularization', 'attendance regularization', 'AttendanceRegularization', 'attendance_regularization', 'RegularizationForm', 'regularization_form'];\n\n      // First try to find in the loaded forms array\n      if (forms && Array.isArray(forms)) {\n        for (const formName of possibleFormNames) {\n          regularizationForm = forms.find(form => form.name === formName || form.formTitle === formName || form.name && form.name.toLowerCase() === formName.toLowerCase() || form.formTitle && form.formTitle.toLowerCase() === formName.toLowerCase());\n          console.log(`🔍 Searching in loaded forms for \"${formName}\":`, regularizationForm ? 'Found' : 'Not found');\n          if (regularizationForm) break;\n        }\n      }\n\n      // If not found in loaded forms, try API calls\n      if (!regularizationForm) {\n        console.log('🌐 Form not found locally, trying API calls...');\n        for (const formName of possibleFormNames) {\n          try {\n            regularizationForm = await getFormByName(formName);\n            console.log(`🔍 API search for \"${formName}\":`, regularizationForm ? 'Found' : 'Not found');\n            if (regularizationForm) break;\n          } catch (error) {\n            console.log(`❌ API error for \"${formName}\":`, error.message);\n          }\n        }\n      }\n      if (!regularizationForm && forms && Array.isArray(forms)) {\n        // If no form found by name, try to find by checking form fields\n        regularizationForm = forms.find(form => {\n          if (!form || !form.fields || !Array.isArray(form.fields)) return false;\n          const fieldNames = form.fields.map(field => field.name.toLowerCase());\n          return fieldNames.includes('date') && (fieldNames.includes('intime') || fieldNames.includes('reason'));\n        });\n      }\n      if (!regularizationForm) {\n        console.log('❌ No regularization form found');\n        console.log('📋 Available form names:', forms ? forms.map(f => f.formTitle || f.name || f._id) : 'No forms');\n        addAssistantMessage('❌ Regularization form not found. Please contact your administrator.');\n        return;\n      }\n      console.log('✅ Found regularization form:', regularizationForm.formTitle || regularizationForm.name);\n      console.log('📋 Form fields:', regularizationForm.fields ? regularizationForm.fields.map(f => f.name) : 'No fields');\n\n      // Prepare pre-filled data for the form\n      // Use actual time if available (not 00:00), otherwise use scheduled time\n      const actualInTime = attendanceRecord.actualInTime !== '00:00' ? attendanceRecord.actualInTime : attendanceRecord.inTime;\n      const actualOutTime = attendanceRecord.actualOutTime !== '00:00' ? attendanceRecord.actualOutTime : attendanceRecord.outTime;\n      const prefillData = {\n        date: attendanceRecord.attendanceDate || attendanceRecord.date,\n        inTime: actualInTime || attendanceRecord.inTime || attendanceRecord.checkIn,\n        outTime: actualOutTime || attendanceRecord.outTime || attendanceRecord.checkOut,\n        appliedTo: localStorage.getItem('supervisorName'),\n        // Add any other common fields that might match\n        attendanceDate: attendanceRecord.attendanceDate || attendanceRecord.date,\n        checkIn: actualInTime || attendanceRecord.inTime || attendanceRecord.checkIn,\n        checkOut: actualOutTime || attendanceRecord.outTime || attendanceRecord.checkOut\n      };\n      console.log('Pre-fill data prepared:', prefillData);\n\n      // Create a copy of the form with prefilled data\n      const formWithPrefillData = {\n        ...regularizationForm,\n        prefillData: prefillData\n      };\n\n      // Show the form with pre-filled data using ChatContext\n      submitForm(regularizationForm._id, {}, true, formWithPrefillData);\n\n      // Add a message to indicate the form is being prepared\n      addAssistantMessage(`📝 Preparing regularization form for ${attendanceRecord.attendanceDate || attendanceRecord.date}. Please fill in the required details.`);\n    } catch (error) {\n      console.error('Error handling regularization apply:', error);\n      addAssistantMessage('❌ Error opening regularization form. Please try again.');\n    }\n  };\n\n  // Show login form when user is not logged in - with proper dependency tracking\n  useEffect(() => {\n    // Only set the login form as active if:\n    // 1. User is not logged in\n    // 2. We have the login form\n    // 3. Either there's no active form or the active form is not the login form\n    if (!isLoggedIn() && loginForm && (!activeForm || activeForm.name !== 'Login')) {\n      // Set the login form as the active form, passing the form object directly to avoid an extra API call\n      submitForm(loginForm._id, null, true, loginForm);\n    }\n  }, [isLoggedIn, loginForm, activeForm, submitForm]); // Include all dependencies\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex flex-col h-full bg-white rounded-lg shadow-lg overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-600 to-blue-800 p-4 text-white flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-bold\",\n        children: \"AI Form Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 434,\n        columnNumber: 9\n      }, this), isLoggedIn() && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm mr-3\",\n          children: [\"Logged in as \", user.firstName || user.username || user.empId]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 437,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: handleLogout,\n          className: \"bg-blue-700 hover:bg-blue-800 text-white text-sm py-1 px-3 rounded\",\n          children: \"Logout\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 436,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4\",\n      children: !isLoggedIn() ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center h-full\",\n        children: loadingLoginForm ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(TypingIndicator, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"mt-2 text-gray-600\",\n            children: \"Loading login form...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 454,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: activeForm && activeForm.name === 'Login' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full max-w-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-800 mb-6 text-center\",\n              children: \"Login to Chat\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(ChatFormDisplay, {\n              form: activeForm,\n              onSubmit: handleFormSubmit,\n              submitButtonText: \"Login\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 19\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: \"Login form not found. Please try again later.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 470,\n            columnNumber: 19\n          }, this)\n        }, void 0, false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 452,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [messages.map((message, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(ChatMessage, {\n            message: message,\n            onOptionSelect: handleOptionSelect,\n            onFormLinkTriggered: handleFormLinkTriggered\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 481,\n            columnNumber: 17\n          }, this), message.isLegacyResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 p-2 bg-orange-50 border border-orange-200 rounded\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-orange-700 text-xs\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-3 h-3 mr-1\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 25\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 493,\n                columnNumber: 23\n              }, this), \"Legacy Response (will be replaced by dynamic APIs)\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 19\n          }, this), message.isRegularizationData && message.attendanceInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2\",\n            children: [console.log('🎨 Rendering AttendanceRegularizationDisplay with:', message.attendanceInfo), /*#__PURE__*/_jsxDEV(AttendanceRegularizationDisplay, {\n              attendanceData: message.attendanceInfo,\n              onApplyClick: handleRegularizationApply\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 505,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 19\n          }, this), message.isRegularizationData && !message.attendanceInfo && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded\",\n            children: [console.log('⚠️ Regularization data found but no attendanceInfo:', message), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-yellow-700\",\n              children: \"Regularization data received but no attendance info available.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 19\n          }, this)]\n        }, message.messageId || `message-${index}-${message.role}-${message.timestamp || index}`, true, {\n          fileName: _jsxFileName,\n          lineNumber: 480,\n          columnNumber: 15\n        }, this)), loading && /*#__PURE__*/_jsxDEV(TypingIndicator, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 25\n        }, this), activeForm && activeForm.name !== 'Login' && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-4\",\n          children: /*#__PURE__*/_jsxDEV(ChatFormDisplay, {\n            form: activeForm,\n            onSubmit: handleFormSubmit,\n            onCancel: handleFormCancel\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 15\n        }, this), hybridFlow && hybridFlow.transitionToForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"my-4\",\n          children: /*#__PURE__*/_jsxDEV(HybridForm, {\n            hybridFlow: hybridFlow,\n            message: {\n              content: hybridFlow.completionMessage\n            },\n            formId: hybridFlow.formId,\n            onCancel: () => {\n              // Cancel hybrid form\n              sendMessage('cancel');\n            },\n            onSubmit: response => {\n              // Handle form submission response\n              console.log('🔄 ChatInterface: Handling hybrid form submission response:', response);\n\n              // Add the response message to chat\n              if (response.message) {\n                console.log('➕ Adding message to chat:', response.message);\n                addAssistantMessage(response.message);\n              }\n\n              // Check if we're continuing to a conversational phase\n              if (response.isConversationalPhase && response.hybridFlow) {\n                console.log('🔄 Continuing to next conversational phase');\n                setHybridFlow({\n                  ...response.hybridFlow,\n                  transitionToForm: false,\n                  isConversationalPhase: true\n                });\n              } else {\n                // Form completed or other state\n                console.log('✅ Hybrid form completed');\n                setHybridFlow(null);\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 537,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 536,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 573,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 450,\n      columnNumber: 7\n    }, this), isLoggedIn() && /*#__PURE__*/_jsxDEV(ChatInput, {\n      onSendMessage: processMessage,\n      loading: loading,\n      conversationalFlow: conversationalFlow,\n      hybridFlow: hybridFlow\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 579,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 432,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatInterface, \"PZe9A7O0rMh9Oz63rkp69QvjObM=\", false, function () {\n  return [useForm, useChat, useAuth];\n});\n_c = ChatInterface;\nexport default ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useForm", "useChat", "useAuth", "api", "ChatInput", "ChatMessage", "ChatFormDisplay", "HybridForm", "TypingIndicator", "AttendanceRegularizationDisplay", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ChatInterface", "_s", "forms", "getForms", "getFormByName", "messages", "loading", "activeForm", "conversationalFlow", "hybridFlow", "conversationId", "sendMessage", "addAssistantMessage", "submitForm", "dismissForm", "clearChat", "setHybridFlow", "setMessages", "setConversationId", "setConversationalFlow", "setActiveForm", "clearLeaveBalanceCache", "user", "login", "isLoggedIn", "logout", "loginForm", "setLoginForm", "loadingLoginForm", "setLoadingLoginForm", "hybridFormId", "setHybridFormId", "messagesEndRef", "fetchLoginForm", "form", "error", "console", "length", "displayName", "firstName", "username", "empId", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "processMessage", "message", "handleFormSubmit", "result", "name", "response", "post", "formData", "password", "data", "success", "userData", "roleType", "localStorage", "setItem", "status", "_id", "apiResponse", "handleFormCancel", "handleOptionSelect", "option", "handleLogout", "handleFormLinkTriggered", "linkData", "_linkData$targetForm$", "_linkData$targetForm$2", "isHybridFlow", "targetForm", "formConfig", "enabled", "_response$data$conver", "_response$data$hybrid", "_response$data$conver2", "_response$data$hybrid2", "_response$data$conver3", "_response$data$conver4", "_response$data$hybrid3", "_response$data$conver5", "_response$data$hybrid4", "_response$data$conver6", "_response$data$hybrid5", "_response$data$apiRes", "log", "JSON", "stringify", "prefillData", "parse", "getItem", "<PERSON><PERSON><PERSON>", "supervisorId", "skipFormNameDisplay", "headers", "token", "undefined", "formId", "formName", "assistant<PERSON><PERSON><PERSON>", "role", "content", "queryIntent", "matchResults", "fieldType", "options", "isFormFlow", "isActive", "fieldName", "currentStep", "totalSteps", "totalConversationalSteps", "isConversationalPhase", "transitionToForm", "formFields", "collectedData", "conversationalData", "isDynamicResponse", "isFormLinkTriggered", "messageId", "Date", "now", "Math", "random", "toString", "substr", "timestamp", "prevMessages", "isDuplicate", "some", "msg", "_msg$formData", "_assistantMessage$for", "abs", "removeItem", "formWithPrefillData", "handleRegularizationApply", "attendanceRecord", "index", "fetchedForms", "regularizationForm", "possibleFormNames", "Array", "isArray", "find", "formTitle", "toLowerCase", "fields", "fieldNames", "map", "field", "includes", "f", "actualInTime", "inTime", "actualOutTime", "outTime", "date", "attendanceDate", "checkIn", "checkOut", "appliedTo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "onSubmit", "submitButtonText", "onOptionSelect", "onFormLinkTriggered", "isLegacyResponse", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "isRegularizationData", "attendanceInfo", "attendanceData", "onApplyClick", "onCancel", "completionMessage", "ref", "onSendMessage", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatInterface.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport { useForm } from '../context/FormContext';\r\nimport { useChat } from '../context/ChatContext';\r\nimport { useAuth } from '../context/AuthContext';\r\nimport api from '../utils/api';\r\nimport ChatInput from './ChatInput';\r\nimport ChatMessage from './ChatMessage';\r\nimport ChatFormDisplay from './ChatFormDisplay';\r\nimport HybridForm from './HybridForm';\r\n\r\nimport TypingIndicator from './TypingIndicator';\r\nimport AttendanceRegularizationDisplay from './AttendanceRegularizationDisplay';\r\n\r\nconst ChatInterface = () => {\r\n  const { forms, getForms, getFormByName } = useForm();\r\n  const {\r\n    messages,\r\n    loading,\r\n    activeForm,\r\n    conversationalFlow,\r\n    hybridFlow,\r\n    conversationId,\r\n    sendMessage,\r\n    addAssistantMessage,\r\n    submitForm,\r\n    dismissForm,\r\n    clearChat,\r\n    setHybridFlow,\r\n    setMessages,\r\n    setConversationId,\r\n    setConversationalFlow,\r\n    setActiveForm,\r\n    clearLeaveBalanceCache\r\n  } = useChat();\r\n  const { user, login, isLoggedIn, logout } = useAuth();\r\n  \r\n  const [loginForm, setLoginForm] = useState(null);\r\n  const [loadingLoginForm, setLoadingLoginForm] = useState(true);\r\n  const [hybridFormId, setHybridFormId] = useState(null);\r\n  \r\n  const messagesEndRef = useRef(null);\r\n  \r\n  // Fetch login form on component mount - only once\r\n  useEffect(() => {\r\n    const fetchLoginForm = async () => {\r\n      // Only fetch if we don't already have the login form\r\n      if (!loginForm) {\r\n        try {\r\n          setLoadingLoginForm(true);\r\n          const form = await getFormByName('Login');\r\n          setLoginForm(form);\r\n        } catch (error) {\r\n          console.error('Error fetching login form:', error);\r\n        } finally {\r\n          setLoadingLoginForm(false);\r\n        }\r\n      }\r\n    };\r\n    \r\n    // Only fetch if user is not logged in and we don't have the form yet\r\n    if (!isLoggedIn() && !loginForm) {\r\n      fetchLoginForm();\r\n    }\r\n  }, [isLoggedIn, loginForm, getFormByName]);\r\n  \r\n  // Fetch forms on component mount\r\n  useEffect(() => {\r\n    if (isLoggedIn()) {\r\n      getForms();\r\n    }\r\n  }, [getForms, isLoggedIn]);\r\n  \r\n  // Add welcome message when user logs in\r\n  useEffect(() => {\r\n    if (isLoggedIn() && messages.length === 0) {\r\n      // Use firstName if available, otherwise fall back to username or empId\r\n      const displayName = user.firstName || user.username || user.empId;\r\n      addAssistantMessage(`Hey ${displayName}, how can I help you today?`);\r\n    }\r\n  }, [isLoggedIn, messages.length, addAssistantMessage, user]);\r\n  \r\n  // Scroll to bottom of messages\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [messages]);\r\n  \r\n  // Process user message using the ChatContext\r\n  const processMessage = async (message) => {\r\n    // Use the ChatContext to send the message to the server\r\n    await sendMessage(message);\r\n  };\r\n  \r\n  // Handle form submission using the ChatContext\r\n  const handleFormSubmit = async (result) => {\r\n    // If this is the login form, handle authentication\r\n    if (activeForm && activeForm.name === 'Login') {\r\n      try {\r\n        // Call the server-side login endpoint (no CORS issues)\r\n        const response = await api.post('/auth/login', {\r\n          empId: result.formData.empId,\r\n          password: result.formData.password\r\n        });\r\n        \r\n        // Check if login was successful\r\n        if (response.data && response.data.success === true && response.data.data) {\r\n          const userData = response.data.data;\r\n          \r\n          // Store roleType in localStorage for form submissions\r\n          if (userData.roleType) {\r\n            localStorage.setItem('roleType', userData.roleType);\r\n          }\r\n          \r\n          // Store user in localStorage and update auth context\r\n          await login(userData);\r\n          \r\n          // Clear chat and dismiss the login form\r\n          clearChat();\r\n          // dismissForm();\r\n          \r\n          // Add welcome message using firstName\r\n          addAssistantMessage(`Hey ${userData.firstName}, how can I help you today?`);\r\n        } else {\r\n          // Show error message in chat\r\n          addAssistantMessage('Login failed. Please check your credentials and try again.');\r\n        }\r\n      } catch (error) {\r\n        console.error('Login error:', error);\r\n        \r\n        // Show appropriate error message based on the error response\r\n        if (error.response && error.response.status === 401) {\r\n          addAssistantMessage('Invalid employee ID or password. Please try again.');\r\n        } else if (error.response && error.response.status === 503) {\r\n          addAssistantMessage('Login service is temporarily unavailable. Please try again later.');\r\n        } else {\r\n          addAssistantMessage('Login failed. Please try again later.');\r\n        }\r\n      }\r\n    } else {\r\n      // For other forms, use the normal submit handler with API response\r\n      submitForm(activeForm._id, result.formData, false, null, result.apiResponse, result.success);\r\n    }\r\n  };\r\n  \r\n  // Handle form cancellation using the ChatContext\r\n  const handleFormCancel = () => {\r\n    // Use the ChatContext to dismiss the form\r\n    // This will also add cancellation messages to the chat\r\n    dismissForm();\r\n  };\r\n\r\n  // Handle conversational form option selection\r\n  const handleOptionSelect = (option) => {\r\n    // Send the selected option as a user message\r\n    processMessage(option);\r\n  };\r\n  \r\n  // Handle user logout\r\n  const handleLogout = () => {\r\n    logout();\r\n    clearChat();\r\n    clearLeaveBalanceCache(); // Clear leave balance cache on logout\r\n  };\r\n\r\n  // Handle form linking (Apply button clicks)\r\n  const handleFormLinkTriggered = async (linkData) => {\r\n    try { \r\n      // Check if the target form has hybrid flow enabled\r\n      const isHybridFlow = linkData.targetForm.formConfig?.hybridFlow?.enabled;\r\n      \r\n      if (isHybridFlow) {\r\n        console.log('🔄 Starting hybrid flow for linked form without showing form name:', linkData.targetForm.name);\r\n        \r\n        // Store prefill data temporarily for the form\r\n        localStorage.setItem('tempPrefillData', JSON.stringify(linkData.prefillData));\r\n        \r\n        // Instead of sending the form name as a message, directly start the conversational phase\r\n        // by making a direct API call to start the form conversation\r\n        const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n        \r\n        const response = await api.post('/chat', {\r\n          message: `__START_FORM_DIRECT__${linkData.targetForm.name}`, // Special marker to start form directly\r\n          conversationId,\r\n          empId: userData.empId,\r\n          roleType: userData.roleType,\r\n          supervisorName: userData.supervisorName,\r\n          supervisorId: userData.supervisorId,\r\n          skipFormNameDisplay: true // Flag to skip form name display\r\n        }, {\r\n          headers: {\r\n            'x-emp-id': userData.empId,\r\n            'x-role-type': userData.roleType,\r\n            'Authorization': userData.token ? `Bearer ${userData.token}` : undefined\r\n          }\r\n        });\r\n        \r\n        // Process the response similar to sendMessage but without adding the form name message\r\n        // Construct formData from hybridFlow if not present\r\n        const formData = response.data.formData || (response.data.hybridFlow ? {\r\n          _id: response.data.hybridFlow.formId,\r\n          name: response.data.hybridFlow.formName,\r\n          formConfig: response.data.formConfig\r\n        } : null);\r\n        \r\n        const assistantMessage = {\r\n          role: 'assistant',\r\n          content: response.data.message,\r\n          formData: formData,\r\n          formConfig: response.data.formConfig,\r\n          queryIntent: response.data.queryIntent,\r\n          apiResponse: response.data.apiResponse,\r\n          matchResults: response.data.matchResults,\r\n          conversationalFlow: response.data.conversationalFlow,\r\n          fieldType: response.data.conversationalFlow?.fieldType || response.data.hybridFlow?.fieldType || response.data.fieldType,\r\n          options: response.data.conversationalFlow?.options || response.data.hybridFlow?.options || response.data.options,\r\n          isFormFlow: response.data.conversationalFlow?.isActive || response.data.isFormFlow,\r\n          fieldName: response.data.conversationalFlow?.fieldName || response.data.hybridFlow?.fieldName || response.data.fieldName,\r\n          currentStep: response.data.conversationalFlow?.currentStep || response.data.hybridFlow?.currentStep || response.data.currentStep,\r\n          totalSteps: response.data.conversationalFlow?.totalSteps || response.data.hybridFlow?.totalConversationalSteps || response.data.totalSteps,\r\n          hybridFlow: response.data.hybridFlow,\r\n          isHybridFlow: response.data.isHybridFlow,\r\n          isConversationalPhase: response.data.isConversationalPhase,\r\n          transitionToForm: response.data.transitionToForm,\r\n          formFields: response.data.formFields,\r\n          collectedData: response.data.collectedData,\r\n          conversationalData: response.data.conversationalData,\r\n          isDynamicResponse: true,\r\n          isFormLinkTriggered: true, // Flag to identify form linking messages\r\n          messageId: `form-link-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // Unique prefix for form linking\r\n          timestamp: response.data.apiResponse?.timestamp || Date.now()\r\n        };\r\n        \r\n        // Update conversation ID if new\r\n        if (response.data.conversationId && !conversationId) {\r\n          setConversationId(response.data.conversationId);\r\n        }\r\n        \r\n        // Handle conversational flow state\r\n        if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {\r\n          setConversationalFlow(response.data.conversationalFlow);\r\n          setActiveForm(null);\r\n        } else if (response.data.queryIntent === 'hybrid_form_conversation') {\r\n          setHybridFlow(response.data.hybridFlow);\r\n          setConversationalFlow(null);\r\n          setActiveForm(null);\r\n        } else if (response.data.queryIntent === 'hybrid_form_transition') {\r\n          // Handle hybrid form transition (conversational to form phase)\r\n          setHybridFlow(response.data.hybridFlow);\r\n          setConversationalFlow(null);\r\n          setActiveForm(null);\r\n        }\r\n        \r\n        // Add the assistant message to chat (this will show the questions directly)\r\n        // Use the same deduplication logic as in ChatContext\r\n        setMessages((prevMessages) => {\r\n          // For form linking messages, use a more specific duplicate check\r\n          const isDuplicate = prevMessages.some(msg => \r\n            msg.role === 'assistant' && (\r\n              // Check for same messageId (most specific)\r\n              (msg.messageId === assistantMessage.messageId) ||\r\n              // For form linking messages, check for same form ID and transition state\r\n              (msg.isFormLinkTriggered && assistantMessage.isFormLinkTriggered &&\r\n               msg.formData?._id === assistantMessage.formData?._id &&\r\n               msg.transitionToForm === assistantMessage.transitionToForm &&\r\n               Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 2000) ||\r\n              // Check for same content + options + timestamp within 2 seconds (for regular messages)\r\n              (!msg.isFormLinkTriggered && !assistantMessage.isFormLinkTriggered &&\r\n               msg.content === assistantMessage.content && \r\n               JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) &&\r\n               Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 2000)\r\n            )\r\n          );\r\n          \r\n          if (isDuplicate) {\r\n            return prevMessages;\r\n          }\r\n          \r\n          return [...prevMessages, assistantMessage];\r\n        });\r\n        \r\n        // Clean up temporary storage\r\n        localStorage.removeItem('tempPrefillData');\r\n        \r\n      } else {\r\n        \r\n        // For traditional forms, also skip the form name display\r\n        // Create a form with prefilled data and show it directly\r\n        const formWithPrefillData = {\r\n          ...linkData.targetForm,\r\n          prefillData: linkData.prefillData\r\n        };\r\n        \r\n        // Show the target form with pre-filled data without the opening message\r\n        submitForm(linkData.targetForm._id, {}, true, formWithPrefillData);\r\n      }\r\n      \r\n    } catch (error) {\r\n      console.error('Error handling form link:', error);\r\n      addAssistantMessage('❌ Error opening linked form. Please try again.');\r\n    }\r\n  };\r\n\r\n  // Handle regularization apply button click\r\n  const handleRegularizationApply = async (attendanceRecord, index) => {\r\n    try {\r\n   \r\n      \r\n      // If forms are not loaded yet, try to load them first\r\n      if (!forms || forms.length === 0) {\r\n        console.log('⏳ Forms not loaded yet, attempting to fetch...');\r\n        try {\r\n          const fetchedForms = await getForms(); // This should fetch forms if not already loaded\r\n          console.log('📋 Fetched forms:', fetchedForms);\r\n        } catch (error) {\r\n          console.error('❌ Failed to fetch forms:', error);\r\n          addAssistantMessage('❌ Unable to load forms. Please try again or contact your administrator.');\r\n          return;\r\n        }\r\n      }\r\n      \r\n      // Find the regularization form in the forms list\r\n      let regularizationForm = null;\r\n      \r\n      // Try to find form by different possible names\r\n      const possibleFormNames = [\r\n        'Regularization',\r\n        'regularization',\r\n        'Attendance Regularization',\r\n        'attendance regularization',\r\n        'AttendanceRegularization',\r\n        'attendance_regularization',\r\n        'RegularizationForm',\r\n        'regularization_form'\r\n      ];\r\n      \r\n      // First try to find in the loaded forms array\r\n      if (forms && Array.isArray(forms)) {\r\n        for (const formName of possibleFormNames) {\r\n          regularizationForm = forms.find(form => \r\n            form.name === formName || \r\n            form.formTitle === formName ||\r\n            (form.name && form.name.toLowerCase() === formName.toLowerCase()) ||\r\n            (form.formTitle && form.formTitle.toLowerCase() === formName.toLowerCase())\r\n          );\r\n          console.log(`🔍 Searching in loaded forms for \"${formName}\":`, regularizationForm ? 'Found' : 'Not found');\r\n          if (regularizationForm) break;\r\n        }\r\n      }\r\n      \r\n      // If not found in loaded forms, try API calls\r\n      if (!regularizationForm) {\r\n        console.log('🌐 Form not found locally, trying API calls...');\r\n        for (const formName of possibleFormNames) {\r\n          try {\r\n            regularizationForm = await getFormByName(formName);\r\n            console.log(`🔍 API search for \"${formName}\":`, regularizationForm ? 'Found' : 'Not found');\r\n            if (regularizationForm) break;\r\n          } catch (error) {\r\n            console.log(`❌ API error for \"${formName}\":`, error.message);\r\n          }\r\n        }\r\n      }\r\n      \r\n      if (!regularizationForm && forms && Array.isArray(forms)) {\r\n        // If no form found by name, try to find by checking form fields\r\n        regularizationForm = forms.find(form => {\r\n          if (!form || !form.fields || !Array.isArray(form.fields)) return false;\r\n          const fieldNames = form.fields.map(field => field.name.toLowerCase());\r\n          return fieldNames.includes('date') && \r\n                 (fieldNames.includes('intime') || fieldNames.includes('reason'));\r\n        });\r\n      }\r\n      \r\n      if (!regularizationForm) {\r\n        console.log('❌ No regularization form found');\r\n        console.log('📋 Available form names:', forms ? forms.map(f => f.formTitle || f.name || f._id) : 'No forms');\r\n        addAssistantMessage('❌ Regularization form not found. Please contact your administrator.');\r\n        return;\r\n      }\r\n      \r\n      console.log('✅ Found regularization form:', regularizationForm.formTitle || regularizationForm.name);\r\n      console.log('📋 Form fields:', regularizationForm.fields ? regularizationForm.fields.map(f => f.name) : 'No fields');\r\n      \r\n      // Prepare pre-filled data for the form\r\n      // Use actual time if available (not 00:00), otherwise use scheduled time\r\n      const actualInTime = attendanceRecord.actualInTime !== '00:00' ? attendanceRecord.actualInTime : attendanceRecord.inTime;\r\n      const actualOutTime = attendanceRecord.actualOutTime !== '00:00' ? attendanceRecord.actualOutTime : attendanceRecord.outTime;\r\n      \r\n      const prefillData = {\r\n        date: attendanceRecord.attendanceDate || attendanceRecord.date,\r\n        inTime: actualInTime || attendanceRecord.inTime || attendanceRecord.checkIn,\r\n        outTime: actualOutTime || attendanceRecord.outTime || attendanceRecord.checkOut,\r\n        appliedTo: localStorage.getItem('supervisorName'),\r\n        // Add any other common fields that might match\r\n        attendanceDate: attendanceRecord.attendanceDate || attendanceRecord.date,\r\n        checkIn: actualInTime || attendanceRecord.inTime || attendanceRecord.checkIn,\r\n        checkOut: actualOutTime || attendanceRecord.outTime || attendanceRecord.checkOut\r\n      };\r\n      \r\n      console.log('Pre-fill data prepared:', prefillData);\r\n      \r\n      // Create a copy of the form with prefilled data\r\n      const formWithPrefillData = {\r\n        ...regularizationForm,\r\n        prefillData: prefillData\r\n      };\r\n      \r\n      // Show the form with pre-filled data using ChatContext\r\n      submitForm(regularizationForm._id, {}, true, formWithPrefillData);\r\n      \r\n      // Add a message to indicate the form is being prepared\r\n      addAssistantMessage(`📝 Preparing regularization form for ${attendanceRecord.attendanceDate || attendanceRecord.date}. Please fill in the required details.`);\r\n      \r\n    } catch (error) {\r\n      console.error('Error handling regularization apply:', error);\r\n      addAssistantMessage('❌ Error opening regularization form. Please try again.');\r\n    }\r\n  };\r\n  \r\n  // Show login form when user is not logged in - with proper dependency tracking\r\n  useEffect(() => {\r\n    // Only set the login form as active if:\r\n    // 1. User is not logged in\r\n    // 2. We have the login form\r\n    // 3. Either there's no active form or the active form is not the login form\r\n    if (!isLoggedIn() && loginForm && (!activeForm || activeForm.name !== 'Login')) {\r\n      // Set the login form as the active form, passing the form object directly to avoid an extra API call\r\n      submitForm(loginForm._id, null, true, loginForm);\r\n    }\r\n  }, [isLoggedIn, loginForm, activeForm, submitForm]); // Include all dependencies\r\n  \r\n  return (\r\n    <div className=\"flex flex-col h-full bg-white rounded-lg shadow-lg overflow-hidden\">\r\n      <div className=\"bg-gradient-to-r from-blue-600 to-blue-800 p-4 text-white flex justify-between items-center\">\r\n        <h2 className=\"text-xl font-bold\">AI Form Assistant</h2>\r\n        {isLoggedIn() && (\r\n          <div className=\"flex items-center\">\r\n            <span className=\"text-sm mr-3\">\r\n              Logged in as {user.firstName || user.username || user.empId}\r\n            </span>\r\n            <button \r\n              onClick={handleLogout}\r\n              className=\"bg-blue-700 hover:bg-blue-800 text-white text-sm py-1 px-3 rounded\"\r\n            >\r\n              Logout\r\n            </button>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      <div className=\"flex-1 overflow-y-auto p-4\">\r\n        {!isLoggedIn() ? (\r\n          <div className=\"flex items-center justify-center h-full\">\r\n            {loadingLoginForm ? (\r\n              <div className=\"text-center\">\r\n                <TypingIndicator />\r\n                <p className=\"mt-2 text-gray-600\">Loading login form...</p>\r\n              </div>\r\n            ) : (\r\n              <>\r\n                {activeForm && activeForm.name === 'Login' ? (\r\n                  <div className=\"w-full max-w-md\">\r\n                    <h2 className=\"text-2xl font-bold text-gray-800 mb-6 text-center\">Login to Chat</h2>\r\n                    <ChatFormDisplay \r\n                      form={activeForm} \r\n                      onSubmit={handleFormSubmit} \r\n                      submitButtonText=\"Login\"\r\n                    />\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"text-center\">\r\n                    <p className=\"text-gray-600\">Login form not found. Please try again later.</p>\r\n                  </div>\r\n                )}\r\n              </>\r\n            )}\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {messages.map((message, index) => (\r\n              <div key={message.messageId || `message-${index}-${message.role}-${message.timestamp || index}`}>\r\n                <ChatMessage \r\n                  message={message} \r\n                  onOptionSelect={handleOptionSelect} \r\n                  onFormLinkTriggered={handleFormLinkTriggered}\r\n                />\r\n                \r\n\r\n                \r\n                {/* Show legacy response indicator */}\r\n                {message.isLegacyResponse && (\r\n                  <div className=\"mt-2 p-2 bg-orange-50 border border-orange-200 rounded\">\r\n                    <div className=\"flex items-center text-orange-700 text-xs\">\r\n                      <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n                      </svg>\r\n                      Legacy Response (will be replaced by dynamic APIs)\r\n                    </div>\r\n                  </div>\r\n                )}\r\n                \r\n                {/* Show attendance regularization display if this message contains attendance info */}\r\n                {message.isRegularizationData && message.attendanceInfo && (\r\n                  <div className=\"mt-2\">\r\n                    {console.log('🎨 Rendering AttendanceRegularizationDisplay with:', message.attendanceInfo)}\r\n                    <AttendanceRegularizationDisplay \r\n                      attendanceData={message.attendanceInfo}\r\n                      onApplyClick={handleRegularizationApply}\r\n                    />\r\n                  </div>\r\n                )}\r\n                {message.isRegularizationData && !message.attendanceInfo && (\r\n                  <div className=\"mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded\">\r\n                    {console.log('⚠️ Regularization data found but no attendanceInfo:', message)}\r\n                    <p className=\"text-yellow-700\">Regularization data received but no attendance info available.</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            ))}\r\n            \r\n            {/* Show typing indicator when loading */}\r\n            {loading && <TypingIndicator />}\r\n            \r\n            {/* Display active form if available and not the login form */}\r\n            {activeForm && activeForm.name !== 'Login' && (\r\n              <div className=\"my-4\">\r\n                <ChatFormDisplay \r\n                  form={activeForm} \r\n                  onSubmit={handleFormSubmit} \r\n                  onCancel={handleFormCancel}\r\n                />\r\n              </div>\r\n            )}\r\n\r\n            {/* Display hybrid form if transitioning to form phase */}\r\n            {hybridFlow && hybridFlow.transitionToForm && (\r\n              <div className=\"my-4\">\r\n                <HybridForm \r\n                  hybridFlow={hybridFlow}\r\n                  message={{ content: hybridFlow.completionMessage }}\r\n                  formId={hybridFlow.formId}\r\n                  onCancel={() => {\r\n                    // Cancel hybrid form\r\n                    sendMessage('cancel');\r\n                  }}\r\n                  onSubmit={(response) => {\r\n                    // Handle form submission response\r\n                    console.log('🔄 ChatInterface: Handling hybrid form submission response:', response);\r\n                    \r\n                    // Add the response message to chat\r\n                    if (response.message) {\r\n                      console.log('➕ Adding message to chat:', response.message);\r\n                      addAssistantMessage(response.message);\r\n                    }\r\n                    \r\n                    // Check if we're continuing to a conversational phase\r\n                    if (response.isConversationalPhase && response.hybridFlow) {\r\n                      console.log('🔄 Continuing to next conversational phase');\r\n                      setHybridFlow({\r\n                        ...response.hybridFlow,\r\n                        transitionToForm: false,\r\n                        isConversationalPhase: true\r\n                      });\r\n                    } else {\r\n                      // Form completed or other state\r\n                      console.log('✅ Hybrid form completed');\r\n                      setHybridFlow(null);\r\n                    }\r\n                  }}\r\n                />\r\n              </div>\r\n            )}\r\n            \r\n            <div ref={messagesEndRef} />\r\n          </>\r\n        )}\r\n      </div>\r\n      \r\n      {isLoggedIn() && (\r\n        <ChatInput \r\n          onSendMessage={processMessage} \r\n          loading={loading}\r\n          conversationalFlow={conversationalFlow}\r\n          hybridFlow={hybridFlow}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatInterface;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAOC,GAAG,MAAM,cAAc;AAC9B,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,UAAU,MAAM,cAAc;AAErC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,+BAA+B,MAAM,mCAAmC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEhF,MAAMC,aAAa,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC1B,MAAM;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAc,CAAC,GAAGlB,OAAO,CAAC,CAAC;EACpD,MAAM;IACJmB,QAAQ;IACRC,OAAO;IACPC,UAAU;IACVC,kBAAkB;IAClBC,UAAU;IACVC,cAAc;IACdC,WAAW;IACXC,mBAAmB;IACnBC,UAAU;IACVC,WAAW;IACXC,SAAS;IACTC,aAAa;IACbC,WAAW;IACXC,iBAAiB;IACjBC,qBAAqB;IACrBC,aAAa;IACbC;EACF,CAAC,GAAGlC,OAAO,CAAC,CAAC;EACb,MAAM;IAAEmC,IAAI;IAAEC,KAAK;IAAEC,UAAU;IAAEC;EAAO,CAAC,GAAGrC,OAAO,CAAC,CAAC;EAErD,MAAM,CAACsC,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAAC6C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC+C,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMiD,cAAc,GAAG/C,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAD,SAAS,CAAC,MAAM;IACd,MAAMiD,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC;MACA,IAAI,CAACP,SAAS,EAAE;QACd,IAAI;UACFG,mBAAmB,CAAC,IAAI,CAAC;UACzB,MAAMK,IAAI,GAAG,MAAM9B,aAAa,CAAC,OAAO,CAAC;UACzCuB,YAAY,CAACO,IAAI,CAAC;QACpB,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD,CAAC,SAAS;UACRN,mBAAmB,CAAC,KAAK,CAAC;QAC5B;MACF;IACF,CAAC;;IAED;IACA,IAAI,CAACL,UAAU,CAAC,CAAC,IAAI,CAACE,SAAS,EAAE;MAC/BO,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACT,UAAU,EAAEE,SAAS,EAAEtB,aAAa,CAAC,CAAC;;EAE1C;EACApB,SAAS,CAAC,MAAM;IACd,IAAIwC,UAAU,CAAC,CAAC,EAAE;MAChBrB,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACA,QAAQ,EAAEqB,UAAU,CAAC,CAAC;;EAE1B;EACAxC,SAAS,CAAC,MAAM;IACd,IAAIwC,UAAU,CAAC,CAAC,IAAInB,QAAQ,CAACgC,MAAM,KAAK,CAAC,EAAE;MACzC;MACA,MAAMC,WAAW,GAAGhB,IAAI,CAACiB,SAAS,IAAIjB,IAAI,CAACkB,QAAQ,IAAIlB,IAAI,CAACmB,KAAK;MACjE7B,mBAAmB,CAAC,OAAO0B,WAAW,6BAA6B,CAAC;IACtE;EACF,CAAC,EAAE,CAACd,UAAU,EAAEnB,QAAQ,CAACgC,MAAM,EAAEzB,mBAAmB,EAAEU,IAAI,CAAC,CAAC;;EAE5D;EACAtC,SAAS,CAAC,MAAM;IAAA,IAAA0D,qBAAA;IACd,CAAAA,qBAAA,GAAAV,cAAc,CAACW,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACxC,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMyC,cAAc,GAAG,MAAOC,OAAO,IAAK;IACxC;IACA,MAAMpC,WAAW,CAACoC,OAAO,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAOC,MAAM,IAAK;IACzC;IACA,IAAI1C,UAAU,IAAIA,UAAU,CAAC2C,IAAI,KAAK,OAAO,EAAE;MAC7C,IAAI;QACF;QACA,MAAMC,QAAQ,GAAG,MAAM9D,GAAG,CAAC+D,IAAI,CAAC,aAAa,EAAE;UAC7CX,KAAK,EAAEQ,MAAM,CAACI,QAAQ,CAACZ,KAAK;UAC5Ba,QAAQ,EAAEL,MAAM,CAACI,QAAQ,CAACC;QAC5B,CAAC,CAAC;;QAEF;QACA,IAAIH,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,KAAK,IAAI,IAAIL,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAE;UACzE,MAAME,QAAQ,GAAGN,QAAQ,CAACI,IAAI,CAACA,IAAI;;UAEnC;UACA,IAAIE,QAAQ,CAACC,QAAQ,EAAE;YACrBC,YAAY,CAACC,OAAO,CAAC,UAAU,EAAEH,QAAQ,CAACC,QAAQ,CAAC;UACrD;;UAEA;UACA,MAAMnC,KAAK,CAACkC,QAAQ,CAAC;;UAErB;UACA1C,SAAS,CAAC,CAAC;UACX;;UAEA;UACAH,mBAAmB,CAAC,OAAO6C,QAAQ,CAAClB,SAAS,6BAA6B,CAAC;QAC7E,CAAC,MAAM;UACL;UACA3B,mBAAmB,CAAC,4DAA4D,CAAC;QACnF;MACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;;QAEpC;QACA,IAAIA,KAAK,CAACgB,QAAQ,IAAIhB,KAAK,CAACgB,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;UACnDjD,mBAAmB,CAAC,oDAAoD,CAAC;QAC3E,CAAC,MAAM,IAAIuB,KAAK,CAACgB,QAAQ,IAAIhB,KAAK,CAACgB,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;UAC1DjD,mBAAmB,CAAC,mEAAmE,CAAC;QAC1F,CAAC,MAAM;UACLA,mBAAmB,CAAC,uCAAuC,CAAC;QAC9D;MACF;IACF,CAAC,MAAM;MACL;MACAC,UAAU,CAACN,UAAU,CAACuD,GAAG,EAAEb,MAAM,CAACI,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAEJ,MAAM,CAACc,WAAW,EAAEd,MAAM,CAACO,OAAO,CAAC;IAC9F;EACF,CAAC;;EAED;EACA,MAAMQ,gBAAgB,GAAGA,CAAA,KAAM;IAC7B;IACA;IACAlD,WAAW,CAAC,CAAC;EACf,CAAC;;EAED;EACA,MAAMmD,kBAAkB,GAAIC,MAAM,IAAK;IACrC;IACApB,cAAc,CAACoB,MAAM,CAAC;EACxB,CAAC;;EAED;EACA,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB1C,MAAM,CAAC,CAAC;IACRV,SAAS,CAAC,CAAC;IACXM,sBAAsB,CAAC,CAAC,CAAC,CAAC;EAC5B,CAAC;;EAED;EACA,MAAM+C,uBAAuB,GAAG,MAAOC,QAAQ,IAAK;IAClD,IAAI;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MACF;MACA,MAAMC,YAAY,IAAAF,qBAAA,GAAGD,QAAQ,CAACI,UAAU,CAACC,UAAU,cAAAJ,qBAAA,wBAAAC,sBAAA,GAA9BD,qBAAA,CAAgC7D,UAAU,cAAA8D,sBAAA,uBAA1CA,sBAAA,CAA4CI,OAAO;MAExE,IAAIH,YAAY,EAAE;QAAA,IAAAI,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,qBAAA;QAChBnD,OAAO,CAACoD,GAAG,CAAC,oEAAoE,EAAEnB,QAAQ,CAACI,UAAU,CAACvB,IAAI,CAAC;;QAE3G;QACAS,YAAY,CAACC,OAAO,CAAC,iBAAiB,EAAE6B,IAAI,CAACC,SAAS,CAACrB,QAAQ,CAACsB,WAAW,CAAC,CAAC;;QAE7E;QACA;QACA,MAAMlC,QAAQ,GAAGgC,IAAI,CAACG,KAAK,CAACjC,YAAY,CAACkC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;QAEjE,MAAM1C,QAAQ,GAAG,MAAM9D,GAAG,CAAC+D,IAAI,CAAC,OAAO,EAAE;UACvCL,OAAO,EAAE,wBAAwBsB,QAAQ,CAACI,UAAU,CAACvB,IAAI,EAAE;UAAE;UAC7DxC,cAAc;UACd+B,KAAK,EAAEgB,QAAQ,CAAChB,KAAK;UACrBiB,QAAQ,EAAED,QAAQ,CAACC,QAAQ;UAC3BoC,cAAc,EAAErC,QAAQ,CAACqC,cAAc;UACvCC,YAAY,EAAEtC,QAAQ,CAACsC,YAAY;UACnCC,mBAAmB,EAAE,IAAI,CAAC;QAC5B,CAAC,EAAE;UACDC,OAAO,EAAE;YACP,UAAU,EAAExC,QAAQ,CAAChB,KAAK;YAC1B,aAAa,EAAEgB,QAAQ,CAACC,QAAQ;YAChC,eAAe,EAAED,QAAQ,CAACyC,KAAK,GAAG,UAAUzC,QAAQ,CAACyC,KAAK,EAAE,GAAGC;UACjE;QACF,CAAC,CAAC;;QAEF;QACA;QACA,MAAM9C,QAAQ,GAAGF,QAAQ,CAACI,IAAI,CAACF,QAAQ,KAAKF,QAAQ,CAACI,IAAI,CAAC9C,UAAU,GAAG;UACrEqD,GAAG,EAAEX,QAAQ,CAACI,IAAI,CAAC9C,UAAU,CAAC2F,MAAM;UACpClD,IAAI,EAAEC,QAAQ,CAACI,IAAI,CAAC9C,UAAU,CAAC4F,QAAQ;UACvC3B,UAAU,EAAEvB,QAAQ,CAACI,IAAI,CAACmB;QAC5B,CAAC,GAAG,IAAI,CAAC;QAET,MAAM4B,gBAAgB,GAAG;UACvBC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAErD,QAAQ,CAACI,IAAI,CAACR,OAAO;UAC9BM,QAAQ,EAAEA,QAAQ;UAClBqB,UAAU,EAAEvB,QAAQ,CAACI,IAAI,CAACmB,UAAU;UACpC+B,WAAW,EAAEtD,QAAQ,CAACI,IAAI,CAACkD,WAAW;UACtC1C,WAAW,EAAEZ,QAAQ,CAACI,IAAI,CAACQ,WAAW;UACtC2C,YAAY,EAAEvD,QAAQ,CAACI,IAAI,CAACmD,YAAY;UACxClG,kBAAkB,EAAE2C,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB;UACpDmG,SAAS,EAAE,EAAA/B,qBAAA,GAAAzB,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,cAAAoE,qBAAA,uBAAhCA,qBAAA,CAAkC+B,SAAS,OAAA9B,qBAAA,GAAI1B,QAAQ,CAACI,IAAI,CAAC9C,UAAU,cAAAoE,qBAAA,uBAAxBA,qBAAA,CAA0B8B,SAAS,KAAIxD,QAAQ,CAACI,IAAI,CAACoD,SAAS;UACxHC,OAAO,EAAE,EAAA9B,sBAAA,GAAA3B,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,cAAAsE,sBAAA,uBAAhCA,sBAAA,CAAkC8B,OAAO,OAAA7B,sBAAA,GAAI5B,QAAQ,CAACI,IAAI,CAAC9C,UAAU,cAAAsE,sBAAA,uBAAxBA,sBAAA,CAA0B6B,OAAO,KAAIzD,QAAQ,CAACI,IAAI,CAACqD,OAAO;UAChHC,UAAU,EAAE,EAAA7B,sBAAA,GAAA7B,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,cAAAwE,sBAAA,uBAAhCA,sBAAA,CAAkC8B,QAAQ,KAAI3D,QAAQ,CAACI,IAAI,CAACsD,UAAU;UAClFE,SAAS,EAAE,EAAA9B,sBAAA,GAAA9B,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,cAAAyE,sBAAA,uBAAhCA,sBAAA,CAAkC8B,SAAS,OAAA7B,sBAAA,GAAI/B,QAAQ,CAACI,IAAI,CAAC9C,UAAU,cAAAyE,sBAAA,uBAAxBA,sBAAA,CAA0B6B,SAAS,KAAI5D,QAAQ,CAACI,IAAI,CAACwD,SAAS;UACxHC,WAAW,EAAE,EAAA7B,sBAAA,GAAAhC,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,cAAA2E,sBAAA,uBAAhCA,sBAAA,CAAkC6B,WAAW,OAAA5B,sBAAA,GAAIjC,QAAQ,CAACI,IAAI,CAAC9C,UAAU,cAAA2E,sBAAA,uBAAxBA,sBAAA,CAA0B4B,WAAW,KAAI7D,QAAQ,CAACI,IAAI,CAACyD,WAAW;UAChIC,UAAU,EAAE,EAAA5B,sBAAA,GAAAlC,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,cAAA6E,sBAAA,uBAAhCA,sBAAA,CAAkC4B,UAAU,OAAA3B,sBAAA,GAAInC,QAAQ,CAACI,IAAI,CAAC9C,UAAU,cAAA6E,sBAAA,uBAAxBA,sBAAA,CAA0B4B,wBAAwB,KAAI/D,QAAQ,CAACI,IAAI,CAAC0D,UAAU;UAC1IxG,UAAU,EAAE0C,QAAQ,CAACI,IAAI,CAAC9C,UAAU;UACpC+D,YAAY,EAAErB,QAAQ,CAACI,IAAI,CAACiB,YAAY;UACxC2C,qBAAqB,EAAEhE,QAAQ,CAACI,IAAI,CAAC4D,qBAAqB;UAC1DC,gBAAgB,EAAEjE,QAAQ,CAACI,IAAI,CAAC6D,gBAAgB;UAChDC,UAAU,EAAElE,QAAQ,CAACI,IAAI,CAAC8D,UAAU;UACpCC,aAAa,EAAEnE,QAAQ,CAACI,IAAI,CAAC+D,aAAa;UAC1CC,kBAAkB,EAAEpE,QAAQ,CAACI,IAAI,CAACgE,kBAAkB;UACpDC,iBAAiB,EAAE,IAAI;UACvBC,mBAAmB,EAAE,IAAI;UAAE;UAC3BC,SAAS,EAAE,aAAaC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAAE;UACjFC,SAAS,EAAE,EAAA1C,qBAAA,GAAApC,QAAQ,CAACI,IAAI,CAACQ,WAAW,cAAAwB,qBAAA,uBAAzBA,qBAAA,CAA2B0C,SAAS,KAAIN,IAAI,CAACC,GAAG,CAAC;QAC9D,CAAC;;QAED;QACA,IAAIzE,QAAQ,CAACI,IAAI,CAAC7C,cAAc,IAAI,CAACA,cAAc,EAAE;UACnDQ,iBAAiB,CAACiC,QAAQ,CAACI,IAAI,CAAC7C,cAAc,CAAC;QACjD;;QAEA;QACA,IAAIyC,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,IAAI2C,QAAQ,CAACI,IAAI,CAACkD,WAAW,KAAK,mBAAmB,EAAE;UACzFtF,qBAAqB,CAACgC,QAAQ,CAACI,IAAI,CAAC/C,kBAAkB,CAAC;UACvDY,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,MAAM,IAAI+B,QAAQ,CAACI,IAAI,CAACkD,WAAW,KAAK,0BAA0B,EAAE;UACnEzF,aAAa,CAACmC,QAAQ,CAACI,IAAI,CAAC9C,UAAU,CAAC;UACvCU,qBAAqB,CAAC,IAAI,CAAC;UAC3BC,aAAa,CAAC,IAAI,CAAC;QACrB,CAAC,MAAM,IAAI+B,QAAQ,CAACI,IAAI,CAACkD,WAAW,KAAK,wBAAwB,EAAE;UACjE;UACAzF,aAAa,CAACmC,QAAQ,CAACI,IAAI,CAAC9C,UAAU,CAAC;UACvCU,qBAAqB,CAAC,IAAI,CAAC;UAC3BC,aAAa,CAAC,IAAI,CAAC;QACrB;;QAEA;QACA;QACAH,WAAW,CAAEiH,YAAY,IAAK;UAC5B;UACA,MAAMC,WAAW,GAAGD,YAAY,CAACE,IAAI,CAACC,GAAG;YAAA,IAAAC,aAAA,EAAAC,qBAAA;YAAA,OACvCF,GAAG,CAAC9B,IAAI,KAAK,WAAW;YACtB;YACC8B,GAAG,CAACX,SAAS,KAAKpB,gBAAgB,CAACoB,SAAS;YAC7C;YACCW,GAAG,CAACZ,mBAAmB,IAAInB,gBAAgB,CAACmB,mBAAmB,IAC/D,EAAAa,aAAA,GAAAD,GAAG,CAAChF,QAAQ,cAAAiF,aAAA,uBAAZA,aAAA,CAAcxE,GAAG,QAAAyE,qBAAA,GAAKjC,gBAAgB,CAACjD,QAAQ,cAAAkF,qBAAA,uBAAzBA,qBAAA,CAA2BzE,GAAG,KACpDuE,GAAG,CAACjB,gBAAgB,KAAKd,gBAAgB,CAACc,gBAAgB,IAC1DS,IAAI,CAACW,GAAG,CAAC,CAACH,GAAG,CAACJ,SAAS,IAAI,CAAC,KAAK3B,gBAAgB,CAAC2B,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK;YAC3E;YACC,CAACI,GAAG,CAACZ,mBAAmB,IAAI,CAACnB,gBAAgB,CAACmB,mBAAmB,IACjEY,GAAG,CAAC7B,OAAO,KAAKF,gBAAgB,CAACE,OAAO,IACxCf,IAAI,CAACC,SAAS,CAAC2C,GAAG,CAACzB,OAAO,CAAC,KAAKnB,IAAI,CAACC,SAAS,CAACY,gBAAgB,CAACM,OAAO,CAAC,IACxEiB,IAAI,CAACW,GAAG,CAAC,CAACH,GAAG,CAACJ,SAAS,IAAI,CAAC,KAAK3B,gBAAgB,CAAC2B,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK,CAC5E;UAAA,CACH,CAAC;UAED,IAAIE,WAAW,EAAE;YACf,OAAOD,YAAY;UACrB;UAEA,OAAO,CAAC,GAAGA,YAAY,EAAE5B,gBAAgB,CAAC;QAC5C,CAAC,CAAC;;QAEF;QACA3C,YAAY,CAAC8E,UAAU,CAAC,iBAAiB,CAAC;MAE5C,CAAC,MAAM;QAEL;QACA;QACA,MAAMC,mBAAmB,GAAG;UAC1B,GAAGrE,QAAQ,CAACI,UAAU;UACtBkB,WAAW,EAAEtB,QAAQ,CAACsB;QACxB,CAAC;;QAED;QACA9E,UAAU,CAACwD,QAAQ,CAACI,UAAU,CAACX,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE4E,mBAAmB,CAAC;MACpE;IAEF,CAAC,CAAC,OAAOvG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDvB,mBAAmB,CAAC,gDAAgD,CAAC;IACvE;EACF,CAAC;;EAED;EACA,MAAM+H,yBAAyB,GAAG,MAAAA,CAAOC,gBAAgB,EAAEC,KAAK,KAAK;IACnE,IAAI;MAGF;MACA,IAAI,CAAC3I,KAAK,IAAIA,KAAK,CAACmC,MAAM,KAAK,CAAC,EAAE;QAChCD,OAAO,CAACoD,GAAG,CAAC,gDAAgD,CAAC;QAC7D,IAAI;UACF,MAAMsD,YAAY,GAAG,MAAM3I,QAAQ,CAAC,CAAC,CAAC,CAAC;UACvCiC,OAAO,CAACoD,GAAG,CAAC,mBAAmB,EAAEsD,YAAY,CAAC;QAChD,CAAC,CAAC,OAAO3G,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;UAChDvB,mBAAmB,CAAC,yEAAyE,CAAC;UAC9F;QACF;MACF;;MAEA;MACA,IAAImI,kBAAkB,GAAG,IAAI;;MAE7B;MACA,MAAMC,iBAAiB,GAAG,CACxB,gBAAgB,EAChB,gBAAgB,EAChB,2BAA2B,EAC3B,2BAA2B,EAC3B,0BAA0B,EAC1B,2BAA2B,EAC3B,oBAAoB,EACpB,qBAAqB,CACtB;;MAED;MACA,IAAI9I,KAAK,IAAI+I,KAAK,CAACC,OAAO,CAAChJ,KAAK,CAAC,EAAE;QACjC,KAAK,MAAMmG,QAAQ,IAAI2C,iBAAiB,EAAE;UACxCD,kBAAkB,GAAG7I,KAAK,CAACiJ,IAAI,CAACjH,IAAI,IAClCA,IAAI,CAACgB,IAAI,KAAKmD,QAAQ,IACtBnE,IAAI,CAACkH,SAAS,KAAK/C,QAAQ,IAC1BnE,IAAI,CAACgB,IAAI,IAAIhB,IAAI,CAACgB,IAAI,CAACmG,WAAW,CAAC,CAAC,KAAKhD,QAAQ,CAACgD,WAAW,CAAC,CAAE,IAChEnH,IAAI,CAACkH,SAAS,IAAIlH,IAAI,CAACkH,SAAS,CAACC,WAAW,CAAC,CAAC,KAAKhD,QAAQ,CAACgD,WAAW,CAAC,CAC3E,CAAC;UACDjH,OAAO,CAACoD,GAAG,CAAC,qCAAqCa,QAAQ,IAAI,EAAE0C,kBAAkB,GAAG,OAAO,GAAG,WAAW,CAAC;UAC1G,IAAIA,kBAAkB,EAAE;QAC1B;MACF;;MAEA;MACA,IAAI,CAACA,kBAAkB,EAAE;QACvB3G,OAAO,CAACoD,GAAG,CAAC,gDAAgD,CAAC;QAC7D,KAAK,MAAMa,QAAQ,IAAI2C,iBAAiB,EAAE;UACxC,IAAI;YACFD,kBAAkB,GAAG,MAAM3I,aAAa,CAACiG,QAAQ,CAAC;YAClDjE,OAAO,CAACoD,GAAG,CAAC,sBAAsBa,QAAQ,IAAI,EAAE0C,kBAAkB,GAAG,OAAO,GAAG,WAAW,CAAC;YAC3F,IAAIA,kBAAkB,EAAE;UAC1B,CAAC,CAAC,OAAO5G,KAAK,EAAE;YACdC,OAAO,CAACoD,GAAG,CAAC,oBAAoBa,QAAQ,IAAI,EAAElE,KAAK,CAACY,OAAO,CAAC;UAC9D;QACF;MACF;MAEA,IAAI,CAACgG,kBAAkB,IAAI7I,KAAK,IAAI+I,KAAK,CAACC,OAAO,CAAChJ,KAAK,CAAC,EAAE;QACxD;QACA6I,kBAAkB,GAAG7I,KAAK,CAACiJ,IAAI,CAACjH,IAAI,IAAI;UACtC,IAAI,CAACA,IAAI,IAAI,CAACA,IAAI,CAACoH,MAAM,IAAI,CAACL,KAAK,CAACC,OAAO,CAAChH,IAAI,CAACoH,MAAM,CAAC,EAAE,OAAO,KAAK;UACtE,MAAMC,UAAU,GAAGrH,IAAI,CAACoH,MAAM,CAACE,GAAG,CAACC,KAAK,IAAIA,KAAK,CAACvG,IAAI,CAACmG,WAAW,CAAC,CAAC,CAAC;UACrE,OAAOE,UAAU,CAACG,QAAQ,CAAC,MAAM,CAAC,KAC1BH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,IAAIH,UAAU,CAACG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACzE,CAAC,CAAC;MACJ;MAEA,IAAI,CAACX,kBAAkB,EAAE;QACvB3G,OAAO,CAACoD,GAAG,CAAC,gCAAgC,CAAC;QAC7CpD,OAAO,CAACoD,GAAG,CAAC,0BAA0B,EAAEtF,KAAK,GAAGA,KAAK,CAACsJ,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACP,SAAS,IAAIO,CAAC,CAACzG,IAAI,IAAIyG,CAAC,CAAC7F,GAAG,CAAC,GAAG,UAAU,CAAC;QAC5GlD,mBAAmB,CAAC,qEAAqE,CAAC;QAC1F;MACF;MAEAwB,OAAO,CAACoD,GAAG,CAAC,8BAA8B,EAAEuD,kBAAkB,CAACK,SAAS,IAAIL,kBAAkB,CAAC7F,IAAI,CAAC;MACpGd,OAAO,CAACoD,GAAG,CAAC,iBAAiB,EAAEuD,kBAAkB,CAACO,MAAM,GAAGP,kBAAkB,CAACO,MAAM,CAACE,GAAG,CAACG,CAAC,IAAIA,CAAC,CAACzG,IAAI,CAAC,GAAG,WAAW,CAAC;;MAEpH;MACA;MACA,MAAM0G,YAAY,GAAGhB,gBAAgB,CAACgB,YAAY,KAAK,OAAO,GAAGhB,gBAAgB,CAACgB,YAAY,GAAGhB,gBAAgB,CAACiB,MAAM;MACxH,MAAMC,aAAa,GAAGlB,gBAAgB,CAACkB,aAAa,KAAK,OAAO,GAAGlB,gBAAgB,CAACkB,aAAa,GAAGlB,gBAAgB,CAACmB,OAAO;MAE5H,MAAMpE,WAAW,GAAG;QAClBqE,IAAI,EAAEpB,gBAAgB,CAACqB,cAAc,IAAIrB,gBAAgB,CAACoB,IAAI;QAC9DH,MAAM,EAAED,YAAY,IAAIhB,gBAAgB,CAACiB,MAAM,IAAIjB,gBAAgB,CAACsB,OAAO;QAC3EH,OAAO,EAAED,aAAa,IAAIlB,gBAAgB,CAACmB,OAAO,IAAInB,gBAAgB,CAACuB,QAAQ;QAC/EC,SAAS,EAAEzG,YAAY,CAACkC,OAAO,CAAC,gBAAgB,CAAC;QACjD;QACAoE,cAAc,EAAErB,gBAAgB,CAACqB,cAAc,IAAIrB,gBAAgB,CAACoB,IAAI;QACxEE,OAAO,EAAEN,YAAY,IAAIhB,gBAAgB,CAACiB,MAAM,IAAIjB,gBAAgB,CAACsB,OAAO;QAC5EC,QAAQ,EAAEL,aAAa,IAAIlB,gBAAgB,CAACmB,OAAO,IAAInB,gBAAgB,CAACuB;MAC1E,CAAC;MAED/H,OAAO,CAACoD,GAAG,CAAC,yBAAyB,EAAEG,WAAW,CAAC;;MAEnD;MACA,MAAM+C,mBAAmB,GAAG;QAC1B,GAAGK,kBAAkB;QACrBpD,WAAW,EAAEA;MACf,CAAC;;MAED;MACA9E,UAAU,CAACkI,kBAAkB,CAACjF,GAAG,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE4E,mBAAmB,CAAC;;MAEjE;MACA9H,mBAAmB,CAAC,wCAAwCgI,gBAAgB,CAACqB,cAAc,IAAIrB,gBAAgB,CAACoB,IAAI,wCAAwC,CAAC;IAE/J,CAAC,CAAC,OAAO7H,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DvB,mBAAmB,CAAC,wDAAwD,CAAC;IAC/E;EACF,CAAC;;EAED;EACA5B,SAAS,CAAC,MAAM;IACd;IACA;IACA;IACA;IACA,IAAI,CAACwC,UAAU,CAAC,CAAC,IAAIE,SAAS,KAAK,CAACnB,UAAU,IAAIA,UAAU,CAAC2C,IAAI,KAAK,OAAO,CAAC,EAAE;MAC9E;MACArC,UAAU,CAACa,SAAS,CAACoC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAEpC,SAAS,CAAC;IAClD;EACF,CAAC,EAAE,CAACF,UAAU,EAAEE,SAAS,EAAEnB,UAAU,EAAEM,UAAU,CAAC,CAAC,CAAC,CAAC;;EAErD,oBACEhB,OAAA;IAAKwK,SAAS,EAAC,oEAAoE;IAAAC,QAAA,gBACjFzK,OAAA;MAAKwK,SAAS,EAAC,6FAA6F;MAAAC,QAAA,gBAC1GzK,OAAA;QAAIwK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EACvDlJ,UAAU,CAAC,CAAC,iBACX3B,OAAA;QAAKwK,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzK,OAAA;UAAMwK,SAAS,EAAC,cAAc;UAAAC,QAAA,GAAC,eAChB,EAAChJ,IAAI,CAACiB,SAAS,IAAIjB,IAAI,CAACkB,QAAQ,IAAIlB,IAAI,CAACmB,KAAK;QAAA;UAAA8H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvD,CAAC,eACP7K,OAAA;UACE8K,OAAO,EAAExG,YAAa;UACtBkG,SAAS,EAAC,oEAAoE;UAAAC,QAAA,EAC/E;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN7K,OAAA;MAAKwK,SAAS,EAAC,4BAA4B;MAAAC,QAAA,EACxC,CAAC9I,UAAU,CAAC,CAAC,gBACZ3B,OAAA;QAAKwK,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EACrD1I,gBAAgB,gBACf/B,OAAA;UAAKwK,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BzK,OAAA,CAACH,eAAe;YAAA6K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnB7K,OAAA;YAAGwK,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAAqB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,gBAEN7K,OAAA,CAAAE,SAAA;UAAAuK,QAAA,EACG/J,UAAU,IAAIA,UAAU,CAAC2C,IAAI,KAAK,OAAO,gBACxCrD,OAAA;YAAKwK,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzK,OAAA;cAAIwK,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACpF7K,OAAA,CAACL,eAAe;cACd0C,IAAI,EAAE3B,UAAW;cACjBqK,QAAQ,EAAE5H,gBAAiB;cAC3B6H,gBAAgB,EAAC;YAAO;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,gBAEN7K,OAAA;YAAKwK,SAAS,EAAC,aAAa;YAAAC,QAAA,eAC1BzK,OAAA;cAAGwK,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3E;QACN,gBACD;MACH;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAEN7K,OAAA,CAAAE,SAAA;QAAAuK,QAAA,GACGjK,QAAQ,CAACmJ,GAAG,CAAC,CAACzG,OAAO,EAAE8F,KAAK,kBAC3BhJ,OAAA;UAAAyK,QAAA,gBACEzK,OAAA,CAACN,WAAW;YACVwD,OAAO,EAAEA,OAAQ;YACjB+H,cAAc,EAAE7G,kBAAmB;YACnC8G,mBAAmB,EAAE3G;UAAwB;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C,CAAC,EAKD3H,OAAO,CAACiI,gBAAgB,iBACvBnL,OAAA;YAAKwK,SAAS,EAAC,wDAAwD;YAAAC,QAAA,eACrEzK,OAAA;cAAKwK,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDzK,OAAA;gBAAKwK,SAAS,EAAC,cAAc;gBAACY,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAb,QAAA,eACjFzK,OAAA;kBAAMuL,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAyI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9M,CAAC,sDAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA3H,OAAO,CAACyI,oBAAoB,IAAIzI,OAAO,CAAC0I,cAAc,iBACrD5L,OAAA;YAAKwK,SAAS,EAAC,MAAM;YAAAC,QAAA,GAClBlI,OAAO,CAACoD,GAAG,CAAC,oDAAoD,EAAEzC,OAAO,CAAC0I,cAAc,CAAC,eAC1F5L,OAAA,CAACF,+BAA+B;cAC9B+L,cAAc,EAAE3I,OAAO,CAAC0I,cAAe;cACvCE,YAAY,EAAEhD;YAA0B;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,EACA3H,OAAO,CAACyI,oBAAoB,IAAI,CAACzI,OAAO,CAAC0I,cAAc,iBACtD5L,OAAA;YAAKwK,SAAS,EAAC,wDAAwD;YAAAC,QAAA,GACpElI,OAAO,CAACoD,GAAG,CAAC,qDAAqD,EAAEzC,OAAO,CAAC,eAC5ElD,OAAA;cAAGwK,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAA8D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F,CACN;QAAA,GApCO3H,OAAO,CAAC2E,SAAS,IAAI,WAAWmB,KAAK,IAAI9F,OAAO,CAACwD,IAAI,IAAIxD,OAAO,CAACkF,SAAS,IAAIY,KAAK,EAAE;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqC1F,CACN,CAAC,EAGDpK,OAAO,iBAAIT,OAAA,CAACH,eAAe;UAAA6K,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EAG9BnK,UAAU,IAAIA,UAAU,CAAC2C,IAAI,KAAK,OAAO,iBACxCrD,OAAA;UAAKwK,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBzK,OAAA,CAACL,eAAe;YACd0C,IAAI,EAAE3B,UAAW;YACjBqK,QAAQ,EAAE5H,gBAAiB;YAC3B4I,QAAQ,EAAE5H;UAAiB;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAGAjK,UAAU,IAAIA,UAAU,CAAC2G,gBAAgB,iBACxCvH,OAAA;UAAKwK,SAAS,EAAC,MAAM;UAAAC,QAAA,eACnBzK,OAAA,CAACJ,UAAU;YACTgB,UAAU,EAAEA,UAAW;YACvBsC,OAAO,EAAE;cAAEyD,OAAO,EAAE/F,UAAU,CAACoL;YAAkB,CAAE;YACnDzF,MAAM,EAAE3F,UAAU,CAAC2F,MAAO;YAC1BwF,QAAQ,EAAEA,CAAA,KAAM;cACd;cACAjL,WAAW,CAAC,QAAQ,CAAC;YACvB,CAAE;YACFiK,QAAQ,EAAGzH,QAAQ,IAAK;cACtB;cACAf,OAAO,CAACoD,GAAG,CAAC,6DAA6D,EAAErC,QAAQ,CAAC;;cAEpF;cACA,IAAIA,QAAQ,CAACJ,OAAO,EAAE;gBACpBX,OAAO,CAACoD,GAAG,CAAC,2BAA2B,EAAErC,QAAQ,CAACJ,OAAO,CAAC;gBAC1DnC,mBAAmB,CAACuC,QAAQ,CAACJ,OAAO,CAAC;cACvC;;cAEA;cACA,IAAII,QAAQ,CAACgE,qBAAqB,IAAIhE,QAAQ,CAAC1C,UAAU,EAAE;gBACzD2B,OAAO,CAACoD,GAAG,CAAC,4CAA4C,CAAC;gBACzDxE,aAAa,CAAC;kBACZ,GAAGmC,QAAQ,CAAC1C,UAAU;kBACtB2G,gBAAgB,EAAE,KAAK;kBACvBD,qBAAqB,EAAE;gBACzB,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL;gBACA/E,OAAO,CAACoD,GAAG,CAAC,yBAAyB,CAAC;gBACtCxE,aAAa,CAAC,IAAI,CAAC;cACrB;YACF;UAAE;YAAAuJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,eAED7K,OAAA;UAAKiM,GAAG,EAAE9J;QAAe;UAAAuI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA,eAC5B;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAELlJ,UAAU,CAAC,CAAC,iBACX3B,OAAA,CAACP,SAAS;MACRyM,aAAa,EAAEjJ,cAAe;MAC9BxC,OAAO,EAAEA,OAAQ;MACjBE,kBAAkB,EAAEA,kBAAmB;MACvCC,UAAU,EAAEA;IAAW;MAAA8J,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzK,EAAA,CA9jBID,aAAa;EAAA,QAC0Bd,OAAO,EAmB9CC,OAAO,EACiCC,OAAO;AAAA;AAAA4M,EAAA,GArB/ChM,aAAa;AAgkBnB,eAAeA,aAAa;AAAC,IAAAgM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}