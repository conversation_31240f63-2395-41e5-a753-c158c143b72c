{"ast": null, "code": "/**\n * Simple cache utility to prevent duplicate leave balance API calls\n */\n\nclass LeaveBalanceCache {\n  constructor() {\n    this.cache = new Map();\n    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n  }\n\n  // Generate cache key based on user and query\n  generateKey(userId, query) {\n    const normalizedQuery = (query === null || query === void 0 ? void 0 : query.toLowerCase().trim()) || 'leave_balance';\n    return `${userId}_${normalizedQuery}`;\n  }\n\n  // Check if we have cached data for this query\n  has(userId, query) {\n    const key = this.generateKey(userId, query);\n    const cached = this.cache.get(key);\n    if (!cached) return false;\n\n    // Check if cache is still valid\n    const now = Date.now();\n    if (now - cached.timestamp > this.CACHE_DURATION) {\n      this.cache.delete(key);\n      return false;\n    }\n    return true;\n  }\n\n  // Get cached data\n  get(userId, query) {\n    const key = this.generateKey(userId, query);\n    const cached = this.cache.get(key);\n    if (!cached) return null;\n\n    // Check if cache is still valid\n    const now = Date.now();\n    if (now - cached.timestamp > this.CACHE_DURATION) {\n      this.cache.delete(key);\n      return null;\n    }\n    return cached.data;\n  }\n\n  // Set cached data\n  set(userId, query, data) {\n    const key = this.generateKey(userId, query);\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now()\n    });\n  }\n\n  // Clear cache for a specific user\n  clearUser(userId) {\n    for (const [key] of this.cache) {\n      if (key.startsWith(`${userId}_`)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  // Clear all cache\n  clear() {\n    this.cache.clear();\n  }\n\n  // Clean expired entries\n  cleanup() {\n    const now = Date.now();\n    for (const [key, value] of this.cache) {\n      if (now - value.timestamp > this.CACHE_DURATION) {\n        this.cache.delete(key);\n      }\n    }\n  }\n}\n\n// Create a singleton instance\nconst leaveBalanceCache = new LeaveBalanceCache();\n\n// Cleanup expired entries every minute\nsetInterval(() => {\n  leaveBalanceCache.cleanup();\n}, 60 * 1000);\nexport default leaveBalanceCache;", "map": {"version": 3, "names": ["LeaveBalanceCache", "constructor", "cache", "Map", "CACHE_DURATION", "<PERSON><PERSON>ey", "userId", "query", "normalizedQuery", "toLowerCase", "trim", "has", "key", "cached", "get", "now", "Date", "timestamp", "delete", "data", "set", "clearUser", "startsWith", "clear", "cleanup", "value", "leaveBalanceCache", "setInterval"], "sources": ["E:/kumaran/botnexus/client/src/utils/leaveBalanceCache.js"], "sourcesContent": ["/**\n * Simple cache utility to prevent duplicate leave balance API calls\n */\n\nclass LeaveBalanceCache {\n  constructor() {\n    this.cache = new Map();\n    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes\n  }\n\n  // Generate cache key based on user and query\n  generateKey(userId, query) {\n    const normalizedQuery = query?.toLowerCase().trim() || 'leave_balance';\n    return `${userId}_${normalizedQuery}`;\n  }\n\n  // Check if we have cached data for this query\n  has(userId, query) {\n    const key = this.generateKey(userId, query);\n    const cached = this.cache.get(key);\n    \n    if (!cached) return false;\n    \n    // Check if cache is still valid\n    const now = Date.now();\n    if (now - cached.timestamp > this.CACHE_DURATION) {\n      this.cache.delete(key);\n      return false;\n    }\n    \n    return true;\n  }\n\n  // Get cached data\n  get(userId, query) {\n    const key = this.generateKey(userId, query);\n    const cached = this.cache.get(key);\n    \n    if (!cached) return null;\n    \n    // Check if cache is still valid\n    const now = Date.now();\n    if (now - cached.timestamp > this.CACHE_DURATION) {\n      this.cache.delete(key);\n      return null;\n    }\n    \n    return cached.data;\n  }\n\n  // Set cached data\n  set(userId, query, data) {\n    const key = this.generateKey(userId, query);\n    this.cache.set(key, {\n      data,\n      timestamp: Date.now()\n    });\n  }\n\n  // Clear cache for a specific user\n  clearUser(userId) {\n    for (const [key] of this.cache) {\n      if (key.startsWith(`${userId}_`)) {\n        this.cache.delete(key);\n      }\n    }\n  }\n\n  // Clear all cache\n  clear() {\n    this.cache.clear();\n  }\n\n  // Clean expired entries\n  cleanup() {\n    const now = Date.now();\n    for (const [key, value] of this.cache) {\n      if (now - value.timestamp > this.CACHE_DURATION) {\n        this.cache.delete(key);\n      }\n    }\n  }\n}\n\n// Create a singleton instance\nconst leaveBalanceCache = new LeaveBalanceCache();\n\n// Cleanup expired entries every minute\nsetInterval(() => {\n  leaveBalanceCache.cleanup();\n}, 60 * 1000);\n\nexport default leaveBalanceCache;\n"], "mappings": "AAAA;AACA;AACA;;AAEA,MAAMA,iBAAiB,CAAC;EACtBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,cAAc,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;EACvC;;EAEA;EACAC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACzB,MAAMC,eAAe,GAAG,CAAAD,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAI,eAAe;IACtE,OAAO,GAAGJ,MAAM,IAAIE,eAAe,EAAE;EACvC;;EAEA;EACAG,GAAGA,CAACL,MAAM,EAAEC,KAAK,EAAE;IACjB,MAAMK,GAAG,GAAG,IAAI,CAACP,WAAW,CAACC,MAAM,EAAEC,KAAK,CAAC;IAC3C,MAAMM,MAAM,GAAG,IAAI,CAACX,KAAK,CAACY,GAAG,CAACF,GAAG,CAAC;IAElC,IAAI,CAACC,MAAM,EAAE,OAAO,KAAK;;IAEzB;IACA,MAAME,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGF,MAAM,CAACI,SAAS,GAAG,IAAI,CAACb,cAAc,EAAE;MAChD,IAAI,CAACF,KAAK,CAACgB,MAAM,CAACN,GAAG,CAAC;MACtB,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;;EAEA;EACAE,GAAGA,CAACR,MAAM,EAAEC,KAAK,EAAE;IACjB,MAAMK,GAAG,GAAG,IAAI,CAACP,WAAW,CAACC,MAAM,EAAEC,KAAK,CAAC;IAC3C,MAAMM,MAAM,GAAG,IAAI,CAACX,KAAK,CAACY,GAAG,CAACF,GAAG,CAAC;IAElC,IAAI,CAACC,MAAM,EAAE,OAAO,IAAI;;IAExB;IACA,MAAME,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,IAAIA,GAAG,GAAGF,MAAM,CAACI,SAAS,GAAG,IAAI,CAACb,cAAc,EAAE;MAChD,IAAI,CAACF,KAAK,CAACgB,MAAM,CAACN,GAAG,CAAC;MACtB,OAAO,IAAI;IACb;IAEA,OAAOC,MAAM,CAACM,IAAI;EACpB;;EAEA;EACAC,GAAGA,CAACd,MAAM,EAAEC,KAAK,EAAEY,IAAI,EAAE;IACvB,MAAMP,GAAG,GAAG,IAAI,CAACP,WAAW,CAACC,MAAM,EAAEC,KAAK,CAAC;IAC3C,IAAI,CAACL,KAAK,CAACkB,GAAG,CAACR,GAAG,EAAE;MAClBO,IAAI;MACJF,SAAS,EAAED,IAAI,CAACD,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ;;EAEA;EACAM,SAASA,CAACf,MAAM,EAAE;IAChB,KAAK,MAAM,CAACM,GAAG,CAAC,IAAI,IAAI,CAACV,KAAK,EAAE;MAC9B,IAAIU,GAAG,CAACU,UAAU,CAAC,GAAGhB,MAAM,GAAG,CAAC,EAAE;QAChC,IAAI,CAACJ,KAAK,CAACgB,MAAM,CAACN,GAAG,CAAC;MACxB;IACF;EACF;;EAEA;EACAW,KAAKA,CAAA,EAAG;IACN,IAAI,CAACrB,KAAK,CAACqB,KAAK,CAAC,CAAC;EACpB;;EAEA;EACAC,OAAOA,CAAA,EAAG;IACR,MAAMT,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,KAAK,MAAM,CAACH,GAAG,EAAEa,KAAK,CAAC,IAAI,IAAI,CAACvB,KAAK,EAAE;MACrC,IAAIa,GAAG,GAAGU,KAAK,CAACR,SAAS,GAAG,IAAI,CAACb,cAAc,EAAE;QAC/C,IAAI,CAACF,KAAK,CAACgB,MAAM,CAACN,GAAG,CAAC;MACxB;IACF;EACF;AACF;;AAEA;AACA,MAAMc,iBAAiB,GAAG,IAAI1B,iBAAiB,CAAC,CAAC;;AAEjD;AACA2B,WAAW,CAAC,MAAM;EAChBD,iBAAiB,CAACF,OAAO,CAAC,CAAC;AAC7B,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC;AAEb,eAAeE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}