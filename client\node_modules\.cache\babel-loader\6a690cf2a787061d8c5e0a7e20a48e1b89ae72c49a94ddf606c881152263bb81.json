{"ast": null, "code": "import axios from 'axios';\n\n// Create an axios instance with default config\n// Use environment variable for dynamic base URL (dev/local)\nconst baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\nconsole.log('API baseURL:', baseURL);\nconsole.log('Environment:', process.env.NODE_ENV);\nconst api = axios.create({\n  baseURL: baseURL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Add a request interceptor\napi.interceptors.request.use(config => {\n  // Log the request details\n  console.log('API Request:', {\n    method: config.method.toUpperCase(),\n    url: config.baseURL + config.url,\n    headers: config.headers,\n    data: config.data\n  });\n\n  // Check if chat is initializing and this is a leave-related API call\n  const isChatInitializing = localStorage.getItem('chatInitializing') === 'true';\n  const initStartTime = localStorage.getItem('chatInitStartTime');\n  const recentlyInitialized = initStartTime && Date.now() - parseInt(initStartTime) < 5000;\n\n  // Check if this is a leave-related API call\n  const isLeaveRelatedCall = () => {\n    const url = config.url.toLowerCase();\n    const data = JSON.stringify(config.data || {}).toLowerCase();\n\n    // Check URL for leave-related endpoints\n    const isLeaveUrl = url.includes('leave') || url.includes('check-auto-trigger') || url.includes('form-link');\n\n    // Check data for leave-related content\n    const isLeaveData = data.includes('leave') || data.includes('balance') || data.includes('sick') || data.includes('casual') || data.includes('privilege');\n    return isLeaveUrl && isLeaveData;\n  };\n\n  // Prevent leave-related API calls during chat initialization\n  if ((isChatInitializing || recentlyInitialized) && isLeaveRelatedCall()) {\n    console.log('🚫 Blocking leave-related API call during chat initialization:', config.url);\n    // Return a cancelled request\n    return Promise.reject({\n      message: 'Request cancelled - chat initializing',\n      __CANCELLED__: true\n    });\n  }\n\n  // You can add auth tokens here if needed\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Add a response interceptor\napi.interceptors.response.use(response => {\n  console.log('API Response:', {\n    status: response.status,\n    statusText: response.statusText,\n    url: response.config.url,\n    data: response.data\n  });\n  return response;\n}, error => {\n  var _error$response, _error$response2, _error$config, _error$config2, _error$config3, _error$config4, _error$response3;\n  // Handle cancelled requests silently\n  if (error.__CANCELLED__) {\n    console.log('🚫 API request cancelled:', error.message);\n    return Promise.reject(error);\n  }\n\n  // Handle common errors here\n  console.error('API Error:', {\n    message: error.message,\n    status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n    statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n    url: (_error$config = error.config) === null || _error$config === void 0 ? void 0 : _error$config.url,\n    baseURL: (_error$config2 = error.config) === null || _error$config2 === void 0 ? void 0 : _error$config2.baseURL,\n    fullURL: ((_error$config3 = error.config) === null || _error$config3 === void 0 ? void 0 : _error$config3.baseURL) + ((_error$config4 = error.config) === null || _error$config4 === void 0 ? void 0 : _error$config4.url),\n    data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data\n  });\n  return Promise.reject(error);\n});\nexport default api;", "map": {"version": 3, "names": ["axios", "baseURL", "process", "env", "REACT_APP_API_URL", "console", "log", "NODE_ENV", "api", "create", "headers", "interceptors", "request", "use", "config", "method", "toUpperCase", "url", "data", "isChatInitializing", "localStorage", "getItem", "initStartTime", "recentlyInitialized", "Date", "now", "parseInt", "isLeaveRelatedCall", "toLowerCase", "JSON", "stringify", "isLeaveUrl", "includes", "isLeaveData", "Promise", "reject", "message", "__CANCELLED__", "error", "response", "status", "statusText", "_error$response", "_error$response2", "_error$config", "_error$config2", "_error$config3", "_error$config4", "_error$response3", "fullURL"], "sources": ["E:/kumaran/botnexus/client/src/utils/api.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// Create an axios instance with default config\r\n// Use environment variable for dynamic base URL (dev/local)\r\nconst baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000';\r\nconsole.log('API baseURL:', baseURL);\r\nconsole.log('Environment:', process.env.NODE_ENV);\r\n\r\nconst api = axios.create({\r\n  baseURL: baseURL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n});\r\n\r\n// Add a request interceptor\r\napi.interceptors.request.use(\r\n  (config) => {\r\n    // Log the request details\r\n    console.log('API Request:', {\r\n      method: config.method.toUpperCase(),\r\n      url: config.baseURL + config.url,\r\n      headers: config.headers,\r\n      data: config.data\r\n    });\r\n\r\n    // Check if chat is initializing and this is a leave-related API call\r\n    const isChatInitializing = localStorage.getItem('chatInitializing') === 'true';\r\n    const initStartTime = localStorage.getItem('chatInitStartTime');\r\n    const recentlyInitialized = initStartTime && (Date.now() - parseInt(initStartTime) < 5000);\r\n\r\n    // Check if this is a leave-related API call\r\n    const isLeaveRelatedCall = () => {\r\n      const url = config.url.toLowerCase();\r\n      const data = JSON.stringify(config.data || {}).toLowerCase();\r\n\r\n      // Check URL for leave-related endpoints\r\n      const isLeaveUrl = url.includes('leave') ||\r\n                        url.includes('check-auto-trigger') ||\r\n                        url.includes('form-link');\r\n\r\n      // Check data for leave-related content\r\n      const isLeaveData = data.includes('leave') ||\r\n                         data.includes('balance') ||\r\n                         data.includes('sick') ||\r\n                         data.includes('casual') ||\r\n                         data.includes('privilege');\r\n\r\n      return isLeaveUrl && isLeaveData;\r\n    };\r\n\r\n    // Prevent leave-related API calls during chat initialization\r\n    if ((isChatInitializing || recentlyInitialized) && isLeaveRelatedCall()) {\r\n      console.log('🚫 Blocking leave-related API call during chat initialization:', config.url);\r\n      // Return a cancelled request\r\n      return Promise.reject({\r\n        message: 'Request cancelled - chat initializing',\r\n        __CANCELLED__: true\r\n      });\r\n    }\r\n\r\n    // You can add auth tokens here if needed\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Add a response interceptor\r\napi.interceptors.response.use(\r\n  (response) => {\r\n    console.log('API Response:', {\r\n      status: response.status,\r\n      statusText: response.statusText,\r\n      url: response.config.url,\r\n      data: response.data\r\n    });\r\n    return response;\r\n  },\r\n  (error) => {\r\n    // Handle cancelled requests silently\r\n    if (error.__CANCELLED__) {\r\n      console.log('🚫 API request cancelled:', error.message);\r\n      return Promise.reject(error);\r\n    }\r\n\r\n    // Handle common errors here\r\n    console.error('API Error:', {\r\n      message: error.message,\r\n      status: error.response?.status,\r\n      statusText: error.response?.statusText,\r\n      url: error.config?.url,\r\n      baseURL: error.config?.baseURL,\r\n      fullURL: error.config?.baseURL + error.config?.url,\r\n      data: error.response?.data\r\n    });\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\nexport default api;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA,MAAMC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;AACxEC,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEL,OAAO,CAAC;AACpCI,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEJ,OAAO,CAACC,GAAG,CAACI,QAAQ,CAAC;AAEjD,MAAMC,GAAG,GAAGR,KAAK,CAACS,MAAM,CAAC;EACvBR,OAAO,EAAEA,OAAO;EAChBS,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAF,GAAG,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CACzBC,MAAM,IAAK;EACV;EACAT,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE;IAC1BS,MAAM,EAAED,MAAM,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC;IACnCC,GAAG,EAAEH,MAAM,CAACb,OAAO,GAAGa,MAAM,CAACG,GAAG;IAChCP,OAAO,EAAEI,MAAM,CAACJ,OAAO;IACvBQ,IAAI,EAAEJ,MAAM,CAACI;EACf,CAAC,CAAC;;EAEF;EACA,MAAMC,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC,KAAK,MAAM;EAC9E,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;EAC/D,MAAME,mBAAmB,GAAGD,aAAa,IAAKE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,QAAQ,CAACJ,aAAa,CAAC,GAAG,IAAK;;EAE1F;EACA,MAAMK,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMV,GAAG,GAAGH,MAAM,CAACG,GAAG,CAACW,WAAW,CAAC,CAAC;IACpC,MAAMV,IAAI,GAAGW,IAAI,CAACC,SAAS,CAAChB,MAAM,CAACI,IAAI,IAAI,CAAC,CAAC,CAAC,CAACU,WAAW,CAAC,CAAC;;IAE5D;IACA,MAAMG,UAAU,GAAGd,GAAG,CAACe,QAAQ,CAAC,OAAO,CAAC,IACtBf,GAAG,CAACe,QAAQ,CAAC,oBAAoB,CAAC,IAClCf,GAAG,CAACe,QAAQ,CAAC,WAAW,CAAC;;IAE3C;IACA,MAAMC,WAAW,GAAGf,IAAI,CAACc,QAAQ,CAAC,OAAO,CAAC,IACvBd,IAAI,CAACc,QAAQ,CAAC,SAAS,CAAC,IACxBd,IAAI,CAACc,QAAQ,CAAC,MAAM,CAAC,IACrBd,IAAI,CAACc,QAAQ,CAAC,QAAQ,CAAC,IACvBd,IAAI,CAACc,QAAQ,CAAC,WAAW,CAAC;IAE7C,OAAOD,UAAU,IAAIE,WAAW;EAClC,CAAC;;EAED;EACA,IAAI,CAACd,kBAAkB,IAAII,mBAAmB,KAAKI,kBAAkB,CAAC,CAAC,EAAE;IACvEtB,OAAO,CAACC,GAAG,CAAC,gEAAgE,EAAEQ,MAAM,CAACG,GAAG,CAAC;IACzF;IACA,OAAOiB,OAAO,CAACC,MAAM,CAAC;MACpBC,OAAO,EAAE,uCAAuC;MAChDC,aAAa,EAAE;IACjB,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOvB,MAAM;AACf,CAAC,EACAwB,KAAK,IAAK;EACT,OAAOJ,OAAO,CAACC,MAAM,CAACG,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACA9B,GAAG,CAACG,YAAY,CAAC4B,QAAQ,CAAC1B,GAAG,CAC1B0B,QAAQ,IAAK;EACZlC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE;IAC3BkC,MAAM,EAAED,QAAQ,CAACC,MAAM;IACvBC,UAAU,EAAEF,QAAQ,CAACE,UAAU;IAC/BxB,GAAG,EAAEsB,QAAQ,CAACzB,MAAM,CAACG,GAAG;IACxBC,IAAI,EAAEqB,QAAQ,CAACrB;EACjB,CAAC,CAAC;EACF,OAAOqB,QAAQ;AACjB,CAAC,EACAD,KAAK,IAAK;EAAA,IAAAI,eAAA,EAAAC,gBAAA,EAAAC,aAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,gBAAA;EACT;EACA,IAAIV,KAAK,CAACD,aAAa,EAAE;IACvBhC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEgC,KAAK,CAACF,OAAO,CAAC;IACvD,OAAOF,OAAO,CAACC,MAAM,CAACG,KAAK,CAAC;EAC9B;;EAEA;EACAjC,OAAO,CAACiC,KAAK,CAAC,YAAY,EAAE;IAC1BF,OAAO,EAAEE,KAAK,CAACF,OAAO;IACtBI,MAAM,GAAAE,eAAA,GAAEJ,KAAK,CAACC,QAAQ,cAAAG,eAAA,uBAAdA,eAAA,CAAgBF,MAAM;IAC9BC,UAAU,GAAAE,gBAAA,GAAEL,KAAK,CAACC,QAAQ,cAAAI,gBAAA,uBAAdA,gBAAA,CAAgBF,UAAU;IACtCxB,GAAG,GAAA2B,aAAA,GAAEN,KAAK,CAACxB,MAAM,cAAA8B,aAAA,uBAAZA,aAAA,CAAc3B,GAAG;IACtBhB,OAAO,GAAA4C,cAAA,GAAEP,KAAK,CAACxB,MAAM,cAAA+B,cAAA,uBAAZA,cAAA,CAAc5C,OAAO;IAC9BgD,OAAO,EAAE,EAAAH,cAAA,GAAAR,KAAK,CAACxB,MAAM,cAAAgC,cAAA,uBAAZA,cAAA,CAAc7C,OAAO,MAAA8C,cAAA,GAAGT,KAAK,CAACxB,MAAM,cAAAiC,cAAA,uBAAZA,cAAA,CAAc9B,GAAG;IAClDC,IAAI,GAAA8B,gBAAA,GAAEV,KAAK,CAACC,QAAQ,cAAAS,gBAAA,uBAAdA,gBAAA,CAAgB9B;EACxB,CAAC,CAAC;EACF,OAAOgB,OAAO,CAACC,MAAM,CAACG,KAAK,CAAC;AAC9B,CACF,CAAC;AAED,eAAe9B,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}