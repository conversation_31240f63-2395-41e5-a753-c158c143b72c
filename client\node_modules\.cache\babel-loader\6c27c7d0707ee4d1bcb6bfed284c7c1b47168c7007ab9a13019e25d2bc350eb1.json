{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatFormDisplay.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport AutoTriggerHandler from './AutoTriggerHandler';\nimport api from '../utils/api';\n\n/**\r\n * Component for displaying and interacting with forms in the chat interface\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatFormDisplay = ({\n  form,\n  onSubmit,\n  onCancel,\n  submitButtonText = 'Submit'\n}) => {\n  _s();\n  var _form$formLinking, _form$formLinking2, _form$formLinking2$re;\n  const [formData, setFormData] = useState({});\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [apiResponse, setApiResponse] = useState(null);\n  const [apiError, setApiError] = useState(null);\n  const [formLinkingActions, setFormLinkingActions] = useState([]);\n  const [linkedForm, setLinkedForm] = useState(null);\n  const [showLinkedFormModal, setShowLinkedFormModal] = useState(false);\n  const [isProcessingLink, setIsProcessingLink] = useState(false);\n\n  // Initialize form data with pre-filled or empty values\n  useEffect(() => {\n    console.log('ChatFormDisplay received form:', form);\n    console.log('Form has fields:', form && Array.isArray(form.fields));\n    console.log('Fields count:', form && form.fields ? form.fields.length : 0);\n    if (form && form.formConfig.fields && Array.isArray(form.formConfig.fields)) {\n      const initialData = {};\n\n      // Get user data from localStorage for auto-population\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n      const roleType = localStorage.getItem('roleType');\n      // Filter to only process required fields\n      const requiredFields = form.formConfig.fields.filter(field => field.required);\n      console.log('formmmmm:', form.formConfig.fields);\n      console.log('Required fields only:', requiredFields);\n      requiredFields.forEach(field => {\n        var _form$prefillData;\n        let fieldValue = '';\n\n        // Check if this field should be auto-populated\n        if (field.name === 'empId' && userData.empId) {\n          fieldValue = userData.empId;\n        } else if (field.name === 'type') {\n          // Prioritize prefill data over roleType for 'type' field\n          fieldValue = form.prefillData && form.prefillData[field.name] ? form.prefillData[field.name] : roleType || field.defaultValue || field.value || '';\n        } else if (field.name === 'appliedTo') {\n          // Get supervisor name from localStorage for appliedTo field\n          const supervisorName = localStorage.getItem('supervisorName');\n          fieldValue = supervisorName || field.defaultValue || field.value || '';\n          console.log('🎯 Auto-filling appliedTo field:', {\n            supervisorName: supervisorName,\n            defaultValue: field.defaultValue,\n            value: field.value,\n            finalValue: fieldValue\n          });\n        } else {\n          // Use pre-filled value if available, otherwise use defaultValue, otherwise empty string\n          fieldValue = form.prefillData && form.prefillData[field.name] ? form.prefillData[field.name] : field.defaultValue !== undefined && field.defaultValue !== null && field.defaultValue !== '' ? field.defaultValue : '';\n        }\n        console.log(`🔍 Field \"${field.name}\" initialization:`, {\n          prefillData: (_form$prefillData = form.prefillData) === null || _form$prefillData === void 0 ? void 0 : _form$prefillData[field.name],\n          defaultValue: field.defaultValue,\n          value: field.value,\n          finalFieldValue: fieldValue\n        });\n        initialData[field.name] = fieldValue;\n      });\n      setFormData(initialData);\n    }\n  }, [form]);\n\n  // Check for form linking configuration\n  useEffect(() => {\n    if (!form || !formData) return;\n    const checkFormLinking = () => {\n      var _form$formConfig, _formLinkingConfig$re;\n      const formLinkingConfig = (_form$formConfig = form.formConfig) === null || _form$formConfig === void 0 ? void 0 : _form$formConfig.formLinking;\n      if (!(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled) || !((_formLinkingConfig$re = formLinkingConfig.recordActions) !== null && _formLinkingConfig$re !== void 0 && _formLinkingConfig$re.length)) {\n        return;\n      }\n      const availableActions = formLinkingConfig.recordActions.map((action, index) => ({\n        ...action,\n        actionIndex: index,\n        shouldShow: shouldShowButton(formData, action.conditions)\n      })).filter(action => action.shouldShow);\n      setFormLinkingActions(availableActions);\n    };\n    checkFormLinking();\n  }, [form, formData]);\n\n  // Helper function to check if button should be shown\n  const shouldShowButton = (record, conditions = []) => {\n    if (!conditions || conditions.length === 0) {\n      return true;\n    }\n    return conditions.every(condition => {\n      const fieldValue = record[condition.field];\n      const conditionValue = condition.value;\n      switch (condition.operator) {\n        case 'equals':\n          return fieldValue === conditionValue;\n        case 'not_equals':\n          return fieldValue !== conditionValue;\n        case 'contains':\n          return fieldValue && fieldValue.toString().includes(conditionValue);\n        case 'not_contains':\n          return !fieldValue || !fieldValue.toString().includes(conditionValue);\n        case 'exists':\n          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';\n        case 'not_exists':\n          return fieldValue === undefined || fieldValue === null || fieldValue === '';\n        default:\n          return true;\n      }\n    });\n  };\n\n  // Handle form linking button click\n  const handleFormLink = async action => {\n    setIsProcessingLink(true);\n    setApiError(null);\n    try {\n      const response = await api.post(`/unifiedconfigs/${form._id}/form-link`, {\n        recordData: formData,\n        parentData: {},\n        actionIndex: action.actionIndex\n      });\n      if (response.data.success) {\n        setLinkedForm({\n          ...response.data.targetForm,\n          prefillData: response.data.prefillData,\n          buttonText: response.data.buttonText,\n          isManuallyTriggered: true\n        });\n        setShowLinkedFormModal(true);\n      } else {\n        setApiError(response.data.message || 'Failed to process form linking');\n      }\n    } catch (error) {\n      console.error('Error processing form linking:', error);\n      setApiError('Failed to process form linking');\n    } finally {\n      setIsProcessingLink(false);\n    }\n  };\n\n  // Handle auto-trigger form submission\n  const handleAutoTriggerSubmit = async (formId, formData, responseData) => {\n    console.log('Auto-triggered form submitted:', {\n      formId,\n      formData,\n      responseData\n    });\n    // You can add additional logic here if needed\n  };\n\n  // Helper function to check if a field is auto-populated\n  const isAutoPopulated = fieldName => {\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\n    const roleType = localStorage.getItem('roleType');\n    const supervisorName = localStorage.getItem('supervisorName');\n    return fieldName === 'empId' && userData.empId || fieldName === 'type' && roleType || fieldName === 'appliedTo' && supervisorName;\n  };\n\n  // Handle field value changes\n  const handleChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked,\n      files\n    } = e.target;\n    let fieldValue;\n    if (type === 'checkbox') {\n      fieldValue = checked;\n    } else if (type === 'file') {\n      // For file inputs, store the file name or empty string\n      fieldValue = files && files.length > 0 ? files[0].name : \"\";\n    } else {\n      fieldValue = value;\n    }\n    setFormData({\n      ...formData,\n      [name]: fieldValue\n    });\n\n    // Clear error for this field if it exists\n    if (errors[name]) {\n      setErrors({\n        ...errors,\n        [name]: null\n      });\n    }\n  };\n\n  // Validate form data\n  const validateForm = () => {\n    const newErrors = {};\n    if (form && form.formConfig && form.formConfig.fields) {\n      // Only validate required fields\n      const requiredFields = form.formConfig.fields.filter(field => field.required);\n      requiredFields.forEach(field => {\n        // Check required fields\n        if (field.required && !formData[field.name]) {\n          newErrors[field.name] = `${field.label} is required`;\n        }\n\n        // Add more validation as needed (email format, etc.)\n        if (field.type === 'email' && formData[field.name] && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i.test(formData[field.name])) {\n          newErrors[field.name] = 'Invalid email address';\n        }\n      });\n    }\n    return newErrors;\n  };\n\n  // Replace placeholders in strings with actual values\n  const replacePlaceholders = (str, data = formData) => {\n    if (!str) return str;\n\n    // Get authentication token from localStorage\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\n    const authToken = userData.token;\n\n    // Debug token retrieval\n    console.log('🔍 Debug - userData:', userData);\n    console.log('🔍 Debug - authToken:', authToken);\n    console.log('🔍 Debug - authToken type:', typeof authToken);\n    console.log('🔍 Debug - authToken length:', authToken ? authToken.length : 'N/A');\n\n    // Create enhanced data object with form data + auth data\n    const enhancedData = {\n      ...data,\n      token: authToken && authToken !== '<token>' ? authToken : '',\n      authToken: authToken && authToken !== '<token>' ? authToken : '',\n      bearerToken: authToken && authToken !== '<token>' ? authToken : '',\n      empId: userData.empId || '',\n      userId: userData.empId || userData.id || '',\n      // Add other common placeholders\n      supervisorId: localStorage.getItem('supervisorId') || '',\n      supervisorName: localStorage.getItem('supervisorName') || '',\n      reviewerId: localStorage.getItem('reviewerId') || '',\n      reviewerName: localStorage.getItem('reviewerName') || '',\n      roleType: localStorage.getItem('roleType') || ''\n    };\n    console.log('🔧 Replacing placeholders with enhanced data:', {\n      availablePlaceholders: Object.keys(enhancedData),\n      originalString: str\n    });\n    const result = str.replace(/\\{([^}]+)\\}/g, (match, fieldName) => {\n      const replacement = enhancedData[fieldName];\n      if (replacement !== undefined && replacement !== '') {\n        console.log(`🔧 Replaced {${fieldName}} with: ${fieldName.includes('token') ? replacement.substring(0, 20) + '...' : replacement}`);\n        return replacement;\n      }\n      console.warn(`⚠️ Placeholder {${fieldName}} not found in data`);\n      return match;\n    });\n    console.log('🔧 Final result after placeholder replacement:', result);\n    return result;\n  };\n\n  // Prepare API request with form data\n  const prepareApiRequest = (data = formData) => {\n    if (!form || !form.apiConfig) return null;\n    const {\n      method,\n      endpoint,\n      headers,\n      authType,\n      authDetails = {}\n    } = form.apiConfig;\n\n    // Prepare request object\n    const request = {\n      method: method || 'GET',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: method !== 'GET' ? JSON.stringify(data) : undefined\n    };\n\n    // Add custom headers with replaced placeholders\n    if (headers && Object.keys(headers).length > 0) {\n      Object.entries(headers).forEach(([key, value]) => {\n        request.headers[key] = replacePlaceholders(value, data);\n      });\n    }\n\n    // Add authentication\n    if (authType && authType !== 'none') {\n      switch (authType) {\n        case 'basic':\n          if (authDetails.username && authDetails.password) {\n            const credentials = btoa(`${replacePlaceholders(authDetails.username, data)}:${replacePlaceholders(authDetails.password, data)}`);\n            request.headers['Authorization'] = `Basic ${credentials}`;\n          }\n          break;\n        case 'bearer':\n          // Use token from authDetails, or fallback to localStorage user token\n          let bearerToken = authDetails.token;\n\n          // If no token in authDetails, try to get from localStorage user object\n          if (!bearerToken) {\n            const userData = JSON.parse(localStorage.getItem('user') || '{}');\n            bearerToken = userData.token;\n          }\n          if (bearerToken && bearerToken !== '<token>') {\n            request.headers['Authorization'] = `Bearer ${replacePlaceholders(bearerToken, data)}`;\n          }\n          break;\n        case 'apiKey':\n          if (authDetails.key && authDetails.value) {\n            if (authDetails.in === 'header') {\n              request.headers[authDetails.key] = replacePlaceholders(authDetails.value, data);\n            } else if (authDetails.in === 'query') {\n              // For query params, we'll handle this in the URL\n            }\n          }\n          break;\n        default:\n          break;\n      }\n    }\n\n    // Ensure Bearer token is added if no other auth is configured\n    if (!authType || authType === 'none') {\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n      const token = userData.token;\n      if (token && token !== '<token>' && !request.headers['Authorization']) {\n        request.headers['Authorization'] = `Bearer ${token}`;\n        console.log('🔐 Added default Bearer token to request');\n      }\n    }\n\n    // Process endpoint (replace placeholders and add query params)\n    let processedEndpoint = replacePlaceholders(endpoint, data);\n\n    // Add query parameters for GET requests\n    if (method === 'GET' && data && Object.keys(data).length > 0) {\n      const queryParams = new URLSearchParams();\n      Object.entries(data).forEach(([key, value]) => {\n        if (value) queryParams.append(key, value);\n      });\n\n      // Add API key as query param if needed\n      if (authType === 'apiKey' && authDetails.in === 'query') {\n        queryParams.append(authDetails.key, replacePlaceholders(authDetails.value, data));\n      }\n      const queryString = queryParams.toString();\n      if (queryString) {\n        processedEndpoint += processedEndpoint.includes('?') ? '&' : '?';\n        processedEndpoint += queryString;\n      }\n    }\n    return {\n      request,\n      endpoint: processedEndpoint\n    };\n  };\n\n  // Prepare API request specifically for regularization forms\n  const prepareRegularizationApiRequest = (data, formConfig) => {\n    if (!formConfig || !formConfig.apiConfig) return null;\n    const {\n      method,\n      endpoint,\n      headers,\n      authType,\n      authDetails = {}\n    } = formConfig.apiConfig;\n\n    // Helper function to ensure date is in YYYY-MM-DD format\n    const formatDate = dateValue => {\n      if (!dateValue) return '';\n\n      // If already in YYYY-MM-DD format, return as is\n      if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateValue)) {\n        return dateValue;\n      }\n\n      // Try to parse and format the date\n      try {\n        const date = new Date(dateValue);\n        if (isNaN(date.getTime())) return dateValue; // Return original if invalid\n\n        // Format as YYYY-MM-DD\n        return date.toISOString().split('T')[0];\n      } catch (error) {\n        console.warn('Date formatting error:', error);\n        return dateValue; // Return original if error\n      }\n    };\n\n    // Create the regularization-specific request body format\n    const regularizationRequestBody = {\n      regularizationInfo: [{\n        date: formatDate(data.date || data.attendanceDate),\n        reason: data.reason || data.reasonForRegularization || '',\n        inTime: data.inTime || data.checkIn || '',\n        outTime: data.outTime || data.checkOut || ''\n      }],\n      appliedTo: localStorage.getItem('supervisorId') || '',\n      remark: data.remark || data.remarks || data.additionalRemarks || ''\n    };\n    console.log('🔧 Prepared regularization request body:', regularizationRequestBody);\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\n    const authToken = userData.token;\n\n    // Enhanced debugging for regularization request\n    console.log('🔐 Regularization Debug - Full userData:', userData);\n    console.log('🔐 Regularization Debug - authToken:', authToken);\n    console.log('🔐 Regularization Debug - authToken is placeholder?', authToken === '<token>');\n    console.log('🔐 Regularization Debug - authToken is valid?', authToken && authToken !== '<token>' && authToken.length > 10);\n    // Prepare request object with token validation\n    const validToken = authToken && authToken !== '<token>' ? authToken : null;\n    if (!validToken) {\n      console.error('❌ No valid authentication token found!');\n      console.error('❌ userData:', userData);\n      console.error('❌ authToken:', authToken);\n      throw new Error('Authentication token is missing or invalid. Please log in again.');\n    }\n    const request = {\n      method: method || 'POST',\n      headers: {\n        \"Authorization\": `Bearer ${validToken}`,\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(regularizationRequestBody)\n    };\n    let processedEndpoint = replacePlaceholders(endpoint, data);\n    return {\n      request,\n      endpoint: processedEndpoint\n    };\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n\n    // Validate form\n    const validationErrors = validateForm();\n    if (Object.keys(validationErrors).length > 0) {\n      setErrors(validationErrors);\n      return;\n    }\n    setIsSubmitting(true);\n    setApiResponse(null);\n    setApiError(null);\n\n    // Enhance formData with automatic values from localStorage\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\n    const roleType = localStorage.getItem('roleType');\n    let enhancedFormData = {\n      ...formData\n    };\n\n    // Add empId if not already present and available in localStorage\n    if (form.name.toLowerCase().includes('leave') && !enhancedFormData.empId && userData.empId) {\n      enhancedFormData.empId = userData.empId;\n    }\n\n    // Add type if not already present and roleType is available in localStorage\n    if (form.name.toLowerCase().includes('leave') && !enhancedFormData.type && roleType) {\n      enhancedFormData.type = roleType;\n    }\n    try {\n      // Log the enhanced form data for debugging\n      console.log('Form submission - Enhanced formData:', enhancedFormData);\n      console.log('Form API Config:', form.formConfig.submitApiConfig.endpoint);\n\n      // Make the actual API call using the form's configuration\n      let apiResponse = null;\n      if (form.formConfig && form.formConfig.submitApiConfig.endpoint) {\n        try {\n          // Check if this is a regularization form and format request accordingly\n          const isRegularizationForm = form.name && form.name.toLowerCase().includes('regular');\n          let apiRequest;\n          console.log('🔍 Form detection:', {\n            formName: form.name,\n            isRegularizationForm,\n            formTitle: form.formTitle || 'Not set'\n          });\n          if (apiRequest) {\n            const {\n              request,\n              endpoint\n            } = apiRequest;\n            console.log('🚀 Making API call to:', endpoint);\n\n            // Make the API call\n            const response = await fetch(endpoint, request);\n            const responseData = await response.text();\n\n            // Try to parse as JSON, fallback to text\n            let parsedData;\n            try {\n              parsedData = JSON.parse(responseData);\n            } catch {\n              parsedData = responseData;\n            }\n            apiResponse = {\n              status: response.status,\n              statusText: response.statusText,\n              data: parsedData,\n              success: response.ok\n            };\n            if (response.ok) {\n              setApiResponse(apiResponse);\n            } else {\n              console.log(\"login response ----------- \", response);\n              console.log(\"login response ----------- \", apiResponse.data.message);\n              setApiError(`${apiResponse.data.message}`);\n              // setApiError(`API Error: ${response.status} ${response.statusText} `);\n            }\n          }\n        } catch (apiError) {\n          console.error('API call failed:', apiError);\n          setApiError(`API call failed: ${apiError.message}`);\n          apiResponse = {\n            status: 0,\n            statusText: 'Network Error',\n            data: {\n              error: apiError.message\n            },\n            success: false\n          };\n        }\n      }\n\n      // Always call onSubmit to update the chat with the result\n      if (onSubmit) {\n        var _apiResponse;\n        onSubmit({\n          formData: enhancedFormData,\n          formId: form._id,\n          apiResponse: apiResponse,\n          success: ((_apiResponse = apiResponse) === null || _apiResponse === void 0 ? void 0 : _apiResponse.success) || false\n        });\n      }\n\n      // Set a default success response if no API was configured\n      if (!apiResponse) {\n        setApiResponse({\n          status: 200,\n          statusText: 'OK',\n          data: {\n            message: 'Form submitted successfully (no API configured)'\n          }\n        });\n      }\n    } catch (error) {\n      console.error('Form submission failed:', error);\n      setApiError(error.message || 'Failed to submit form');\n\n      // Call onSubmit with error information\n      if (onSubmit) {\n        onSubmit({\n          formData: enhancedFormData || formData,\n          formId: form._id,\n          error: error.message || 'Failed to submit form',\n          success: false\n        });\n      }\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n  if (!form) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden mb-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-white font-medium\",\n        children: form.name\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 586,\n        columnNumber: 9\n      }, this), form.description && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-blue-100 text-sm mt-1\",\n        children: form.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 588,\n        columnNumber: 11\n      }, this), form && form.formConfig && form.formConfig.fields && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 text-blue-100 text-xs\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"bg-blue-400 bg-opacity-50 px-2 py-1 rounded\",\n          children: [\"Required Fields Only: \", form.formConfig.fields.filter(field => field.required).length, \" of \", form.formConfig.fields.length, \" fields\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 592,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 585,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-4\",\n          children: form && form.formConfig && form.formConfig.fields && Array.isArray(form.formConfig.fields) ? form.formConfig.fields.filter(field => field.required).length > 0 ? form.formConfig.fields.filter(field => field.required).map(field => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"form-field\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: [field.label, field.required && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-red-500 ml-1\",\n                children: \"*\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 611,\n                columnNumber: 38\n              }, this), isAutoPopulated(field.name) && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-500 text-xs ml-2\",\n                children: \"(Auto-filled)\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 613,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 17\n            }, this), field.type === 'text' && /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: field.name,\n              value: formData[field.name] || '',\n              onChange: handleChange,\n              placeholder: field.placeholder,\n              readOnly: field.readonly,\n              className: `w-full p-2 border rounded-md ${errors[field.name] ? 'border-red-500' : field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' : isAutoPopulated(field.name) ? 'border-blue-300 bg-blue-50' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 19\n            }, this), field.type === 'email' && /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"email\",\n              name: field.name,\n              value: formData[field.name] || '',\n              onChange: handleChange,\n              placeholder: field.placeholder,\n              readOnly: field.readonly,\n              className: `w-full p-2 border rounded-md ${errors[field.name] ? 'border-red-500' : field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 19\n            }, this), field.type === 'number' && /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              name: field.name,\n              value: formData[field.name] || '',\n              onChange: handleChange,\n              placeholder: field.placeholder,\n              readOnly: field.readonly,\n              className: `w-full p-2 border rounded-md ${errors[field.name] ? 'border-red-500' : field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 650,\n              columnNumber: 19\n            }, this), field.type === 'textarea' && /*#__PURE__*/_jsxDEV(\"textarea\", {\n              name: field.name,\n              value: formData[field.name] || '',\n              onChange: handleChange,\n              placeholder: field.placeholder,\n              rows: \"3\",\n              className: `w-full p-2 border rounded-md ${errors[field.name] ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 666,\n              columnNumber: 19\n            }, this), field.type === 'select' && /*#__PURE__*/_jsxDEV(\"select\", {\n              name: field.name,\n              value: formData[field.name] || '',\n              onChange: handleChange,\n              className: `w-full p-2 border rounded-md ${errors[field.name] ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"Select an option\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 687,\n                columnNumber: 21\n              }, this), field.options && field.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: option,\n                children: option\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 689,\n                columnNumber: 23\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 19\n            }, this), field.type === 'checkbox' && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                name: field.name,\n                checked: formData[field.name] || false,\n                onChange: handleChange,\n                className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 698,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"ml-2 text-sm text-gray-600\",\n                children: field.placeholder\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 705,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 697,\n              columnNumber: 19\n            }, this), field.type === 'radio' && field.options && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: field.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"radio\",\n                  id: `${field.name}-${index}`,\n                  name: field.name,\n                  value: option,\n                  checked: formData[field.name] === option,\n                  onChange: handleChange,\n                  className: \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 715,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                  htmlFor: `${field.name}-${index}`,\n                  className: \"ml-2 text-sm text-gray-600\",\n                  children: option\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 724,\n                  columnNumber: 25\n                }, this)]\n              }, index, true, {\n                fileName: _jsxFileName,\n                lineNumber: 714,\n                columnNumber: 23\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 712,\n              columnNumber: 19\n            }, this), field.type === 'date' && /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"date\",\n              name: field.name,\n              value: formData[field.name] || '',\n              onChange: handleChange,\n              className: `w-full p-2 border rounded-md ${errors[field.name] ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 736,\n              columnNumber: 19\n            }, this), field.type === 'file' && /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"file\",\n              name: field.name,\n              onChange: handleChange,\n              className: `w-full p-2 border rounded-md ${errors[field.name] ? 'border-red-500' : 'border-gray-300'} focus:outline-none focus:ring-2 focus:ring-blue-500`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 748,\n              columnNumber: 19\n            }, this), errors[field.name] && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-1 text-sm text-red-500\",\n              children: errors[field.name]\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 759,\n              columnNumber: 19\n            }, this)]\n          }, field.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 15\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-yellow-50 border border-yellow-200 rounded-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-yellow-700 font-medium\",\n              children: \"\\u26A0\\uFE0F No Required Fields\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 764,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-yellow-600 text-sm mt-1\",\n              children: \"This form has no required fields to display. All fields are optional.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 765,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 763,\n            columnNumber: 15\n          }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-4 bg-red-50 border border-red-200 rounded-md\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-700 font-medium\",\n              children: \"\\u274C Form Error\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 771,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-600 text-sm mt-1\",\n              children: \"Form fields are not available. Please contact your administrator.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 772,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-xs mt-2\",\n              children: [\"Debug: form=\", form ? 'exists' : 'null', \", fields=\", form && form.formConfig && form.formConfig.fields ? 'exists' : 'null']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 775,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 770,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 603,\n          columnNumber: 11\n        }, this), apiError && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 p-3 bg-red-50 rounded-md border border-red-200\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-red-600\",\n            children: apiError\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 796,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 794,\n          columnNumber: 13\n        }, this), formLinkingActions.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-sm font-medium text-gray-700 mb-3\",\n            children: \"Available Actions\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 803,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-wrap gap-2\",\n            children: formLinkingActions.map(action => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleFormLink(action),\n              disabled: isProcessingLink,\n              className: `px-4 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${getButtonClasses(action.buttonStyle)} ${isProcessingLink ? 'opacity-50 cursor-not-allowed' : ''}`,\n              children: isProcessingLink ? 'Processing...' : action.buttonText\n            }, action.actionIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 806,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 804,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 802,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-4 flex justify-end space-x-2\",\n          children: [onCancel && /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            onClick: onCancel,\n            className: \"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 824,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            disabled: isSubmitting,\n            className: `px-3 py-2 rounded-md text-sm font-medium text-white ${isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`,\n            children: isSubmitting ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  className: \"opacity-25\",\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  className: \"opacity-75\",\n                  fill: \"currentColor\",\n                  d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 841,\n                columnNumber: 19\n              }, this), \"Submitting...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 840,\n              columnNumber: 17\n            }, this) : submitButtonText\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 832,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 822,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 602,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 600,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AutoTriggerHandler, {\n      formId: form._id,\n      recordData: formData,\n      onFormSubmit: handleAutoTriggerSubmit,\n      enabled: (form === null || form === void 0 ? void 0 : (_form$formLinking = form.formLinking) === null || _form$formLinking === void 0 ? void 0 : _form$formLinking.enabled) && (form === null || form === void 0 ? void 0 : (_form$formLinking2 = form.formLinking) === null || _form$formLinking2 === void 0 ? void 0 : (_form$formLinking2$re = _form$formLinking2.recordActions) === null || _form$formLinking2$re === void 0 ? void 0 : _form$formLinking2$re.some(action => {\n        var _action$autoTrigger;\n        return (_action$autoTrigger = action.autoTrigger) === null || _action$autoTrigger === void 0 ? void 0 : _action$autoTrigger.enabled;\n      }))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 856,\n      columnNumber: 7\n    }, this), showLinkedFormModal && linkedForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-green-500 text-white px-6 py-4 rounded-t-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold\",\n                children: \"\\uD83D\\uDD17 Linked Form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 871,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-green-100 mt-1\",\n                children: linkedForm.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 874,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 870,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowLinkedFormModal(false),\n              className: \"text-white hover:text-gray-200 focus:outline-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 883,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 882,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 878,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 869,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 868,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-green-50 border border-green-200 rounded-md p-3 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-green-400\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 895,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 894,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 893,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-green-800\",\n                  children: \"Linked Form\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 899,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 text-sm text-green-700\",\n                  children: \"This form was opened through form linking. Some fields may be pre-filled based on the original record.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 900,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 898,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 892,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 891,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(ChatFormDisplay, {\n            form: linkedForm,\n            onSubmit: async (formId, formData) => {\n              try {\n                await api.post(`/unifiedconfigs/${formId}/submit`, formData);\n                setShowLinkedFormModal(false);\n                setLinkedForm(null);\n              } catch (error) {\n                console.error('Error submitting linked form:', error);\n              }\n            },\n            onCancel: () => {\n              setShowLinkedFormModal(false);\n              setLinkedForm(null);\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 908,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 890,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 866,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 865,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 584,\n    columnNumber: 5\n  }, this);\n};\n\n// Helper function to get button classes based on style\n_s(ChatFormDisplay, \"+p2qt5Iuo0wb49fCIsC0sclzUbU=\");\n_c = ChatFormDisplay;\nconst getButtonClasses = style => {\n  switch (style) {\n    case 'primary':\n      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';\n    case 'secondary':\n      return 'bg-gray-500 text-white hover:bg-gray-600 focus:ring-gray-500';\n    case 'success':\n      return 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500';\n    case 'warning':\n      return 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500';\n    case 'danger':\n      return 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500';\n    default:\n      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';\n  }\n};\nexport default ChatFormDisplay;\nvar _c;\n$RefreshReg$(_c, \"ChatFormDisplay\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "AutoTriggerHandler", "api", "jsxDEV", "_jsxDEV", "ChatFormDisplay", "form", "onSubmit", "onCancel", "submitButtonText", "_s", "_form$formLinking", "_form$formLinking2", "_form$formLinking2$re", "formData", "setFormData", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "apiResponse", "setApiResponse", "apiError", "setApiError", "formLinkingActions", "setFormLinkingActions", "linkedForm", "setLinkedForm", "showLinkedFormModal", "setShowLinkedFormModal", "isProcessingLink", "setIsProcessingLink", "console", "log", "Array", "isArray", "fields", "length", "formConfig", "initialData", "userData", "JSON", "parse", "localStorage", "getItem", "roleType", "requiredFields", "filter", "field", "required", "for<PERSON>ach", "_form$prefillData", "fieldValue", "name", "empId", "prefillData", "defaultValue", "value", "<PERSON><PERSON><PERSON>", "finalValue", "undefined", "finalFieldValue", "checkFormLinking", "_form$formConfig", "_formLinkingConfig$re", "formLinkingConfig", "formLinking", "enabled", "recordActions", "availableActions", "map", "action", "index", "actionIndex", "shouldShow", "shouldShowButton", "conditions", "record", "every", "condition", "conditionValue", "operator", "toString", "includes", "handleFormLink", "response", "post", "_id", "recordData", "parentData", "data", "success", "targetForm", "buttonText", "isManuallyTriggered", "message", "error", "handleAutoTriggerSubmit", "formId", "responseData", "isAutoPopulated", "fieldName", "handleChange", "e", "type", "checked", "files", "target", "validateForm", "newErrors", "label", "test", "replacePlaceholders", "str", "authToken", "token", "enhancedData", "bearerToken", "userId", "id", "supervisorId", "reviewerId", "reviewerName", "availablePlaceholders", "Object", "keys", "originalString", "result", "replace", "match", "replacement", "substring", "warn", "prepareApiRequest", "apiConfig", "method", "endpoint", "headers", "authType", "authDetails", "request", "body", "stringify", "entries", "key", "username", "password", "credentials", "btoa", "in", "processedEndpoint", "queryParams", "URLSearchParams", "append", "queryString", "prepareRegularizationApiRequest", "formatDate", "dateValue", "date", "Date", "isNaN", "getTime", "toISOString", "split", "regularizationRequestBody", "regularizationInfo", "attendanceDate", "reason", "reasonForRegularization", "inTime", "checkIn", "outTime", "checkOut", "appliedTo", "remark", "remarks", "additionalRemarks", "validToken", "Error", "handleSubmit", "preventDefault", "validationErrors", "enhancedFormData", "toLowerCase", "submitApiConfig", "isRegularizationForm", "apiRequest", "formName", "formTitle", "fetch", "text", "parsedData", "status", "statusText", "ok", "_apiResponse", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "description", "onChange", "placeholder", "readOnly", "readonly", "rows", "options", "option", "htmlFor", "onClick", "disabled", "getButtonClasses", "buttonStyle", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "d", "onFormSubmit", "some", "_action$autoTrigger", "autoTrigger", "strokeLinecap", "strokeLinejoin", "fillRule", "clipRule", "_c", "style", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatFormDisplay.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport <PERSON>TriggerHandler from './AutoTriggerHandler';\r\nimport api from '../utils/api';\r\n\r\n/**\r\n * Component for displaying and interacting with forms in the chat interface\r\n */\r\nconst ChatFormDisplay = ({ form, onSubmit, onCancel, submitButtonText = 'Submit' }) => {\r\n  const [formData, setFormData] = useState({});\r\n  const [errors, setErrors] = useState({});\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const [apiResponse, setApiResponse] = useState(null);\r\n  const [apiError, setApiError] = useState(null);\r\n  const [formLinkingActions, setFormLinkingActions] = useState([]);\r\n  const [linkedForm, setLinkedForm] = useState(null);\r\n  const [showLinkedFormModal, setShowLinkedFormModal] = useState(false);\r\n  const [isProcessingLink, setIsProcessingLink] = useState(false);\r\n  \r\n  // Initialize form data with pre-filled or empty values\r\n  useEffect(() => {\r\n    console.log('ChatFormDisplay received form:', form);\r\n    console.log('Form has fields:', form && Array.isArray(form.fields));\r\n    console.log('Fields count:', form && form.fields ? form.fields.length : 0);\r\n    \r\n    if (form && form.formConfig.fields && Array.isArray(form.formConfig.fields)) {\r\n      const initialData = {};\r\n      \r\n      // Get user data from localStorage for auto-population\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      const roleType = localStorage.getItem('roleType');\r\n      // Filter to only process required fields\r\n      const requiredFields = form.formConfig.fields.filter(field => field.required);\r\n      console.log('formmmmm:', form.formConfig.fields);\r\n      console.log('Required fields only:', requiredFields);\r\n      requiredFields.forEach(field => {\r\n        let fieldValue = '';\r\n        \r\n        // Check if this field should be auto-populated\r\n        if (field.name === 'empId' && userData.empId) {\r\n          fieldValue = userData.empId;\r\n        } else if (field.name === 'type') {\r\n          // Prioritize prefill data over roleType for 'type' field\r\n          fieldValue = form.prefillData && form.prefillData[field.name] \r\n            ? form.prefillData[field.name]\r\n            : roleType || field.defaultValue || field.value || '';\r\n        } else if (field.name === 'appliedTo') {\r\n          // Get supervisor name from localStorage for appliedTo field\r\n          const supervisorName = localStorage.getItem('supervisorName');\r\n          fieldValue = supervisorName || field.defaultValue || field.value || '';\r\n          console.log('🎯 Auto-filling appliedTo field:', {\r\n            supervisorName: supervisorName,\r\n            defaultValue: field.defaultValue,\r\n            value: field.value,\r\n            finalValue: fieldValue\r\n          });\r\n        } else {\r\n          // Use pre-filled value if available, otherwise use defaultValue, otherwise empty string\r\n          fieldValue = form.prefillData && form.prefillData[field.name] \r\n            ? form.prefillData[field.name] \r\n            : (field.defaultValue !== undefined && field.defaultValue !== null && field.defaultValue !== '') \r\n            ? field.defaultValue \r\n            : '';\r\n        }\r\n\r\n        console.log(`🔍 Field \"${field.name}\" initialization:`, {\r\n          prefillData: form.prefillData?.[field.name],\r\n          defaultValue: field.defaultValue,\r\n          value: field.value,\r\n          finalFieldValue: fieldValue\r\n        });\r\n        \r\n        initialData[field.name] = fieldValue;\r\n\r\n      });\r\n      setFormData(initialData);\r\n    }\r\n  }, [form]);\r\n\r\n  // Check for form linking configuration\r\n  useEffect(() => {\r\n    if (!form || !formData) return;\r\n\r\n    const checkFormLinking = () => {\r\n      const formLinkingConfig = form.formConfig?.formLinking;\r\n      if (!formLinkingConfig?.enabled || !formLinkingConfig.recordActions?.length) {\r\n        return;\r\n      }\r\n\r\n      const availableActions = formLinkingConfig.recordActions.map((action, index) => ({\r\n        ...action,\r\n        actionIndex: index,\r\n        shouldShow: shouldShowButton(formData, action.conditions)\r\n      })).filter(action => action.shouldShow);\r\n\r\n      setFormLinkingActions(availableActions);\r\n    };\r\n\r\n    checkFormLinking();\r\n  }, [form, formData]);\r\n\r\n  // Helper function to check if button should be shown\r\n  const shouldShowButton = (record, conditions = []) => {\r\n    if (!conditions || conditions.length === 0) {\r\n      return true;\r\n    }\r\n    \r\n    return conditions.every(condition => {\r\n      const fieldValue = record[condition.field];\r\n      const conditionValue = condition.value;\r\n      \r\n      switch (condition.operator) {\r\n        case 'equals':\r\n          return fieldValue === conditionValue;\r\n        case 'not_equals':\r\n          return fieldValue !== conditionValue;\r\n        case 'contains':\r\n          return fieldValue && fieldValue.toString().includes(conditionValue);\r\n        case 'not_contains':\r\n          return !fieldValue || !fieldValue.toString().includes(conditionValue);\r\n        case 'exists':\r\n          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';\r\n        case 'not_exists':\r\n          return fieldValue === undefined || fieldValue === null || fieldValue === '';\r\n        default:\r\n          return true;\r\n      }\r\n    });\r\n  };\r\n\r\n  // Handle form linking button click\r\n  const handleFormLink = async (action) => {\r\n    setIsProcessingLink(true);\r\n    setApiError(null);\r\n    \r\n    try {\r\n      const response = await api.post(`/unifiedconfigs/${form._id}/form-link`, {\r\n        recordData: formData,\r\n        parentData: {},\r\n        actionIndex: action.actionIndex\r\n      });\r\n\r\n      if (response.data.success) {\r\n        setLinkedForm({\r\n          ...response.data.targetForm,\r\n          prefillData: response.data.prefillData,\r\n          buttonText: response.data.buttonText,\r\n          isManuallyTriggered: true\r\n        });\r\n        setShowLinkedFormModal(true);\r\n      } else {\r\n        setApiError(response.data.message || 'Failed to process form linking');\r\n      }\r\n    } catch (error) {\r\n      console.error('Error processing form linking:', error);\r\n      setApiError('Failed to process form linking');\r\n    } finally {\r\n      setIsProcessingLink(false);\r\n    }\r\n  };\r\n\r\n  // Handle auto-trigger form submission\r\n  const handleAutoTriggerSubmit = async (formId, formData, responseData) => {\r\n    console.log('Auto-triggered form submitted:', { formId, formData, responseData });\r\n    // You can add additional logic here if needed\r\n  };\r\n  \r\n  // Helper function to check if a field is auto-populated\r\n  const isAutoPopulated = (fieldName) => {\r\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n    const roleType = localStorage.getItem('roleType');\r\n    const supervisorName = localStorage.getItem('supervisorName');\r\n    \r\n    return (fieldName === 'empId' && userData.empId) || \r\n           (fieldName === 'type' && roleType) ||\r\n           (fieldName === 'appliedTo' && supervisorName);\r\n  };\r\n\r\n  // Handle field value changes\r\n  const handleChange = (e) => {\r\n    const { name, value, type, checked, files } = e.target;\r\n    \r\n    let fieldValue;\r\n    if (type === 'checkbox') {\r\n      fieldValue = checked;\r\n    } else if (type === 'file') {\r\n      // For file inputs, store the file name or empty string\r\n      fieldValue = files && files.length > 0 ? files[0].name : \"\";\r\n    } else {\r\n      fieldValue = value;\r\n    }\r\n    \r\n    setFormData({\r\n      ...formData,\r\n      [name]: fieldValue\r\n    });\r\n    \r\n    // Clear error for this field if it exists\r\n    if (errors[name]) {\r\n      setErrors({\r\n        ...errors,\r\n        [name]: null\r\n      });\r\n    }\r\n  };\r\n  \r\n  // Validate form data\r\n  const validateForm = () => {\r\n    const newErrors = {};\r\n    \r\n    if (form && form.formConfig && form.formConfig.fields) {\r\n      // Only validate required fields\r\n      const requiredFields = form.formConfig.fields.filter(field => field.required);\r\n      requiredFields.forEach(field => {\r\n        // Check required fields\r\n        if (field.required && !formData[field.name]) {\r\n          newErrors[field.name] = `${field.label} is required`;\r\n        }\r\n        \r\n        // Add more validation as needed (email format, etc.)\r\n        if (field.type === 'email' && formData[field.name] && \r\n            !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i.test(formData[field.name])) {\r\n          newErrors[field.name] = 'Invalid email address';\r\n        }\r\n      });\r\n    }\r\n    \r\n    return newErrors;\r\n  };\r\n  \r\n  // Replace placeholders in strings with actual values\r\n  const replacePlaceholders = (str, data = formData) => {\r\n    if (!str) return str;\r\n    \r\n    // Get authentication token from localStorage\r\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n    const authToken = userData.token;\r\n    \r\n    // Debug token retrieval\r\n    console.log('🔍 Debug - userData:', userData);\r\n    console.log('🔍 Debug - authToken:', authToken);\r\n    console.log('🔍 Debug - authToken type:', typeof authToken);\r\n    console.log('🔍 Debug - authToken length:', authToken ? authToken.length : 'N/A');\r\n    \r\n    // Create enhanced data object with form data + auth data\r\n    const enhancedData = {\r\n      ...data,\r\n      token: authToken && authToken !== '<token>' ? authToken : '',\r\n      authToken: authToken && authToken !== '<token>' ? authToken : '',\r\n      bearerToken: authToken && authToken !== '<token>' ? authToken : '',\r\n      empId: userData.empId || '',\r\n      userId: userData.empId || userData.id || '',\r\n      // Add other common placeholders\r\n      supervisorId: localStorage.getItem('supervisorId') || '',\r\n      supervisorName: localStorage.getItem('supervisorName') || '',\r\n      reviewerId: localStorage.getItem('reviewerId') || '',\r\n      reviewerName: localStorage.getItem('reviewerName') || '',\r\n      roleType: localStorage.getItem('roleType') || ''\r\n    };\r\n    \r\n    console.log('🔧 Replacing placeholders with enhanced data:', {\r\n      availablePlaceholders: Object.keys(enhancedData),\r\n      originalString: str\r\n    });\r\n    \r\n    const result = str.replace(/\\{([^}]+)\\}/g, (match, fieldName) => {\r\n      const replacement = enhancedData[fieldName];\r\n      if (replacement !== undefined && replacement !== '') {\r\n        console.log(`🔧 Replaced {${fieldName}} with: ${fieldName.includes('token') ? replacement.substring(0, 20) + '...' : replacement}`);\r\n        return replacement;\r\n      }\r\n      console.warn(`⚠️ Placeholder {${fieldName}} not found in data`);\r\n      return match;\r\n    });\r\n    \r\n    console.log('🔧 Final result after placeholder replacement:', result);\r\n    return result;\r\n  };\r\n  \r\n  // Prepare API request with form data\r\n  const prepareApiRequest = (data = formData) => {\r\n    if (!form || !form.apiConfig) return null;\r\n    \r\n    const { method, endpoint, headers, authType, authDetails = {} } = form.apiConfig;\r\n    \r\n    // Prepare request object\r\n    const request = {\r\n      method: method || 'GET',\r\n      headers: { 'Content-Type': 'application/json' },\r\n      body: method !== 'GET' ? JSON.stringify(data) : undefined\r\n    };\r\n    \r\n    // Add custom headers with replaced placeholders\r\n    if (headers && Object.keys(headers).length > 0) {\r\n      Object.entries(headers).forEach(([key, value]) => {\r\n        request.headers[key] = replacePlaceholders(value, data);\r\n      });\r\n    }\r\n    \r\n    // Add authentication\r\n    if (authType && authType !== 'none') {\r\n      switch (authType) {\r\n        case 'basic':\r\n          if (authDetails.username && authDetails.password) {\r\n            const credentials = btoa(`${replacePlaceholders(authDetails.username, data)}:${replacePlaceholders(authDetails.password, data)}`);\r\n            request.headers['Authorization'] = `Basic ${credentials}`;\r\n          }\r\n          break;\r\n        case 'bearer':\r\n          // Use token from authDetails, or fallback to localStorage user token\r\n          let bearerToken = authDetails.token;\r\n          \r\n          // If no token in authDetails, try to get from localStorage user object\r\n          if (!bearerToken) {\r\n            const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n            bearerToken = userData.token;\r\n          }\r\n          \r\n          if (bearerToken && bearerToken !== '<token>') {\r\n            request.headers['Authorization'] = `Bearer ${replacePlaceholders(bearerToken, data)}`;\r\n          }\r\n          break;\r\n        case 'apiKey':\r\n          if (authDetails.key && authDetails.value) {\r\n            if (authDetails.in === 'header') {\r\n              request.headers[authDetails.key] = replacePlaceholders(authDetails.value, data);\r\n            } else if (authDetails.in === 'query') {\r\n              // For query params, we'll handle this in the URL\r\n            }\r\n          }\r\n          break;\r\n        default:\r\n          break;\r\n      }\r\n    }\r\n    \r\n    // Ensure Bearer token is added if no other auth is configured\r\n    if (!authType || authType === 'none') {\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      const token = userData.token;\r\n      if (token && token !== '<token>' && !request.headers['Authorization']) {\r\n        request.headers['Authorization'] = `Bearer ${token}`;\r\n        console.log('🔐 Added default Bearer token to request');\r\n      }\r\n    }\r\n    \r\n    // Process endpoint (replace placeholders and add query params)\r\n    let processedEndpoint = replacePlaceholders(endpoint, data);\r\n    \r\n    // Add query parameters for GET requests\r\n    if (method === 'GET' && data && Object.keys(data).length > 0) {\r\n      const queryParams = new URLSearchParams();\r\n      Object.entries(data).forEach(([key, value]) => {\r\n        if (value) queryParams.append(key, value);\r\n      });\r\n      \r\n      // Add API key as query param if needed\r\n      if (authType === 'apiKey' && authDetails.in === 'query') {\r\n        queryParams.append(authDetails.key, replacePlaceholders(authDetails.value, data));\r\n      }\r\n      \r\n      const queryString = queryParams.toString();\r\n      if (queryString) {\r\n        processedEndpoint += processedEndpoint.includes('?') ? '&' : '?';\r\n        processedEndpoint += queryString;\r\n      }\r\n    }\r\n    \r\n    return { request, endpoint: processedEndpoint };\r\n  };\r\n\r\n  // Prepare API request specifically for regularization forms\r\n  const prepareRegularizationApiRequest = (data, formConfig) => {\r\n    if (!formConfig || !formConfig.apiConfig) return null;\r\n    \r\n    const { method, endpoint, headers, authType, authDetails = {} } = formConfig.apiConfig;\r\n    \r\n    // Helper function to ensure date is in YYYY-MM-DD format\r\n    const formatDate = (dateValue) => {\r\n      if (!dateValue) return '';\r\n      \r\n      // If already in YYYY-MM-DD format, return as is\r\n      if (/^\\d{4}-\\d{2}-\\d{2}$/.test(dateValue)) {\r\n        return dateValue;\r\n      }\r\n      \r\n      // Try to parse and format the date\r\n      try {\r\n        const date = new Date(dateValue);\r\n        if (isNaN(date.getTime())) return dateValue; // Return original if invalid\r\n        \r\n        // Format as YYYY-MM-DD\r\n        return date.toISOString().split('T')[0];\r\n      } catch (error) {\r\n        console.warn('Date formatting error:', error);\r\n        return dateValue; // Return original if error\r\n      }\r\n    };\r\n\r\n    // Create the regularization-specific request body format\r\n    const regularizationRequestBody = {\r\n      regularizationInfo: [\r\n        {\r\n          date: formatDate(data.date || data.attendanceDate),\r\n          reason: data.reason || data.reasonForRegularization || '',\r\n          inTime: data.inTime || data.checkIn || '',\r\n          outTime: data.outTime || data.checkOut || ''\r\n        }\r\n      ],\r\n      appliedTo: localStorage.getItem('supervisorId') || '',\r\n      remark: data.remark || data.remarks || data.additionalRemarks || ''\r\n    };\r\n    \r\n    console.log('🔧 Prepared regularization request body:', regularizationRequestBody);\r\n  \r\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n    const authToken = userData.token;\r\n    \r\n    // Enhanced debugging for regularization request\r\n    console.log('🔐 Regularization Debug - Full userData:', userData);\r\n    console.log('🔐 Regularization Debug - authToken:', authToken);\r\n    console.log('🔐 Regularization Debug - authToken is placeholder?', authToken === '<token>');\r\n    console.log('🔐 Regularization Debug - authToken is valid?', authToken && authToken !== '<token>' && authToken.length > 10);\r\n    // Prepare request object with token validation\r\n    const validToken = authToken && authToken !== '<token>' ? authToken : null;\r\n    \r\n    if (!validToken) {\r\n      console.error('❌ No valid authentication token found!');\r\n      console.error('❌ userData:', userData);\r\n      console.error('❌ authToken:', authToken);\r\n      throw new Error('Authentication token is missing or invalid. Please log in again.');\r\n    }\r\n    \r\n    const request = {\r\n      method: method || 'POST',\r\n      headers: { \r\n        \"Authorization\": `Bearer ${validToken}`,\r\n        'Content-Type': 'application/json' \r\n      },\r\n      body: JSON.stringify(regularizationRequestBody)\r\n    };    \r\n  \r\n    let processedEndpoint = replacePlaceholders(endpoint, data);\r\n    \r\n    return { request, endpoint: processedEndpoint };\r\n  };\r\n  \r\n  // Handle form submission\r\n  const handleSubmit = async (e) => {\r\n    e.preventDefault();\r\n    \r\n    // Validate form\r\n    const validationErrors = validateForm();\r\n    if (Object.keys(validationErrors).length > 0) {\r\n      setErrors(validationErrors);\r\n      return;\r\n    }\r\n    \r\n    setIsSubmitting(true);\r\n    setApiResponse(null);\r\n    setApiError(null);\r\n    \r\n    // Enhance formData with automatic values from localStorage\r\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n    const roleType = localStorage.getItem('roleType');\r\n    \r\n    let enhancedFormData = { ...formData };\r\n    \r\n    // Add empId if not already present and available in localStorage\r\n    if (form.name.toLowerCase().includes('leave') && !enhancedFormData.empId && userData.empId) {\r\n      enhancedFormData.empId = userData.empId;\r\n    }\r\n    \r\n    // Add type if not already present and roleType is available in localStorage\r\n    if (form.name.toLowerCase().includes('leave') && !enhancedFormData.type && roleType) {\r\n      enhancedFormData.type = roleType;\r\n    }\r\n  \r\n    \r\n    try {\r\n      \r\n      // Log the enhanced form data for debugging\r\n      console.log('Form submission - Enhanced formData:', enhancedFormData);\r\n      console.log('Form API Config:', form.formConfig.submitApiConfig.endpoint);\r\n      \r\n      // Make the actual API call using the form's configuration\r\n      let apiResponse = null;\r\n      \r\n      if (form.formConfig && form.formConfig.submitApiConfig.endpoint) {\r\n        try {\r\n          // Check if this is a regularization form and format request accordingly\r\n          const isRegularizationForm = form.name && form.name.toLowerCase().includes('regular');\r\n          let apiRequest;\r\n          \r\n          console.log('🔍 Form detection:', {\r\n            formName: form.name,\r\n            isRegularizationForm,\r\n            formTitle: form.formTitle || 'Not set'\r\n          });\r\n          \r\n          \r\n          if (apiRequest) {\r\n            const { request, endpoint } = apiRequest;\r\n            console.log('🚀 Making API call to:', endpoint);\r\n            \r\n            // Make the API call\r\n            const response = await fetch(endpoint, request);\r\n            const responseData = await response.text();\r\n            \r\n            // Try to parse as JSON, fallback to text\r\n            let parsedData;\r\n            try {\r\n              parsedData = JSON.parse(responseData);\r\n            } catch {\r\n              parsedData = responseData;\r\n            }\r\n            \r\n            apiResponse = {\r\n              status: response.status,\r\n              statusText: response.statusText,\r\n              data: parsedData,\r\n              success: response.ok\r\n            };\r\n            \r\n            if (response.ok) {\r\n              setApiResponse(apiResponse);\r\n            } else {\r\n              console.log(\"login response ----------- \", response)\r\n              console.log(\"login response ----------- \", apiResponse.data.message)\r\n              setApiError(`${apiResponse.data.message}`);\r\n              // setApiError(`API Error: ${response.status} ${response.statusText} `);\r\n            }\r\n          }\r\n        } catch (apiError) {\r\n          console.error('API call failed:', apiError);\r\n          setApiError(`API call failed: ${apiError.message}`);\r\n          apiResponse = {\r\n            status: 0,\r\n            statusText: 'Network Error',\r\n            data: { error: apiError.message },\r\n            success: false\r\n          };\r\n        }\r\n      }\r\n      \r\n      // Always call onSubmit to update the chat with the result\r\n      if (onSubmit) {\r\n        onSubmit({\r\n          formData: enhancedFormData,\r\n          formId: form._id,\r\n          apiResponse: apiResponse,\r\n          success: apiResponse?.success || false\r\n        });\r\n      }\r\n      \r\n      // Set a default success response if no API was configured\r\n      if (!apiResponse) {\r\n        setApiResponse({\r\n          status: 200,\r\n          statusText: 'OK',\r\n          data: { message: 'Form submitted successfully (no API configured)' }\r\n        });\r\n      }\r\n    } catch (error) {\r\n      console.error('Form submission failed:', error);\r\n      setApiError(error.message || 'Failed to submit form');\r\n      \r\n      // Call onSubmit with error information\r\n      if (onSubmit) {\r\n        onSubmit({\r\n          formData: enhancedFormData || formData,\r\n          formId: form._id,\r\n          error: error.message || 'Failed to submit form',\r\n          success: false\r\n        });\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n  \r\n  if (!form) return null;\r\n  \r\n  return (\r\n    <div className=\"bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden mb-4\">\r\n      <div className=\"bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3\">\r\n        <h3 className=\"text-white font-medium\">{form.name}</h3>\r\n        {form.description && (\r\n          <p className=\"text-blue-100 text-sm mt-1\">{form.description}</p>\r\n        )}\r\n        {/* Show required fields info */}\r\n        {form && form.formConfig && form.formConfig.fields && (\r\n          <div className=\"mt-2 text-blue-100 text-xs\">\r\n            <span className=\"bg-blue-400 bg-opacity-50 px-2 py-1 rounded\">\r\n              Required Fields Only: {form.formConfig.fields.filter(field => field.required).length} of {form.formConfig.fields.length} fields\r\n            </span>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      <div className=\"p-4\">\r\n        {/* Form Fields */}\r\n        <form onSubmit={handleSubmit}>\r\n          <div className=\"space-y-4\">\r\n            {form && form.formConfig && form.formConfig.fields && Array.isArray(form.formConfig.fields) ? \r\n              form.formConfig.fields.filter(field => field.required).length > 0 ? \r\n                form.formConfig.fields.filter(field => field.required).map((field) => (\r\n\r\n              <div key={field.name} className=\"form-field\">\r\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                  {field.label}\r\n                  {field.required && <span className=\"text-red-500 ml-1\">*</span>}\r\n                  {isAutoPopulated(field.name) && (\r\n                    <span className=\"text-blue-500 text-xs ml-2\">(Auto-filled)</span>\r\n                  )}\r\n                </label>\r\n                \r\n                {field.type === 'text' && (\r\n                  <input\r\n                    type=\"text\"\r\n                    name={field.name}\r\n                    value={formData[field.name] || ''}\r\n                    onChange={handleChange}\r\n                    placeholder={field.placeholder}\r\n                    readOnly={field.readonly}\r\n                    className={`w-full p-2 border rounded-md ${\r\n                      errors[field.name] ? 'border-red-500' : \r\n                      field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' :\r\n                      isAutoPopulated(field.name) ? 'border-blue-300 bg-blue-50' : 'border-gray-300'\r\n                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                  />\r\n                )}\r\n                \r\n                {field.type === 'email' && (\r\n                  <input\r\n                    type=\"email\"\r\n                    name={field.name}\r\n                    value={formData[field.name] || ''}\r\n                    onChange={handleChange}\r\n                    placeholder={field.placeholder}\r\n                    readOnly={field.readonly}\r\n                    className={`w-full p-2 border rounded-md ${\r\n                      errors[field.name] ? 'border-red-500' : \r\n                      field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' :\r\n                      'border-gray-300'\r\n                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                  />\r\n                )}\r\n                \r\n                {field.type === 'number' && (\r\n                  <input\r\n                    type=\"number\"\r\n                    name={field.name}\r\n                    value={formData[field.name] || ''}\r\n                    onChange={handleChange}\r\n                    placeholder={field.placeholder}\r\n                    readOnly={field.readonly}\r\n                    className={`w-full p-2 border rounded-md ${\r\n                      errors[field.name] ? 'border-red-500' : \r\n                      field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' :\r\n                      'border-gray-300'\r\n                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                  />\r\n                )}\r\n                \r\n                {field.type === 'textarea' && (\r\n                  <textarea\r\n                    name={field.name}\r\n                    value={formData[field.name] || ''}\r\n                    onChange={handleChange}\r\n                    placeholder={field.placeholder}\r\n                    rows=\"3\"\r\n                    className={`w-full p-2 border rounded-md ${\r\n                      errors[field.name] ? 'border-red-500' : 'border-gray-300'\r\n                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                  />\r\n                )}\r\n                \r\n                {field.type === 'select' && (\r\n                  <select\r\n                    name={field.name}\r\n                    value={formData[field.name] || ''}\r\n                    onChange={handleChange}\r\n                    className={`w-full p-2 border rounded-md ${\r\n                      errors[field.name] ? 'border-red-500' : 'border-gray-300'\r\n                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                  >\r\n                    <option value=\"\">Select an option</option>\r\n                    {field.options && field.options.map((option, index) => (\r\n                      <option key={index} value={option}>\r\n                        {option}\r\n                      </option>\r\n                    ))}\r\n                  </select>\r\n                )}\r\n                \r\n                {field.type === 'checkbox' && (\r\n                  <div className=\"flex items-center\">\r\n                    <input\r\n                      type=\"checkbox\"\r\n                      name={field.name}\r\n                      checked={formData[field.name] || false}\r\n                      onChange={handleChange}\r\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\r\n                    />\r\n                    <span className=\"ml-2 text-sm text-gray-600\">\r\n                      {field.placeholder}\r\n                    </span>\r\n                  </div>\r\n                )}\r\n                \r\n                {field.type === 'radio' && field.options && (\r\n                  <div className=\"space-y-2\">\r\n                    {field.options.map((option, index) => (\r\n                      <div key={index} className=\"flex items-center\">\r\n                        <input\r\n                          type=\"radio\"\r\n                          id={`${field.name}-${index}`}\r\n                          name={field.name}\r\n                          value={option}\r\n                          checked={formData[field.name] === option}\r\n                          onChange={handleChange}\r\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300\"\r\n                        />\r\n                        <label\r\n                          htmlFor={`${field.name}-${index}`}\r\n                          className=\"ml-2 text-sm text-gray-600\"\r\n                        >\r\n                          {option}\r\n                        </label>\r\n                      </div>\r\n                    ))}\r\n                  </div>\r\n                )}\r\n                \r\n                {field.type === 'date' && (\r\n                  <input\r\n                    type=\"date\"\r\n                    name={field.name}\r\n                    value={formData[field.name] || ''}\r\n                    onChange={handleChange}\r\n                    className={`w-full p-2 border rounded-md ${\r\n                      errors[field.name] ? 'border-red-500' : 'border-gray-300'\r\n                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                  />\r\n                )}\r\n                \r\n                {field.type === 'file' && (\r\n                  <input\r\n                    type=\"file\"\r\n                    name={field.name}\r\n                    onChange={handleChange}\r\n                    className={`w-full p-2 border rounded-md ${\r\n                      errors[field.name] ? 'border-red-500' : 'border-gray-300'\r\n                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}\r\n                  />\r\n                )}\r\n                \r\n                {errors[field.name] && (\r\n                  <p className=\"mt-1 text-sm text-red-500\">{errors[field.name]}</p>\r\n                )}\r\n              </div>\r\n            )) : (\r\n              <div className=\"p-4 bg-yellow-50 border border-yellow-200 rounded-md\">\r\n                <p className=\"text-yellow-700 font-medium\">⚠️ No Required Fields</p>\r\n                <p className=\"text-yellow-600 text-sm mt-1\">\r\n                  This form has no required fields to display. All fields are optional.\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"p-4 bg-red-50 border border-red-200 rounded-md\">\r\n                <p className=\"text-red-700 font-medium\">❌ Form Error</p>\r\n                <p className=\"text-red-600 text-sm mt-1\">\r\n                  Form fields are not available. Please contact your administrator.\r\n                </p>\r\n                <p className=\"text-gray-600 text-xs mt-2\">\r\n                  Debug: form={form ? 'exists' : 'null'}, fields={form && form.formConfig && form.formConfig.fields ? 'exists' : 'null'}\r\n                </p>\r\n              </div>\r\n            )}\r\n          </div>\r\n          \r\n          {/* API Response Display */}\r\n          {/* {apiResponse && (\r\n            <div className=\"mt-4 p-3 bg-gray-50 rounded-md border border-gray-200\">\r\n              <h4 className=\"text-sm font-medium text-gray-700 mb-2\">API Response</h4>\r\n              <div className=\"bg-gray-800 text-green-400 p-3 rounded-md text-sm font-mono overflow-auto max-h-60\">\r\n                <pre>{JSON.stringify(apiResponse.data, null, 2)}</pre>\r\n              </div>\r\n            </div>\r\n          )} */}\r\n          \r\n          {/* API Error Display */}\r\n          {apiError && (\r\n            <div className=\"mt-4 p-3 bg-red-50 rounded-md border border-red-200\">\r\n              {/* <h4 className=\"text-sm font-medium text-red-700 mb-1\">Error</h4> */}\r\n              <p className=\"text-sm text-red-600\">{apiError}</p>\r\n            </div>\r\n          )}\r\n          \r\n          {/* Form Linking Buttons */}\r\n          {formLinkingActions.length > 0 && (\r\n            <div className=\"mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4\">\r\n              <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Available Actions</h3>\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                {formLinkingActions.map((action) => (\r\n                  <button\r\n                    key={action.actionIndex}\r\n                    onClick={() => handleFormLink(action)}\r\n                    disabled={isProcessingLink}\r\n                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${\r\n                      getButtonClasses(action.buttonStyle)\r\n                    } ${isProcessingLink ? 'opacity-50 cursor-not-allowed' : ''}`}\r\n                  >\r\n                    {isProcessingLink ? 'Processing...' : action.buttonText}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          )}\r\n          \r\n          {/* Form Actions */}\r\n          <div className=\"mt-4 flex justify-end space-x-2\">\r\n            {onCancel && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={onCancel}\r\n                className=\"px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\r\n              >\r\n                Cancel\r\n              </button>\r\n            )}\r\n            <button\r\n              type=\"submit\"\r\n              disabled={isSubmitting}\r\n              className={`px-3 py-2 rounded-md text-sm font-medium text-white ${\r\n                isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'\r\n              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}\r\n            >\r\n              {isSubmitting ? (\r\n                <span className=\"flex items-center\">\r\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                  </svg>\r\n                  Submitting...\r\n                </span>\r\n              ) : (\r\n                submitButtonText\r\n              )}\r\n            </button>\r\n          </div>\r\n        </form>\r\n      </div>\r\n\r\n      {/* Auto-Trigger Handler */}\r\n      <AutoTriggerHandler\r\n        formId={form._id}\r\n        recordData={formData}\r\n        onFormSubmit={handleAutoTriggerSubmit}\r\n        enabled={form?.formLinking?.enabled && form?.formLinking?.recordActions?.some(action => action.autoTrigger?.enabled)}\r\n      />\r\n\r\n      {/* Linked Form Modal */}\r\n      {showLinkedFormModal && linkedForm && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n            {/* Modal Header */}\r\n            <div className=\"bg-green-500 text-white px-6 py-4 rounded-t-lg\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <h2 className=\"text-lg font-semibold\">\r\n                    🔗 Linked Form\r\n                  </h2>\r\n                  <p className=\"text-sm text-green-100 mt-1\">\r\n                    {linkedForm.name}\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={() => setShowLinkedFormModal(false)}\r\n                  className=\"text-white hover:text-gray-200 focus:outline-none\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Modal Content */}\r\n            <div className=\"p-6\">\r\n              <div className=\"bg-green-50 border border-green-200 rounded-md p-3 mb-4\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                      <path fillRule=\"evenodd\" d=\"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <h3 className=\"text-sm font-medium text-green-800\">Linked Form</h3>\r\n                    <div className=\"mt-1 text-sm text-green-700\">\r\n                      This form was opened through form linking. \r\n                      Some fields may be pre-filled based on the original record.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <ChatFormDisplay\r\n                form={linkedForm}\r\n                onSubmit={async (formId, formData) => {\r\n                  try {\r\n                    await api.post(`/unifiedconfigs/${formId}/submit`, formData);\r\n                    setShowLinkedFormModal(false);\r\n                    setLinkedForm(null);\r\n                  } catch (error) {\r\n                    console.error('Error submitting linked form:', error);\r\n                  }\r\n                }}\r\n                onCancel={() => {\r\n                  setShowLinkedFormModal(false);\r\n                  setLinkedForm(null);\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\n// Helper function to get button classes based on style\r\nconst getButtonClasses = (style) => {\r\n  switch (style) {\r\n    case 'primary':\r\n      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';\r\n    case 'secondary':\r\n      return 'bg-gray-500 text-white hover:bg-gray-600 focus:ring-gray-500';\r\n    case 'success':\r\n      return 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500';\r\n    case 'warning':\r\n      return 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500';\r\n    case 'danger':\r\n      return 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500';\r\n    default:\r\n      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';\r\n  }\r\n};\r\n\r\nexport default ChatFormDisplay;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,GAAG,MAAM,cAAc;;AAE9B;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA;AAGA,MAAMC,eAAe,GAAGA,CAAC;EAAEC,IAAI;EAAEC,QAAQ;EAAEC,QAAQ;EAAEC,gBAAgB,GAAG;AAAS,CAAC,KAAK;EAAAC,EAAA;EAAA,IAAAC,iBAAA,EAAAC,kBAAA,EAAAC,qBAAA;EACrF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACmB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACyB,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAChE,MAAM,CAAC2B,UAAU,EAAEC,aAAa,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAAC6B,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAAC+B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACdgC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE3B,IAAI,CAAC;IACnD0B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE3B,IAAI,IAAI4B,KAAK,CAACC,OAAO,CAAC7B,IAAI,CAAC8B,MAAM,CAAC,CAAC;IACnEJ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE3B,IAAI,IAAIA,IAAI,CAAC8B,MAAM,GAAG9B,IAAI,CAAC8B,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IAE1E,IAAI/B,IAAI,IAAIA,IAAI,CAACgC,UAAU,CAACF,MAAM,IAAIF,KAAK,CAACC,OAAO,CAAC7B,IAAI,CAACgC,UAAU,CAACF,MAAM,CAAC,EAAE;MAC3E,MAAMG,WAAW,GAAG,CAAC,CAAC;;MAEtB;MACA,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MACjE,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;MACjD;MACA,MAAME,cAAc,GAAGxC,IAAI,CAACgC,UAAU,CAACF,MAAM,CAACW,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;MAC7EjB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE3B,IAAI,CAACgC,UAAU,CAACF,MAAM,CAAC;MAChDJ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEa,cAAc,CAAC;MACpDA,cAAc,CAACI,OAAO,CAACF,KAAK,IAAI;QAAA,IAAAG,iBAAA;QAC9B,IAAIC,UAAU,GAAG,EAAE;;QAEnB;QACA,IAAIJ,KAAK,CAACK,IAAI,KAAK,OAAO,IAAIb,QAAQ,CAACc,KAAK,EAAE;UAC5CF,UAAU,GAAGZ,QAAQ,CAACc,KAAK;QAC7B,CAAC,MAAM,IAAIN,KAAK,CAACK,IAAI,KAAK,MAAM,EAAE;UAChC;UACAD,UAAU,GAAG9C,IAAI,CAACiD,WAAW,IAAIjD,IAAI,CAACiD,WAAW,CAACP,KAAK,CAACK,IAAI,CAAC,GACzD/C,IAAI,CAACiD,WAAW,CAACP,KAAK,CAACK,IAAI,CAAC,GAC5BR,QAAQ,IAAIG,KAAK,CAACQ,YAAY,IAAIR,KAAK,CAACS,KAAK,IAAI,EAAE;QACzD,CAAC,MAAM,IAAIT,KAAK,CAACK,IAAI,KAAK,WAAW,EAAE;UACrC;UACA,MAAMK,cAAc,GAAGf,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;UAC7DQ,UAAU,GAAGM,cAAc,IAAIV,KAAK,CAACQ,YAAY,IAAIR,KAAK,CAACS,KAAK,IAAI,EAAE;UACtEzB,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE;YAC9CyB,cAAc,EAAEA,cAAc;YAC9BF,YAAY,EAAER,KAAK,CAACQ,YAAY;YAChCC,KAAK,EAAET,KAAK,CAACS,KAAK;YAClBE,UAAU,EAAEP;UACd,CAAC,CAAC;QACJ,CAAC,MAAM;UACL;UACAA,UAAU,GAAG9C,IAAI,CAACiD,WAAW,IAAIjD,IAAI,CAACiD,WAAW,CAACP,KAAK,CAACK,IAAI,CAAC,GACzD/C,IAAI,CAACiD,WAAW,CAACP,KAAK,CAACK,IAAI,CAAC,GAC3BL,KAAK,CAACQ,YAAY,KAAKI,SAAS,IAAIZ,KAAK,CAACQ,YAAY,KAAK,IAAI,IAAIR,KAAK,CAACQ,YAAY,KAAK,EAAE,GAC7FR,KAAK,CAACQ,YAAY,GAClB,EAAE;QACR;QAEAxB,OAAO,CAACC,GAAG,CAAC,aAAae,KAAK,CAACK,IAAI,mBAAmB,EAAE;UACtDE,WAAW,GAAAJ,iBAAA,GAAE7C,IAAI,CAACiD,WAAW,cAAAJ,iBAAA,uBAAhBA,iBAAA,CAAmBH,KAAK,CAACK,IAAI,CAAC;UAC3CG,YAAY,EAAER,KAAK,CAACQ,YAAY;UAChCC,KAAK,EAAET,KAAK,CAACS,KAAK;UAClBI,eAAe,EAAET;QACnB,CAAC,CAAC;QAEFb,WAAW,CAACS,KAAK,CAACK,IAAI,CAAC,GAAGD,UAAU;MAEtC,CAAC,CAAC;MACFrC,WAAW,CAACwB,WAAW,CAAC;IAC1B;EACF,CAAC,EAAE,CAACjC,IAAI,CAAC,CAAC;;EAEV;EACAN,SAAS,CAAC,MAAM;IACd,IAAI,CAACM,IAAI,IAAI,CAACQ,QAAQ,EAAE;IAExB,MAAMgD,gBAAgB,GAAGA,CAAA,KAAM;MAAA,IAAAC,gBAAA,EAAAC,qBAAA;MAC7B,MAAMC,iBAAiB,IAAAF,gBAAA,GAAGzD,IAAI,CAACgC,UAAU,cAAAyB,gBAAA,uBAAfA,gBAAA,CAAiBG,WAAW;MACtD,IAAI,EAACD,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEE,OAAO,KAAI,GAAAH,qBAAA,GAACC,iBAAiB,CAACG,aAAa,cAAAJ,qBAAA,eAA/BA,qBAAA,CAAiC3B,MAAM,GAAE;QAC3E;MACF;MAEA,MAAMgC,gBAAgB,GAAGJ,iBAAiB,CAACG,aAAa,CAACE,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,MAAM;QAC/E,GAAGD,MAAM;QACTE,WAAW,EAAED,KAAK;QAClBE,UAAU,EAAEC,gBAAgB,CAAC7D,QAAQ,EAAEyD,MAAM,CAACK,UAAU;MAC1D,CAAC,CAAC,CAAC,CAAC7B,MAAM,CAACwB,MAAM,IAAIA,MAAM,CAACG,UAAU,CAAC;MAEvCjD,qBAAqB,CAAC4C,gBAAgB,CAAC;IACzC,CAAC;IAEDP,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACxD,IAAI,EAAEQ,QAAQ,CAAC,CAAC;;EAEpB;EACA,MAAM6D,gBAAgB,GAAGA,CAACE,MAAM,EAAED,UAAU,GAAG,EAAE,KAAK;IACpD,IAAI,CAACA,UAAU,IAAIA,UAAU,CAACvC,MAAM,KAAK,CAAC,EAAE;MAC1C,OAAO,IAAI;IACb;IAEA,OAAOuC,UAAU,CAACE,KAAK,CAACC,SAAS,IAAI;MACnC,MAAM3B,UAAU,GAAGyB,MAAM,CAACE,SAAS,CAAC/B,KAAK,CAAC;MAC1C,MAAMgC,cAAc,GAAGD,SAAS,CAACtB,KAAK;MAEtC,QAAQsB,SAAS,CAACE,QAAQ;QACxB,KAAK,QAAQ;UACX,OAAO7B,UAAU,KAAK4B,cAAc;QACtC,KAAK,YAAY;UACf,OAAO5B,UAAU,KAAK4B,cAAc;QACtC,KAAK,UAAU;UACb,OAAO5B,UAAU,IAAIA,UAAU,CAAC8B,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACH,cAAc,CAAC;QACrE,KAAK,cAAc;UACjB,OAAO,CAAC5B,UAAU,IAAI,CAACA,UAAU,CAAC8B,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAACH,cAAc,CAAC;QACvE,KAAK,QAAQ;UACX,OAAO5B,UAAU,KAAKQ,SAAS,IAAIR,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,EAAE;QAC7E,KAAK,YAAY;UACf,OAAOA,UAAU,KAAKQ,SAAS,IAAIR,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,EAAE;QAC7E;UACE,OAAO,IAAI;MACf;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgC,cAAc,GAAG,MAAOb,MAAM,IAAK;IACvCxC,mBAAmB,CAAC,IAAI,CAAC;IACzBR,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI;MACF,MAAM8D,QAAQ,GAAG,MAAMnF,GAAG,CAACoF,IAAI,CAAC,mBAAmBhF,IAAI,CAACiF,GAAG,YAAY,EAAE;QACvEC,UAAU,EAAE1E,QAAQ;QACpB2E,UAAU,EAAE,CAAC,CAAC;QACdhB,WAAW,EAAEF,MAAM,CAACE;MACtB,CAAC,CAAC;MAEF,IAAIY,QAAQ,CAACK,IAAI,CAACC,OAAO,EAAE;QACzBhE,aAAa,CAAC;UACZ,GAAG0D,QAAQ,CAACK,IAAI,CAACE,UAAU;UAC3BrC,WAAW,EAAE8B,QAAQ,CAACK,IAAI,CAACnC,WAAW;UACtCsC,UAAU,EAAER,QAAQ,CAACK,IAAI,CAACG,UAAU;UACpCC,mBAAmB,EAAE;QACvB,CAAC,CAAC;QACFjE,sBAAsB,CAAC,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLN,WAAW,CAAC8D,QAAQ,CAACK,IAAI,CAACK,OAAO,IAAI,gCAAgC,CAAC;MACxE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtDzE,WAAW,CAAC,gCAAgC,CAAC;IAC/C,CAAC,SAAS;MACRQ,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMkE,uBAAuB,GAAG,MAAAA,CAAOC,MAAM,EAAEpF,QAAQ,EAAEqF,YAAY,KAAK;IACxEnE,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE;MAAEiE,MAAM;MAAEpF,QAAQ;MAAEqF;IAAa,CAAC,CAAC;IACjF;EACF,CAAC;;EAED;EACA,MAAMC,eAAe,GAAIC,SAAS,IAAK;IACrC,MAAM7D,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACjE,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IACjD,MAAMc,cAAc,GAAGf,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAE7D,OAAQyD,SAAS,KAAK,OAAO,IAAI7D,QAAQ,CAACc,KAAK,IACvC+C,SAAS,KAAK,MAAM,IAAIxD,QAAS,IACjCwD,SAAS,KAAK,WAAW,IAAI3C,cAAe;EACtD,CAAC;;EAED;EACA,MAAM4C,YAAY,GAAIC,CAAC,IAAK;IAC1B,MAAM;MAAElD,IAAI;MAAEI,KAAK;MAAE+C,IAAI;MAAEC,OAAO;MAAEC;IAAM,CAAC,GAAGH,CAAC,CAACI,MAAM;IAEtD,IAAIvD,UAAU;IACd,IAAIoD,IAAI,KAAK,UAAU,EAAE;MACvBpD,UAAU,GAAGqD,OAAO;IACtB,CAAC,MAAM,IAAID,IAAI,KAAK,MAAM,EAAE;MAC1B;MACApD,UAAU,GAAGsD,KAAK,IAAIA,KAAK,CAACrE,MAAM,GAAG,CAAC,GAAGqE,KAAK,CAAC,CAAC,CAAC,CAACrD,IAAI,GAAG,EAAE;IAC7D,CAAC,MAAM;MACLD,UAAU,GAAGK,KAAK;IACpB;IAEA1C,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACuC,IAAI,GAAGD;IACV,CAAC,CAAC;;IAEF;IACA,IAAIpC,MAAM,CAACqC,IAAI,CAAC,EAAE;MAChBpC,SAAS,CAAC;QACR,GAAGD,MAAM;QACT,CAACqC,IAAI,GAAG;MACV,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMuD,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAIvG,IAAI,IAAIA,IAAI,CAACgC,UAAU,IAAIhC,IAAI,CAACgC,UAAU,CAACF,MAAM,EAAE;MACrD;MACA,MAAMU,cAAc,GAAGxC,IAAI,CAACgC,UAAU,CAACF,MAAM,CAACW,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC;MAC7EH,cAAc,CAACI,OAAO,CAACF,KAAK,IAAI;QAC9B;QACA,IAAIA,KAAK,CAACC,QAAQ,IAAI,CAACnC,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,EAAE;UAC3CwD,SAAS,CAAC7D,KAAK,CAACK,IAAI,CAAC,GAAG,GAAGL,KAAK,CAAC8D,KAAK,cAAc;QACtD;;QAEA;QACA,IAAI9D,KAAK,CAACwD,IAAI,KAAK,OAAO,IAAI1F,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAC9C,CAAC,0CAA0C,CAAC0D,IAAI,CAACjG,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,CAAC,EAAE;UAC1EwD,SAAS,CAAC7D,KAAK,CAACK,IAAI,CAAC,GAAG,uBAAuB;QACjD;MACF,CAAC,CAAC;IACJ;IAEA,OAAOwD,SAAS;EAClB,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAGA,CAACC,GAAG,EAAEvB,IAAI,GAAG5E,QAAQ,KAAK;IACpD,IAAI,CAACmG,GAAG,EAAE,OAAOA,GAAG;;IAEpB;IACA,MAAMzE,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACjE,MAAMsE,SAAS,GAAG1E,QAAQ,CAAC2E,KAAK;;IAEhC;IACAnF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAAC;IAC7CR,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEiF,SAAS,CAAC;IAC/ClF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE,OAAOiF,SAAS,CAAC;IAC3DlF,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEiF,SAAS,GAAGA,SAAS,CAAC7E,MAAM,GAAG,KAAK,CAAC;;IAEjF;IACA,MAAM+E,YAAY,GAAG;MACnB,GAAG1B,IAAI;MACPyB,KAAK,EAAED,SAAS,IAAIA,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,EAAE;MAC5DA,SAAS,EAAEA,SAAS,IAAIA,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,EAAE;MAChEG,WAAW,EAAEH,SAAS,IAAIA,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,EAAE;MAClE5D,KAAK,EAAEd,QAAQ,CAACc,KAAK,IAAI,EAAE;MAC3BgE,MAAM,EAAE9E,QAAQ,CAACc,KAAK,IAAId,QAAQ,CAAC+E,EAAE,IAAI,EAAE;MAC3C;MACAC,YAAY,EAAE7E,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE;MACxDc,cAAc,EAAEf,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE;MAC5D6E,UAAU,EAAE9E,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE;MACpD8E,YAAY,EAAE/E,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE;MACxDC,QAAQ,EAAEF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAI;IAChD,CAAC;IAEDZ,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;MAC3D0F,qBAAqB,EAAEC,MAAM,CAACC,IAAI,CAACT,YAAY,CAAC;MAChDU,cAAc,EAAEb;IAClB,CAAC,CAAC;IAEF,MAAMc,MAAM,GAAGd,GAAG,CAACe,OAAO,CAAC,cAAc,EAAE,CAACC,KAAK,EAAE5B,SAAS,KAAK;MAC/D,MAAM6B,WAAW,GAAGd,YAAY,CAACf,SAAS,CAAC;MAC3C,IAAI6B,WAAW,KAAKtE,SAAS,IAAIsE,WAAW,KAAK,EAAE,EAAE;QACnDlG,OAAO,CAACC,GAAG,CAAC,gBAAgBoE,SAAS,WAAWA,SAAS,CAAClB,QAAQ,CAAC,OAAO,CAAC,GAAG+C,WAAW,CAACC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAGD,WAAW,EAAE,CAAC;QACnI,OAAOA,WAAW;MACpB;MACAlG,OAAO,CAACoG,IAAI,CAAC,mBAAmB/B,SAAS,qBAAqB,CAAC;MAC/D,OAAO4B,KAAK;IACd,CAAC,CAAC;IAEFjG,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE8F,MAAM,CAAC;IACrE,OAAOA,MAAM;EACf,CAAC;;EAED;EACA,MAAMM,iBAAiB,GAAGA,CAAC3C,IAAI,GAAG5E,QAAQ,KAAK;IAC7C,IAAI,CAACR,IAAI,IAAI,CAACA,IAAI,CAACgI,SAAS,EAAE,OAAO,IAAI;IAEzC,MAAM;MAAEC,MAAM;MAAEC,QAAQ;MAAEC,OAAO;MAAEC,QAAQ;MAAEC,WAAW,GAAG,CAAC;IAAE,CAAC,GAAGrI,IAAI,CAACgI,SAAS;;IAEhF;IACA,MAAMM,OAAO,GAAG;MACdL,MAAM,EAAEA,MAAM,IAAI,KAAK;MACvBE,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CI,IAAI,EAAEN,MAAM,KAAK,KAAK,GAAG9F,IAAI,CAACqG,SAAS,CAACpD,IAAI,CAAC,GAAG9B;IAClD,CAAC;;IAED;IACA,IAAI6E,OAAO,IAAIb,MAAM,CAACC,IAAI,CAACY,OAAO,CAAC,CAACpG,MAAM,GAAG,CAAC,EAAE;MAC9CuF,MAAM,CAACmB,OAAO,CAACN,OAAO,CAAC,CAACvF,OAAO,CAAC,CAAC,CAAC8F,GAAG,EAAEvF,KAAK,CAAC,KAAK;QAChDmF,OAAO,CAACH,OAAO,CAACO,GAAG,CAAC,GAAGhC,mBAAmB,CAACvD,KAAK,EAAEiC,IAAI,CAAC;MACzD,CAAC,CAAC;IACJ;;IAEA;IACA,IAAIgD,QAAQ,IAAIA,QAAQ,KAAK,MAAM,EAAE;MACnC,QAAQA,QAAQ;QACd,KAAK,OAAO;UACV,IAAIC,WAAW,CAACM,QAAQ,IAAIN,WAAW,CAACO,QAAQ,EAAE;YAChD,MAAMC,WAAW,GAAGC,IAAI,CAAC,GAAGpC,mBAAmB,CAAC2B,WAAW,CAACM,QAAQ,EAAEvD,IAAI,CAAC,IAAIsB,mBAAmB,CAAC2B,WAAW,CAACO,QAAQ,EAAExD,IAAI,CAAC,EAAE,CAAC;YACjIkD,OAAO,CAACH,OAAO,CAAC,eAAe,CAAC,GAAG,SAASU,WAAW,EAAE;UAC3D;UACA;QACF,KAAK,QAAQ;UACX;UACA,IAAI9B,WAAW,GAAGsB,WAAW,CAACxB,KAAK;;UAEnC;UACA,IAAI,CAACE,WAAW,EAAE;YAChB,MAAM7E,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;YACjEyE,WAAW,GAAG7E,QAAQ,CAAC2E,KAAK;UAC9B;UAEA,IAAIE,WAAW,IAAIA,WAAW,KAAK,SAAS,EAAE;YAC5CuB,OAAO,CAACH,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUzB,mBAAmB,CAACK,WAAW,EAAE3B,IAAI,CAAC,EAAE;UACvF;UACA;QACF,KAAK,QAAQ;UACX,IAAIiD,WAAW,CAACK,GAAG,IAAIL,WAAW,CAAClF,KAAK,EAAE;YACxC,IAAIkF,WAAW,CAACU,EAAE,KAAK,QAAQ,EAAE;cAC/BT,OAAO,CAACH,OAAO,CAACE,WAAW,CAACK,GAAG,CAAC,GAAGhC,mBAAmB,CAAC2B,WAAW,CAAClF,KAAK,EAAEiC,IAAI,CAAC;YACjF,CAAC,MAAM,IAAIiD,WAAW,CAACU,EAAE,KAAK,OAAO,EAAE;cACrC;YAAA;UAEJ;UACA;QACF;UACE;MACJ;IACF;;IAEA;IACA,IAAI,CAACX,QAAQ,IAAIA,QAAQ,KAAK,MAAM,EAAE;MACpC,MAAMlG,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MACjE,MAAMuE,KAAK,GAAG3E,QAAQ,CAAC2E,KAAK;MAC5B,IAAIA,KAAK,IAAIA,KAAK,KAAK,SAAS,IAAI,CAACyB,OAAO,CAACH,OAAO,CAAC,eAAe,CAAC,EAAE;QACrEG,OAAO,CAACH,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUtB,KAAK,EAAE;QACpDnF,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACzD;IACF;;IAEA;IACA,IAAIqH,iBAAiB,GAAGtC,mBAAmB,CAACwB,QAAQ,EAAE9C,IAAI,CAAC;;IAE3D;IACA,IAAI6C,MAAM,KAAK,KAAK,IAAI7C,IAAI,IAAIkC,MAAM,CAACC,IAAI,CAACnC,IAAI,CAAC,CAACrD,MAAM,GAAG,CAAC,EAAE;MAC5D,MAAMkH,WAAW,GAAG,IAAIC,eAAe,CAAC,CAAC;MACzC5B,MAAM,CAACmB,OAAO,CAACrD,IAAI,CAAC,CAACxC,OAAO,CAAC,CAAC,CAAC8F,GAAG,EAAEvF,KAAK,CAAC,KAAK;QAC7C,IAAIA,KAAK,EAAE8F,WAAW,CAACE,MAAM,CAACT,GAAG,EAAEvF,KAAK,CAAC;MAC3C,CAAC,CAAC;;MAEF;MACA,IAAIiF,QAAQ,KAAK,QAAQ,IAAIC,WAAW,CAACU,EAAE,KAAK,OAAO,EAAE;QACvDE,WAAW,CAACE,MAAM,CAACd,WAAW,CAACK,GAAG,EAAEhC,mBAAmB,CAAC2B,WAAW,CAAClF,KAAK,EAAEiC,IAAI,CAAC,CAAC;MACnF;MAEA,MAAMgE,WAAW,GAAGH,WAAW,CAACrE,QAAQ,CAAC,CAAC;MAC1C,IAAIwE,WAAW,EAAE;QACfJ,iBAAiB,IAAIA,iBAAiB,CAACnE,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG;QAChEmE,iBAAiB,IAAII,WAAW;MAClC;IACF;IAEA,OAAO;MAAEd,OAAO;MAAEJ,QAAQ,EAAEc;IAAkB,CAAC;EACjD,CAAC;;EAED;EACA,MAAMK,+BAA+B,GAAGA,CAACjE,IAAI,EAAEpD,UAAU,KAAK;IAC5D,IAAI,CAACA,UAAU,IAAI,CAACA,UAAU,CAACgG,SAAS,EAAE,OAAO,IAAI;IAErD,MAAM;MAAEC,MAAM;MAAEC,QAAQ;MAAEC,OAAO;MAAEC,QAAQ;MAAEC,WAAW,GAAG,CAAC;IAAE,CAAC,GAAGrG,UAAU,CAACgG,SAAS;;IAEtF;IACA,MAAMsB,UAAU,GAAIC,SAAS,IAAK;MAChC,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;;MAEzB;MACA,IAAI,qBAAqB,CAAC9C,IAAI,CAAC8C,SAAS,CAAC,EAAE;QACzC,OAAOA,SAAS;MAClB;;MAEA;MACA,IAAI;QACF,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;QAChC,IAAIG,KAAK,CAACF,IAAI,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAOJ,SAAS,CAAC,CAAC;;QAE7C;QACA,OAAOC,IAAI,CAACI,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACzC,CAAC,CAAC,OAAOnE,KAAK,EAAE;QACdhE,OAAO,CAACoG,IAAI,CAAC,wBAAwB,EAAEpC,KAAK,CAAC;QAC7C,OAAO6D,SAAS,CAAC,CAAC;MACpB;IACF,CAAC;;IAED;IACA,MAAMO,yBAAyB,GAAG;MAChCC,kBAAkB,EAAE,CAClB;QACEP,IAAI,EAAEF,UAAU,CAAClE,IAAI,CAACoE,IAAI,IAAIpE,IAAI,CAAC4E,cAAc,CAAC;QAClDC,MAAM,EAAE7E,IAAI,CAAC6E,MAAM,IAAI7E,IAAI,CAAC8E,uBAAuB,IAAI,EAAE;QACzDC,MAAM,EAAE/E,IAAI,CAAC+E,MAAM,IAAI/E,IAAI,CAACgF,OAAO,IAAI,EAAE;QACzCC,OAAO,EAAEjF,IAAI,CAACiF,OAAO,IAAIjF,IAAI,CAACkF,QAAQ,IAAI;MAC5C,CAAC,CACF;MACDC,SAAS,EAAElI,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE;MACrDkI,MAAM,EAAEpF,IAAI,CAACoF,MAAM,IAAIpF,IAAI,CAACqF,OAAO,IAAIrF,IAAI,CAACsF,iBAAiB,IAAI;IACnE,CAAC;IAEDhJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEmI,yBAAyB,CAAC;IAElF,MAAM5H,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACjE,MAAMsE,SAAS,GAAG1E,QAAQ,CAAC2E,KAAK;;IAEhC;IACAnF,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEO,QAAQ,CAAC;IACjER,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEiF,SAAS,CAAC;IAC9DlF,OAAO,CAACC,GAAG,CAAC,qDAAqD,EAAEiF,SAAS,KAAK,SAAS,CAAC;IAC3FlF,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEiF,SAAS,IAAIA,SAAS,KAAK,SAAS,IAAIA,SAAS,CAAC7E,MAAM,GAAG,EAAE,CAAC;IAC3H;IACA,MAAM4I,UAAU,GAAG/D,SAAS,IAAIA,SAAS,KAAK,SAAS,GAAGA,SAAS,GAAG,IAAI;IAE1E,IAAI,CAAC+D,UAAU,EAAE;MACfjJ,OAAO,CAACgE,KAAK,CAAC,wCAAwC,CAAC;MACvDhE,OAAO,CAACgE,KAAK,CAAC,aAAa,EAAExD,QAAQ,CAAC;MACtCR,OAAO,CAACgE,KAAK,CAAC,cAAc,EAAEkB,SAAS,CAAC;MACxC,MAAM,IAAIgE,KAAK,CAAC,kEAAkE,CAAC;IACrF;IAEA,MAAMtC,OAAO,GAAG;MACdL,MAAM,EAAEA,MAAM,IAAI,MAAM;MACxBE,OAAO,EAAE;QACP,eAAe,EAAE,UAAUwC,UAAU,EAAE;QACvC,cAAc,EAAE;MAClB,CAAC;MACDpC,IAAI,EAAEpG,IAAI,CAACqG,SAAS,CAACsB,yBAAyB;IAChD,CAAC;IAED,IAAId,iBAAiB,GAAGtC,mBAAmB,CAACwB,QAAQ,EAAE9C,IAAI,CAAC;IAE3D,OAAO;MAAEkD,OAAO;MAAEJ,QAAQ,EAAEc;IAAkB,CAAC;EACjD,CAAC;;EAED;EACA,MAAM6B,YAAY,GAAG,MAAO5E,CAAC,IAAK;IAChCA,CAAC,CAAC6E,cAAc,CAAC,CAAC;;IAElB;IACA,MAAMC,gBAAgB,GAAGzE,YAAY,CAAC,CAAC;IACvC,IAAIgB,MAAM,CAACC,IAAI,CAACwD,gBAAgB,CAAC,CAAChJ,MAAM,GAAG,CAAC,EAAE;MAC5CpB,SAAS,CAACoK,gBAAgB,CAAC;MAC3B;IACF;IAEAlK,eAAe,CAAC,IAAI,CAAC;IACrBE,cAAc,CAAC,IAAI,CAAC;IACpBE,WAAW,CAAC,IAAI,CAAC;;IAEjB;IACA,MAAMiB,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACjE,MAAMC,QAAQ,GAAGF,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC;IAEjD,IAAI0I,gBAAgB,GAAG;MAAE,GAAGxK;IAAS,CAAC;;IAEtC;IACA,IAAIR,IAAI,CAAC+C,IAAI,CAACkI,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAChI,KAAK,IAAId,QAAQ,CAACc,KAAK,EAAE;MAC1FgI,gBAAgB,CAAChI,KAAK,GAAGd,QAAQ,CAACc,KAAK;IACzC;;IAEA;IACA,IAAIhD,IAAI,CAAC+C,IAAI,CAACkI,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACmG,gBAAgB,CAAC9E,IAAI,IAAI3D,QAAQ,EAAE;MACnFyI,gBAAgB,CAAC9E,IAAI,GAAG3D,QAAQ;IAClC;IAGA,IAAI;MAEF;MACAb,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEqJ,gBAAgB,CAAC;MACrEtJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE3B,IAAI,CAACgC,UAAU,CAACkJ,eAAe,CAAChD,QAAQ,CAAC;;MAEzE;MACA,IAAIpH,WAAW,GAAG,IAAI;MAEtB,IAAId,IAAI,CAACgC,UAAU,IAAIhC,IAAI,CAACgC,UAAU,CAACkJ,eAAe,CAAChD,QAAQ,EAAE;QAC/D,IAAI;UACF;UACA,MAAMiD,oBAAoB,GAAGnL,IAAI,CAAC+C,IAAI,IAAI/C,IAAI,CAAC+C,IAAI,CAACkI,WAAW,CAAC,CAAC,CAACpG,QAAQ,CAAC,SAAS,CAAC;UACrF,IAAIuG,UAAU;UAEd1J,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE;YAChC0J,QAAQ,EAAErL,IAAI,CAAC+C,IAAI;YACnBoI,oBAAoB;YACpBG,SAAS,EAAEtL,IAAI,CAACsL,SAAS,IAAI;UAC/B,CAAC,CAAC;UAGF,IAAIF,UAAU,EAAE;YACd,MAAM;cAAE9C,OAAO;cAAEJ;YAAS,CAAC,GAAGkD,UAAU;YACxC1J,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEuG,QAAQ,CAAC;;YAE/C;YACA,MAAMnD,QAAQ,GAAG,MAAMwG,KAAK,CAACrD,QAAQ,EAAEI,OAAO,CAAC;YAC/C,MAAMzC,YAAY,GAAG,MAAMd,QAAQ,CAACyG,IAAI,CAAC,CAAC;;YAE1C;YACA,IAAIC,UAAU;YACd,IAAI;cACFA,UAAU,GAAGtJ,IAAI,CAACC,KAAK,CAACyD,YAAY,CAAC;YACvC,CAAC,CAAC,MAAM;cACN4F,UAAU,GAAG5F,YAAY;YAC3B;YAEA/E,WAAW,GAAG;cACZ4K,MAAM,EAAE3G,QAAQ,CAAC2G,MAAM;cACvBC,UAAU,EAAE5G,QAAQ,CAAC4G,UAAU;cAC/BvG,IAAI,EAAEqG,UAAU;cAChBpG,OAAO,EAAEN,QAAQ,CAAC6G;YACpB,CAAC;YAED,IAAI7G,QAAQ,CAAC6G,EAAE,EAAE;cACf7K,cAAc,CAACD,WAAW,CAAC;YAC7B,CAAC,MAAM;cACLY,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEoD,QAAQ,CAAC;cACpDrD,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEb,WAAW,CAACsE,IAAI,CAACK,OAAO,CAAC;cACpExE,WAAW,CAAC,GAAGH,WAAW,CAACsE,IAAI,CAACK,OAAO,EAAE,CAAC;cAC1C;YACF;UACF;QACF,CAAC,CAAC,OAAOzE,QAAQ,EAAE;UACjBU,OAAO,CAACgE,KAAK,CAAC,kBAAkB,EAAE1E,QAAQ,CAAC;UAC3CC,WAAW,CAAC,oBAAoBD,QAAQ,CAACyE,OAAO,EAAE,CAAC;UACnD3E,WAAW,GAAG;YACZ4K,MAAM,EAAE,CAAC;YACTC,UAAU,EAAE,eAAe;YAC3BvG,IAAI,EAAE;cAAEM,KAAK,EAAE1E,QAAQ,CAACyE;YAAQ,CAAC;YACjCJ,OAAO,EAAE;UACX,CAAC;QACH;MACF;;MAEA;MACA,IAAIpF,QAAQ,EAAE;QAAA,IAAA4L,YAAA;QACZ5L,QAAQ,CAAC;UACPO,QAAQ,EAAEwK,gBAAgB;UAC1BpF,MAAM,EAAE5F,IAAI,CAACiF,GAAG;UAChBnE,WAAW,EAAEA,WAAW;UACxBuE,OAAO,EAAE,EAAAwG,YAAA,GAAA/K,WAAW,cAAA+K,YAAA,uBAAXA,YAAA,CAAaxG,OAAO,KAAI;QACnC,CAAC,CAAC;MACJ;;MAEA;MACA,IAAI,CAACvE,WAAW,EAAE;QAChBC,cAAc,CAAC;UACb2K,MAAM,EAAE,GAAG;UACXC,UAAU,EAAE,IAAI;UAChBvG,IAAI,EAAE;YAAEK,OAAO,EAAE;UAAkD;QACrE,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdhE,OAAO,CAACgE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CzE,WAAW,CAACyE,KAAK,CAACD,OAAO,IAAI,uBAAuB,CAAC;;MAErD;MACA,IAAIxF,QAAQ,EAAE;QACZA,QAAQ,CAAC;UACPO,QAAQ,EAAEwK,gBAAgB,IAAIxK,QAAQ;UACtCoF,MAAM,EAAE5F,IAAI,CAACiF,GAAG;UAChBS,KAAK,EAAEA,KAAK,CAACD,OAAO,IAAI,uBAAuB;UAC/CJ,OAAO,EAAE;QACX,CAAC,CAAC;MACJ;IACF,CAAC,SAAS;MACRxE,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;EAED,IAAI,CAACb,IAAI,EAAE,OAAO,IAAI;EAEtB,oBACEF,OAAA;IAAKgM,SAAS,EAAC,2EAA2E;IAAAC,QAAA,gBACxFjM,OAAA;MAAKgM,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEjM,OAAA;QAAIgM,SAAS,EAAC,wBAAwB;QAAAC,QAAA,EAAE/L,IAAI,CAAC+C;MAAI;QAAAiJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACtDnM,IAAI,CAACoM,WAAW,iBACftM,OAAA;QAAGgM,SAAS,EAAC,4BAA4B;QAAAC,QAAA,EAAE/L,IAAI,CAACoM;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAChE,EAEAnM,IAAI,IAAIA,IAAI,CAACgC,UAAU,IAAIhC,IAAI,CAACgC,UAAU,CAACF,MAAM,iBAChDhC,OAAA;QAAKgM,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCjM,OAAA;UAAMgM,SAAS,EAAC,6CAA6C;UAAAC,QAAA,GAAC,wBACtC,EAAC/L,IAAI,CAACgC,UAAU,CAACF,MAAM,CAACW,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAACZ,MAAM,EAAC,MAAI,EAAC/B,IAAI,CAACgC,UAAU,CAACF,MAAM,CAACC,MAAM,EAAC,SAC1H;QAAA;UAAAiK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENrM,OAAA;MAAKgM,SAAS,EAAC,KAAK;MAAAC,QAAA,eAElBjM,OAAA;QAAMG,QAAQ,EAAE4K,YAAa;QAAAkB,QAAA,gBAC3BjM,OAAA;UAAKgM,SAAS,EAAC,WAAW;UAAAC,QAAA,EACvB/L,IAAI,IAAIA,IAAI,CAACgC,UAAU,IAAIhC,IAAI,CAACgC,UAAU,CAACF,MAAM,IAAIF,KAAK,CAACC,OAAO,CAAC7B,IAAI,CAACgC,UAAU,CAACF,MAAM,CAAC,GACzF9B,IAAI,CAACgC,UAAU,CAACF,MAAM,CAACW,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAACZ,MAAM,GAAG,CAAC,GAC/D/B,IAAI,CAACgC,UAAU,CAACF,MAAM,CAACW,MAAM,CAACC,KAAK,IAAIA,KAAK,CAACC,QAAQ,CAAC,CAACqB,GAAG,CAAEtB,KAAK,iBAEnE5C,OAAA;YAAsBgM,SAAS,EAAC,YAAY;YAAAC,QAAA,gBAC1CjM,OAAA;cAAOgM,SAAS,EAAC,8CAA8C;cAAAC,QAAA,GAC5DrJ,KAAK,CAAC8D,KAAK,EACX9D,KAAK,CAACC,QAAQ,iBAAI7C,OAAA;gBAAMgM,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC9DrG,eAAe,CAACpD,KAAK,CAACK,IAAI,CAAC,iBAC1BjD,OAAA;gBAAMgM,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACjE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,EAEPzJ,KAAK,CAACwD,IAAI,KAAK,MAAM,iBACpBpG,OAAA;cACEoG,IAAI,EAAC,MAAM;cACXnD,IAAI,EAAEL,KAAK,CAACK,IAAK;cACjBI,KAAK,EAAE3C,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAAI,EAAG;cAClCsJ,QAAQ,EAAErG,YAAa;cACvBsG,WAAW,EAAE5J,KAAK,CAAC4J,WAAY;cAC/BC,QAAQ,EAAE7J,KAAK,CAAC8J,QAAS;cACzBV,SAAS,EAAE,gCACTpL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,GAAG,gBAAgB,GACrCL,KAAK,CAAC8J,QAAQ,GAAG,8DAA8D,GAC/E1G,eAAe,CAACpD,KAAK,CAACK,IAAI,CAAC,GAAG,4BAA4B,GAAG,iBAAiB;YACzB;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACF,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,OAAO,iBACrBpG,OAAA;cACEoG,IAAI,EAAC,OAAO;cACZnD,IAAI,EAAEL,KAAK,CAACK,IAAK;cACjBI,KAAK,EAAE3C,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAAI,EAAG;cAClCsJ,QAAQ,EAAErG,YAAa;cACvBsG,WAAW,EAAE5J,KAAK,CAAC4J,WAAY;cAC/BC,QAAQ,EAAE7J,KAAK,CAAC8J,QAAS;cACzBV,SAAS,EAAE,gCACTpL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,GAAG,gBAAgB,GACrCL,KAAK,CAAC8J,QAAQ,GAAG,8DAA8D,GAC/E,iBAAiB;YACoC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACF,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,QAAQ,iBACtBpG,OAAA;cACEoG,IAAI,EAAC,QAAQ;cACbnD,IAAI,EAAEL,KAAK,CAACK,IAAK;cACjBI,KAAK,EAAE3C,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAAI,EAAG;cAClCsJ,QAAQ,EAAErG,YAAa;cACvBsG,WAAW,EAAE5J,KAAK,CAAC4J,WAAY;cAC/BC,QAAQ,EAAE7J,KAAK,CAAC8J,QAAS;cACzBV,SAAS,EAAE,gCACTpL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,GAAG,gBAAgB,GACrCL,KAAK,CAAC8J,QAAQ,GAAG,8DAA8D,GAC/E,iBAAiB;YACoC;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACF,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,UAAU,iBACxBpG,OAAA;cACEiD,IAAI,EAAEL,KAAK,CAACK,IAAK;cACjBI,KAAK,EAAE3C,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAAI,EAAG;cAClCsJ,QAAQ,EAAErG,YAAa;cACvBsG,WAAW,EAAE5J,KAAK,CAAC4J,WAAY;cAC/BG,IAAI,EAAC,GAAG;cACRX,SAAS,EAAE,gCACTpL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,GAAG,gBAAgB,GAAG,iBAAiB;YACJ;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACF,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,QAAQ,iBACtBpG,OAAA;cACEiD,IAAI,EAAEL,KAAK,CAACK,IAAK;cACjBI,KAAK,EAAE3C,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAAI,EAAG;cAClCsJ,QAAQ,EAAErG,YAAa;cACvB8F,SAAS,EAAE,gCACTpL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,GAAG,gBAAgB,GAAG,iBAAiB,sDACJ;cAAAgJ,QAAA,gBAEvDjM,OAAA;gBAAQqD,KAAK,EAAC,EAAE;gBAAA4I,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EACzCzJ,KAAK,CAACgK,OAAO,IAAIhK,KAAK,CAACgK,OAAO,CAAC1I,GAAG,CAAC,CAAC2I,MAAM,EAAEzI,KAAK,kBAChDpE,OAAA;gBAAoBqD,KAAK,EAAEwJ,MAAO;gBAAAZ,QAAA,EAC/BY;cAAM,GADIzI,KAAK;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEV,CACT,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CACT,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,UAAU,iBACxBpG,OAAA;cAAKgM,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBAChCjM,OAAA;gBACEoG,IAAI,EAAC,UAAU;gBACfnD,IAAI,EAAEL,KAAK,CAACK,IAAK;gBACjBoD,OAAO,EAAE3F,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAAI,KAAM;gBACvCsJ,QAAQ,EAAErG,YAAa;gBACvB8F,SAAS,EAAC;cAAmE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9E,CAAC,eACFrM,OAAA;gBAAMgM,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,EACzCrJ,KAAK,CAAC4J;cAAW;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CACN,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,OAAO,IAAIxD,KAAK,CAACgK,OAAO,iBACtC5M,OAAA;cAAKgM,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBrJ,KAAK,CAACgK,OAAO,CAAC1I,GAAG,CAAC,CAAC2I,MAAM,EAAEzI,KAAK,kBAC/BpE,OAAA;gBAAiBgM,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAC5CjM,OAAA;kBACEoG,IAAI,EAAC,OAAO;kBACZe,EAAE,EAAE,GAAGvE,KAAK,CAACK,IAAI,IAAImB,KAAK,EAAG;kBAC7BnB,IAAI,EAAEL,KAAK,CAACK,IAAK;kBACjBI,KAAK,EAAEwJ,MAAO;kBACdxG,OAAO,EAAE3F,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,KAAK4J,MAAO;kBACzCN,QAAQ,EAAErG,YAAa;kBACvB8F,SAAS,EAAC;gBAA2D;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtE,CAAC,eACFrM,OAAA;kBACE8M,OAAO,EAAE,GAAGlK,KAAK,CAACK,IAAI,IAAImB,KAAK,EAAG;kBAClC4H,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,EAErCY;gBAAM;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC;cAAA,GAfAjI,KAAK;gBAAA8H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAgBV,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,MAAM,iBACpBpG,OAAA;cACEoG,IAAI,EAAC,MAAM;cACXnD,IAAI,EAAEL,KAAK,CAACK,IAAK;cACjBI,KAAK,EAAE3C,QAAQ,CAACkC,KAAK,CAACK,IAAI,CAAC,IAAI,EAAG;cAClCsJ,QAAQ,EAAErG,YAAa;cACvB8F,SAAS,EAAE,gCACTpL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,GAAG,gBAAgB,GAAG,iBAAiB;YACJ;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACF,EAEAzJ,KAAK,CAACwD,IAAI,KAAK,MAAM,iBACpBpG,OAAA;cACEoG,IAAI,EAAC,MAAM;cACXnD,IAAI,EAAEL,KAAK,CAACK,IAAK;cACjBsJ,QAAQ,EAAErG,YAAa;cACvB8F,SAAS,EAAE,gCACTpL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,GAAG,gBAAgB,GAAG,iBAAiB;YACJ;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CACF,EAEAzL,MAAM,CAACgC,KAAK,CAACK,IAAI,CAAC,iBACjBjD,OAAA;cAAGgM,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAErL,MAAM,CAACgC,KAAK,CAACK,IAAI;YAAC;cAAAiJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACjE;UAAA,GAxJOzJ,KAAK,CAACK,IAAI;YAAAiJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyJf,CACN,CAAC,gBACArM,OAAA;YAAKgM,SAAS,EAAC,sDAAsD;YAAAC,QAAA,gBACnEjM,OAAA;cAAGgM,SAAS,EAAC,6BAA6B;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACpErM,OAAA;cAAGgM,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAE5C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CACN,gBACCrM,OAAA;YAAKgM,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DjM,OAAA;cAAGgM,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACxDrM,OAAA;cAAGgM,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAEzC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJrM,OAAA;cAAGgM,SAAS,EAAC,4BAA4B;cAAAC,QAAA,GAAC,cAC5B,EAAC/L,IAAI,GAAG,QAAQ,GAAG,MAAM,EAAC,WAAS,EAACA,IAAI,IAAIA,IAAI,CAACgC,UAAU,IAAIhC,IAAI,CAACgC,UAAU,CAACF,MAAM,GAAG,QAAQ,GAAG,MAAM;YAAA;cAAAkK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACN;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAaLnL,QAAQ,iBACPlB,OAAA;UAAKgM,SAAS,EAAC,qDAAqD;UAAAC,QAAA,eAElEjM,OAAA;YAAGgM,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAE/K;UAAQ;YAAAgL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/C,CACN,EAGAjL,kBAAkB,CAACa,MAAM,GAAG,CAAC,iBAC5BjC,OAAA;UAAKgM,SAAS,EAAC,uDAAuD;UAAAC,QAAA,gBACpEjM,OAAA;YAAIgM,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7ErM,OAAA;YAAKgM,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAClC7K,kBAAkB,CAAC8C,GAAG,CAAEC,MAAM,iBAC7BnE,OAAA;cAEE+M,OAAO,EAAEA,CAAA,KAAM/H,cAAc,CAACb,MAAM,CAAE;cACtC6I,QAAQ,EAAEtL,gBAAiB;cAC3BsK,SAAS,EAAE,kHACTiB,gBAAgB,CAAC9I,MAAM,CAAC+I,WAAW,CAAC,IAClCxL,gBAAgB,GAAG,+BAA+B,GAAG,EAAE,EAAG;cAAAuK,QAAA,EAE7DvK,gBAAgB,GAAG,eAAe,GAAGyC,MAAM,CAACsB;YAAU,GAPlDtB,MAAM,CAACE,WAAW;cAAA6H,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQjB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAGDrM,OAAA;UAAKgM,SAAS,EAAC,iCAAiC;UAAAC,QAAA,GAC7C7L,QAAQ,iBACPJ,OAAA;YACEoG,IAAI,EAAC,QAAQ;YACb2G,OAAO,EAAE3M,QAAS;YAClB4L,SAAS,EAAC,iLAAiL;YAAAC,QAAA,EAC5L;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT,eACDrM,OAAA;YACEoG,IAAI,EAAC,QAAQ;YACb4G,QAAQ,EAAElM,YAAa;YACvBkL,SAAS,EAAE,uDACTlL,YAAY,GAAG,gCAAgC,GAAG,+BAA+B,0EACR;YAAAmL,QAAA,EAE1EnL,YAAY,gBACXd,OAAA;cAAMgM,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjCjM,OAAA;gBAAKgM,SAAS,EAAC,4CAA4C;gBAACmB,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAApB,QAAA,gBAC5HjM,OAAA;kBAAQgM,SAAS,EAAC,YAAY;kBAACsB,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACrGrM,OAAA;kBAAMgM,SAAS,EAAC,YAAY;kBAACoB,IAAI,EAAC,cAAc;kBAACO,CAAC,EAAC;gBAAiH;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK,CAAC,iBAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GAEPhM;UACD;YAAA6L,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNrM,OAAA,CAACH,kBAAkB;MACjBiG,MAAM,EAAE5F,IAAI,CAACiF,GAAI;MACjBC,UAAU,EAAE1E,QAAS;MACrBkN,YAAY,EAAE/H,uBAAwB;MACtC9B,OAAO,EAAE,CAAA7D,IAAI,aAAJA,IAAI,wBAAAK,iBAAA,GAAJL,IAAI,CAAE4D,WAAW,cAAAvD,iBAAA,uBAAjBA,iBAAA,CAAmBwD,OAAO,MAAI7D,IAAI,aAAJA,IAAI,wBAAAM,kBAAA,GAAJN,IAAI,CAAE4D,WAAW,cAAAtD,kBAAA,wBAAAC,qBAAA,GAAjBD,kBAAA,CAAmBwD,aAAa,cAAAvD,qBAAA,uBAAhCA,qBAAA,CAAkCoN,IAAI,CAAC1J,MAAM;QAAA,IAAA2J,mBAAA;QAAA,QAAAA,mBAAA,GAAI3J,MAAM,CAAC4J,WAAW,cAAAD,mBAAA,uBAAlBA,mBAAA,CAAoB/J,OAAO;MAAA,EAAC;IAAC;MAAAmI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtH,CAAC,EAGD7K,mBAAmB,IAAIF,UAAU,iBAChCtB,OAAA;MAAKgM,SAAS,EAAC,4EAA4E;MAAAC,QAAA,eACzFjM,OAAA;QAAKgM,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAE/FjM,OAAA;UAAKgM,SAAS,EAAC,gDAAgD;UAAAC,QAAA,eAC7DjM,OAAA;YAAKgM,SAAS,EAAC,mCAAmC;YAAAC,QAAA,gBAChDjM,OAAA;cAAAiM,QAAA,gBACEjM,OAAA;gBAAIgM,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAEtC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLrM,OAAA;gBAAGgM,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EACvC3K,UAAU,CAAC2B;cAAI;gBAAAiJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNrM,OAAA;cACE+M,OAAO,EAAEA,CAAA,KAAMtL,sBAAsB,CAAC,KAAK,CAAE;cAC7CuK,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAE7DjM,OAAA;gBAAKgM,SAAS,EAAC,SAAS;gBAACoB,IAAI,EAAC,MAAM;gBAACK,MAAM,EAAC,cAAc;gBAACJ,OAAO,EAAC,WAAW;gBAAApB,QAAA,eAC5EjM,OAAA;kBAAMgO,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACP,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsB;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNrM,OAAA;UAAKgM,SAAS,EAAC,KAAK;UAAAC,QAAA,gBAClBjM,OAAA;YAAKgM,SAAS,EAAC,yDAAyD;YAAAC,QAAA,eACtEjM,OAAA;cAAKgM,SAAS,EAAC,kBAAkB;cAAAC,QAAA,gBAC/BjM,OAAA;gBAAKgM,SAAS,EAAC,eAAe;gBAAAC,QAAA,eAC5BjM,OAAA;kBAAKgM,SAAS,EAAC,wBAAwB;kBAACqB,OAAO,EAAC,WAAW;kBAACD,IAAI,EAAC,cAAc;kBAAAnB,QAAA,eAC7EjM,OAAA;oBAAMkO,QAAQ,EAAC,SAAS;oBAACP,CAAC,EAAC,kHAAkH;oBAACQ,QAAQ,EAAC;kBAAS;oBAAAjC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrM,OAAA;gBAAKgM,SAAS,EAAC,MAAM;gBAAAC,QAAA,gBACnBjM,OAAA;kBAAIgM,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAAC;gBAAW;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnErM,OAAA;kBAAKgM,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAC;gBAG7C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrM,OAAA,CAACC,eAAe;YACdC,IAAI,EAAEoB,UAAW;YACjBnB,QAAQ,EAAE,MAAAA,CAAO2F,MAAM,EAAEpF,QAAQ,KAAK;cACpC,IAAI;gBACF,MAAMZ,GAAG,CAACoF,IAAI,CAAC,mBAAmBY,MAAM,SAAS,EAAEpF,QAAQ,CAAC;gBAC5De,sBAAsB,CAAC,KAAK,CAAC;gBAC7BF,aAAa,CAAC,IAAI,CAAC;cACrB,CAAC,CAAC,OAAOqE,KAAK,EAAE;gBACdhE,OAAO,CAACgE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;cACvD;YACF,CAAE;YACFxF,QAAQ,EAAEA,CAAA,KAAM;cACdqB,sBAAsB,CAAC,KAAK,CAAC;cAC7BF,aAAa,CAAC,IAAI,CAAC;YACrB;UAAE;YAAA2K,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA/L,EAAA,CA55BML,eAAe;AAAAmO,EAAA,GAAfnO,eAAe;AA65BrB,MAAMgN,gBAAgB,GAAIoB,KAAK,IAAK;EAClC,QAAQA,KAAK;IACX,KAAK,SAAS;MACZ,OAAO,8DAA8D;IACvE,KAAK,WAAW;MACd,OAAO,8DAA8D;IACvE,KAAK,SAAS;MACZ,OAAO,iEAAiE;IAC1E,KAAK,SAAS;MACZ,OAAO,oEAAoE;IAC7E,KAAK,QAAQ;MACX,OAAO,2DAA2D;IACpE;MACE,OAAO,8DAA8D;EACzE;AACF,CAAC;AAED,eAAepO,eAAe;AAAC,IAAAmO,EAAA;AAAAE,YAAA,CAAAF,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}