{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\AutoTriggerHandler.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport DynamicForm from './DynamicForm';\nimport api from '../utils/api';\n\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AutoTriggerHandler = ({\n  formId,\n  recordData,\n  onFormSubmit,\n  enabled = true\n}) => {\n  _s();\n  const [isChecking, setIsChecking] = useState(false);\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n\n  // Refs to track cleanup\n  const abortControllerRef = useRef(null);\n  const countdownIntervalRef = useRef(null);\n  const isMountedRef = useRef(true);\n  useEffect(() => {\n    if (!enabled || !formId || !recordData) return;\n\n    // Cleanup previous request if exists\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n\n    // Clear any existing countdown\n    if (countdownIntervalRef.current) {\n      clearInterval(countdownIntervalRef.current);\n      countdownIntervalRef.current = null;\n    }\n\n    // Create new abort controller for this request\n    abortControllerRef.current = new AbortController();\n    const checkAutoTrigger = async () => {\n      // Prevent multiple simultaneous calls\n      if (isChecking) return;\n      setIsChecking(true);\n      try {\n        console.log('🔍 Checking auto-trigger for form:', formId);\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\n          recordData\n        }, {\n          signal: abortControllerRef.current.signal\n        });\n\n        // Check if component is still mounted and request wasn't aborted\n        if (!isMountedRef.current || abortControllerRef.current.signal.aborted) {\n          return;\n        }\n        if (response.data.success && response.data.shouldTrigger) {\n          const triggerInfo = response.data;\n          const delay = triggerInfo.delaySeconds || 0;\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\n          if (delay > 0) {\n            // Show countdown\n            setCountdown(delay);\n            countdownIntervalRef.current = setInterval(() => {\n              setCountdown(prev => {\n                if (prev <= 1) {\n                  if (countdownIntervalRef.current) {\n                    clearInterval(countdownIntervalRef.current);\n                    countdownIntervalRef.current = null;\n                  }\n                  // Check if component is still mounted before triggering\n                  if (isMountedRef.current) {\n                    triggerForm(triggerInfo);\n                  }\n                  return 0;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else {\n            // Trigger immediately\n            triggerForm(triggerInfo);\n          }\n        }\n      } catch (error) {\n        // Don't log error if request was aborted (normal cleanup)\n        if (error.name !== 'AbortError') {\n          console.error('❌ Error checking auto-trigger:', error);\n        }\n      } finally {\n        // Only update state if component is still mounted\n        if (isMountedRef.current) {\n          setIsChecking(false);\n        }\n      }\n    };\n    const triggerForm = async triggerInfo => {\n      try {\n        // Process the form linking to get the target form and prefilled data\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\n          recordData,\n          parentData: {},\n          actionIndex: triggerInfo.actionIndex\n        });\n        if (linkingResponse.data.success) {\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\n\n          // Set the auto-triggered form data\n          setAutoTriggerForm({\n            ...linkingResponse.data.targetForm,\n            prefillData: linkingResponse.data.prefillData,\n            buttonText: linkingResponse.data.buttonText,\n            isAutoTriggered: true\n          });\n          setShowAutoTriggerModal(true);\n        }\n      } catch (error) {\n        console.error('❌ Error processing auto-trigger:', error);\n      }\n    };\n    checkAutoTrigger();\n\n    // Cleanup function\n    return () => {\n      // Abort any ongoing request\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n      }\n\n      // Clear countdown interval\n      if (countdownIntervalRef.current) {\n        clearInterval(countdownIntervalRef.current);\n        countdownIntervalRef.current = null;\n      }\n    };\n  }, [formId, recordData, enabled]);\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      isMountedRef.current = false;\n\n      // Abort any ongoing request\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n      }\n\n      // Clear countdown interval\n      if (countdownIntervalRef.current) {\n        clearInterval(countdownIntervalRef.current);\n      }\n    };\n  }, []);\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\n    try {\n      // Submit the form\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\n      if (response.data.success) {\n        setShowAutoTriggerModal(false);\n        setAutoTriggerForm(null);\n\n        // Notify parent component\n        if (onFormSubmit) {\n          onFormSubmit(formId, formData, response.data);\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error submitting auto-triggered form:', error);\n    }\n  };\n  const handleCloseAutoTriggerModal = () => {\n    setShowAutoTriggerModal(false);\n    setAutoTriggerForm(null);\n    setCountdown(0);\n\n    // Clear countdown interval if it's running\n    if (countdownIntervalRef.current) {\n      clearInterval(countdownIntervalRef.current);\n      countdownIntervalRef.current = null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [countdown > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: [\"Auto-opening form in \", countdown, \"s\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 9\n    }, this), showAutoTriggerModal && autoTriggerForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-500 text-white px-6 py-4 rounded-t-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold\",\n                children: \"\\uD83D\\uDE80 Auto-Opened Form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-100 mt-1\",\n                children: autoTriggerForm.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseAutoTriggerModal,\n              className: \"text-white hover:text-gray-200 focus:outline-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-blue-400\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 242,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-blue-800\",\n                  children: \"Auto-Triggered Form\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 text-sm text-blue-700\",\n                  children: \"This form was automatically opened based on the data you're viewing. Some fields may be pre-filled based on the original record.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 238,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DynamicForm, {\n            form: autoTriggerForm,\n            onSubmit: handleAutoTriggerFormSubmit,\n            onCancel: handleCloseAutoTriggerModal\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 237,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 213,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 212,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(AutoTriggerHandler, \"XdQhxpMEaOwYnTBkPl35gitmvPc=\");\n_c = AutoTriggerHandler;\nexport default AutoTriggerHandler;\nvar _c;\n$RefreshReg$(_c, \"AutoTriggerHandler\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "DynamicForm", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AutoTriggerHandler", "formId", "recordData", "onFormSubmit", "enabled", "_s", "isChecking", "setIsChecking", "autoTriggerForm", "setAutoTriggerForm", "showAutoTriggerModal", "setShowAutoTriggerModal", "countdown", "setCountdown", "abortControllerRef", "countdownIntervalRef", "isMountedRef", "current", "abort", "clearInterval", "AbortController", "checkAutoTrigger", "console", "log", "response", "post", "signal", "aborted", "data", "success", "should<PERSON><PERSON>ger", "triggerInfo", "delay", "delaySeconds", "setInterval", "prev", "triggerForm", "error", "name", "linkingResponse", "parentData", "actionIndex", "targetForm", "prefillData", "buttonText", "isAutoTriggered", "handleAutoTriggerFormSubmit", "formData", "handleCloseAutoTriggerModal", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fillRule", "clipRule", "form", "onSubmit", "onCancel", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/AutoTriggerHandler.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport DynamicForm from './DynamicForm';\r\nimport api from '../utils/api';\r\n\r\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\r\nconst AutoTriggerHandler = ({ \r\n  formId, \r\n  recordData, \r\n  onFormSubmit,\r\n  enabled = true \r\n}) => {\r\n  const [isChecking, setIsChecking] = useState(false);\r\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\r\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\r\n  const [countdown, setCountdown] = useState(0);\r\n  \r\n  // Refs to track cleanup\r\n  const abortControllerRef = useRef(null);\r\n  const countdownIntervalRef = useRef(null);\r\n  const isMountedRef = useRef(true);\r\n\r\n  useEffect(() => {\r\n    if (!enabled || !formId || !recordData) return;\r\n\r\n    // Cleanup previous request if exists\r\n    if (abortControllerRef.current) {\r\n      abortControllerRef.current.abort();\r\n    }\r\n    \r\n    // Clear any existing countdown\r\n    if (countdownIntervalRef.current) {\r\n      clearInterval(countdownIntervalRef.current);\r\n      countdownIntervalRef.current = null;\r\n    }\r\n\r\n    // Create new abort controller for this request\r\n    abortControllerRef.current = new AbortController();\r\n\r\n    const checkAutoTrigger = async () => {\r\n      // Prevent multiple simultaneous calls\r\n      if (isChecking) return;\r\n      \r\n      setIsChecking(true);\r\n      try {\r\n        console.log('🔍 Checking auto-trigger for form:', formId);\r\n        \r\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\r\n          recordData\r\n        }, {\r\n          signal: abortControllerRef.current.signal\r\n        });\r\n\r\n        // Check if component is still mounted and request wasn't aborted\r\n        if (!isMountedRef.current || abortControllerRef.current.signal.aborted) {\r\n          return;\r\n        }\r\n\r\n        if (response.data.success && response.data.shouldTrigger) {\r\n          const triggerInfo = response.data;\r\n          const delay = triggerInfo.delaySeconds || 0;\r\n          \r\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\r\n          \r\n          if (delay > 0) {\r\n            // Show countdown\r\n            setCountdown(delay);\r\n            countdownIntervalRef.current = setInterval(() => {\r\n              setCountdown(prev => {\r\n                if (prev <= 1) {\r\n                  if (countdownIntervalRef.current) {\r\n                    clearInterval(countdownIntervalRef.current);\r\n                    countdownIntervalRef.current = null;\r\n                  }\r\n                  // Check if component is still mounted before triggering\r\n                  if (isMountedRef.current) {\r\n                    triggerForm(triggerInfo);\r\n                  }\r\n                  return 0;\r\n                }\r\n                return prev - 1;\r\n              });\r\n            }, 1000);\r\n          } else {\r\n            // Trigger immediately\r\n            triggerForm(triggerInfo);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        // Don't log error if request was aborted (normal cleanup)\r\n        if (error.name !== 'AbortError') {\r\n          console.error('❌ Error checking auto-trigger:', error);\r\n        }\r\n      } finally {\r\n        // Only update state if component is still mounted\r\n        if (isMountedRef.current) {\r\n          setIsChecking(false);\r\n        }\r\n      }\r\n    };\r\n\r\n    const triggerForm = async (triggerInfo) => {\r\n      try {\r\n        // Process the form linking to get the target form and prefilled data\r\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\r\n          recordData,\r\n          parentData: {},\r\n          actionIndex: triggerInfo.actionIndex\r\n        });\r\n\r\n        if (linkingResponse.data.success) {\r\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\r\n          \r\n          // Set the auto-triggered form data\r\n          setAutoTriggerForm({\r\n            ...linkingResponse.data.targetForm,\r\n            prefillData: linkingResponse.data.prefillData,\r\n            buttonText: linkingResponse.data.buttonText,\r\n            isAutoTriggered: true\r\n          });\r\n          setShowAutoTriggerModal(true);\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Error processing auto-trigger:', error);\r\n      }\r\n    };\r\n\r\n    checkAutoTrigger();\r\n\r\n    // Cleanup function\r\n    return () => {\r\n      // Abort any ongoing request\r\n      if (abortControllerRef.current) {\r\n        abortControllerRef.current.abort();\r\n      }\r\n      \r\n      // Clear countdown interval\r\n      if (countdownIntervalRef.current) {\r\n        clearInterval(countdownIntervalRef.current);\r\n        countdownIntervalRef.current = null;\r\n      }\r\n    };\r\n  }, [formId, recordData, enabled]);\r\n\r\n  // Cleanup on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      isMountedRef.current = false;\r\n      \r\n      // Abort any ongoing request\r\n      if (abortControllerRef.current) {\r\n        abortControllerRef.current.abort();\r\n      }\r\n      \r\n      // Clear countdown interval\r\n      if (countdownIntervalRef.current) {\r\n        clearInterval(countdownIntervalRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\r\n    try {\r\n      // Submit the form\r\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\r\n      \r\n      if (response.data.success) {\r\n        setShowAutoTriggerModal(false);\r\n        setAutoTriggerForm(null);\r\n        \r\n        // Notify parent component\r\n        if (onFormSubmit) {\r\n          onFormSubmit(formId, formData, response.data);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error submitting auto-triggered form:', error);\r\n    }\r\n  };\r\n\r\n  const handleCloseAutoTriggerModal = () => {\r\n    setShowAutoTriggerModal(false);\r\n    setAutoTriggerForm(null);\r\n    setCountdown(0);\r\n    \r\n    // Clear countdown interval if it's running\r\n    if (countdownIntervalRef.current) {\r\n      clearInterval(countdownIntervalRef.current);\r\n      countdownIntervalRef.current = null;\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Countdown Indicator */}\r\n      {countdown > 0 && (\r\n        <div className=\"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n            <span className=\"text-sm font-medium\">\r\n              Auto-opening form in {countdown}s\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Auto-Trigger Form Modal */}\r\n      {showAutoTriggerModal && autoTriggerForm && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n            {/* Modal Header */}\r\n            <div className=\"bg-blue-500 text-white px-6 py-4 rounded-t-lg\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <h2 className=\"text-lg font-semibold\">\r\n                    🚀 Auto-Opened Form\r\n                  </h2>\r\n                  <p className=\"text-sm text-blue-100 mt-1\">\r\n                    {autoTriggerForm.name}\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={handleCloseAutoTriggerModal}\r\n                  className=\"text-white hover:text-gray-200 focus:outline-none\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Modal Content */}\r\n            <div className=\"p-6\">\r\n              <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <h3 className=\"text-sm font-medium text-blue-800\">Auto-Triggered Form</h3>\r\n                    <div className=\"mt-1 text-sm text-blue-700\">\r\n                      This form was automatically opened based on the data you're viewing. \r\n                      Some fields may be pre-filled based on the original record.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <DynamicForm\r\n                form={autoTriggerForm}\r\n                onSubmit={handleAutoTriggerFormSubmit}\r\n                onCancel={handleCloseAutoTriggerModal}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AutoTriggerHandler;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,GAAG,MAAM,cAAc;;AAE9B;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKA,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,UAAU;EACVC,YAAY;EACZC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACmB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqB,SAAS,EAAEC,YAAY,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC;;EAE7C;EACA,MAAMuB,kBAAkB,GAAGrB,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMsB,oBAAoB,GAAGtB,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMuB,YAAY,GAAGvB,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,IAAI,CAACY,OAAO,IAAI,CAACH,MAAM,IAAI,CAACC,UAAU,EAAE;;IAExC;IACA,IAAIY,kBAAkB,CAACG,OAAO,EAAE;MAC9BH,kBAAkB,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC;IACpC;;IAEA;IACA,IAAIH,oBAAoB,CAACE,OAAO,EAAE;MAChCE,aAAa,CAACJ,oBAAoB,CAACE,OAAO,CAAC;MAC3CF,oBAAoB,CAACE,OAAO,GAAG,IAAI;IACrC;;IAEA;IACAH,kBAAkB,CAACG,OAAO,GAAG,IAAIG,eAAe,CAAC,CAAC;IAElD,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnC;MACA,IAAIf,UAAU,EAAE;MAEhBC,aAAa,CAAC,IAAI,CAAC;MACnB,IAAI;QACFe,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEtB,MAAM,CAAC;QAEzD,MAAMuB,QAAQ,GAAG,MAAM7B,GAAG,CAAC8B,IAAI,CAAC,mBAAmBxB,MAAM,qBAAqB,EAAE;UAC9EC;QACF,CAAC,EAAE;UACDwB,MAAM,EAAEZ,kBAAkB,CAACG,OAAO,CAACS;QACrC,CAAC,CAAC;;QAEF;QACA,IAAI,CAACV,YAAY,CAACC,OAAO,IAAIH,kBAAkB,CAACG,OAAO,CAACS,MAAM,CAACC,OAAO,EAAE;UACtE;QACF;QAEA,IAAIH,QAAQ,CAACI,IAAI,CAACC,OAAO,IAAIL,QAAQ,CAACI,IAAI,CAACE,aAAa,EAAE;UACxD,MAAMC,WAAW,GAAGP,QAAQ,CAACI,IAAI;UACjC,MAAMI,KAAK,GAAGD,WAAW,CAACE,YAAY,IAAI,CAAC;UAE3CX,OAAO,CAACC,GAAG,CAAC,8BAA8BS,KAAK,aAAa,CAAC;UAE7D,IAAIA,KAAK,GAAG,CAAC,EAAE;YACb;YACAnB,YAAY,CAACmB,KAAK,CAAC;YACnBjB,oBAAoB,CAACE,OAAO,GAAGiB,WAAW,CAAC,MAAM;cAC/CrB,YAAY,CAACsB,IAAI,IAAI;gBACnB,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACb,IAAIpB,oBAAoB,CAACE,OAAO,EAAE;oBAChCE,aAAa,CAACJ,oBAAoB,CAACE,OAAO,CAAC;oBAC3CF,oBAAoB,CAACE,OAAO,GAAG,IAAI;kBACrC;kBACA;kBACA,IAAID,YAAY,CAACC,OAAO,EAAE;oBACxBmB,WAAW,CAACL,WAAW,CAAC;kBAC1B;kBACA,OAAO,CAAC;gBACV;gBACA,OAAOI,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACL;YACAC,WAAW,CAACL,WAAW,CAAC;UAC1B;QACF;MACF,CAAC,CAAC,OAAOM,KAAK,EAAE;QACd;QACA,IAAIA,KAAK,CAACC,IAAI,KAAK,YAAY,EAAE;UAC/BhB,OAAO,CAACe,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF,CAAC,SAAS;QACR;QACA,IAAIrB,YAAY,CAACC,OAAO,EAAE;UACxBV,aAAa,CAAC,KAAK,CAAC;QACtB;MACF;IACF,CAAC;IAED,MAAM6B,WAAW,GAAG,MAAOL,WAAW,IAAK;MACzC,IAAI;QACF;QACA,MAAMQ,eAAe,GAAG,MAAM5C,GAAG,CAAC8B,IAAI,CAAC,mBAAmBxB,MAAM,YAAY,EAAE;UAC5EC,UAAU;UACVsC,UAAU,EAAE,CAAC,CAAC;UACdC,WAAW,EAAEV,WAAW,CAACU;QAC3B,CAAC,CAAC;QAEF,IAAIF,eAAe,CAACX,IAAI,CAACC,OAAO,EAAE;UAChCP,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEgB,eAAe,CAACX,IAAI,CAACc,UAAU,CAACJ,IAAI,CAAC;;UAE7E;UACA7B,kBAAkB,CAAC;YACjB,GAAG8B,eAAe,CAACX,IAAI,CAACc,UAAU;YAClCC,WAAW,EAAEJ,eAAe,CAACX,IAAI,CAACe,WAAW;YAC7CC,UAAU,EAAEL,eAAe,CAACX,IAAI,CAACgB,UAAU;YAC3CC,eAAe,EAAE;UACnB,CAAC,CAAC;UACFlC,uBAAuB,CAAC,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC,OAAO0B,KAAK,EAAE;QACdf,OAAO,CAACe,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDhB,gBAAgB,CAAC,CAAC;;IAElB;IACA,OAAO,MAAM;MACX;MACA,IAAIP,kBAAkB,CAACG,OAAO,EAAE;QAC9BH,kBAAkB,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC;MACpC;;MAEA;MACA,IAAIH,oBAAoB,CAACE,OAAO,EAAE;QAChCE,aAAa,CAACJ,oBAAoB,CAACE,OAAO,CAAC;QAC3CF,oBAAoB,CAACE,OAAO,GAAG,IAAI;MACrC;IACF,CAAC;EACH,CAAC,EAAE,CAAChB,MAAM,EAAEC,UAAU,EAAEE,OAAO,CAAC,CAAC;;EAEjC;EACAZ,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACXwB,YAAY,CAACC,OAAO,GAAG,KAAK;;MAE5B;MACA,IAAIH,kBAAkB,CAACG,OAAO,EAAE;QAC9BH,kBAAkB,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC;MACpC;;MAEA;MACA,IAAIH,oBAAoB,CAACE,OAAO,EAAE;QAChCE,aAAa,CAACJ,oBAAoB,CAACE,OAAO,CAAC;MAC7C;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM6B,2BAA2B,GAAG,MAAAA,CAAO7C,MAAM,EAAE8C,QAAQ,KAAK;IAC9D,IAAI;MACF;MACA,MAAMvB,QAAQ,GAAG,MAAM7B,GAAG,CAAC8B,IAAI,CAAC,mBAAmBxB,MAAM,SAAS,EAAE8C,QAAQ,CAAC;MAE7E,IAAIvB,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QACzBlB,uBAAuB,CAAC,KAAK,CAAC;QAC9BF,kBAAkB,CAAC,IAAI,CAAC;;QAExB;QACA,IAAIN,YAAY,EAAE;UAChBA,YAAY,CAACF,MAAM,EAAE8C,QAAQ,EAAEvB,QAAQ,CAACI,IAAI,CAAC;QAC/C;MACF;IACF,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdf,OAAO,CAACe,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;EAED,MAAMW,2BAA2B,GAAGA,CAAA,KAAM;IACxCrC,uBAAuB,CAAC,KAAK,CAAC;IAC9BF,kBAAkB,CAAC,IAAI,CAAC;IACxBI,YAAY,CAAC,CAAC,CAAC;;IAEf;IACA,IAAIE,oBAAoB,CAACE,OAAO,EAAE;MAChCE,aAAa,CAACJ,oBAAoB,CAACE,OAAO,CAAC;MAC3CF,oBAAoB,CAACE,OAAO,GAAG,IAAI;IACrC;EACF,CAAC;EAED,oBACEpB,OAAA,CAAAE,SAAA;IAAAkD,QAAA,GAEGrC,SAAS,GAAG,CAAC,iBACZf,OAAA;MAAKqD,SAAS,EAAC,mFAAmF;MAAAD,QAAA,eAChGpD,OAAA;QAAKqD,SAAS,EAAC,6BAA6B;QAAAD,QAAA,gBAC1CpD,OAAA;UAAKqD,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpGzD,OAAA;UAAMqD,SAAS,EAAC,qBAAqB;UAAAD,QAAA,GAAC,uBACf,EAACrC,SAAS,EAAC,GAClC;QAAA;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGA5C,oBAAoB,IAAIF,eAAe,iBACtCX,OAAA;MAAKqD,SAAS,EAAC,4EAA4E;MAAAD,QAAA,eACzFpD,OAAA;QAAKqD,SAAS,EAAC,kFAAkF;QAAAD,QAAA,gBAE/FpD,OAAA;UAAKqD,SAAS,EAAC,+CAA+C;UAAAD,QAAA,eAC5DpD,OAAA;YAAKqD,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDpD,OAAA;cAAAoD,QAAA,gBACEpD,OAAA;gBAAIqD,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLzD,OAAA;gBAAGqD,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EACtCzC,eAAe,CAAC8B;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACNzD,OAAA;cACE0D,OAAO,EAAEP,2BAA4B;cACrCE,SAAS,EAAC,mDAAmD;cAAAD,QAAA,eAE7DpD,OAAA;gBAAKqD,SAAS,EAAC,SAAS;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5EpD,OAAA;kBAAM8D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNzD,OAAA;UAAKqD,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBpD,OAAA;YAAKqD,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACpEpD,OAAA;cAAKqD,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC/BpD,OAAA;gBAAKqD,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC5BpD,OAAA;kBAAKqD,SAAS,EAAC,uBAAuB;kBAACQ,OAAO,EAAC,WAAW;kBAACF,IAAI,EAAC,cAAc;kBAAAP,QAAA,eAC5EpD,OAAA;oBAAMkE,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,kIAAkI;oBAACE,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNzD,OAAA;gBAAKqD,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBpD,OAAA;kBAAIqD,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1EzD,OAAA;kBAAKqD,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,EAAC;gBAG5C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENzD,OAAA,CAACH,WAAW;YACVuE,IAAI,EAAEzD,eAAgB;YACtB0D,QAAQ,EAAEpB,2BAA4B;YACtCqB,QAAQ,EAAEnB;UAA4B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAACjD,EAAA,CAhQIL,kBAAkB;AAAAoE,EAAA,GAAlBpE,kBAAkB;AAkQxB,eAAeA,kBAAkB;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}