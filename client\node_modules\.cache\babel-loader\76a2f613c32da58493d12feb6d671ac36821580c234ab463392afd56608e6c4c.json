{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\RecordDisplayWithActions.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\nimport api from '../utils/api';\n\n/**\r\n * Component for displaying records with Apply buttons for form linking\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RecordDisplayWithActions = ({\n  data,\n  formId,\n  formLinkingConfig,\n  onFormLinkTriggered,\n  hideRecordDisplay = false,\n  showOnlyButtons = false,\n  autoTriggerOptions = {} // New flexible auto-trigger options\n}) => {\n  _s();\n  const [loadingActions, setLoadingActions] = useState({});\n  const [autoTriggerState, setAutoTriggerState] = useState({});\n  const [executedTriggers, setExecutedTriggers] = useState(() => {\n    // Initialize with persistent triggers from localStorage\n    const persistentTriggers = localStorage.getItem('persistentExecutedTriggers');\n    const triggerTimestamp = localStorage.getItem('persistentTriggersTimestamp');\n\n    // Clear old triggers if they're more than 1 hour old\n    const oneHourAgo = Date.now() - 60 * 60 * 1000;\n    if (triggerTimestamp && parseInt(triggerTimestamp) < oneHourAgo) {\n      console.log('🧹 Clearing old persistent triggers (older than 1 hour)');\n      localStorage.removeItem('persistentExecutedTriggers');\n      localStorage.removeItem('persistentTriggersTimestamp');\n      return new Set();\n    }\n    return persistentTriggers ? new Set(JSON.parse(persistentTriggers)) : new Set();\n  });\n  const componentId = useRef(Math.random().toString(36).substr(2, 9));\n  const timeoutRefs = useRef({});\n\n  // Auto-trigger configuration with defaults\n  const autoTriggerConfig = useMemo(() => {\n    var _autoTriggerOptions$e, _autoTriggerOptions$d, _autoTriggerOptions$s, _autoTriggerOptions$t, _autoTriggerOptions$r, _autoTriggerOptions$t2;\n    return {\n      enabled: (_autoTriggerOptions$e = autoTriggerOptions.enabled) !== null && _autoTriggerOptions$e !== void 0 ? _autoTriggerOptions$e : false,\n      delay: (_autoTriggerOptions$d = autoTriggerOptions.delay) !== null && _autoTriggerOptions$d !== void 0 ? _autoTriggerOptions$d : 2,\n      showCountdown: (_autoTriggerOptions$s = autoTriggerOptions.showCountdown) !== null && _autoTriggerOptions$s !== void 0 ? _autoTriggerOptions$s : true,\n      triggerOnce: (_autoTriggerOptions$t = autoTriggerOptions.triggerOnce) !== null && _autoTriggerOptions$t !== void 0 ? _autoTriggerOptions$t : true,\n      resetOnDataChange: (_autoTriggerOptions$r = autoTriggerOptions.resetOnDataChange) !== null && _autoTriggerOptions$r !== void 0 ? _autoTriggerOptions$r : true,\n      triggerCondition: (_autoTriggerOptions$t2 = autoTriggerOptions.triggerCondition) !== null && _autoTriggerOptions$t2 !== void 0 ? _autoTriggerOptions$t2 : 'first',\n      // 'first', 'all', 'custom'\n      customTriggerLogic: autoTriggerOptions.customTriggerLogic,\n      // Custom function\n      onBeforeTrigger: autoTriggerOptions.onBeforeTrigger,\n      // Callback before trigger\n      onAfterTrigger: autoTriggerOptions.onAfterTrigger,\n      // Callback after trigger\n      ...autoTriggerOptions\n    };\n  }, [autoTriggerOptions]);\n\n  // Generate stable data hash for change detection\n  const dataHash = useMemo(() => JSON.stringify(data), [data]);\n\n  // Process records from various data structures\n  const processedRecords = useMemo(() => {\n    if (!data) return [];\n    if (Array.isArray(data)) return data;\n    if (typeof data === 'object') {\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n      for (const field of arrayFields) {\n        if (data[field] && Array.isArray(data[field])) {\n          return data[field];\n        }\n      }\n      return [data]; // Single object as record\n    }\n    return [];\n  }, [data]);\n\n  // Check if record meets action conditions\n  const shouldShowApplyButton = useCallback((record, conditions = []) => {\n    if (!(conditions !== null && conditions !== void 0 && conditions.length)) return true;\n    const validConditions = conditions.filter(condition => {\n      var _condition$field;\n      return (_condition$field = condition.field) === null || _condition$field === void 0 ? void 0 : _condition$field.trim();\n    });\n    if (!validConditions.length) return true;\n    return validConditions.every(condition => {\n      const fieldValue = record[condition.field];\n      const conditionValue = condition.value;\n      switch (condition.operator) {\n        case 'equals':\n          return fieldValue === conditionValue;\n        case 'not_equals':\n          return fieldValue !== conditionValue;\n        case 'contains':\n          return fieldValue === null || fieldValue === void 0 ? void 0 : fieldValue.toString().includes(conditionValue);\n        case 'not_contains':\n          return !(fieldValue !== null && fieldValue !== void 0 && fieldValue.toString().includes(conditionValue));\n        case 'exists':\n          return fieldValue != null && fieldValue !== '';\n        case 'not_exists':\n          return fieldValue == null || fieldValue === '';\n        default:\n          return true;\n      }\n    });\n  }, []);\n\n  // Get eligible records for auto-trigger\n  const getEligibleRecords = useCallback(() => {\n    if (!(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.recordActions)) return [];\n    const eligible = [];\n    formLinkingConfig.recordActions.forEach((action, actionIndex) => {\n      var _action$autoTrigger;\n      const shouldAutoTrigger = ((_action$autoTrigger = action.autoTrigger) === null || _action$autoTrigger === void 0 ? void 0 : _action$autoTrigger.enabled) || autoTriggerConfig.enabled;\n      if (shouldAutoTrigger) {\n        processedRecords.forEach((record, recordIndex) => {\n          if (shouldShowApplyButton(record, action.conditions)) {\n            eligible.push({\n              record,\n              recordIndex,\n              action,\n              actionIndex,\n              key: `${recordIndex}-${actionIndex}`\n            });\n          }\n        });\n      }\n    });\n    return eligible;\n  }, [processedRecords, formLinkingConfig, autoTriggerConfig.enabled, shouldShowApplyButton]);\n\n  // Determine which records to trigger based on condition\n  const getRecordsToTrigger = useCallback(eligibleRecords => {\n    if (!eligibleRecords.length) return [];\n    switch (autoTriggerConfig.triggerCondition) {\n      case 'first':\n        return [eligibleRecords[0]];\n      case 'all':\n        return eligibleRecords;\n      case 'custom':\n        return autoTriggerConfig.customTriggerLogic ? autoTriggerConfig.customTriggerLogic(eligibleRecords) : [eligibleRecords[0]];\n      default:\n        return [eligibleRecords[0]];\n    }\n  }, [autoTriggerConfig]);\n\n  // Clear all timeouts\n  const clearAllTimeouts = useCallback(() => {\n    Object.values(timeoutRefs.current).forEach(clearTimeout);\n    timeoutRefs.current = {};\n  }, []);\n\n  // Reset auto-trigger state when data changes\n  useEffect(() => {\n    if (!autoTriggerConfig.resetOnDataChange) return;\n\n    // Check if this is a chat opening scenario (don't reset triggers during chat open)\n    const lastChatLoad = localStorage.getItem('lastChatLoadTime');\n    const timeSinceLoad = lastChatLoad ? Date.now() - parseInt(lastChatLoad) : Infinity;\n    if (timeSinceLoad < 10000) {\n      // Extended to 10 seconds for stronger prevention\n      console.log(`⏸️ [${componentId.current}] Skipping trigger reset - chat recently opened (${timeSinceLoad}ms ago)`);\n      return;\n    }\n    console.log(`🔄 [${componentId.current}] Data changed, resetting auto-trigger state`);\n    clearAllTimeouts();\n\n    // Don't clear executed triggers if we have persistent triggers\n    // This prevents duplicate triggers when data changes during normal operation\n    const persistentTriggers = localStorage.getItem('persistentExecutedTriggers');\n    if (!persistentTriggers) {\n      console.log(`🧹 [${componentId.current}] No persistent triggers found, clearing executed triggers`);\n      setExecutedTriggers(new Set());\n    } else {\n      console.log(`🔒 [${componentId.current}] Preserving persistent triggers during data change`);\n    }\n    setAutoTriggerState({});\n  }, [dataHash, autoTriggerConfig.resetOnDataChange, clearAllTimeouts]);\n\n  // Execute auto-trigger logic\n  const executeAutoTrigger = useCallback(() => {\n    const eligibleRecords = getEligibleRecords();\n    const recordsToTrigger = getRecordsToTrigger(eligibleRecords);\n    recordsToTrigger.forEach(({\n      record,\n      actionIndex,\n      key\n    }) => {\n      var _autoTriggerState$key, _action$autoTrigger$d, _action$autoTrigger2;\n      const triggerKey = `${dataHash}-${key}`;\n\n      // Check persistent triggers first\n      const persistentTriggers = localStorage.getItem('persistentExecutedTriggers');\n      const persistentTriggersSet = persistentTriggers ? new Set(JSON.parse(persistentTriggers)) : new Set();\n\n      // Skip if already triggered in persistent storage\n      if (persistentTriggersSet.has(triggerKey)) {\n        console.log(`🔒 [${componentId.current}] Skipping trigger - already executed persistently: ${triggerKey}`);\n        return;\n      }\n\n      // Skip if already triggered and triggerOnce is enabled\n      if (autoTriggerConfig.triggerOnce && executedTriggers.has(triggerKey)) {\n        console.log(`🔒 [${componentId.current}] Skipping trigger - already executed in session: ${triggerKey}`);\n        return;\n      }\n\n      // Skip if currently processing\n      if ((_autoTriggerState$key = autoTriggerState[key]) !== null && _autoTriggerState$key !== void 0 && _autoTriggerState$key.processing) {\n        console.log(`⏸️ [${componentId.current}] Skipping trigger - currently processing: ${key}`);\n        return;\n      }\n\n      // Additional check for chat opening prevention specifically for leave-related triggers\n      const lastChatLoad = localStorage.getItem('lastChatLoadTime');\n      const timeSinceLoad = lastChatLoad ? Date.now() - parseInt(lastChatLoad) : Infinity;\n      const isLeaveRelated = key.includes('leave') || triggerKey.includes('leave') || JSON.stringify(record).toLowerCase().includes('leave');\n      if (isLeaveRelated && timeSinceLoad < 15000) {\n        // 15 seconds for leave-related triggers\n        console.log(`🚫 [${componentId.current}] Skipping leave-related trigger - chat recently opened (${timeSinceLoad}ms ago): ${key}`);\n        return;\n      }\n      const action = formLinkingConfig.recordActions[actionIndex];\n      const delay = (_action$autoTrigger$d = (_action$autoTrigger2 = action.autoTrigger) === null || _action$autoTrigger2 === void 0 ? void 0 : _action$autoTrigger2.delaySeconds) !== null && _action$autoTrigger$d !== void 0 ? _action$autoTrigger$d : autoTriggerConfig.delay;\n      console.log(`⏰ [${componentId.current}] Auto-triggering in ${delay}s for key: ${key}`);\n\n      // Call before trigger callback\n      if (autoTriggerConfig.onBeforeTrigger) {\n        autoTriggerConfig.onBeforeTrigger({\n          record,\n          action,\n          actionIndex\n        });\n      }\n\n      // Mark as executed\n      setExecutedTriggers(prev => {\n        const newSet = new Set(prev).add(triggerKey);\n        // Persist to localStorage to prevent duplicate triggers across sessions\n        localStorage.setItem('persistentExecutedTriggers', JSON.stringify([...newSet]));\n        localStorage.setItem('persistentTriggersTimestamp', Date.now().toString());\n        return newSet;\n      });\n      if (hideRecordDisplay || delay === 0) {\n        // Immediate trigger\n        handleApplyClick(record, actionIndex);\n        if (autoTriggerConfig.onAfterTrigger) {\n          autoTriggerConfig.onAfterTrigger({\n            record,\n            action,\n            actionIndex\n          });\n        }\n      } else {\n        // Delayed trigger with countdown\n        setAutoTriggerState(prev => ({\n          ...prev,\n          [key]: {\n            processing: true,\n            countdown: delay\n          }\n        }));\n\n        // Countdown logic\n        if (autoTriggerConfig.showCountdown) {\n          const countdownInterval = setInterval(() => {\n            setAutoTriggerState(prev => {\n              const current = prev[key];\n              if (!current || current.countdown <= 1) {\n                clearInterval(countdownInterval);\n                return {\n                  ...prev,\n                  [key]: {\n                    ...current,\n                    countdown: 0\n                  }\n                };\n              }\n              return {\n                ...prev,\n                [key]: {\n                  ...current,\n                  countdown: current.countdown - 1\n                }\n              };\n            });\n          }, 1000);\n        }\n\n        // Execute trigger after delay\n        const timeoutId = setTimeout(() => {\n          handleApplyClick(record, actionIndex);\n          setAutoTriggerState(prev => ({\n            ...prev,\n            [key]: undefined\n          }));\n          if (autoTriggerConfig.onAfterTrigger) {\n            autoTriggerConfig.onAfterTrigger({\n              record,\n              action,\n              actionIndex\n            });\n          }\n        }, delay * 1000);\n        timeoutRefs.current[key] = timeoutId;\n      }\n    });\n  }, [getEligibleRecords, getRecordsToTrigger, autoTriggerConfig, executedTriggers, autoTriggerState, dataHash, hideRecordDisplay, formLinkingConfig]);\n\n  // Auto-trigger effect\n  useEffect(() => {\n    var _formLinkingConfig$re;\n    if (!autoTriggerConfig.enabled && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && (_formLinkingConfig$re = formLinkingConfig.recordActions) !== null && _formLinkingConfig$re !== void 0 && _formLinkingConfig$re.some(action => {\n      var _action$autoTrigger3;\n      return (_action$autoTrigger3 = action.autoTrigger) === null || _action$autoTrigger3 === void 0 ? void 0 : _action$autoTrigger3.enabled;\n    }))) {\n      return;\n    }\n    const timeoutId = setTimeout(executeAutoTrigger, 100);\n    return () => clearTimeout(timeoutId);\n  }, [executeAutoTrigger, autoTriggerConfig.enabled]);\n\n  // Handle Apply button click\n  const handleApplyClick = useCallback(async (record, actionIndex = 0) => {\n    const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\n    if (loadingActions[actionKey]) return;\n    setLoadingActions(prev => ({\n      ...prev,\n      [actionKey]: true\n    }));\n    try {\n      var _formLinkingConfig$re2;\n      const action = formLinkingConfig === null || formLinkingConfig === void 0 ? void 0 : (_formLinkingConfig$re2 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re2 === void 0 ? void 0 : _formLinkingConfig$re2[actionIndex];\n      const buttonText = (action === null || action === void 0 ? void 0 : action.buttonText) || 'Apply';\n      const response = await api.post(`/unifiedconfigs/${formId}/form-link`, {\n        recordData: record,\n        parentData: data,\n        actionIndex,\n        buttonText\n      });\n      if (response.data.success) {\n        const {\n          autoSubmitOnClick,\n          targetForm,\n          prefillData\n        } = response.data;\n        if (autoSubmitOnClick) {\n          await handleAutoSubmitLinkingForm(targetForm._id, prefillData, targetForm.name, actionKey);\n        } else if (onFormLinkTriggered) {\n          onFormLinkTriggered({\n            targetForm,\n            prefillData,\n            buttonText: response.data.buttonText,\n            buttonStyle: response.data.buttonStyle\n          });\n        }\n      } else {\n        throw new Error(response.data.message || 'Form linking failed');\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('❌ Error during form linking:', error);\n      alert('Error opening form: ' + (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || error.message));\n    } finally {\n      setLoadingActions(prev => ({\n        ...prev,\n        [actionKey]: false\n      }));\n    }\n  }, [formLinkingConfig, formId, data, loadingActions, onFormLinkTriggered]);\n\n  // Handle auto-submit linking form\n  const handleAutoSubmitLinkingForm = useCallback(async (targetFormId, prefillData, formName, actionKey) => {\n    try {\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n      const headers = {\n        'Authorization': userData.token ? `Bearer ${userData.token}` : undefined,\n        'x-emp-id': userData.empId || undefined,\n        'x-role-type': userData.roleType || undefined\n      };\n      const response = await api.post(`/unifiedconfigs/${targetFormId}/submit`, {\n        formData: prefillData\n      }, {\n        headers\n      });\n      if (response.data.success) {\n        alert(`✅ ${formName} submitted successfully!`);\n      } else {\n        throw new Error(response.data.message || 'Auto-submit failed');\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      console.error('❌ Error auto-submitting linking form:', error);\n      alert(`❌ Error submitting ${formName}: ${((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message}`);\n    } finally {\n      setLoadingActions(prev => ({\n        ...prev,\n        [actionKey]: false\n      }));\n    }\n  }, []);\n\n  // Get button style classes\n  const getButtonClasses = (style, isLoading) => {\n    const baseClasses = 'px-3 py-1 rounded text-sm font-medium transition-colors duration-200';\n    if (isLoading) {\n      return `${baseClasses} bg-gray-400 text-white cursor-not-allowed`;\n    }\n    const styles = {\n      primary: 'bg-blue-500 hover:bg-blue-600 text-white',\n      secondary: 'bg-gray-500 hover:bg-gray-600 text-white',\n      success: 'bg-green-500 hover:bg-green-600 text-white',\n      warning: 'bg-yellow-500 hover:bg-yellow-600 text-white',\n      danger: 'bg-red-500 hover:bg-red-600 text-white'\n    };\n    return `${baseClasses} ${styles[style] || styles.primary}`;\n  };\n\n  // Format field value for display\n  const formatFieldValue = value => {\n    if (value == null) return '-';\n    if (typeof value === 'boolean') return value ? 'Yes' : 'No';\n    if (typeof value === 'object') return JSON.stringify(value);\n    return value.toString();\n  };\n\n  // Render action buttons for a record\n  const renderActionButtons = (record, recordIndex) => {\n    if (!(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled) || !formLinkingConfig.recordActions) return null;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex flex-wrap gap-2\",\n      children: formLinkingConfig.recordActions.map((action, actionIndex) => {\n        var _action$autoTrigger4;\n        if (!shouldShowApplyButton(record, action.conditions)) return null;\n        const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\n        const stateKey = `${recordIndex}-${actionIndex}`;\n        const isLoading = loadingActions[actionKey];\n        const triggerState = autoTriggerState[stateKey];\n        const shouldAutoTrigger = ((_action$autoTrigger4 = action.autoTrigger) === null || _action$autoTrigger4 === void 0 ? void 0 : _action$autoTrigger4.enabled) || autoTriggerConfig.enabled;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: [!shouldAutoTrigger && /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleApplyClick(record, actionIndex),\n            disabled: isLoading,\n            className: getButtonClasses(action.buttonStyle, isLoading),\n            children: isLoading ? /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"animate-spin -ml-1 mr-1 h-3 w-3 text-white\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n                  className: \"opacity-25\",\n                  cx: \"12\",\n                  cy: \"12\",\n                  r: \"10\",\n                  stroke: \"currentColor\",\n                  strokeWidth: \"4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                  className: \"opacity-75\",\n                  fill: \"currentColor\",\n                  d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 439,\n                columnNumber: 23\n              }, this), \"Loading...\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 21\n            }, this) : action.buttonText || 'Apply'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 17\n          }, this), (triggerState === null || triggerState === void 0 ? void 0 : triggerState.countdown) > 0 && autoTriggerConfig.showCountdown && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 455,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 454,\n              columnNumber: 19\n            }, this), \"Auto-triggering in \", triggerState.countdown, \"s\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 17\n          }, this)]\n        }, actionIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 13\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Render individual record\n  const renderRecord = (record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mb-4\",\n    children: [!showOnlyButtons && /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-2\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium text-gray-800\",\n          children: [\"Record \", index + 1, \":\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 472,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-3 text-sm text-gray-700 bg-gray-50 p-3 rounded-lg\",\n        children: (() => {\n          const isLeaveData = record.hasOwnProperty('leaveTypeName') && record.hasOwnProperty('balance');\n          const fieldsToDisplay = isLeaveData ? [['leaveTypeName', record.leaveTypeName], ['balance', record.balance]] : Object.entries(record);\n          return fieldsToDisplay.map(([key, value], fieldIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [fieldIndex > 0 && ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: [key, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), \" \", formatFieldValue(value), fieldIndex < fieldsToDisplay.length - 1 && ' ']\n          }, key, true, {\n            fileName: _jsxFileName,\n            lineNumber: 484,\n            columnNumber: 17\n          }, this));\n        })()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), renderActionButtons(record, index)]\n  }, index, true, {\n    fileName: _jsxFileName,\n    lineNumber: 469,\n    columnNumber: 5\n  }, this);\n\n  // Early return for hidden display\n  if (hideRecordDisplay) return null;\n\n  // Render records\n  if (!processedRecords.length) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-gray-500 text-center py-4\",\n      children: \"No records to display\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 505,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-4\",\n    children: processedRecords.map(renderRecord)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 512,\n    columnNumber: 5\n  }, this);\n};\n_s(RecordDisplayWithActions, \"xP+ue6UjHk6YO5XpjMbSZ+dndE0=\");\n_c = RecordDisplayWithActions;\nexport default RecordDisplayWithActions;\nvar _c;\n$RefreshReg$(_c, \"RecordDisplayWithActions\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "useCallback", "useMemo", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RecordDisplayWithActions", "data", "formId", "formLinkingConfig", "onFormLinkTriggered", "hideRecordDisplay", "showOnlyButtons", "autoTriggerOptions", "_s", "loadingActions", "setLoadingActions", "autoTriggerState", "setAutoTriggerState", "executedTriggers", "setExecutedTriggers", "persistentTriggers", "localStorage", "getItem", "triggerTimestamp", "oneHourAgo", "Date", "now", "parseInt", "console", "log", "removeItem", "Set", "JSON", "parse", "componentId", "Math", "random", "toString", "substr", "timeoutRefs", "autoTriggerConfig", "_autoTriggerOptions$e", "_autoTriggerOptions$d", "_autoTriggerOptions$s", "_autoTriggerOptions$t", "_autoTriggerOptions$r", "_autoTriggerOptions$t2", "enabled", "delay", "showCountdown", "triggerOnce", "resetOnDataChange", "triggerCondition", "customTriggerLogic", "onBeforeTrigger", "onAfterTrigger", "dataHash", "stringify", "processedRecords", "Array", "isArray", "arrayFields", "field", "shouldShowApplyButton", "record", "conditions", "length", "validConditions", "filter", "condition", "_condition$field", "trim", "every", "fieldValue", "conditionValue", "value", "operator", "includes", "getEligibleRecords", "recordActions", "eligible", "for<PERSON>ach", "action", "actionIndex", "_action$autoTrigger", "should<PERSON>utoTrigger", "autoTrigger", "recordIndex", "push", "key", "getRecordsToTrigger", "eligibleRecords", "clearAllTimeouts", "Object", "values", "current", "clearTimeout", "lastChatLoad", "timeSinceLoad", "Infinity", "executeAutoTrigger", "recordsToTrigger", "_autoTriggerState$key", "_action$autoTrigger$d", "_action$autoTrigger2", "<PERSON><PERSON><PERSON>", "persistentTriggersSet", "has", "processing", "isLeaveRelated", "toLowerCase", "delaySeconds", "prev", "newSet", "add", "setItem", "handleApplyClick", "countdown", "countdownInterval", "setInterval", "clearInterval", "timeoutId", "setTimeout", "undefined", "_formLinkingConfig$re", "some", "_action$autoTrigger3", "action<PERSON>ey", "_formLinkingConfig$re2", "buttonText", "response", "post", "recordData", "parentData", "success", "autoSubmitOnClick", "targetForm", "prefillData", "handleAutoSubmitLinkingForm", "_id", "name", "buttonStyle", "Error", "message", "error", "_error$response", "_error$response$data", "alert", "targetFormId", "formName", "userData", "headers", "token", "empId", "roleType", "formData", "_error$response2", "_error$response2$data", "getButtonClasses", "style", "isLoading", "baseClasses", "styles", "primary", "secondary", "warning", "danger", "formatFieldValue", "renderActionButtons", "className", "children", "map", "_action$autoTrigger4", "stateKey", "triggerState", "onClick", "disabled", "xmlns", "fill", "viewBox", "cx", "cy", "r", "stroke", "strokeWidth", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "strokeLinecap", "strokeLinejoin", "renderRecord", "index", "isLeaveData", "hasOwnProperty", "fieldsToDisplay", "leaveTypeName", "balance", "entries", "fieldIndex", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/RecordDisplayWithActions.js"], "sourcesContent": ["import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';\r\nimport api from '../utils/api';\r\n\r\n/**\r\n * Component for displaying records with Apply buttons for form linking\r\n */\r\nconst RecordDisplayWithActions = ({ \r\n  data, \r\n  formId, \r\n  formLinkingConfig, \r\n  onFormLinkTriggered,\r\n  hideRecordDisplay = false,\r\n  showOnlyButtons = false,\r\n  autoTriggerOptions = {} // New flexible auto-trigger options\r\n}) => {\r\n  const [loadingActions, setLoadingActions] = useState({});\r\n  const [autoTriggerState, setAutoTriggerState] = useState({});\r\n  const [executedTriggers, setExecutedTriggers] = useState(() => {\r\n    // Initialize with persistent triggers from localStorage\r\n    const persistentTriggers = localStorage.getItem('persistentExecutedTriggers');\r\n    const triggerTimestamp = localStorage.getItem('persistentTriggersTimestamp');\r\n\r\n    // Clear old triggers if they're more than 1 hour old\r\n    const oneHourAgo = Date.now() - (60 * 60 * 1000);\r\n    if (triggerTimestamp && parseInt(triggerTimestamp) < oneHourAgo) {\r\n      console.log('🧹 Clearing old persistent triggers (older than 1 hour)');\r\n      localStorage.removeItem('persistentExecutedTriggers');\r\n      localStorage.removeItem('persistentTriggersTimestamp');\r\n      return new Set();\r\n    }\r\n\r\n    return persistentTriggers ? new Set(JSON.parse(persistentTriggers)) : new Set();\r\n  });\r\n  const componentId = useRef(Math.random().toString(36).substr(2, 9));\r\n  const timeoutRefs = useRef({});\r\n\r\n  // Auto-trigger configuration with defaults\r\n  const autoTriggerConfig = useMemo(() => ({\r\n    enabled: autoTriggerOptions.enabled ?? false,\r\n    delay: autoTriggerOptions.delay ?? 2,\r\n    showCountdown: autoTriggerOptions.showCountdown ?? true,\r\n    triggerOnce: autoTriggerOptions.triggerOnce ?? true,\r\n    resetOnDataChange: autoTriggerOptions.resetOnDataChange ?? true,\r\n    triggerCondition: autoTriggerOptions.triggerCondition ?? 'first', // 'first', 'all', 'custom'\r\n    customTriggerLogic: autoTriggerOptions.customTriggerLogic, // Custom function\r\n    onBeforeTrigger: autoTriggerOptions.onBeforeTrigger, // Callback before trigger\r\n    onAfterTrigger: autoTriggerOptions.onAfterTrigger, // Callback after trigger\r\n    ...autoTriggerOptions\r\n  }), [autoTriggerOptions]);\r\n\r\n  // Generate stable data hash for change detection\r\n  const dataHash = useMemo(() => JSON.stringify(data), [data]);\r\n\r\n  // Process records from various data structures\r\n  const processedRecords = useMemo(() => {\r\n    if (!data) return [];\r\n    \r\n    if (Array.isArray(data)) return data;\r\n    \r\n    if (typeof data === 'object') {\r\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n      \r\n      for (const field of arrayFields) {\r\n        if (data[field] && Array.isArray(data[field])) {\r\n          return data[field];\r\n        }\r\n      }\r\n      \r\n      return [data]; // Single object as record\r\n    }\r\n    \r\n    return [];\r\n  }, [data]);\r\n\r\n  // Check if record meets action conditions\r\n  const shouldShowApplyButton = useCallback((record, conditions = []) => {\r\n    if (!conditions?.length) return true;\r\n    \r\n    const validConditions = conditions.filter(condition => \r\n      condition.field?.trim()\r\n    );\r\n    \r\n    if (!validConditions.length) return true;\r\n    \r\n    return validConditions.every(condition => {\r\n      const fieldValue = record[condition.field];\r\n      const conditionValue = condition.value;\r\n      \r\n      switch (condition.operator) {\r\n        case 'equals':\r\n          return fieldValue === conditionValue;\r\n        case 'not_equals':\r\n          return fieldValue !== conditionValue;\r\n        case 'contains':\r\n          return fieldValue?.toString().includes(conditionValue);\r\n        case 'not_contains':\r\n          return !fieldValue?.toString().includes(conditionValue);\r\n        case 'exists':\r\n          return fieldValue != null && fieldValue !== '';\r\n        case 'not_exists':\r\n          return fieldValue == null || fieldValue === '';\r\n        default:\r\n          return true;\r\n      }\r\n    });\r\n  }, []);\r\n\r\n  // Get eligible records for auto-trigger\r\n  const getEligibleRecords = useCallback(() => {\r\n    if (!formLinkingConfig?.recordActions) return [];\r\n    \r\n    const eligible = [];\r\n    \r\n    formLinkingConfig.recordActions.forEach((action, actionIndex) => {\r\n      const shouldAutoTrigger = action.autoTrigger?.enabled || autoTriggerConfig.enabled;\r\n      \r\n      if (shouldAutoTrigger) {\r\n        processedRecords.forEach((record, recordIndex) => {\r\n          if (shouldShowApplyButton(record, action.conditions)) {\r\n            eligible.push({\r\n              record,\r\n              recordIndex,\r\n              action,\r\n              actionIndex,\r\n              key: `${recordIndex}-${actionIndex}`\r\n            });\r\n          }\r\n        });\r\n      }\r\n    });\r\n    \r\n    return eligible;\r\n  }, [processedRecords, formLinkingConfig, autoTriggerConfig.enabled, shouldShowApplyButton]);\r\n\r\n  // Determine which records to trigger based on condition\r\n  const getRecordsToTrigger = useCallback((eligibleRecords) => {\r\n    if (!eligibleRecords.length) return [];\r\n    \r\n    switch (autoTriggerConfig.triggerCondition) {\r\n      case 'first':\r\n        return [eligibleRecords[0]];\r\n      case 'all':\r\n        return eligibleRecords;\r\n      case 'custom':\r\n        return autoTriggerConfig.customTriggerLogic \r\n          ? autoTriggerConfig.customTriggerLogic(eligibleRecords)\r\n          : [eligibleRecords[0]];\r\n      default:\r\n        return [eligibleRecords[0]];\r\n    }\r\n  }, [autoTriggerConfig]);\r\n\r\n  // Clear all timeouts\r\n  const clearAllTimeouts = useCallback(() => {\r\n    Object.values(timeoutRefs.current).forEach(clearTimeout);\r\n    timeoutRefs.current = {};\r\n  }, []);\r\n\r\n  // Reset auto-trigger state when data changes\r\n  useEffect(() => {\r\n    if (!autoTriggerConfig.resetOnDataChange) return;\r\n\r\n    // Check if this is a chat opening scenario (don't reset triggers during chat open)\r\n    const lastChatLoad = localStorage.getItem('lastChatLoadTime');\r\n    const timeSinceLoad = lastChatLoad ? Date.now() - parseInt(lastChatLoad) : Infinity;\r\n\r\n    if (timeSinceLoad < 10000) { // Extended to 10 seconds for stronger prevention\r\n      console.log(`⏸️ [${componentId.current}] Skipping trigger reset - chat recently opened (${timeSinceLoad}ms ago)`);\r\n      return;\r\n    }\r\n\r\n    console.log(`🔄 [${componentId.current}] Data changed, resetting auto-trigger state`);\r\n\r\n    clearAllTimeouts();\r\n\r\n    // Don't clear executed triggers if we have persistent triggers\r\n    // This prevents duplicate triggers when data changes during normal operation\r\n    const persistentTriggers = localStorage.getItem('persistentExecutedTriggers');\r\n    if (!persistentTriggers) {\r\n      console.log(`🧹 [${componentId.current}] No persistent triggers found, clearing executed triggers`);\r\n      setExecutedTriggers(new Set());\r\n    } else {\r\n      console.log(`🔒 [${componentId.current}] Preserving persistent triggers during data change`);\r\n    }\r\n\r\n    setAutoTriggerState({});\r\n  }, [dataHash, autoTriggerConfig.resetOnDataChange, clearAllTimeouts]);\r\n\r\n  // Execute auto-trigger logic\r\n  const executeAutoTrigger = useCallback(() => {\r\n    const eligibleRecords = getEligibleRecords();\r\n    const recordsToTrigger = getRecordsToTrigger(eligibleRecords);\r\n    \r\n    recordsToTrigger.forEach(({ record, actionIndex, key }) => {\r\n      const triggerKey = `${dataHash}-${key}`;\r\n\r\n      // Check persistent triggers first\r\n      const persistentTriggers = localStorage.getItem('persistentExecutedTriggers');\r\n      const persistentTriggersSet = persistentTriggers ? new Set(JSON.parse(persistentTriggers)) : new Set();\r\n\r\n      // Skip if already triggered in persistent storage\r\n      if (persistentTriggersSet.has(triggerKey)) {\r\n        console.log(`🔒 [${componentId.current}] Skipping trigger - already executed persistently: ${triggerKey}`);\r\n        return;\r\n      }\r\n\r\n      // Skip if already triggered and triggerOnce is enabled\r\n      if (autoTriggerConfig.triggerOnce && executedTriggers.has(triggerKey)) {\r\n        console.log(`🔒 [${componentId.current}] Skipping trigger - already executed in session: ${triggerKey}`);\r\n        return;\r\n      }\r\n\r\n      // Skip if currently processing\r\n      if (autoTriggerState[key]?.processing) {\r\n        console.log(`⏸️ [${componentId.current}] Skipping trigger - currently processing: ${key}`);\r\n        return;\r\n      }\r\n\r\n      // Additional check for chat opening prevention specifically for leave-related triggers\r\n      const lastChatLoad = localStorage.getItem('lastChatLoadTime');\r\n      const timeSinceLoad = lastChatLoad ? Date.now() - parseInt(lastChatLoad) : Infinity;\r\n      const isLeaveRelated = key.includes('leave') || triggerKey.includes('leave') ||\r\n                            JSON.stringify(record).toLowerCase().includes('leave');\r\n\r\n      if (isLeaveRelated && timeSinceLoad < 15000) { // 15 seconds for leave-related triggers\r\n        console.log(`🚫 [${componentId.current}] Skipping leave-related trigger - chat recently opened (${timeSinceLoad}ms ago): ${key}`);\r\n        return;\r\n      }\r\n      \r\n      const action = formLinkingConfig.recordActions[actionIndex];\r\n      const delay = action.autoTrigger?.delaySeconds ?? autoTriggerConfig.delay;\r\n      \r\n      console.log(`⏰ [${componentId.current}] Auto-triggering in ${delay}s for key: ${key}`);\r\n      \r\n      // Call before trigger callback\r\n      if (autoTriggerConfig.onBeforeTrigger) {\r\n        autoTriggerConfig.onBeforeTrigger({ record, action, actionIndex });\r\n      }\r\n      \r\n      // Mark as executed\r\n      setExecutedTriggers(prev => {\r\n        const newSet = new Set(prev).add(triggerKey);\r\n        // Persist to localStorage to prevent duplicate triggers across sessions\r\n        localStorage.setItem('persistentExecutedTriggers', JSON.stringify([...newSet]));\r\n        localStorage.setItem('persistentTriggersTimestamp', Date.now().toString());\r\n        return newSet;\r\n      });\r\n      \r\n      if (hideRecordDisplay || delay === 0) {\r\n        // Immediate trigger\r\n        handleApplyClick(record, actionIndex);\r\n        \r\n        if (autoTriggerConfig.onAfterTrigger) {\r\n          autoTriggerConfig.onAfterTrigger({ record, action, actionIndex });\r\n        }\r\n      } else {\r\n        // Delayed trigger with countdown\r\n        setAutoTriggerState(prev => ({\r\n          ...prev,\r\n          [key]: { processing: true, countdown: delay }\r\n        }));\r\n        \r\n        // Countdown logic\r\n        if (autoTriggerConfig.showCountdown) {\r\n          const countdownInterval = setInterval(() => {\r\n            setAutoTriggerState(prev => {\r\n              const current = prev[key];\r\n              if (!current || current.countdown <= 1) {\r\n                clearInterval(countdownInterval);\r\n                return { ...prev, [key]: { ...current, countdown: 0 } };\r\n              }\r\n              return { ...prev, [key]: { ...current, countdown: current.countdown - 1 } };\r\n            });\r\n          }, 1000);\r\n        }\r\n        \r\n        // Execute trigger after delay\r\n        const timeoutId = setTimeout(() => {\r\n          handleApplyClick(record, actionIndex);\r\n          setAutoTriggerState(prev => ({ ...prev, [key]: undefined }));\r\n          \r\n          if (autoTriggerConfig.onAfterTrigger) {\r\n            autoTriggerConfig.onAfterTrigger({ record, action, actionIndex });\r\n          }\r\n        }, delay * 1000);\r\n        \r\n        timeoutRefs.current[key] = timeoutId;\r\n      }\r\n    });\r\n  }, [\r\n    getEligibleRecords, \r\n    getRecordsToTrigger, \r\n    autoTriggerConfig, \r\n    executedTriggers, \r\n    autoTriggerState, \r\n    dataHash, \r\n    hideRecordDisplay,\r\n    formLinkingConfig\r\n  ]);\r\n\r\n  // Auto-trigger effect\r\n  useEffect(() => {\r\n    if (!autoTriggerConfig.enabled && !formLinkingConfig?.recordActions?.some(action => action.autoTrigger?.enabled)) {\r\n      return;\r\n    }\r\n    \r\n    const timeoutId = setTimeout(executeAutoTrigger, 100);\r\n    return () => clearTimeout(timeoutId);\r\n  }, [executeAutoTrigger, autoTriggerConfig.enabled]);\r\n\r\n  // Handle Apply button click\r\n  const handleApplyClick = useCallback(async (record, actionIndex = 0) => {\r\n    const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\r\n    \r\n    if (loadingActions[actionKey]) return;\r\n    \r\n    setLoadingActions(prev => ({ ...prev, [actionKey]: true }));\r\n\r\n    try {\r\n      const action = formLinkingConfig?.recordActions?.[actionIndex];\r\n      const buttonText = action?.buttonText || 'Apply';\r\n      \r\n      const response = await api.post(`/unifiedconfigs/${formId}/form-link`, {\r\n        recordData: record,\r\n        parentData: data,\r\n        actionIndex,\r\n        buttonText\r\n      });\r\n\r\n      if (response.data.success) {\r\n        const { autoSubmitOnClick, targetForm, prefillData } = response.data;\r\n        \r\n        if (autoSubmitOnClick) {\r\n          await handleAutoSubmitLinkingForm(\r\n            targetForm._id, \r\n            prefillData,\r\n            targetForm.name,\r\n            actionKey\r\n          );\r\n        } else if (onFormLinkTriggered) {\r\n          onFormLinkTriggered({\r\n            targetForm,\r\n            prefillData,\r\n            buttonText: response.data.buttonText,\r\n            buttonStyle: response.data.buttonStyle\r\n          });\r\n        }\r\n      } else {\r\n        throw new Error(response.data.message || 'Form linking failed');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error during form linking:', error);\r\n      alert('Error opening form: ' + (error.response?.data?.message || error.message));\r\n    } finally {\r\n      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));\r\n    }\r\n  }, [formLinkingConfig, formId, data, loadingActions, onFormLinkTriggered]);\r\n\r\n  // Handle auto-submit linking form\r\n  const handleAutoSubmitLinkingForm = useCallback(async (targetFormId, prefillData, formName, actionKey) => {\r\n    try {\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      const headers = {\r\n        'Authorization': userData.token ? `Bearer ${userData.token}` : undefined,\r\n        'x-emp-id': userData.empId || undefined,\r\n        'x-role-type': userData.roleType || undefined,\r\n      };\r\n      \r\n      const response = await api.post(`/unifiedconfigs/${targetFormId}/submit`, {\r\n        formData: prefillData\r\n      }, { headers });\r\n      \r\n      if (response.data.success) {\r\n        alert(`✅ ${formName} submitted successfully!`);\r\n      } else {\r\n        throw new Error(response.data.message || 'Auto-submit failed');\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error auto-submitting linking form:', error);\r\n      alert(`❌ Error submitting ${formName}: ${error.response?.data?.message || error.message}`);\r\n    } finally {\r\n      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));\r\n    }\r\n  }, []);\r\n\r\n  // Get button style classes\r\n  const getButtonClasses = (style, isLoading) => {\r\n    const baseClasses = 'px-3 py-1 rounded text-sm font-medium transition-colors duration-200';\r\n    \r\n    if (isLoading) {\r\n      return `${baseClasses} bg-gray-400 text-white cursor-not-allowed`;\r\n    }\r\n\r\n    const styles = {\r\n      primary: 'bg-blue-500 hover:bg-blue-600 text-white',\r\n      secondary: 'bg-gray-500 hover:bg-gray-600 text-white',\r\n      success: 'bg-green-500 hover:bg-green-600 text-white',\r\n      warning: 'bg-yellow-500 hover:bg-yellow-600 text-white',\r\n      danger: 'bg-red-500 hover:bg-red-600 text-white'\r\n    };\r\n\r\n    return `${baseClasses} ${styles[style] || styles.primary}`;\r\n  };\r\n\r\n  // Format field value for display\r\n  const formatFieldValue = (value) => {\r\n    if (value == null) return '-';\r\n    if (typeof value === 'boolean') return value ? 'Yes' : 'No';\r\n    if (typeof value === 'object') return JSON.stringify(value);\r\n    return value.toString();\r\n  };\r\n\r\n  // Render action buttons for a record\r\n  const renderActionButtons = (record, recordIndex) => {\r\n    if (!formLinkingConfig?.enabled || !formLinkingConfig.recordActions) return null;\r\n\r\n    return (\r\n      <div className=\"flex flex-wrap gap-2\">\r\n        {formLinkingConfig.recordActions.map((action, actionIndex) => {\r\n          if (!shouldShowApplyButton(record, action.conditions)) return null;\r\n          \r\n          const actionKey = `${JSON.stringify(record)}-${actionIndex}`;\r\n          const stateKey = `${recordIndex}-${actionIndex}`;\r\n          const isLoading = loadingActions[actionKey];\r\n          const triggerState = autoTriggerState[stateKey];\r\n          const shouldAutoTrigger = action.autoTrigger?.enabled || autoTriggerConfig.enabled;\r\n          \r\n          return (\r\n            <div key={actionIndex} className=\"flex items-center gap-2\">\r\n              {/* Show button if not auto-triggering or if auto-trigger is disabled */}\r\n              {!shouldAutoTrigger && (\r\n                <button\r\n                  onClick={() => handleApplyClick(record, actionIndex)}\r\n                  disabled={isLoading}\r\n                  className={getButtonClasses(action.buttonStyle, isLoading)}\r\n                >\r\n                  {isLoading ? (\r\n                    <span className=\"flex items-center\">\r\n                      <svg className=\"animate-spin -ml-1 mr-1 h-3 w-3 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\r\n                        <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\r\n                        <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\r\n                      </svg>\r\n                      Loading...\r\n                    </span>\r\n                  ) : (\r\n                    action.buttonText || 'Apply'\r\n                  )}\r\n                </button>\r\n              )}\r\n              \r\n              {/* Auto-trigger countdown */}\r\n              {triggerState?.countdown > 0 && autoTriggerConfig.showCountdown && (\r\n                <div className=\"flex items-center text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded\">\r\n                  <svg className=\"w-4 h-4 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\r\n                  </svg>\r\n                  Auto-triggering in {triggerState.countdown}s\r\n                </div>\r\n              )}\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Render individual record\r\n  const renderRecord = (record, index) => (\r\n    <div key={index} className=\"mb-4\">\r\n      {!showOnlyButtons && (\r\n        <>\r\n          <div className=\"mb-2\">\r\n            <span className=\"font-medium text-gray-800\">Record {index + 1}:</span>\r\n          </div>\r\n          \r\n          <div className=\"mb-3 text-sm text-gray-700 bg-gray-50 p-3 rounded-lg\">\r\n            {(() => {\r\n              const isLeaveData = record.hasOwnProperty('leaveTypeName') && record.hasOwnProperty('balance');\r\n              const fieldsToDisplay = isLeaveData \r\n                ? [['leaveTypeName', record.leaveTypeName], ['balance', record.balance]]\r\n                : Object.entries(record);\r\n              \r\n              return fieldsToDisplay.map(([key, value], fieldIndex) => (\r\n                <span key={key}>\r\n                  {fieldIndex > 0 && ' '}\r\n                  <span className=\"font-medium\">{key}:</span> {formatFieldValue(value)}\r\n                  {fieldIndex < fieldsToDisplay.length - 1 && ' '}\r\n                </span>\r\n              ));\r\n            })()}\r\n          </div>\r\n        </>\r\n      )}\r\n      \r\n      {renderActionButtons(record, index)}\r\n    </div>\r\n  );\r\n\r\n  // Early return for hidden display\r\n  if (hideRecordDisplay) return null;\r\n\r\n  // Render records\r\n  if (!processedRecords.length) {\r\n    return (\r\n      <div className=\"text-gray-500 text-center py-4\">\r\n        No records to display\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      {processedRecords.map(renderRecord)}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RecordDisplayWithActions;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AAChF,OAAOC,GAAG,MAAM,cAAc;;AAE9B;AACA;AACA;AAFA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAGA,MAAMC,wBAAwB,GAAGA,CAAC;EAChCC,IAAI;EACJC,MAAM;EACNC,iBAAiB;EACjBC,mBAAmB;EACnBC,iBAAiB,GAAG,KAAK;EACzBC,eAAe,GAAG,KAAK;EACvBC,kBAAkB,GAAG,CAAC,CAAC,CAAC;AAC1B,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGpB,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAM,CAACqB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGtB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACuB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGxB,QAAQ,CAAC,MAAM;IAC7D;IACA,MAAMyB,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;IAC7E,MAAMC,gBAAgB,GAAGF,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC;;IAE5E;IACA,MAAME,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAI,EAAE,GAAG,EAAE,GAAG,IAAK;IAChD,IAAIH,gBAAgB,IAAII,QAAQ,CAACJ,gBAAgB,CAAC,GAAGC,UAAU,EAAE;MAC/DI,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;MACtER,YAAY,CAACS,UAAU,CAAC,4BAA4B,CAAC;MACrDT,YAAY,CAACS,UAAU,CAAC,6BAA6B,CAAC;MACtD,OAAO,IAAIC,GAAG,CAAC,CAAC;IAClB;IAEA,OAAOX,kBAAkB,GAAG,IAAIW,GAAG,CAACC,IAAI,CAACC,KAAK,CAACb,kBAAkB,CAAC,CAAC,GAAG,IAAIW,GAAG,CAAC,CAAC;EACjF,CAAC,CAAC;EACF,MAAMG,WAAW,GAAGrC,MAAM,CAACsC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACnE,MAAMC,WAAW,GAAG1C,MAAM,CAAC,CAAC,CAAC,CAAC;;EAE9B;EACA,MAAM2C,iBAAiB,GAAGzC,OAAO,CAAC;IAAA,IAAA0C,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAAA,OAAO;MACvCC,OAAO,GAAAN,qBAAA,GAAE7B,kBAAkB,CAACmC,OAAO,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,KAAK;MAC5CO,KAAK,GAAAN,qBAAA,GAAE9B,kBAAkB,CAACoC,KAAK,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,CAAC;MACpCO,aAAa,GAAAN,qBAAA,GAAE/B,kBAAkB,CAACqC,aAAa,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MACvDO,WAAW,GAAAN,qBAAA,GAAEhC,kBAAkB,CAACsC,WAAW,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MACnDO,iBAAiB,GAAAN,qBAAA,GAAEjC,kBAAkB,CAACuC,iBAAiB,cAAAN,qBAAA,cAAAA,qBAAA,GAAI,IAAI;MAC/DO,gBAAgB,GAAAN,sBAAA,GAAElC,kBAAkB,CAACwC,gBAAgB,cAAAN,sBAAA,cAAAA,sBAAA,GAAI,OAAO;MAAE;MAClEO,kBAAkB,EAAEzC,kBAAkB,CAACyC,kBAAkB;MAAE;MAC3DC,eAAe,EAAE1C,kBAAkB,CAAC0C,eAAe;MAAE;MACrDC,cAAc,EAAE3C,kBAAkB,CAAC2C,cAAc;MAAE;MACnD,GAAG3C;IACL,CAAC;EAAA,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;;EAEzB;EACA,MAAM4C,QAAQ,GAAGzD,OAAO,CAAC,MAAMiC,IAAI,CAACyB,SAAS,CAACnD,IAAI,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAE5D;EACA,MAAMoD,gBAAgB,GAAG3D,OAAO,CAAC,MAAM;IACrC,IAAI,CAACO,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAIqD,KAAK,CAACC,OAAO,CAACtD,IAAI,CAAC,EAAE,OAAOA,IAAI;IAEpC,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAMuD,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;MAE7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;QAC/B,IAAIvD,IAAI,CAACwD,KAAK,CAAC,IAAIH,KAAK,CAACC,OAAO,CAACtD,IAAI,CAACwD,KAAK,CAAC,CAAC,EAAE;UAC7C,OAAOxD,IAAI,CAACwD,KAAK,CAAC;QACpB;MACF;MAEA,OAAO,CAACxD,IAAI,CAAC,CAAC,CAAC;IACjB;IAEA,OAAO,EAAE;EACX,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;;EAEV;EACA,MAAMyD,qBAAqB,GAAGjE,WAAW,CAAC,CAACkE,MAAM,EAAEC,UAAU,GAAG,EAAE,KAAK;IACrE,IAAI,EAACA,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEC,MAAM,GAAE,OAAO,IAAI;IAEpC,MAAMC,eAAe,GAAGF,UAAU,CAACG,MAAM,CAACC,SAAS;MAAA,IAAAC,gBAAA;MAAA,QAAAA,gBAAA,GACjDD,SAAS,CAACP,KAAK,cAAAQ,gBAAA,uBAAfA,gBAAA,CAAiBC,IAAI,CAAC,CAAC;IAAA,CACzB,CAAC;IAED,IAAI,CAACJ,eAAe,CAACD,MAAM,EAAE,OAAO,IAAI;IAExC,OAAOC,eAAe,CAACK,KAAK,CAACH,SAAS,IAAI;MACxC,MAAMI,UAAU,GAAGT,MAAM,CAACK,SAAS,CAACP,KAAK,CAAC;MAC1C,MAAMY,cAAc,GAAGL,SAAS,CAACM,KAAK;MAEtC,QAAQN,SAAS,CAACO,QAAQ;QACxB,KAAK,QAAQ;UACX,OAAOH,UAAU,KAAKC,cAAc;QACtC,KAAK,YAAY;UACf,OAAOD,UAAU,KAAKC,cAAc;QACtC,KAAK,UAAU;UACb,OAAOD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpC,QAAQ,CAAC,CAAC,CAACwC,QAAQ,CAACH,cAAc,CAAC;QACxD,KAAK,cAAc;UACjB,OAAO,EAACD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAEpC,QAAQ,CAAC,CAAC,CAACwC,QAAQ,CAACH,cAAc,CAAC;QACzD,KAAK,QAAQ;UACX,OAAOD,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,EAAE;QAChD,KAAK,YAAY;UACf,OAAOA,UAAU,IAAI,IAAI,IAAIA,UAAU,KAAK,EAAE;QAChD;UACE,OAAO,IAAI;MACf;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMK,kBAAkB,GAAGhF,WAAW,CAAC,MAAM;IAC3C,IAAI,EAACU,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEuE,aAAa,GAAE,OAAO,EAAE;IAEhD,MAAMC,QAAQ,GAAG,EAAE;IAEnBxE,iBAAiB,CAACuE,aAAa,CAACE,OAAO,CAAC,CAACC,MAAM,EAAEC,WAAW,KAAK;MAAA,IAAAC,mBAAA;MAC/D,MAAMC,iBAAiB,GAAG,EAAAD,mBAAA,GAAAF,MAAM,CAACI,WAAW,cAAAF,mBAAA,uBAAlBA,mBAAA,CAAoBrC,OAAO,KAAIP,iBAAiB,CAACO,OAAO;MAElF,IAAIsC,iBAAiB,EAAE;QACrB3B,gBAAgB,CAACuB,OAAO,CAAC,CAACjB,MAAM,EAAEuB,WAAW,KAAK;UAChD,IAAIxB,qBAAqB,CAACC,MAAM,EAAEkB,MAAM,CAACjB,UAAU,CAAC,EAAE;YACpDe,QAAQ,CAACQ,IAAI,CAAC;cACZxB,MAAM;cACNuB,WAAW;cACXL,MAAM;cACNC,WAAW;cACXM,GAAG,EAAE,GAAGF,WAAW,IAAIJ,WAAW;YACpC,CAAC,CAAC;UACJ;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAOH,QAAQ;EACjB,CAAC,EAAE,CAACtB,gBAAgB,EAAElD,iBAAiB,EAAEgC,iBAAiB,CAACO,OAAO,EAAEgB,qBAAqB,CAAC,CAAC;;EAE3F;EACA,MAAM2B,mBAAmB,GAAG5F,WAAW,CAAE6F,eAAe,IAAK;IAC3D,IAAI,CAACA,eAAe,CAACzB,MAAM,EAAE,OAAO,EAAE;IAEtC,QAAQ1B,iBAAiB,CAACY,gBAAgB;MACxC,KAAK,OAAO;QACV,OAAO,CAACuC,eAAe,CAAC,CAAC,CAAC,CAAC;MAC7B,KAAK,KAAK;QACR,OAAOA,eAAe;MACxB,KAAK,QAAQ;QACX,OAAOnD,iBAAiB,CAACa,kBAAkB,GACvCb,iBAAiB,CAACa,kBAAkB,CAACsC,eAAe,CAAC,GACrD,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC;MAC1B;QACE,OAAO,CAACA,eAAe,CAAC,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC,EAAE,CAACnD,iBAAiB,CAAC,CAAC;;EAEvB;EACA,MAAMoD,gBAAgB,GAAG9F,WAAW,CAAC,MAAM;IACzC+F,MAAM,CAACC,MAAM,CAACvD,WAAW,CAACwD,OAAO,CAAC,CAACd,OAAO,CAACe,YAAY,CAAC;IACxDzD,WAAW,CAACwD,OAAO,GAAG,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;;EAEN;EACAnG,SAAS,CAAC,MAAM;IACd,IAAI,CAAC4C,iBAAiB,CAACW,iBAAiB,EAAE;;IAE1C;IACA,MAAM8C,YAAY,GAAG5E,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;IAC7D,MAAM4E,aAAa,GAAGD,YAAY,GAAGxE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,QAAQ,CAACsE,YAAY,CAAC,GAAGE,QAAQ;IAEnF,IAAID,aAAa,GAAG,KAAK,EAAE;MAAE;MAC3BtE,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,oDAAoDG,aAAa,SAAS,CAAC;MACjH;IACF;IAEAtE,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,8CAA8C,CAAC;IAErFH,gBAAgB,CAAC,CAAC;;IAElB;IACA;IACA,MAAMxE,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;IAC7E,IAAI,CAACF,kBAAkB,EAAE;MACvBQ,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,4DAA4D,CAAC;MACnG5E,mBAAmB,CAAC,IAAIY,GAAG,CAAC,CAAC,CAAC;IAChC,CAAC,MAAM;MACLH,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,qDAAqD,CAAC;IAC9F;IAEA9E,mBAAmB,CAAC,CAAC,CAAC,CAAC;EACzB,CAAC,EAAE,CAACuC,QAAQ,EAAEhB,iBAAiB,CAACW,iBAAiB,EAAEyC,gBAAgB,CAAC,CAAC;;EAErE;EACA,MAAMQ,kBAAkB,GAAGtG,WAAW,CAAC,MAAM;IAC3C,MAAM6F,eAAe,GAAGb,kBAAkB,CAAC,CAAC;IAC5C,MAAMuB,gBAAgB,GAAGX,mBAAmB,CAACC,eAAe,CAAC;IAE7DU,gBAAgB,CAACpB,OAAO,CAAC,CAAC;MAAEjB,MAAM;MAAEmB,WAAW;MAAEM;IAAI,CAAC,KAAK;MAAA,IAAAa,qBAAA,EAAAC,qBAAA,EAAAC,oBAAA;MACzD,MAAMC,UAAU,GAAG,GAAGjD,QAAQ,IAAIiC,GAAG,EAAE;;MAEvC;MACA,MAAMrE,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,4BAA4B,CAAC;MAC7E,MAAMoF,qBAAqB,GAAGtF,kBAAkB,GAAG,IAAIW,GAAG,CAACC,IAAI,CAACC,KAAK,CAACb,kBAAkB,CAAC,CAAC,GAAG,IAAIW,GAAG,CAAC,CAAC;;MAEtG;MACA,IAAI2E,qBAAqB,CAACC,GAAG,CAACF,UAAU,CAAC,EAAE;QACzC7E,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,uDAAuDU,UAAU,EAAE,CAAC;QAC1G;MACF;;MAEA;MACA,IAAIjE,iBAAiB,CAACU,WAAW,IAAIhC,gBAAgB,CAACyF,GAAG,CAACF,UAAU,CAAC,EAAE;QACrE7E,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,qDAAqDU,UAAU,EAAE,CAAC;QACxG;MACF;;MAEA;MACA,KAAAH,qBAAA,GAAItF,gBAAgB,CAACyE,GAAG,CAAC,cAAAa,qBAAA,eAArBA,qBAAA,CAAuBM,UAAU,EAAE;QACrChF,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,8CAA8CN,GAAG,EAAE,CAAC;QAC1F;MACF;;MAEA;MACA,MAAMQ,YAAY,GAAG5E,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;MAC7D,MAAM4E,aAAa,GAAGD,YAAY,GAAGxE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,QAAQ,CAACsE,YAAY,CAAC,GAAGE,QAAQ;MACnF,MAAMU,cAAc,GAAGpB,GAAG,CAACZ,QAAQ,CAAC,OAAO,CAAC,IAAI4B,UAAU,CAAC5B,QAAQ,CAAC,OAAO,CAAC,IACtD7C,IAAI,CAACyB,SAAS,CAACO,MAAM,CAAC,CAAC8C,WAAW,CAAC,CAAC,CAACjC,QAAQ,CAAC,OAAO,CAAC;MAE5E,IAAIgC,cAAc,IAAIX,aAAa,GAAG,KAAK,EAAE;QAAE;QAC7CtE,OAAO,CAACC,GAAG,CAAC,OAAOK,WAAW,CAAC6D,OAAO,4DAA4DG,aAAa,YAAYT,GAAG,EAAE,CAAC;QACjI;MACF;MAEA,MAAMP,MAAM,GAAG1E,iBAAiB,CAACuE,aAAa,CAACI,WAAW,CAAC;MAC3D,MAAMnC,KAAK,IAAAuD,qBAAA,IAAAC,oBAAA,GAAGtB,MAAM,CAACI,WAAW,cAAAkB,oBAAA,uBAAlBA,oBAAA,CAAoBO,YAAY,cAAAR,qBAAA,cAAAA,qBAAA,GAAI/D,iBAAiB,CAACQ,KAAK;MAEzEpB,OAAO,CAACC,GAAG,CAAC,MAAMK,WAAW,CAAC6D,OAAO,wBAAwB/C,KAAK,cAAcyC,GAAG,EAAE,CAAC;;MAEtF;MACA,IAAIjD,iBAAiB,CAACc,eAAe,EAAE;QACrCd,iBAAiB,CAACc,eAAe,CAAC;UAAEU,MAAM;UAAEkB,MAAM;UAAEC;QAAY,CAAC,CAAC;MACpE;;MAEA;MACAhE,mBAAmB,CAAC6F,IAAI,IAAI;QAC1B,MAAMC,MAAM,GAAG,IAAIlF,GAAG,CAACiF,IAAI,CAAC,CAACE,GAAG,CAACT,UAAU,CAAC;QAC5C;QACApF,YAAY,CAAC8F,OAAO,CAAC,4BAA4B,EAAEnF,IAAI,CAACyB,SAAS,CAAC,CAAC,GAAGwD,MAAM,CAAC,CAAC,CAAC;QAC/E5F,YAAY,CAAC8F,OAAO,CAAC,6BAA6B,EAAE1F,IAAI,CAACC,GAAG,CAAC,CAAC,CAACW,QAAQ,CAAC,CAAC,CAAC;QAC1E,OAAO4E,MAAM;MACf,CAAC,CAAC;MAEF,IAAIvG,iBAAiB,IAAIsC,KAAK,KAAK,CAAC,EAAE;QACpC;QACAoE,gBAAgB,CAACpD,MAAM,EAAEmB,WAAW,CAAC;QAErC,IAAI3C,iBAAiB,CAACe,cAAc,EAAE;UACpCf,iBAAiB,CAACe,cAAc,CAAC;YAAES,MAAM;YAAEkB,MAAM;YAAEC;UAAY,CAAC,CAAC;QACnE;MACF,CAAC,MAAM;QACL;QACAlE,mBAAmB,CAAC+F,IAAI,KAAK;UAC3B,GAAGA,IAAI;UACP,CAACvB,GAAG,GAAG;YAAEmB,UAAU,EAAE,IAAI;YAAES,SAAS,EAAErE;UAAM;QAC9C,CAAC,CAAC,CAAC;;QAEH;QACA,IAAIR,iBAAiB,CAACS,aAAa,EAAE;UACnC,MAAMqE,iBAAiB,GAAGC,WAAW,CAAC,MAAM;YAC1CtG,mBAAmB,CAAC+F,IAAI,IAAI;cAC1B,MAAMjB,OAAO,GAAGiB,IAAI,CAACvB,GAAG,CAAC;cACzB,IAAI,CAACM,OAAO,IAAIA,OAAO,CAACsB,SAAS,IAAI,CAAC,EAAE;gBACtCG,aAAa,CAACF,iBAAiB,CAAC;gBAChC,OAAO;kBAAE,GAAGN,IAAI;kBAAE,CAACvB,GAAG,GAAG;oBAAE,GAAGM,OAAO;oBAAEsB,SAAS,EAAE;kBAAE;gBAAE,CAAC;cACzD;cACA,OAAO;gBAAE,GAAGL,IAAI;gBAAE,CAACvB,GAAG,GAAG;kBAAE,GAAGM,OAAO;kBAAEsB,SAAS,EAAEtB,OAAO,CAACsB,SAAS,GAAG;gBAAE;cAAE,CAAC;YAC7E,CAAC,CAAC;UACJ,CAAC,EAAE,IAAI,CAAC;QACV;;QAEA;QACA,MAAMI,SAAS,GAAGC,UAAU,CAAC,MAAM;UACjCN,gBAAgB,CAACpD,MAAM,EAAEmB,WAAW,CAAC;UACrClE,mBAAmB,CAAC+F,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACvB,GAAG,GAAGkC;UAAU,CAAC,CAAC,CAAC;UAE5D,IAAInF,iBAAiB,CAACe,cAAc,EAAE;YACpCf,iBAAiB,CAACe,cAAc,CAAC;cAAES,MAAM;cAAEkB,MAAM;cAAEC;YAAY,CAAC,CAAC;UACnE;QACF,CAAC,EAAEnC,KAAK,GAAG,IAAI,CAAC;QAEhBT,WAAW,CAACwD,OAAO,CAACN,GAAG,CAAC,GAAGgC,SAAS;MACtC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CACD3C,kBAAkB,EAClBY,mBAAmB,EACnBlD,iBAAiB,EACjBtB,gBAAgB,EAChBF,gBAAgB,EAChBwC,QAAQ,EACR9C,iBAAiB,EACjBF,iBAAiB,CAClB,CAAC;;EAEF;EACAZ,SAAS,CAAC,MAAM;IAAA,IAAAgI,qBAAA;IACd,IAAI,CAACpF,iBAAiB,CAACO,OAAO,IAAI,EAACvC,iBAAiB,aAAjBA,iBAAiB,gBAAAoH,qBAAA,GAAjBpH,iBAAiB,CAAEuE,aAAa,cAAA6C,qBAAA,eAAhCA,qBAAA,CAAkCC,IAAI,CAAC3C,MAAM;MAAA,IAAA4C,oBAAA;MAAA,QAAAA,oBAAA,GAAI5C,MAAM,CAACI,WAAW,cAAAwC,oBAAA,uBAAlBA,oBAAA,CAAoB/E,OAAO;IAAA,EAAC,GAAE;MAChH;IACF;IAEA,MAAM0E,SAAS,GAAGC,UAAU,CAACtB,kBAAkB,EAAE,GAAG,CAAC;IACrD,OAAO,MAAMJ,YAAY,CAACyB,SAAS,CAAC;EACtC,CAAC,EAAE,CAACrB,kBAAkB,EAAE5D,iBAAiB,CAACO,OAAO,CAAC,CAAC;;EAEnD;EACA,MAAMqE,gBAAgB,GAAGtH,WAAW,CAAC,OAAOkE,MAAM,EAAEmB,WAAW,GAAG,CAAC,KAAK;IACtE,MAAM4C,SAAS,GAAG,GAAG/F,IAAI,CAACyB,SAAS,CAACO,MAAM,CAAC,IAAImB,WAAW,EAAE;IAE5D,IAAIrE,cAAc,CAACiH,SAAS,CAAC,EAAE;IAE/BhH,iBAAiB,CAACiG,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACe,SAAS,GAAG;IAAK,CAAC,CAAC,CAAC;IAE3D,IAAI;MAAA,IAAAC,sBAAA;MACF,MAAM9C,MAAM,GAAG1E,iBAAiB,aAAjBA,iBAAiB,wBAAAwH,sBAAA,GAAjBxH,iBAAiB,CAAEuE,aAAa,cAAAiD,sBAAA,uBAAhCA,sBAAA,CAAmC7C,WAAW,CAAC;MAC9D,MAAM8C,UAAU,GAAG,CAAA/C,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE+C,UAAU,KAAI,OAAO;MAEhD,MAAMC,QAAQ,GAAG,MAAMlI,GAAG,CAACmI,IAAI,CAAC,mBAAmB5H,MAAM,YAAY,EAAE;QACrE6H,UAAU,EAAEpE,MAAM;QAClBqE,UAAU,EAAE/H,IAAI;QAChB6E,WAAW;QACX8C;MACF,CAAC,CAAC;MAEF,IAAIC,QAAQ,CAAC5H,IAAI,CAACgI,OAAO,EAAE;QACzB,MAAM;UAAEC,iBAAiB;UAAEC,UAAU;UAAEC;QAAY,CAAC,GAAGP,QAAQ,CAAC5H,IAAI;QAEpE,IAAIiI,iBAAiB,EAAE;UACrB,MAAMG,2BAA2B,CAC/BF,UAAU,CAACG,GAAG,EACdF,WAAW,EACXD,UAAU,CAACI,IAAI,EACfb,SACF,CAAC;QACH,CAAC,MAAM,IAAItH,mBAAmB,EAAE;UAC9BA,mBAAmB,CAAC;YAClB+H,UAAU;YACVC,WAAW;YACXR,UAAU,EAAEC,QAAQ,CAAC5H,IAAI,CAAC2H,UAAU;YACpCY,WAAW,EAAEX,QAAQ,CAAC5H,IAAI,CAACuI;UAC7B,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,MAAM,IAAIC,KAAK,CAACZ,QAAQ,CAAC5H,IAAI,CAACyI,OAAO,IAAI,qBAAqB,CAAC;MACjE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACdtH,OAAO,CAACoH,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDG,KAAK,CAAC,sBAAsB,IAAI,EAAAF,eAAA,GAAAD,KAAK,CAACd,QAAQ,cAAAe,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgB3I,IAAI,cAAA4I,oBAAA,uBAApBA,oBAAA,CAAsBH,OAAO,KAAIC,KAAK,CAACD,OAAO,CAAC,CAAC;IAClF,CAAC,SAAS;MACRhI,iBAAiB,CAACiG,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACe,SAAS,GAAG;MAAM,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC,EAAE,CAACvH,iBAAiB,EAAED,MAAM,EAAED,IAAI,EAAEQ,cAAc,EAAEL,mBAAmB,CAAC,CAAC;;EAE1E;EACA,MAAMiI,2BAA2B,GAAG5I,WAAW,CAAC,OAAOsJ,YAAY,EAAEX,WAAW,EAAEY,QAAQ,EAAEtB,SAAS,KAAK;IACxG,IAAI;MACF,MAAMuB,QAAQ,GAAGtH,IAAI,CAACC,KAAK,CAACZ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MACjE,MAAMiI,OAAO,GAAG;QACd,eAAe,EAAED,QAAQ,CAACE,KAAK,GAAG,UAAUF,QAAQ,CAACE,KAAK,EAAE,GAAG7B,SAAS;QACxE,UAAU,EAAE2B,QAAQ,CAACG,KAAK,IAAI9B,SAAS;QACvC,aAAa,EAAE2B,QAAQ,CAACI,QAAQ,IAAI/B;MACtC,CAAC;MAED,MAAMO,QAAQ,GAAG,MAAMlI,GAAG,CAACmI,IAAI,CAAC,mBAAmBiB,YAAY,SAAS,EAAE;QACxEO,QAAQ,EAAElB;MACZ,CAAC,EAAE;QAAEc;MAAQ,CAAC,CAAC;MAEf,IAAIrB,QAAQ,CAAC5H,IAAI,CAACgI,OAAO,EAAE;QACzBa,KAAK,CAAC,KAAKE,QAAQ,0BAA0B,CAAC;MAChD,CAAC,MAAM;QACL,MAAM,IAAIP,KAAK,CAACZ,QAAQ,CAAC5H,IAAI,CAACyI,OAAO,IAAI,oBAAoB,CAAC;MAChE;IACF,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAA,IAAAY,gBAAA,EAAAC,qBAAA;MACdjI,OAAO,CAACoH,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DG,KAAK,CAAC,sBAAsBE,QAAQ,KAAK,EAAAO,gBAAA,GAAAZ,KAAK,CAACd,QAAQ,cAAA0B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBtJ,IAAI,cAAAuJ,qBAAA,uBAApBA,qBAAA,CAAsBd,OAAO,KAAIC,KAAK,CAACD,OAAO,EAAE,CAAC;IAC5F,CAAC,SAAS;MACRhI,iBAAiB,CAACiG,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACe,SAAS,GAAG;MAAM,CAAC,CAAC,CAAC;IAC9D;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAM+B,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;IAC7C,MAAMC,WAAW,GAAG,sEAAsE;IAE1F,IAAID,SAAS,EAAE;MACb,OAAO,GAAGC,WAAW,4CAA4C;IACnE;IAEA,MAAMC,MAAM,GAAG;MACbC,OAAO,EAAE,0CAA0C;MACnDC,SAAS,EAAE,0CAA0C;MACrD9B,OAAO,EAAE,4CAA4C;MACrD+B,OAAO,EAAE,8CAA8C;MACvDC,MAAM,EAAE;IACV,CAAC;IAED,OAAO,GAAGL,WAAW,IAAIC,MAAM,CAACH,KAAK,CAAC,IAAIG,MAAM,CAACC,OAAO,EAAE;EAC5D,CAAC;;EAED;EACA,MAAMI,gBAAgB,GAAI5F,KAAK,IAAK;IAClC,IAAIA,KAAK,IAAI,IAAI,EAAE,OAAO,GAAG;IAC7B,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE,OAAOA,KAAK,GAAG,KAAK,GAAG,IAAI;IAC3D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO3C,IAAI,CAACyB,SAAS,CAACkB,KAAK,CAAC;IAC3D,OAAOA,KAAK,CAACtC,QAAQ,CAAC,CAAC;EACzB,CAAC;;EAED;EACA,MAAMmI,mBAAmB,GAAGA,CAACxG,MAAM,EAAEuB,WAAW,KAAK;IACnD,IAAI,EAAC/E,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEuC,OAAO,KAAI,CAACvC,iBAAiB,CAACuE,aAAa,EAAE,OAAO,IAAI;IAEhF,oBACE7E,OAAA;MAAKuK,SAAS,EAAC,sBAAsB;MAAAC,QAAA,EAClClK,iBAAiB,CAACuE,aAAa,CAAC4F,GAAG,CAAC,CAACzF,MAAM,EAAEC,WAAW,KAAK;QAAA,IAAAyF,oBAAA;QAC5D,IAAI,CAAC7G,qBAAqB,CAACC,MAAM,EAAEkB,MAAM,CAACjB,UAAU,CAAC,EAAE,OAAO,IAAI;QAElE,MAAM8D,SAAS,GAAG,GAAG/F,IAAI,CAACyB,SAAS,CAACO,MAAM,CAAC,IAAImB,WAAW,EAAE;QAC5D,MAAM0F,QAAQ,GAAG,GAAGtF,WAAW,IAAIJ,WAAW,EAAE;QAChD,MAAM6E,SAAS,GAAGlJ,cAAc,CAACiH,SAAS,CAAC;QAC3C,MAAM+C,YAAY,GAAG9J,gBAAgB,CAAC6J,QAAQ,CAAC;QAC/C,MAAMxF,iBAAiB,GAAG,EAAAuF,oBAAA,GAAA1F,MAAM,CAACI,WAAW,cAAAsF,oBAAA,uBAAlBA,oBAAA,CAAoB7H,OAAO,KAAIP,iBAAiB,CAACO,OAAO;QAElF,oBACE7C,OAAA;UAAuBuK,SAAS,EAAC,yBAAyB;UAAAC,QAAA,GAEvD,CAACrF,iBAAiB,iBACjBnF,OAAA;YACE6K,OAAO,EAAEA,CAAA,KAAM3D,gBAAgB,CAACpD,MAAM,EAAEmB,WAAW,CAAE;YACrD6F,QAAQ,EAAEhB,SAAU;YACpBS,SAAS,EAAEX,gBAAgB,CAAC5E,MAAM,CAAC2D,WAAW,EAAEmB,SAAS,CAAE;YAAAU,QAAA,EAE1DV,SAAS,gBACR9J,OAAA;cAAMuK,SAAS,EAAC,mBAAmB;cAAAC,QAAA,gBACjCxK,OAAA;gBAAKuK,SAAS,EAAC,4CAA4C;gBAACQ,KAAK,EAAC,4BAA4B;gBAACC,IAAI,EAAC,MAAM;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,gBAC5HxK,OAAA;kBAAQuK,SAAS,EAAC,YAAY;kBAACW,EAAE,EAAC,IAAI;kBAACC,EAAE,EAAC,IAAI;kBAACC,CAAC,EAAC,IAAI;kBAACC,MAAM,EAAC,cAAc;kBAACC,WAAW,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAS,CAAC,eACrG1L,OAAA;kBAAMuK,SAAS,EAAC,YAAY;kBAACS,IAAI,EAAC,cAAc;kBAACW,CAAC,EAAC;gBAAiH;kBAAAJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzK,CAAC,cAER;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,GAEP1G,MAAM,CAAC+C,UAAU,IAAI;UACtB;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CACT,EAGA,CAAAd,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEzD,SAAS,IAAG,CAAC,IAAI7E,iBAAiB,CAACS,aAAa,iBAC7D/C,OAAA;YAAKuK,SAAS,EAAC,sEAAsE;YAAAC,QAAA,gBACnFxK,OAAA;cAAKuK,SAAS,EAAC,cAAc;cAACS,IAAI,EAAC,MAAM;cAACK,MAAM,EAAC,cAAc;cAACJ,OAAO,EAAC,WAAW;cAAAT,QAAA,eACjFxK,OAAA;gBAAM4L,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACP,WAAW,EAAE,CAAE;gBAACK,CAAC,EAAC;cAA6C;gBAAAJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH,CAAC,uBACa,EAACd,YAAY,CAACzD,SAAS,EAAC,GAC7C;UAAA;YAAAoE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CACN;QAAA,GA9BOzG,WAAW;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA+BhB,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;;EAED;EACA,MAAMI,YAAY,GAAGA,CAAChI,MAAM,EAAEiI,KAAK,kBACjC/L,OAAA;IAAiBuK,SAAS,EAAC,MAAM;IAAAC,QAAA,GAC9B,CAAC/J,eAAe,iBACfT,OAAA,CAAAE,SAAA;MAAAsK,QAAA,gBACExK,OAAA;QAAKuK,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBxK,OAAA;UAAMuK,SAAS,EAAC,2BAA2B;UAAAC,QAAA,GAAC,SAAO,EAACuB,KAAK,GAAG,CAAC,EAAC,GAAC;QAAA;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eAEN1L,OAAA;QAAKuK,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClE,CAAC,MAAM;UACN,MAAMwB,WAAW,GAAGlI,MAAM,CAACmI,cAAc,CAAC,eAAe,CAAC,IAAInI,MAAM,CAACmI,cAAc,CAAC,SAAS,CAAC;UAC9F,MAAMC,eAAe,GAAGF,WAAW,GAC/B,CAAC,CAAC,eAAe,EAAElI,MAAM,CAACqI,aAAa,CAAC,EAAE,CAAC,SAAS,EAAErI,MAAM,CAACsI,OAAO,CAAC,CAAC,GACtEzG,MAAM,CAAC0G,OAAO,CAACvI,MAAM,CAAC;UAE1B,OAAOoI,eAAe,CAACzB,GAAG,CAAC,CAAC,CAAClF,GAAG,EAAEd,KAAK,CAAC,EAAE6H,UAAU,kBAClDtM,OAAA;YAAAwK,QAAA,GACG8B,UAAU,GAAG,CAAC,IAAI,GAAG,eACtBtM,OAAA;cAAMuK,SAAS,EAAC,aAAa;cAAAC,QAAA,GAAEjF,GAAG,EAAC,GAAC;YAAA;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,KAAC,EAACrB,gBAAgB,CAAC5F,KAAK,CAAC,EACnE6H,UAAU,GAAGJ,eAAe,CAAClI,MAAM,GAAG,CAAC,IAAI,GAAG;UAAA,GAHtCuB,GAAG;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIR,CACP,CAAC;QACJ,CAAC,EAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA,eACN,CACH,EAEApB,mBAAmB,CAACxG,MAAM,EAAEiI,KAAK,CAAC;EAAA,GA1B3BA,KAAK;IAAAR,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA2BV,CACN;;EAED;EACA,IAAIlL,iBAAiB,EAAE,OAAO,IAAI;;EAElC;EACA,IAAI,CAACgD,gBAAgB,CAACQ,MAAM,EAAE;IAC5B,oBACEhE,OAAA;MAAKuK,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAAC;IAEhD;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACE1L,OAAA;IAAKuK,SAAS,EAAC,WAAW;IAAAC,QAAA,EACvBhH,gBAAgB,CAACiH,GAAG,CAACqB,YAAY;EAAC;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChC,CAAC;AAEV,CAAC;AAAC/K,EAAA,CA7fIR,wBAAwB;AAAAoM,EAAA,GAAxBpM,wBAAwB;AA+f9B,eAAeA,wBAAwB;AAAC,IAAAoM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}