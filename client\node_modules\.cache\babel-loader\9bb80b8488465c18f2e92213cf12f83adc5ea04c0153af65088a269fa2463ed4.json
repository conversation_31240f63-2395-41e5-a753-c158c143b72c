{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\AutoTriggerHandler.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport DynamicForm from './DynamicForm';\nimport api from '../utils/api';\n\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AutoTriggerHandler = ({\n  formId,\n  recordData,\n  onFormSubmit,\n  enabled = true\n}) => {\n  _s();\n  const [isChecking, setIsChecking] = useState(false);\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\n  const [countdown, setCountdown] = useState(0);\n  useEffect(() => {\n    if (!enabled || !formId || !recordData) return;\n\n    // Check if chat is still initializing\n    const isChatInitializing = () => {\n      const initFlag = localStorage.getItem('chatInitializing');\n      const initStartTime = localStorage.getItem('chatInitStartTime');\n      if (initFlag === 'true') {\n        console.log('🚫 Preventing auto-trigger - chat is still initializing');\n        return true;\n      }\n\n      // Also check if initialization started recently (within last 5 seconds)\n      if (initStartTime) {\n        const timeSinceInit = Date.now() - parseInt(initStartTime);\n        if (timeSinceInit < 5000) {\n          console.log('🚫 Preventing auto-trigger - chat initialized recently');\n          return true;\n        }\n      }\n      return false;\n    };\n\n    // Don't auto-trigger during chat initialization\n    if (isChatInitializing()) {\n      return;\n    }\n\n    // Check if this is a leave-related auto-trigger that should be prevented\n    const isLeaveRelatedTrigger = () => {\n      var _recentMessages$filte, _recentMessages$filte2;\n      // Check if the recordData contains leave-related information\n      const recordStr = JSON.stringify(recordData).toLowerCase();\n      const isLeaveData = recordStr.includes('leave') || recordStr.includes('balance') || recordStr.includes('sick') || recordStr.includes('casual') || recordStr.includes('privilege');\n      if (!isLeaveData) return false;\n\n      // Check if user is actually asking about leave balance\n      const recentMessages = JSON.parse(localStorage.getItem('chatMessages') || '[]');\n      const lastUserMessage = ((_recentMessages$filte = recentMessages.filter(msg => msg.role === 'user').slice(-1)[0]) === null || _recentMessages$filte === void 0 ? void 0 : (_recentMessages$filte2 = _recentMessages$filte.content) === null || _recentMessages$filte2 === void 0 ? void 0 : _recentMessages$filte2.toLowerCase()) || '';\n      const isLeaveQuery = lastUserMessage.includes('leave') || lastUserMessage.includes('balance') || lastUserMessage.includes('sick') || lastUserMessage.includes('casual') || lastUserMessage.includes('privilege');\n      if (!isLeaveQuery) {\n        console.log('🚫 Preventing leave-related auto-trigger - user not asking about leave');\n        return true;\n      }\n      return false;\n    };\n\n    // Don't auto-trigger leave forms unless user is asking about leave\n    if (isLeaveRelatedTrigger()) {\n      return;\n    }\n    const checkAutoTrigger = async () => {\n      setIsChecking(true);\n      try {\n        console.log('🔍 Checking auto-trigger for form:', formId);\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\n          recordData\n        });\n        if (response.data.success && response.data.shouldTrigger) {\n          const triggerInfo = response.data;\n          const delay = triggerInfo.delaySeconds || 0;\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\n          if (delay > 0) {\n            // Show countdown\n            setCountdown(delay);\n            const countdownInterval = setInterval(() => {\n              setCountdown(prev => {\n                if (prev <= 1) {\n                  clearInterval(countdownInterval);\n                  triggerForm(triggerInfo);\n                  return 0;\n                }\n                return prev - 1;\n              });\n            }, 1000);\n          } else {\n            // Trigger immediately\n            triggerForm(triggerInfo);\n          }\n        }\n      } catch (error) {\n        console.error('❌ Error checking auto-trigger:', error);\n      } finally {\n        setIsChecking(false);\n      }\n    };\n    const triggerForm = async triggerInfo => {\n      try {\n        // Process the form linking to get the target form and prefilled data\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\n          recordData,\n          parentData: {},\n          actionIndex: triggerInfo.actionIndex\n        });\n        if (linkingResponse.data.success) {\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\n\n          // Set the auto-triggered form data\n          setAutoTriggerForm({\n            ...linkingResponse.data.targetForm,\n            prefillData: linkingResponse.data.prefillData,\n            buttonText: linkingResponse.data.buttonText,\n            isAutoTriggered: true\n          });\n          setShowAutoTriggerModal(true);\n        }\n      } catch (error) {\n        console.error('❌ Error processing auto-trigger:', error);\n      }\n    };\n    checkAutoTrigger();\n  }, [formId, recordData, enabled]);\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\n    try {\n      // Submit the form\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\n      if (response.data.success) {\n        setShowAutoTriggerModal(false);\n        setAutoTriggerForm(null);\n\n        // Notify parent component\n        if (onFormSubmit) {\n          onFormSubmit(formId, formData, response.data);\n        }\n      }\n    } catch (error) {\n      console.error('❌ Error submitting auto-triggered form:', error);\n    }\n  };\n  const handleCloseAutoTriggerModal = () => {\n    setShowAutoTriggerModal(false);\n    setAutoTriggerForm(null);\n    setCountdown(0);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [countdown > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-sm font-medium\",\n          children: [\"Auto-opening form in \", countdown, \"s\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }, this), showAutoTriggerModal && autoTriggerForm && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-blue-500 text-white px-6 py-4 rounded-t-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-lg font-semibold\",\n                children: \"\\uD83D\\uDE80 Auto-Opened Form\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm text-blue-100 mt-1\",\n                children: autoTriggerForm.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCloseAutoTriggerModal,\n              className: \"text-white hover:text-gray-200 focus:outline-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: \"2\",\n                  d: \"M6 18L18 6M6 6l12 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"h-5 w-5 text-blue-400\",\n                  viewBox: \"0 0 20 20\",\n                  fill: \"currentColor\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                    clipRule: \"evenodd\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 226,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 225,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"ml-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-sm font-medium text-blue-800\",\n                  children: \"Auto-Triggered Form\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mt-1 text-sm text-blue-700\",\n                  children: \"This form was automatically opened based on the data you're viewing. Some fields may be pre-filled based on the original record.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 231,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(DynamicForm, {\n            form: autoTriggerForm,\n            onSubmit: handleAutoTriggerFormSubmit,\n            onCancel: handleCloseAutoTriggerModal\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(AutoTriggerHandler, \"AMZ419gay0+2AF7PRQoJXJ6hDfU=\");\n_c = AutoTriggerHandler;\nexport default AutoTriggerHandler;\nvar _c;\n$RefreshReg$(_c, \"AutoTriggerHandler\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "DynamicForm", "api", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AutoTriggerHandler", "formId", "recordData", "onFormSubmit", "enabled", "_s", "isChecking", "setIsChecking", "autoTriggerForm", "setAutoTriggerForm", "showAutoTriggerModal", "setShowAutoTriggerModal", "countdown", "setCountdown", "isChatInitializing", "initFlag", "localStorage", "getItem", "initStartTime", "console", "log", "timeSinceInit", "Date", "now", "parseInt", "isLeaveRelatedTrigger", "_recentMessages$filte", "_recentMessages$filte2", "recordStr", "JSON", "stringify", "toLowerCase", "isLeaveData", "includes", "recentMessages", "parse", "lastUserMessage", "filter", "msg", "role", "slice", "content", "is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "checkAutoTrigger", "response", "post", "data", "success", "should<PERSON><PERSON>ger", "triggerInfo", "delay", "delaySeconds", "countdownInterval", "setInterval", "prev", "clearInterval", "triggerForm", "error", "linkingResponse", "parentData", "actionIndex", "targetForm", "name", "prefillData", "buttonText", "isAutoTriggered", "handleAutoTriggerFormSubmit", "formData", "handleCloseAutoTriggerModal", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fillRule", "clipRule", "form", "onSubmit", "onCancel", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/AutoTriggerHandler.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport DynamicForm from './DynamicForm';\r\nimport api from '../utils/api';\r\n\r\n/**\r\n * AutoTriggerHandler - Component to handle auto-trigger form linking\r\n * This component checks if any forms should be auto-triggered when data is displayed\r\n * and provides a modal for the auto-triggered form\r\n */\r\nconst AutoTriggerHandler = ({ \r\n  formId, \r\n  recordData, \r\n  onFormSubmit,\r\n  enabled = true \r\n}) => {\r\n  const [isChecking, setIsChecking] = useState(false);\r\n  const [autoTriggerForm, setAutoTriggerForm] = useState(null);\r\n  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);\r\n  const [countdown, setCountdown] = useState(0);\r\n\r\n  useEffect(() => {\r\n    if (!enabled || !formId || !recordData) return;\r\n\r\n    // Check if chat is still initializing\r\n    const isChatInitializing = () => {\r\n      const initFlag = localStorage.getItem('chatInitializing');\r\n      const initStartTime = localStorage.getItem('chatInitStartTime');\r\n\r\n      if (initFlag === 'true') {\r\n        console.log('🚫 Preventing auto-trigger - chat is still initializing');\r\n        return true;\r\n      }\r\n\r\n      // Also check if initialization started recently (within last 5 seconds)\r\n      if (initStartTime) {\r\n        const timeSinceInit = Date.now() - parseInt(initStartTime);\r\n        if (timeSinceInit < 5000) {\r\n          console.log('🚫 Preventing auto-trigger - chat initialized recently');\r\n          return true;\r\n        }\r\n      }\r\n\r\n      return false;\r\n    };\r\n\r\n    // Don't auto-trigger during chat initialization\r\n    if (isChatInitializing()) {\r\n      return;\r\n    }\r\n\r\n    // Check if this is a leave-related auto-trigger that should be prevented\r\n    const isLeaveRelatedTrigger = () => {\r\n      // Check if the recordData contains leave-related information\r\n      const recordStr = JSON.stringify(recordData).toLowerCase();\r\n      const isLeaveData = recordStr.includes('leave') || recordStr.includes('balance') ||\r\n                         recordStr.includes('sick') || recordStr.includes('casual') ||\r\n                         recordStr.includes('privilege');\r\n\r\n      if (!isLeaveData) return false;\r\n\r\n      // Check if user is actually asking about leave balance\r\n      const recentMessages = JSON.parse(localStorage.getItem('chatMessages') || '[]');\r\n      const lastUserMessage = recentMessages\r\n        .filter(msg => msg.role === 'user')\r\n        .slice(-1)[0]?.content?.toLowerCase() || '';\r\n\r\n      const isLeaveQuery = lastUserMessage.includes('leave') ||\r\n                          lastUserMessage.includes('balance') ||\r\n                          lastUserMessage.includes('sick') ||\r\n                          lastUserMessage.includes('casual') ||\r\n                          lastUserMessage.includes('privilege');\r\n\r\n      if (!isLeaveQuery) {\r\n        console.log('🚫 Preventing leave-related auto-trigger - user not asking about leave');\r\n        return true;\r\n      }\r\n\r\n      return false;\r\n    };\r\n\r\n    // Don't auto-trigger leave forms unless user is asking about leave\r\n    if (isLeaveRelatedTrigger()) {\r\n      return;\r\n    }\r\n\r\n    const checkAutoTrigger = async () => {\r\n      setIsChecking(true);\r\n      try {\r\n        console.log('🔍 Checking auto-trigger for form:', formId);\r\n        \r\n        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {\r\n          recordData\r\n        });\r\n\r\n        if (response.data.success && response.data.shouldTrigger) {\r\n          const triggerInfo = response.data;\r\n          const delay = triggerInfo.delaySeconds || 0;\r\n          \r\n          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);\r\n          \r\n          if (delay > 0) {\r\n            // Show countdown\r\n            setCountdown(delay);\r\n            const countdownInterval = setInterval(() => {\r\n              setCountdown(prev => {\r\n                if (prev <= 1) {\r\n                  clearInterval(countdownInterval);\r\n                  triggerForm(triggerInfo);\r\n                  return 0;\r\n                }\r\n                return prev - 1;\r\n              });\r\n            }, 1000);\r\n          } else {\r\n            // Trigger immediately\r\n            triggerForm(triggerInfo);\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Error checking auto-trigger:', error);\r\n      } finally {\r\n        setIsChecking(false);\r\n      }\r\n    };\r\n\r\n    const triggerForm = async (triggerInfo) => {\r\n      try {\r\n        // Process the form linking to get the target form and prefilled data\r\n        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {\r\n          recordData,\r\n          parentData: {},\r\n          actionIndex: triggerInfo.actionIndex\r\n        });\r\n\r\n        if (linkingResponse.data.success) {\r\n          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);\r\n          \r\n          // Set the auto-triggered form data\r\n          setAutoTriggerForm({\r\n            ...linkingResponse.data.targetForm,\r\n            prefillData: linkingResponse.data.prefillData,\r\n            buttonText: linkingResponse.data.buttonText,\r\n            isAutoTriggered: true\r\n          });\r\n          setShowAutoTriggerModal(true);\r\n        }\r\n      } catch (error) {\r\n        console.error('❌ Error processing auto-trigger:', error);\r\n      }\r\n    };\r\n\r\n    checkAutoTrigger();\r\n  }, [formId, recordData, enabled]);\r\n\r\n  const handleAutoTriggerFormSubmit = async (formId, formData) => {\r\n    try {\r\n      // Submit the form\r\n      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);\r\n      \r\n      if (response.data.success) {\r\n        setShowAutoTriggerModal(false);\r\n        setAutoTriggerForm(null);\r\n        \r\n        // Notify parent component\r\n        if (onFormSubmit) {\r\n          onFormSubmit(formId, formData, response.data);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('❌ Error submitting auto-triggered form:', error);\r\n    }\r\n  };\r\n\r\n  const handleCloseAutoTriggerModal = () => {\r\n    setShowAutoTriggerModal(false);\r\n    setAutoTriggerForm(null);\r\n    setCountdown(0);\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {/* Countdown Indicator */}\r\n      {countdown > 0 && (\r\n        <div className=\"fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40\">\r\n          <div className=\"flex items-center space-x-2\">\r\n            <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\r\n            <span className=\"text-sm font-medium\">\r\n              Auto-opening form in {countdown}s\r\n            </span>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {/* Auto-Trigger Form Modal */}\r\n      {showAutoTriggerModal && autoTriggerForm && (\r\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n          <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto\">\r\n            {/* Modal Header */}\r\n            <div className=\"bg-blue-500 text-white px-6 py-4 rounded-t-lg\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div>\r\n                  <h2 className=\"text-lg font-semibold\">\r\n                    🚀 Auto-Opened Form\r\n                  </h2>\r\n                  <p className=\"text-sm text-blue-100 mt-1\">\r\n                    {autoTriggerForm.name}\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={handleCloseAutoTriggerModal}\r\n                  className=\"text-white hover:text-gray-200 focus:outline-none\"\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M6 18L18 6M6 6l12 12\"></path>\r\n                  </svg>\r\n                </button>\r\n              </div>\r\n            </div>\r\n            \r\n            {/* Modal Content */}\r\n            <div className=\"p-6\">\r\n              <div className=\"bg-blue-50 border border-blue-200 rounded-md p-3 mb-4\">\r\n                <div className=\"flex items-start\">\r\n                  <div className=\"flex-shrink-0\">\r\n                    <svg className=\"h-5 w-5 text-blue-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\r\n                      <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\r\n                    </svg>\r\n                  </div>\r\n                  <div className=\"ml-3\">\r\n                    <h3 className=\"text-sm font-medium text-blue-800\">Auto-Triggered Form</h3>\r\n                    <div className=\"mt-1 text-sm text-blue-700\">\r\n                      This form was automatically opened based on the data you're viewing. \r\n                      Some fields may be pre-filled based on the original record.\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n              \r\n              <DynamicForm\r\n                form={autoTriggerForm}\r\n                onSubmit={handleAutoTriggerFormSubmit}\r\n                onCancel={handleCloseAutoTriggerModal}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AutoTriggerHandler;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,GAAG,MAAM,cAAc;;AAE9B;AACA;AACA;AACA;AACA;AAJA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAKA,MAAMC,kBAAkB,GAAGA,CAAC;EAC1BC,MAAM;EACNC,UAAU;EACVC,YAAY;EACZC,OAAO,GAAG;AACZ,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACgB,eAAe,EAAEC,kBAAkB,CAAC,GAAGjB,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACkB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EAE7CC,SAAS,CAAC,MAAM;IACd,IAAI,CAACW,OAAO,IAAI,CAACH,MAAM,IAAI,CAACC,UAAU,EAAE;;IAExC;IACA,MAAMY,kBAAkB,GAAGA,CAAA,KAAM;MAC/B,MAAMC,QAAQ,GAAGC,YAAY,CAACC,OAAO,CAAC,kBAAkB,CAAC;MACzD,MAAMC,aAAa,GAAGF,YAAY,CAACC,OAAO,CAAC,mBAAmB,CAAC;MAE/D,IAAIF,QAAQ,KAAK,MAAM,EAAE;QACvBI,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI;MACb;;MAEA;MACA,IAAIF,aAAa,EAAE;QACjB,MAAMG,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,QAAQ,CAACN,aAAa,CAAC;QAC1D,IAAIG,aAAa,GAAG,IAAI,EAAE;UACxBF,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;UACrE,OAAO,IAAI;QACb;MACF;MAEA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,IAAIN,kBAAkB,CAAC,CAAC,EAAE;MACxB;IACF;;IAEA;IACA,MAAMW,qBAAqB,GAAGA,CAAA,KAAM;MAAA,IAAAC,qBAAA,EAAAC,sBAAA;MAClC;MACA,MAAMC,SAAS,GAAGC,IAAI,CAACC,SAAS,CAAC5B,UAAU,CAAC,CAAC6B,WAAW,CAAC,CAAC;MAC1D,MAAMC,WAAW,GAAGJ,SAAS,CAACK,QAAQ,CAAC,OAAO,CAAC,IAAIL,SAAS,CAACK,QAAQ,CAAC,SAAS,CAAC,IAC7DL,SAAS,CAACK,QAAQ,CAAC,MAAM,CAAC,IAAIL,SAAS,CAACK,QAAQ,CAAC,QAAQ,CAAC,IAC1DL,SAAS,CAACK,QAAQ,CAAC,WAAW,CAAC;MAElD,IAAI,CAACD,WAAW,EAAE,OAAO,KAAK;;MAE9B;MACA,MAAME,cAAc,GAAGL,IAAI,CAACM,KAAK,CAACnB,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC;MAC/E,MAAMmB,eAAe,GAAG,EAAAV,qBAAA,GAAAQ,cAAc,CACnCG,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,IAAI,KAAK,MAAM,CAAC,CAClCC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,cAAAd,qBAAA,wBAAAC,sBAAA,GAFSD,qBAAA,CAEPe,OAAO,cAAAd,sBAAA,uBAFAA,sBAAA,CAEEI,WAAW,CAAC,CAAC,KAAI,EAAE;MAE7C,MAAMW,YAAY,GAAGN,eAAe,CAACH,QAAQ,CAAC,OAAO,CAAC,IAClCG,eAAe,CAACH,QAAQ,CAAC,SAAS,CAAC,IACnCG,eAAe,CAACH,QAAQ,CAAC,MAAM,CAAC,IAChCG,eAAe,CAACH,QAAQ,CAAC,QAAQ,CAAC,IAClCG,eAAe,CAACH,QAAQ,CAAC,WAAW,CAAC;MAEzD,IAAI,CAACS,YAAY,EAAE;QACjBvB,OAAO,CAACC,GAAG,CAAC,wEAAwE,CAAC;QACrF,OAAO,IAAI;MACb;MAEA,OAAO,KAAK;IACd,CAAC;;IAED;IACA,IAAIK,qBAAqB,CAAC,CAAC,EAAE;MAC3B;IACF;IAEA,MAAMkB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACnCpC,aAAa,CAAC,IAAI,CAAC;MACnB,IAAI;QACFY,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAEnB,MAAM,CAAC;QAEzD,MAAM2C,QAAQ,GAAG,MAAMjD,GAAG,CAACkD,IAAI,CAAC,mBAAmB5C,MAAM,qBAAqB,EAAE;UAC9EC;QACF,CAAC,CAAC;QAEF,IAAI0C,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACE,aAAa,EAAE;UACxD,MAAMC,WAAW,GAAGL,QAAQ,CAACE,IAAI;UACjC,MAAMI,KAAK,GAAGD,WAAW,CAACE,YAAY,IAAI,CAAC;UAE3ChC,OAAO,CAACC,GAAG,CAAC,8BAA8B8B,KAAK,aAAa,CAAC;UAE7D,IAAIA,KAAK,GAAG,CAAC,EAAE;YACb;YACArC,YAAY,CAACqC,KAAK,CAAC;YACnB,MAAME,iBAAiB,GAAGC,WAAW,CAAC,MAAM;cAC1CxC,YAAY,CAACyC,IAAI,IAAI;gBACnB,IAAIA,IAAI,IAAI,CAAC,EAAE;kBACbC,aAAa,CAACH,iBAAiB,CAAC;kBAChCI,WAAW,CAACP,WAAW,CAAC;kBACxB,OAAO,CAAC;gBACV;gBACA,OAAOK,IAAI,GAAG,CAAC;cACjB,CAAC,CAAC;YACJ,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,MAAM;YACL;YACAE,WAAW,CAACP,WAAW,CAAC;UAC1B;QACF;MACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;QACdtC,OAAO,CAACsC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD,CAAC,SAAS;QACRlD,aAAa,CAAC,KAAK,CAAC;MACtB;IACF,CAAC;IAED,MAAMiD,WAAW,GAAG,MAAOP,WAAW,IAAK;MACzC,IAAI;QACF;QACA,MAAMS,eAAe,GAAG,MAAM/D,GAAG,CAACkD,IAAI,CAAC,mBAAmB5C,MAAM,YAAY,EAAE;UAC5EC,UAAU;UACVyD,UAAU,EAAE,CAAC,CAAC;UACdC,WAAW,EAAEX,WAAW,CAACW;QAC3B,CAAC,CAAC;QAEF,IAAIF,eAAe,CAACZ,IAAI,CAACC,OAAO,EAAE;UAChC5B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsC,eAAe,CAACZ,IAAI,CAACe,UAAU,CAACC,IAAI,CAAC;;UAE7E;UACArD,kBAAkB,CAAC;YACjB,GAAGiD,eAAe,CAACZ,IAAI,CAACe,UAAU;YAClCE,WAAW,EAAEL,eAAe,CAACZ,IAAI,CAACiB,WAAW;YAC7CC,UAAU,EAAEN,eAAe,CAACZ,IAAI,CAACkB,UAAU;YAC3CC,eAAe,EAAE;UACnB,CAAC,CAAC;UACFtD,uBAAuB,CAAC,IAAI,CAAC;QAC/B;MACF,CAAC,CAAC,OAAO8C,KAAK,EAAE;QACdtC,OAAO,CAACsC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAC1D;IACF,CAAC;IAEDd,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAAC1C,MAAM,EAAEC,UAAU,EAAEE,OAAO,CAAC,CAAC;EAEjC,MAAM8D,2BAA2B,GAAG,MAAAA,CAAOjE,MAAM,EAAEkE,QAAQ,KAAK;IAC9D,IAAI;MACF;MACA,MAAMvB,QAAQ,GAAG,MAAMjD,GAAG,CAACkD,IAAI,CAAC,mBAAmB5C,MAAM,SAAS,EAAEkE,QAAQ,CAAC;MAE7E,IAAIvB,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QACzBpC,uBAAuB,CAAC,KAAK,CAAC;QAC9BF,kBAAkB,CAAC,IAAI,CAAC;;QAExB;QACA,IAAIN,YAAY,EAAE;UAChBA,YAAY,CAACF,MAAM,EAAEkE,QAAQ,EAAEvB,QAAQ,CAACE,IAAI,CAAC;QAC/C;MACF;IACF,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdtC,OAAO,CAACsC,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;IACjE;EACF,CAAC;EAED,MAAMW,2BAA2B,GAAGA,CAAA,KAAM;IACxCzD,uBAAuB,CAAC,KAAK,CAAC;IAC9BF,kBAAkB,CAAC,IAAI,CAAC;IACxBI,YAAY,CAAC,CAAC,CAAC;EACjB,CAAC;EAED,oBACEhB,OAAA,CAAAE,SAAA;IAAAsE,QAAA,GAEGzD,SAAS,GAAG,CAAC,iBACZf,OAAA;MAAKyE,SAAS,EAAC,mFAAmF;MAAAD,QAAA,eAChGxE,OAAA;QAAKyE,SAAS,EAAC,6BAA6B;QAAAD,QAAA,gBAC1CxE,OAAA;UAAKyE,SAAS,EAAC;QAA8E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACpG7E,OAAA;UAAMyE,SAAS,EAAC,qBAAqB;UAAAD,QAAA,GAAC,uBACf,EAACzD,SAAS,EAAC,GAClC;QAAA;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,EAGAhE,oBAAoB,IAAIF,eAAe,iBACtCX,OAAA;MAAKyE,SAAS,EAAC,4EAA4E;MAAAD,QAAA,eACzFxE,OAAA;QAAKyE,SAAS,EAAC,kFAAkF;QAAAD,QAAA,gBAE/FxE,OAAA;UAAKyE,SAAS,EAAC,+CAA+C;UAAAD,QAAA,eAC5DxE,OAAA;YAAKyE,SAAS,EAAC,mCAAmC;YAAAD,QAAA,gBAChDxE,OAAA;cAAAwE,QAAA,gBACExE,OAAA;gBAAIyE,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,EAAC;cAEtC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7E,OAAA;gBAAGyE,SAAS,EAAC,4BAA4B;gBAAAD,QAAA,EACtC7D,eAAe,CAACsD;cAAI;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACN7E,OAAA;cACE8E,OAAO,EAAEP,2BAA4B;cACrCE,SAAS,EAAC,mDAAmD;cAAAD,QAAA,eAE7DxE,OAAA;gBAAKyE,SAAS,EAAC,SAAS;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAT,QAAA,eAC5ExE,OAAA;kBAAMkF,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAC,GAAG;kBAACC,CAAC,EAAC;gBAAsB;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN7E,OAAA;UAAKyE,SAAS,EAAC,KAAK;UAAAD,QAAA,gBAClBxE,OAAA;YAAKyE,SAAS,EAAC,uDAAuD;YAAAD,QAAA,eACpExE,OAAA;cAAKyE,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC/BxE,OAAA;gBAAKyE,SAAS,EAAC,eAAe;gBAAAD,QAAA,eAC5BxE,OAAA;kBAAKyE,SAAS,EAAC,uBAAuB;kBAACQ,OAAO,EAAC,WAAW;kBAACF,IAAI,EAAC,cAAc;kBAAAP,QAAA,eAC5ExE,OAAA;oBAAMsF,QAAQ,EAAC,SAAS;oBAACD,CAAC,EAAC,kIAAkI;oBAACE,QAAQ,EAAC;kBAAS;oBAAAb,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN7E,OAAA;gBAAKyE,SAAS,EAAC,MAAM;gBAAAD,QAAA,gBACnBxE,OAAA;kBAAIyE,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,EAAC;gBAAmB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1E7E,OAAA;kBAAKyE,SAAS,EAAC,4BAA4B;kBAAAD,QAAA,EAAC;gBAG5C;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7E,OAAA,CAACH,WAAW;YACV2F,IAAI,EAAE7E,eAAgB;YACtB8E,QAAQ,EAAEpB,2BAA4B;YACtCqB,QAAQ,EAAEnB;UAA4B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA,eACD,CAAC;AAEP,CAAC;AAACrE,EAAA,CAhPIL,kBAAkB;AAAAwF,EAAA,GAAlBxF,kBAAkB;AAkPxB,eAAeA,kBAAkB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}