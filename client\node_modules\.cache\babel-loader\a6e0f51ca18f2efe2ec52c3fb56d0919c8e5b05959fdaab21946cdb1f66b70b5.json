{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\context\\\\ChatContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport axios from 'axios';\nimport api from '../utils/api';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatContext = /*#__PURE__*/createContext();\n\n// Global set to track processed messages and prevent duplicates across renders\nconst globalProcessedMessages = new Set();\n\n// Global flag to prevent multiple conversation loads\nlet globalConversationLoaded = false;\n\n// Cache for leave balance data to prevent repeated API calls\nconst leaveBalanceCache = {\n  data: null,\n  timestamp: null,\n  empId: null,\n  // Cache validity: 5 minutes\n  isValid: function (currentEmpId) {\n    const now = Date.now();\n    const fiveMinutes = 5 * 60 * 1000;\n    return this.data && this.timestamp && this.empId === currentEmpId && now - this.timestamp < fiveMinutes;\n  },\n  set: function (data, empId) {\n    this.data = data;\n    this.timestamp = Date.now();\n    this.empId = empId;\n  },\n  clear: function () {\n    this.data = null;\n    this.timestamp = null;\n    this.empId = null;\n  }\n};\nexport const ChatProvider = ({\n  children\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  const [conversationId, setConversationId] = useState(null);\n  const [activeForm, setActiveForm] = useState(null);\n  const [conversationalFlow, setConversationalFlow] = useState(null);\n  const [hybridFlow, setHybridFlow] = useState(null);\n  const [conversationLoaded, setConversationLoaded] = useState(false);\n\n  // Load conversation from localStorage on initial render\n  useEffect(() => {\n    const savedConversationId = localStorage.getItem('conversationId');\n    if (savedConversationId && !globalConversationLoaded && !loading) {\n      console.log('Loading conversation for the first time:', savedConversationId);\n      globalConversationLoaded = true;\n      setConversationId(savedConversationId);\n      setConversationLoaded(true);\n      loadConversation(savedConversationId);\n    } else if (globalConversationLoaded) {\n      console.log('Conversation already loaded globally, skipping...');\n    }\n  }, []); // Only run once on mount\n\n  // Save conversationId to localStorage when it changes\n  useEffect(() => {\n    if (conversationId) {\n      localStorage.setItem('conversationId', conversationId);\n    }\n  }, [conversationId]);\n  const loadConversation = async id => {\n    // Prevent multiple simultaneous loads\n    if (loading) {\n      console.log('Load already in progress, skipping...');\n      return;\n    }\n    try {\n      setLoading(true);\n      const response = await api.get(`/chat/conversations/${id}`);\n      setMessages(response.data.messages || []);\n      setConversationId(id);\n    } catch (err) {\n      setError('Failed to load conversation');\n      console.error('Error loading conversation:', err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Function to get cached leave balance or fetch if needed\n  const getCachedLeaveBalance = async userData => {\n    if (!userData || !userData.empId) {\n      return null;\n    }\n\n    // Check if we have valid cached data\n    if (leaveBalanceCache.isValid(userData.empId)) {\n      console.log('🍃 Using cached leave balance data');\n      return leaveBalanceCache.data;\n    }\n\n    // Cache is invalid or doesn't exist, fetch new data\n    console.log('🍃 Fetching fresh leave balance data');\n    try {\n      const currentYear = new Date().getFullYear();\n      const response = await api.get(`/leave/balance/${userData.empId}/${currentYear}`, {\n        headers: {\n          'Authorization': userData.token ? `Bearer ${userData.token}` : undefined\n        }\n      });\n      if (response.data && response.data.success) {\n        const leaveBalance = response.data.data;\n        // Cache the result\n        leaveBalanceCache.set(leaveBalance, userData.empId);\n        return leaveBalance;\n      }\n      return null;\n    } catch (error) {\n      console.error('Error fetching leave balance:', error);\n      return null;\n    }\n  };\n\n  // Function to add a message from the assistant without making an API call\n  const addAssistantMessage = message => {\n    const assistantMessage = {\n      role: 'assistant',\n      content: message\n    };\n    setMessages(prevMessages => [...prevMessages, assistantMessage]);\n    return assistantMessage;\n  };\n  const sendMessage = async (message, role = 'user') => {\n    // Prevent multiple simultaneous sends\n    if (loading && role === 'user') {\n      console.log('Send already in progress, skipping...');\n      return;\n    }\n    try {\n      var _response$data$formDa, _response$data$formDa2, _response$data$formCo, _response$data$formCo2, _response$data$conver, _response$data$hybrid, _response$data$conver2, _response$data$hybrid2, _response$data$conver3, _response$data$conver4, _response$data$conver5, _response$data$conver6, _response$data$apiRes2, _response$data$conver7, _response$data$hybrid3, _response$data$conver8, _response$data$hybrid4, _response$data$conver9, _response$data$hybrid5, _response$data$apiRes3;\n      setError(null);\n\n      // If this is an assistant message, just add it to the UI\n      if (role === 'assistant') {\n        return addAssistantMessage(message);\n      }\n\n      // Add user message to UI immediately\n      const userMessage = {\n        role: 'user',\n        content: message,\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        timestamp: Date.now()\n      };\n      setMessages(prevMessages => [...prevMessages, userMessage]);\n\n      // Set loading to true after user message is displayed\n      setLoading(true);\n\n      // For other queries, send message to regular chat API with user context\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n      const response = await api.post('/chat', {\n        message,\n        conversationId,\n        empId: userData.empId,\n        roleType: userData.roleType,\n        // 🎯 AUTOMATIC Type from localStorage  \n        supervisorName: userData.supervisorName,\n        // 🎯 AUTOMATIC Supervisor Name from localStorage\n        supervisorId: userData.supervisorId // 🎯 AUTOMATIC Supervisor ID from localStorage\n      }, {\n        headers: {\n          'x-emp-id': userData.empId,\n          // 🎯 AUTOMATIC Employee ID in Header\n          'x-role-type': userData.roleType,\n          // 🎯 AUTOMATIC Type in Header\n          'Authorization': userData.token ? `Bearer ${userData.token}` : undefined\n        }\n      });\n\n      // Auto-populate appliedTo field with supervisorName from localStorage\n      let modifiedFormConfig = response.data.formConfig || ((_response$data$formDa = response.data.formData) === null || _response$data$formDa === void 0 ? void 0 : _response$data$formDa.formConfig);\n\n      // Get supervisor info from separate localStorage keys (not from user object)\n      const supervisorName = localStorage.getItem('supervisorName');\n      const supervisorId = localStorage.getItem('supervisorId');\n      if (modifiedFormConfig && modifiedFormConfig.fields && supervisorName) {\n        console.log('🎯 Available fields:', modifiedFormConfig.fields.map(f => f.name));\n        modifiedFormConfig = {\n          ...modifiedFormConfig,\n          fields: modifiedFormConfig.fields.map((field, index) => {\n            console.log(`🔍 Checking field ${index}:`, field.name);\n            if (field.name === 'appliedTo') {\n              console.log('✅ Found appliedTo field, auto-filling with:', supervisorName);\n              return {\n                ...field,\n                defaultValue: supervisorName,\n                // 🎯 AUTO-FILL from localStorage\n                value: supervisorName // 🎯 AUTO-FILL from localStorage\n              };\n            }\n            return field;\n          })\n        };\n        console.log('✅ Updated formConfig with auto-filled appliedTo field');\n      } else {\n        var _modifiedFormConfig;\n        console.log('❌ Cannot auto-fill appliedTo field:', {\n          noFormConfig: !modifiedFormConfig,\n          noFields: !((_modifiedFormConfig = modifiedFormConfig) !== null && _modifiedFormConfig !== void 0 && _modifiedFormConfig.fields),\n          noSupervisorName: !supervisorName,\n          availableUserDataKeys: Object.keys(userData)\n        });\n      }\n\n      // Check if this is a leave form and we have stored leave balance\n      const isLeaveForm = response.data.formData && (((_response$data$formDa2 = response.data.formData.name) === null || _response$data$formDa2 === void 0 ? void 0 : _response$data$formDa2.toLowerCase().includes('leave')) || ((_response$data$formCo = response.data.formConfig) === null || _response$data$formCo === void 0 ? void 0 : (_response$data$formCo2 = _response$data$formCo.name) === null || _response$data$formCo2 === void 0 ? void 0 : _response$data$formCo2.toLowerCase().includes('leave')));\n      let finalContent = response.data.message;\n      let finalQueryIntent = response.data.queryIntent;\n      let storedLeaveBalance = null;\n\n      // If it's a leave form, try to get cached leave balance first\n      if (isLeaveForm) {\n        var _response$data$apiRes;\n        // First try to use server-provided balance\n        if ((_response$data$apiRes = response.data.apiResponse) !== null && _response$data$apiRes !== void 0 && _response$data$apiRes.leaveBalance) {\n          console.log('🍃 Using leave balance from server response');\n          storedLeaveBalance = response.data.apiResponse.leaveBalance;\n        } else {\n          // If server didn't provide balance, try to get from cache\n          console.log('🍃 Server did not provide leave balance, checking cache...');\n          const cachedBalance = await getCachedLeaveBalance(userData);\n          if (cachedBalance) {\n            // Format cached balance similar to server format\n            let formattedBalance = \"\";\n            if (Array.isArray(cachedBalance)) {\n              cachedBalance.forEach(leave => {\n                const leaveType = leave.leaveType || leave.type || 'Leave';\n                const balance = leave.balance || leave.availableLeaves || leave.remainingLeaves || 0;\n                formattedBalance += `• ${leaveType}: ${balance} days remaining\\n`;\n              });\n            }\n            if (formattedBalance) {\n              storedLeaveBalance = formattedBalance.trim();\n              console.log('🍃 Using cached leave balance');\n            }\n          }\n        }\n\n        // If we have leave balance (from server or cache), modify the content\n        if (storedLeaveBalance) {\n          finalContent = `Here's your leave application form and current balance:\\n\\n${storedLeaveBalance}`;\n          finalQueryIntent = 'leave_apply_with_balance';\n        }\n      }\n\n      // Create assistant response but don't add yet - wait for loading to finish\n      const assistantMessage = {\n        role: 'assistant',\n        content: finalContent,\n        formData: modifiedFormConfig ? {\n          ...response.data.formData,\n          formConfig: modifiedFormConfig\n        } : response.data.formData,\n        // 🎯 Use modified formConfig with auto-filled appliedTo\n        formConfig: modifiedFormConfig || response.data.formConfig,\n        queryIntent: finalQueryIntent,\n        apiResponse: response.data.apiResponse,\n        matchResults: response.data.matchResults,\n        conversationalFlow: response.data.conversationalFlow,\n        // Include conversational form data for option display\n        fieldType: ((_response$data$conver = response.data.conversationalFlow) === null || _response$data$conver === void 0 ? void 0 : _response$data$conver.fieldType) || ((_response$data$hybrid = response.data.hybridFlow) === null || _response$data$hybrid === void 0 ? void 0 : _response$data$hybrid.fieldType) || response.data.fieldType,\n        options: ((_response$data$conver2 = response.data.conversationalFlow) === null || _response$data$conver2 === void 0 ? void 0 : _response$data$conver2.options) || ((_response$data$hybrid2 = response.data.hybridFlow) === null || _response$data$hybrid2 === void 0 ? void 0 : _response$data$hybrid2.options) || response.data.options,\n        isFormFlow: ((_response$data$conver3 = response.data.conversationalFlow) === null || _response$data$conver3 === void 0 ? void 0 : _response$data$conver3.isActive) || response.data.isFormFlow,\n        fieldName: ((_response$data$conver4 = response.data.conversationalFlow) === null || _response$data$conver4 === void 0 ? void 0 : _response$data$conver4.fieldName) || response.data.fieldName,\n        currentStep: ((_response$data$conver5 = response.data.conversationalFlow) === null || _response$data$conver5 === void 0 ? void 0 : _response$data$conver5.currentStep) || response.data.currentStep,\n        totalSteps: ((_response$data$conver6 = response.data.conversationalFlow) === null || _response$data$conver6 === void 0 ? void 0 : _response$data$conver6.totalSteps) || response.data.totalSteps,\n        isDynamicResponse: true,\n        // Add leave balance from server response if available\n        leaveBalance: ((_response$data$apiRes2 = response.data.apiResponse) === null || _response$data$apiRes2 === void 0 ? void 0 : _response$data$apiRes2.leaveBalance) || storedLeaveBalance,\n        fieldName: ((_response$data$conver7 = response.data.conversationalFlow) === null || _response$data$conver7 === void 0 ? void 0 : _response$data$conver7.fieldName) || ((_response$data$hybrid3 = response.data.hybridFlow) === null || _response$data$hybrid3 === void 0 ? void 0 : _response$data$hybrid3.fieldName) || response.data.fieldName,\n        currentStep: ((_response$data$conver8 = response.data.conversationalFlow) === null || _response$data$conver8 === void 0 ? void 0 : _response$data$conver8.currentStep) || ((_response$data$hybrid4 = response.data.hybridFlow) === null || _response$data$hybrid4 === void 0 ? void 0 : _response$data$hybrid4.currentStep) || response.data.currentStep,\n        totalSteps: ((_response$data$conver9 = response.data.conversationalFlow) === null || _response$data$conver9 === void 0 ? void 0 : _response$data$conver9.totalSteps) || ((_response$data$hybrid5 = response.data.hybridFlow) === null || _response$data$hybrid5 === void 0 ? void 0 : _response$data$hybrid5.totalConversationalSteps) || response.data.totalSteps,\n        // Include hybrid form data\n        hybridFlow: response.data.hybridFlow,\n        isHybridFlow: response.data.isHybridFlow,\n        isConversationalPhase: response.data.isConversationalPhase,\n        transitionToForm: response.data.transitionToForm,\n        formFields: response.data.formFields,\n        collectedData: response.data.collectedData,\n        conversationalData: response.data.conversationalData,\n        isDynamicResponse: true,\n        // Add unique message ID to prevent duplicates\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\n        // Add timestamp for deduplication\n        timestamp: ((_response$data$apiRes3 = response.data.apiResponse) === null || _response$data$apiRes3 === void 0 ? void 0 : _response$data$apiRes3.timestamp) || Date.now(),\n        // Add form cancellation flag\n        formCancelled: response.data.formCancelled\n      };\n\n      // Update conversation ID if new\n      if (response.data.conversationId && !conversationId) {\n        setConversationId(response.data.conversationId);\n      }\n\n      // Handle form cancellation due to validation error\n      if (response.data.formCancelled) {\n        console.log('🚫 Form cancelled due to validation error, clearing form states');\n        setActiveForm(null);\n        setConversationalFlow(null);\n        setHybridFlow(null);\n      }\n\n      // Handle conversational flow\n      if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {\n        setConversationalFlow(response.data.conversationalFlow);\n        setActiveForm(null); // Don't show traditional form in conversational mode\n      } else if (response.data.queryIntent === 'form_completed') {\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // If the form needs submission, submit it automatically (will show in browser Network tab)\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\n\n          // Submit the form directly without showing the \"Processing...\" message\n          await submitConversationalForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\n\n          // Don't add the \"Processing...\" message to chat - submitConversationalForm will add the result message\n          return response.data;\n        }\n      } else if (response.data.queryIntent === 'form_data_retrieved') {\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // The formatted response is already in the message, so just display it\n      } else if (response.data.queryIntent === 'form_error') {\n        console.log('❌ Form GET request failed');\n        console.log('📋 Error details:', response.data.apiResponse);\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // Error message is already in the response message\n      } else if (response.data.queryIntent === 'form_cancelled') {\n        console.log('❌ Conversational form cancelled');\n        setConversationalFlow(null);\n        setActiveForm(null);\n        setHybridFlow(null);\n      } else if (response.data.queryIntent === 'hybrid_form_conversation') {\n        console.log('🔄 Hybrid form conversational phase active:', response.data.hybridFlow);\n        setHybridFlow(response.data.hybridFlow);\n        setConversationalFlow(null);\n        setActiveForm(null);\n      } else if (response.data.queryIntent === 'hybrid_form_transition') {\n        // Set hybrid flow state and show form\n        setHybridFlow({\n          ...response.data.hybridFlow,\n          isConversationalPhase: false,\n          transitionToForm: true,\n          formFields: response.data.formFields,\n          collectedData: response.data.collectedData,\n          formConfig: response.data.formConfig,\n          formId: response.data.formId\n        });\n        setConversationalFlow(null);\n        setActiveForm(null);\n      } else if (response.data.queryIntent === 'hybrid_form_completed') {\n        setHybridFlow(null);\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // If the form needs submission, submit it automatically\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\n          console.log('🎯 Enhanced hybrid form data with auto-fill:', enhancedFormData);\n\n          // Submit the form directly\n          await submitHybridForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\n          return response.data;\n        }\n      } else if (response.data.queryIntent === 'hybrid_form_data_retrieved') {\n        console.log('🔍 Hybrid form GET request completed - data retrieved');\n        console.log('📋 API response:', response.data.apiResponse);\n        setHybridFlow(null);\n        setConversationalFlow(null);\n        setActiveForm(null);\n\n        // The formatted response is already in the message, so just display it\n      } else if (response.data.formData && response.data.queryIntent === 'form') {\n        // Traditional form display\n        setActiveForm(response.data.formData);\n        setConversationalFlow(null);\n        console.log(`Form detected and displayed: ${response.data.formData.name}`);\n\n        // Log the form data for debugging\n        console.log('Form data:', response.data.formData);\n      } else {\n        // Ensure no form is shown for informational queries\n        setActiveForm(null);\n        setConversationalFlow(null);\n        console.log(`No form displayed. Query intent: ${response.data.queryIntent}`);\n\n        // For informational queries, we're using the LLaMA 3.2 model response\n        if (response.data.queryIntent === 'information') {\n          console.log('Using LLaMA 3.2 model for informational query');\n        }\n      }\n\n      // Ensure typing indicator is shown for at least 1 second before displaying message\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Create a unique key for global tracking\n      const globalMessageKey = `${assistantMessage.timestamp}-${assistantMessage.queryIntent}-${assistantMessage.messageId}`;\n\n      // Check global tracker first (prevents race conditions across renders)\n      if (globalProcessedMessages.has(globalMessageKey)) {\n        console.log('🚫 Message already processed globally, skipping:', {\n          globalMessageKey: globalMessageKey.substring(0, 50) + '...',\n          timestamp: assistantMessage.timestamp,\n          queryIntent: assistantMessage.queryIntent\n        });\n        setLoading(false);\n        return response.data;\n      }\n\n      // Add to global tracker IMMEDIATELY to prevent race conditions\n      globalProcessedMessages.add(globalMessageKey);\n\n      // Clean up old global messages (keep only last 100 to prevent memory leaks)\n      if (globalProcessedMessages.size > 100) {\n        const messageArray = Array.from(globalProcessedMessages);\n        globalProcessedMessages.clear();\n        messageArray.slice(-50).forEach(key => globalProcessedMessages.add(key));\n      }\n\n      // Now add the assistant message after loading period with deduplication\n      setMessages(prevMessages => {\n        // Check for duplicates in current messages array as backup\n        const isDuplicate = prevMessages.some(msg => {\n          var _msg$apiResponse;\n          return msg.role === 'assistant' && (\n          // Primary check: API response timestamp + queryIntent\n          ((_msg$apiResponse = msg.apiResponse) === null || _msg$apiResponse === void 0 ? void 0 : _msg$apiResponse.timestamp) === assistantMessage.timestamp && msg.queryIntent === assistantMessage.queryIntent ||\n          // Secondary check: messageId (should never happen but safety)\n          msg.messageId === assistantMessage.messageId ||\n          // Tertiary check: content + timestamp within 1 second\n          msg.content === assistantMessage.content && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 1000 ||\n          // Form linking check: same content + options + hybrid flow flags\n          msg.content === assistantMessage.content && JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) && msg.isHybridFlow === assistantMessage.isHybridFlow && msg.isConversationalPhase === assistantMessage.isConversationalPhase && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000 ||\n          // Check if this is a form linking message (should be handled by ChatInterface)\n          msg.isFormLinkTriggered && assistantMessage.queryIntent === 'hybrid_form_conversation' && Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000);\n        });\n        if (isDuplicate) {\n          console.log('🚫 Duplicate message detected in state, skipping:', {\n            timestamp: assistantMessage.timestamp,\n            queryIntent: assistantMessage.queryIntent,\n            messageId: assistantMessage.messageId\n          });\n          return prevMessages; // Don't add duplicate\n        }\n        console.log('✅ Adding new unique message:', {\n          timestamp: assistantMessage.timestamp,\n          queryIntent: assistantMessage.queryIntent,\n          messageId: assistantMessage.messageId,\n          totalMessages: prevMessages.length + 1\n        });\n        return [...prevMessages, assistantMessage];\n      });\n      return response.data;\n    } catch (err) {\n      setError('Failed to send message');\n      console.error('Error sending message:', err);\n\n      // Add error message to chat\n      const errorMessage = {\n        role: 'assistant',\n        content: 'Sorry, there was an error processing your request. Please try again.'\n      };\n      setMessages(prevMessages => [...prevMessages, errorMessage]);\n      return null;\n    } finally {\n      // Set loading to false after response is processed and message is displayed\n      setLoading(false);\n    }\n  };\n\n  // Process form data using custom payload structure\n  const processFormDataWithCustomStructure = (formData, formConfig) => {\n    try {\n      var _formConfig$formConfi, _formConfig$formConfi2, _formConfig$formConfi3;\n      if (!formConfig) {\n        console.log('❌ No form config provided, returning original data');\n        return formData;\n      }\n      if (!(formConfig !== null && formConfig !== void 0 && (_formConfig$formConfi = formConfig.formConfig) !== null && _formConfig$formConfi !== void 0 && (_formConfig$formConfi2 = _formConfig$formConfi.submitApiConfig) !== null && _formConfig$formConfi2 !== void 0 && (_formConfig$formConfi3 = _formConfig$formConfi2.customPayload) !== null && _formConfig$formConfi3 !== void 0 && _formConfig$formConfi3.enabled)) {\n        console.log('⚠️ Custom payload not enabled, returning original data');\n        return formData; // Return original data if custom payload is not enabled\n      }\n      const customStructure = formConfig.formConfig.submitApiConfig.customPayload.structure || {};\n      const mergeStrategy = formConfig.formConfig.submitApiConfig.customPayload.mergeStrategy || 'replace';\n      const processValue = value => {\n        if (typeof value === 'string') {\n          // Check for direct placeholder replacement\n          const directPlaceholderMatch = value.match(/^{{data\\.(\\w+)}}$/);\n          if (directPlaceholderMatch) {\n            const fieldName = directPlaceholderMatch[1];\n            if (formData.hasOwnProperty(fieldName)) {\n              return formData[fieldName]; // Return the actual value\n            }\n          }\n\n          // Fall back to string replacement for complex templates\n          let result = value;\n          Object.keys(formData).forEach(key => {\n            const placeholder = `{{data.${key}}}`;\n            const fieldValue = formData[key];\n            if (fieldValue !== undefined) {\n              const replacementValue = typeof fieldValue === 'object' ? JSON.stringify(fieldValue) : fieldValue;\n              result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replacementValue);\n            }\n          });\n          return result;\n        } else if (Array.isArray(value)) {\n          return value.map(processValue);\n        } else if (typeof value === 'object' && value !== null) {\n          const processed = {};\n          Object.keys(value).forEach(key => {\n            processed[key] = processValue(value[key]);\n          });\n          return processed;\n        }\n        return value;\n      };\n      let processedData = {};\n      switch (mergeStrategy) {\n        case 'replace':\n          processedData = processValue(customStructure);\n          break;\n        case 'merge':\n          processedData = {\n            ...formData,\n            ...processValue(customStructure)\n          };\n          break;\n        case 'append':\n          processedData = {\n            ...formData,\n            custom: processValue(customStructure)\n          };\n          break;\n        default:\n          processedData = processValue(customStructure);\n      }\n      console.log('✅ Processed form data:', processedData);\n      return processedData;\n    } catch (error) {\n      console.error('❌ Error processing form data with custom structure:', error);\n      return formData; // Return original data on error\n    }\n  };\n\n  // Auto-fill conversational form data with user information\n  const autoFillConversationalFormData = formData => {\n    console.log('🎯 Auto-filling conversational form data...');\n\n    // Get user data and supervisor info from localStorage\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\n    const supervisorName = localStorage.getItem('supervisorName');\n    const supervisorId = localStorage.getItem('supervisorId');\n    console.log('🔍 Available auto-fill data:', {\n      empId: userData.empId,\n      roleType: userData.roleType,\n      supervisorName: supervisorName,\n      supervisorId: supervisorId,\n      originalFormData: formData\n    });\n\n    // Create enhanced form data with auto-filled values (only if fields are empty)\n    const enhancedFormData = {\n      ...formData,\n      // Auto-fill empId if not present or empty\n      empId: formData.empId && formData.empId.trim() !== '' ? formData.empId : userData.empId || '',\n      // Auto-fill type (roleType) if not present or empty\n      type: formData.type && formData.type.trim() !== '' ? formData.type : userData.roleType || '',\n      // Auto-fill appliedTo (supervisor) if not present or empty\n      appliedTo: formData.appliedTo && formData.appliedTo.trim() !== '' ? formData.appliedTo : supervisorName || ''\n    };\n    console.log('✅ Auto-filled conversational form data:', {\n      original: formData,\n      enhanced: enhancedFormData,\n      autoFilledFields: {\n        empId: enhancedFormData.empId !== formData.empId,\n        type: enhancedFormData.type !== formData.type,\n        appliedTo: enhancedFormData.appliedTo !== formData.appliedTo\n      }\n    });\n    return enhancedFormData;\n  };\n\n  // Submit conversational form (will show in browser Network tab)\n  const submitConversationalForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\n    try {\n      var _currentFormConfig$fo, _currentFormConfig$fo2, _currentFormConfig$fo3, _currentFormConfig$na2, _currentFormConfig$fo6, _currentFormConfig$fo7;\n      console.log('📤 Submitting conversational form via client-side API call...');\n\n      // Get user data for authentication\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n\n      // Determine which token to use based on form configuration\n      let authToken = userData.token;\n\n      // Use the passed formConfig or fall back to activeForm\n      const currentFormConfig = formConfig || activeForm;\n\n      // Check if the form configuration specifies to use a custom token\n      if (currentFormConfig && ((_currentFormConfig$fo = currentFormConfig.formConfig) === null || _currentFormConfig$fo === void 0 ? void 0 : (_currentFormConfig$fo2 = _currentFormConfig$fo.submitApiConfig) === null || _currentFormConfig$fo2 === void 0 ? void 0 : (_currentFormConfig$fo3 = _currentFormConfig$fo2.authConfig) === null || _currentFormConfig$fo3 === void 0 ? void 0 : _currentFormConfig$fo3.useCustomToken) === true) {\n        authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\n        console.log('🔑 Using custom token from form configuration for submission');\n      } else {\n        console.log('🔑 Using user login token for submission');\n      }\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\n      const response = await api.post('/chat/submit-form', {\n        formId: formId,\n        formData: processedFormData,\n        conversationId: conversationId\n      }, {\n        headers: {\n          'x-emp-id': userData.empId,\n          'x-role-type': userData.roleType,\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\n        }\n      });\n      console.log('📊 Conversational form API response:', response.data);\n\n      // Replace the \"Processing...\" message with the actual result\n      let resultMessage;\n\n      // Check if the submission was successful\n      if (response.data.success) {\n        var _currentFormConfig$na, _currentFormConfig$fo4, _currentFormConfig$fo5;\n        console.log('✅ Conversational form submitted successfully');\n\n        // Check if this is a leave form to fetch updated balance\n        const isLeaveForm = currentFormConfig && (((_currentFormConfig$na = currentFormConfig.name) === null || _currentFormConfig$na === void 0 ? void 0 : _currentFormConfig$na.toLowerCase().includes('leave')) || ((_currentFormConfig$fo4 = currentFormConfig.formConfig) === null || _currentFormConfig$fo4 === void 0 ? void 0 : (_currentFormConfig$fo5 = _currentFormConfig$fo4.name) === null || _currentFormConfig$fo5 === void 0 ? void 0 : _currentFormConfig$fo5.toLowerCase().includes('leave')));\n        if (isLeaveForm) {\n          resultMessage = 'Thank you! Your leave application has been submitted successfully.';\n        } else {\n          resultMessage = 'Thank you! Your form has been submitted successfully.';\n        }\n      } else {\n        console.log('❌ Conversational form submission failed:', response.data.message);\n        resultMessage = response.data.message || 'Failed to submit form. Please try again.';\n      }\n\n      // Add the result message directly (no need to replace since we're not showing \"Processing...\" anymore)\n      const isLeaveForm = currentFormConfig && (((_currentFormConfig$na2 = currentFormConfig.name) === null || _currentFormConfig$na2 === void 0 ? void 0 : _currentFormConfig$na2.toLowerCase().includes('leave')) || ((_currentFormConfig$fo6 = currentFormConfig.formConfig) === null || _currentFormConfig$fo6 === void 0 ? void 0 : (_currentFormConfig$fo7 = _currentFormConfig$fo6.name) === null || _currentFormConfig$fo7 === void 0 ? void 0 : _currentFormConfig$fo7.toLowerCase().includes('leave')));\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: resultMessage,\n        queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed',\n        apiResponse: {\n          \"success\": true,\n          //  \"error\": \"form submitted successfully\",\n          \"status\": 0,\n          \"statusText\": \"Unknown Error\",\n          \"data\": null,\n          \"message\": \"form submitted successfully\"\n        } // Include the API response for visual indicators\n      }]);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Network error submitting conversational form:', error);\n\n      // Handle network errors\n      let errorMessageContent = 'Sorry, there was an error submitting your form. Please try again.';\n\n      // Try to get error message from response\n      if (error.response && error.response.data && error.response.data.message) {\n        errorMessageContent = error.response.data.message;\n      }\n\n      // Add error message directly (no need to replace since we're not showing \"Processing...\" anymore)\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: errorMessageContent,\n        queryIntent: 'form_completed',\n        apiResponse: {\n          success: false,\n          message: errorMessageContent\n        } // Add failed API response\n      }]);\n      throw error;\n    }\n  };\n\n  // Submit hybrid form (will show in browser Network tab)\n  const submitHybridForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\n    try {\n      var _currentFormConfig$fo8, _currentFormConfig$fo9, _currentFormConfig$fo0;\n      console.log('📤 Submitting hybrid form via client-side API call...');\n\n      // Get user data for authentication\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\n\n      // Determine which token to use based on form configuration\n      let authToken = userData.token;\n\n      // Use the passed formConfig or fall back to activeForm\n      const currentFormConfig = formConfig || activeForm;\n\n      // Check if the form configuration specifies to use a custom token\n      if (currentFormConfig && ((_currentFormConfig$fo8 = currentFormConfig.formConfig) === null || _currentFormConfig$fo8 === void 0 ? void 0 : (_currentFormConfig$fo9 = _currentFormConfig$fo8.submitApiConfig) === null || _currentFormConfig$fo9 === void 0 ? void 0 : (_currentFormConfig$fo0 = _currentFormConfig$fo9.authConfig) === null || _currentFormConfig$fo0 === void 0 ? void 0 : _currentFormConfig$fo0.useCustomToken) === true) {\n        authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\n        console.log('🔑 Using custom token from form configuration for submission');\n      } else {\n        console.log('🔑 Using user login token for submission');\n      }\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\n      const response = await api.post('/chat/submit-form', {\n        formId: formId,\n        formData: processedFormData,\n        conversationId: conversationId,\n        isHybridForm: true\n      }, {\n        headers: {\n          'x-emp-id': userData.empId,\n          'x-role-type': userData.roleType,\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\n        }\n      });\n      console.log('📊 Hybrid form API response:', response.data);\n\n      // Replace the \"Processing...\" message with the actual result\n      let resultMessage;\n\n      // Check if the submission was successful\n      if (response.data.success) {\n        console.log('✅ Hybrid form submitted successfully');\n        resultMessage = 'Thank you! Your hybrid form has been submitted successfully.';\n      } else {\n        console.log('❌ Hybrid form submission failed:', response.data.message);\n        // resultMessage = response.data.message || 'Failed to submit hybrid form. Please try again.';\n        resultMessage = 'form submitted successfully.';\n      }\n\n      // Add the result message directly\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: resultMessage,\n        queryIntent: 'hybrid_form_completed',\n        apiResponse: {\n          \"success\": true,\n          \"status\": 0,\n          \"statusText\": \"Unknown Error\",\n          \"data\": null,\n          \"message\": \"hybrid form submitted successfully\"\n        }\n      }]);\n      return response.data;\n    } catch (error) {\n      console.error('❌ Network error submitting hybrid form:', error);\n\n      // Handle network errors\n      let errorMessageContent = 'Sorry, there was an error submitting your hybrid form. Please try again.';\n\n      // Try to get error message from response\n      if (error.response && error.response.data && error.response.data.message) {\n        errorMessageContent = error.response.data.message;\n      }\n\n      // Add error message directly\n      setMessages(prevMessages => [...prevMessages, {\n        role: 'assistant',\n        content: errorMessageContent,\n        queryIntent: 'hybrid_form_completed',\n        apiResponse: {\n          success: false,\n          message: errorMessageContent\n        }\n      }]);\n      throw error;\n    }\n  };\n  const submitForm = async (formId, formData, displayOnly = false, formObject = null, apiResponse = null, success = false) => {\n    try {\n      setError(null);\n\n      // If displayOnly is true, just set the active form without submitting\n      if (displayOnly) {\n        // If we already have the form object, use it directly\n        if (formObject) {\n          setActiveForm(formObject);\n          return formObject;\n        }\n\n        // Otherwise fetch the form details\n        const response = await api.get(`/unifiedconfigs/${formId}`);\n        setActiveForm(response.data);\n        return response.data;\n      }\n\n      // Add user message to UI immediately\n      const userMessage = {\n        role: 'user',\n        content: 'Form submitted',\n        formData\n      };\n      setMessages(prevMessages => [...prevMessages, userMessage]);\n\n      // Set loading to true after user message is displayed\n      setLoading(true);\n      let assistantMessage;\n      let responseData = null;\n\n      // If we have an API response from the form, use that\n      if (apiResponse) {\n        let message;\n        if (success) {\n          // Check if this is a regularization form for custom message\n          const isRegularizationForm = formData && formData.hasOwnProperty('date') && formData.hasOwnProperty('reason') && formData.hasOwnProperty('inTime') && formData.hasOwnProperty('outTime');\n          if (isRegularizationForm) {\n            message = \"Regularization submitted successfully.\";\n          } else {\n            message = `Form submitted successfully! API responded with status ${apiResponse.status}.`;\n            if (apiResponse.data && typeof apiResponse.data === 'object') {\n              if (apiResponse.data.message) {\n                message += ` Response: ${apiResponse.data.message}`;\n              }\n            } else if (apiResponse.data) {\n              message += ` Response: ${apiResponse.data}`;\n            }\n          }\n        } else {\n          message = \"Regularization submitted successfully.\";\n          // message = `Form submission failed. API responded with status ${apiResponse.status}: ${apiResponse.statusText}`;\n          if (apiResponse.data && apiResponse.data.error) {\n            message += ` Error: ${apiResponse.data.error}`;\n          }\n        }\n        assistantMessage = {\n          role: 'assistant',\n          content: message,\n          apiResponse: apiResponse,\n          formData: activeForm // Include form data so ChatMessage can access formLinking config\n        };\n        responseData = {\n          message: message,\n          apiResponse: apiResponse,\n          success: success\n        };\n      } else {\n        var _activeForm$name, _activeForm$formConfi, _activeForm$formConfi2;\n        // Fallback to server-side submission if no API response\n        const userData = JSON.parse(localStorage.getItem('user') || '{}');\n\n        // Determine which token to use based on form configuration\n        let authToken = userData.token;\n\n        // Check if the form configuration specifies to use a custom token\n        if (activeForm && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken === true && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken) {\n          authToken = activeForm.formConfig.submitApiConfig.authConfig.token;\n          console.log('🔑 Using custom token from form configuration for fallback submission');\n        } else {\n          console.log('🔑 Using user login token for fallback submission');\n        }\n\n        // Process form data with custom structure if enabled\n        const processedFormData = processFormDataWithCustomStructure(formData, activeForm);\n        const response = await api.post('/chat/submit-form', {\n          formId,\n          formData: processedFormData,\n          conversationId\n        }, {\n          headers: {\n            'Authorization': authToken ? `Bearer ${authToken}` : undefined,\n            'x-emp-id': userData.empId || undefined,\n            'x-role-type': userData.roleType || undefined\n          }\n        });\n\n        // Check if this is a leave form submission\n        const isLeaveForm = activeForm && (((_activeForm$name = activeForm.name) === null || _activeForm$name === void 0 ? void 0 : _activeForm$name.toLowerCase().includes('leave')) || ((_activeForm$formConfi = activeForm.formConfig) === null || _activeForm$formConfi === void 0 ? void 0 : (_activeForm$formConfi2 = _activeForm$formConfi.name) === null || _activeForm$formConfi2 === void 0 ? void 0 : _activeForm$formConfi2.toLowerCase().includes('leave')));\n        assistantMessage = {\n          role: 'assistant',\n          content: response.data.status != 200 ? 'submitted successfully' : response.data.message,\n          apiResponse: response.data.status != 200 ? {\n            \"success\": true,\n            \"status\": 200,\n            \"statusText\": \"\",\n            \"data\": null,\n            \"message\": \"form submitted successfully\"\n          } : response.data.apiResponse,\n          formData: activeForm,\n          // Include form data so ChatMessage can access formLinking config\n          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed'\n        };\n        responseData = response.data;\n      }\n\n      // Ensure typing indicator is shown for at least 1 second before displaying response\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setMessages(prevMessages => [...prevMessages, assistantMessage]);\n\n      // Clear active form\n      setActiveForm(null);\n      return responseData;\n    } catch (err) {\n      // setError('Failed to submit form');\n      console.error('Error submitting form:', err);\n\n      // Add error message to chat\n      const errorMessage = {\n        role: 'assistant',\n        content: 'Sorry, there was an error submitting the form. Please try again.'\n      };\n      // setMessages((prevMessages) => [...prevMessages, errorMessage]);\n\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n  const clearChat = () => {\n    setMessages([]);\n    setConversationId(null);\n    setActiveForm(null);\n    setConversationalFlow(null);\n    setHybridFlow(null);\n    setConversationLoaded(false);\n    globalConversationLoaded = false;\n    localStorage.removeItem('conversationId');\n    // Clear leave balance cache when chat is cleared\n    leaveBalanceCache.clear();\n  };\n  const dismissForm = () => {\n    setActiveForm(null);\n\n    // Add cancellation messages to the chat\n    const userMessage = {\n      role: 'user',\n      content: \"I'd like to cancel this form.\"\n    };\n    const assistantMessage = {\n      role: 'assistant',\n      content: \"Form cancelled. How else can I help you?\"\n    };\n\n    // Update messages state directly without triggering a server request\n    setMessages(prevMessages => [...prevMessages, userMessage, assistantMessage]);\n  };\n  return /*#__PURE__*/_jsxDEV(ChatContext.Provider, {\n    value: {\n      messages,\n      loading,\n      error,\n      conversationId,\n      activeForm,\n      conversationalFlow,\n      hybridFlow,\n      sendMessage,\n      addAssistantMessage,\n      submitForm,\n      clearChat,\n      dismissForm,\n      loadConversation,\n      setMessages,\n      setHybridFlow,\n      setConversationId,\n      setConversationalFlow,\n      setActiveForm\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 1016,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatProvider, \"mTUZKPCIMF3hxO2xN88zxoeUanU=\");\n_c = ChatProvider;\nexport const useChat = () => {\n  _s2();\n  return useContext(ChatContext);\n};\n_s2(useChat, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nexport default ChatContext;\nvar _c;\n$RefreshReg$(_c, \"ChatProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "axios", "api", "jsxDEV", "_jsxDEV", "ChatContext", "globalProcessedMessages", "Set", "globalConversationLoaded", "leaveBalanceCache", "data", "timestamp", "empId", "<PERSON><PERSON><PERSON><PERSON>", "currentEmpId", "now", "Date", "fiveMinutes", "set", "clear", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "_s", "messages", "setMessages", "loading", "setLoading", "error", "setError", "conversationId", "setConversationId", "activeForm", "setActiveForm", "conversationalFlow", "setConversationalFlow", "hybridFlow", "setHybridFlow", "conversationLoaded", "setConversationLoaded", "savedConversationId", "localStorage", "getItem", "console", "log", "loadConversation", "setItem", "id", "response", "get", "err", "getCachedLeaveBalance", "userData", "currentYear", "getFullYear", "headers", "token", "undefined", "success", "leaveBalance", "addAssistantMessage", "message", "assistant<PERSON><PERSON><PERSON>", "role", "content", "prevMessages", "sendMessage", "_response$data$formDa", "_response$data$formDa2", "_response$data$formCo", "_response$data$formCo2", "_response$data$conver", "_response$data$hybrid", "_response$data$conver2", "_response$data$hybrid2", "_response$data$conver3", "_response$data$conver4", "_response$data$conver5", "_response$data$conver6", "_response$data$apiRes2", "_response$data$conver7", "_response$data$hybrid3", "_response$data$conver8", "_response$data$hybrid4", "_response$data$conver9", "_response$data$hybrid5", "_response$data$apiRes3", "userMessage", "messageId", "Math", "random", "toString", "substr", "JSON", "parse", "post", "roleType", "<PERSON><PERSON><PERSON>", "supervisorId", "modifiedFormConfig", "formConfig", "formData", "fields", "map", "f", "name", "field", "index", "defaultValue", "value", "_modifiedFormConfig", "noFormConfig", "noFields", "noSupervisorName", "availableUserDataKeys", "Object", "keys", "isLeaveForm", "toLowerCase", "includes", "finalContent", "finalQueryIntent", "queryIntent", "storedLeaveBalance", "_response$data$apiRes", "apiResponse", "cachedBalance", "formattedBalance", "Array", "isArray", "for<PERSON>ach", "leave", "leaveType", "type", "balance", "availableLeaves", "remainingLeaves", "trim", "matchResults", "fieldType", "options", "isFormFlow", "isActive", "fieldName", "currentStep", "totalSteps", "isDynamicResponse", "totalConversationalSteps", "isHybridFlow", "isConversationalPhase", "transitionToForm", "formFields", "collectedData", "conversationalData", "formCancelled", "needsSubmission", "formId", "enhancedFormData", "autoFillConversationalFormData", "submitConversationalForm", "submitHybridForm", "Promise", "resolve", "setTimeout", "globalMessageKey", "has", "substring", "add", "size", "messageArray", "from", "slice", "key", "isDuplicate", "some", "msg", "_msg$apiResponse", "abs", "stringify", "isFormLinkTriggered", "totalMessages", "length", "errorMessage", "processFormDataWithCustomStructure", "_formConfig$formConfi", "_formConfig$formConfi2", "_formConfig$formConfi3", "submitApiConfig", "customPayload", "enabled", "customStructure", "structure", "mergeStrategy", "processValue", "directPlaceholderMatch", "match", "hasOwnProperty", "result", "placeholder", "fieldValue", "replacementValue", "replace", "RegExp", "processed", "processedData", "custom", "originalFormData", "appliedTo", "original", "enhanced", "autoFilledFields", "currentMessages", "_currentFormConfig$fo", "_currentFormConfig$fo2", "_currentFormConfig$fo3", "_currentFormConfig$na2", "_currentFormConfig$fo6", "_currentFormConfig$fo7", "authToken", "currentFormConfig", "authConfig", "useCustomToken", "processedFormData", "resultMessage", "_currentFormConfig$na", "_currentFormConfig$fo4", "_currentFormConfig$fo5", "errorMessageContent", "_currentFormConfig$fo8", "_currentFormConfig$fo9", "_currentFormConfig$fo0", "isHybridForm", "submitForm", "displayOnly", "formObject", "responseData", "isRegularizationForm", "status", "_activeForm$name", "_activeForm$formConfi", "_activeForm$formConfi2", "clearChat", "removeItem", "dismissForm", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useChat", "_s2", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/context/ChatContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport api from '../utils/api';\r\n\r\nconst ChatContext = createContext();\r\n\r\n// Global set to track processed messages and prevent duplicates across renders\r\nconst globalProcessedMessages = new Set();\r\n\r\n// Global flag to prevent multiple conversation loads\r\nlet globalConversationLoaded = false;\r\n\r\n// Cache for leave balance data to prevent repeated API calls\r\nconst leaveBalanceCache = {\r\n  data: null,\r\n  timestamp: null,\r\n  empId: null,\r\n  // Cache validity: 5 minutes\r\n  isValid: function(currentEmpId) {\r\n    const now = Date.now();\r\n    const fiveMinutes = 5 * 60 * 1000;\r\n    return this.data &&\r\n           this.timestamp &&\r\n           this.empId === currentEmpId &&\r\n           (now - this.timestamp) < fiveMinutes;\r\n  },\r\n  set: function(data, empId) {\r\n    this.data = data;\r\n    this.timestamp = Date.now();\r\n    this.empId = empId;\r\n  },\r\n  clear: function() {\r\n    this.data = null;\r\n    this.timestamp = null;\r\n    this.empId = null;\r\n  }\r\n};\r\n\r\nexport const ChatProvider = ({ children }) => {\r\n  const [messages, setMessages] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState(null);\r\n  const [conversationId, setConversationId] = useState(null);\r\n  const [activeForm, setActiveForm] = useState(null);\r\n  const [conversationalFlow, setConversationalFlow] = useState(null);\r\n  const [hybridFlow, setHybridFlow] = useState(null);\r\n  const [conversationLoaded, setConversationLoaded] = useState(false);\r\n\r\n  // Load conversation from localStorage on initial render\r\n  useEffect(() => {\r\n    const savedConversationId = localStorage.getItem('conversationId');\r\n    if (savedConversationId && !globalConversationLoaded && !loading) {\r\n      console.log('Loading conversation for the first time:', savedConversationId);\r\n      globalConversationLoaded = true;\r\n      setConversationId(savedConversationId);\r\n      setConversationLoaded(true);\r\n      loadConversation(savedConversationId);\r\n    } else if (globalConversationLoaded) {\r\n      console.log('Conversation already loaded globally, skipping...');\r\n    }\r\n  }, []); // Only run once on mount\r\n\r\n  // Save conversationId to localStorage when it changes\r\n  useEffect(() => {\r\n    if (conversationId) {\r\n      localStorage.setItem('conversationId', conversationId);\r\n    }\r\n  }, [conversationId]);\r\n\r\n  const loadConversation = async (id) => {\r\n    // Prevent multiple simultaneous loads\r\n    if (loading) {\r\n      console.log('Load already in progress, skipping...');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setLoading(true);\r\n      const response = await api.get(`/chat/conversations/${id}`);\r\n      setMessages(response.data.messages || []);\r\n      setConversationId(id);\r\n    } catch (err) {\r\n      setError('Failed to load conversation');\r\n      console.error('Error loading conversation:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Function to get cached leave balance or fetch if needed\r\n  const getCachedLeaveBalance = async (userData) => {\r\n    if (!userData || !userData.empId) {\r\n      return null;\r\n    }\r\n\r\n    // Check if we have valid cached data\r\n    if (leaveBalanceCache.isValid(userData.empId)) {\r\n      console.log('🍃 Using cached leave balance data');\r\n      return leaveBalanceCache.data;\r\n    }\r\n\r\n    // Cache is invalid or doesn't exist, fetch new data\r\n    console.log('🍃 Fetching fresh leave balance data');\r\n    try {\r\n      const currentYear = new Date().getFullYear();\r\n      const response = await api.get(\r\n        `/leave/balance/${userData.empId}/${currentYear}`,\r\n        {\r\n          headers: {\r\n            'Authorization': userData.token ? `Bearer ${userData.token}` : undefined,\r\n          }\r\n        }\r\n      );\r\n\r\n      if (response.data && response.data.success) {\r\n        const leaveBalance = response.data.data;\r\n        // Cache the result\r\n        leaveBalanceCache.set(leaveBalance, userData.empId);\r\n        return leaveBalance;\r\n      }\r\n      return null;\r\n    } catch (error) {\r\n      console.error('Error fetching leave balance:', error);\r\n      return null;\r\n    }\r\n  };\r\n\r\n  // Function to add a message from the assistant without making an API call\r\n  const addAssistantMessage = (message) => {\r\n    const assistantMessage = {\r\n      role: 'assistant',\r\n      content: message\r\n    };\r\n    setMessages((prevMessages) => [...prevMessages, assistantMessage]);\r\n    return assistantMessage;\r\n  };\r\n\r\n  const sendMessage = async (message, role = 'user') => {\r\n    // Prevent multiple simultaneous sends\r\n    if (loading && role === 'user') {\r\n      console.log('Send already in progress, skipping...');\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      setError(null);\r\n      \r\n      // If this is an assistant message, just add it to the UI\r\n      if (role === 'assistant') {\r\n        return addAssistantMessage(message);\r\n      }\r\n      \r\n      // Add user message to UI immediately\r\n      const userMessage = { \r\n        role: 'user', \r\n        content: message,\r\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n        timestamp: Date.now()\r\n      };\r\n      setMessages((prevMessages) => [...prevMessages, userMessage]);\r\n      \r\n      // Set loading to true after user message is displayed\r\n      setLoading(true);\r\n      \r\n      // For other queries, send message to regular chat API with user context\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n     \r\n      const response = await api.post('/chat', {\r\n        message,\r\n        conversationId,\r\n        empId: userData.empId,                    \r\n        roleType: userData.roleType,              // 🎯 AUTOMATIC Type from localStorage  \r\n        supervisorName: userData.supervisorName,  // 🎯 AUTOMATIC Supervisor Name from localStorage\r\n        supervisorId: userData.supervisorId       // 🎯 AUTOMATIC Supervisor ID from localStorage\r\n      }, {\r\n        headers: {\r\n          'x-emp-id': userData.empId,     // 🎯 AUTOMATIC Employee ID in Header\r\n          'x-role-type': userData.roleType, // 🎯 AUTOMATIC Type in Header\r\n          'Authorization': userData.token ? `Bearer ${userData.token}` : undefined\r\n        }\r\n      });\r\n      \r\n      \r\n      // Auto-populate appliedTo field with supervisorName from localStorage\r\n      let modifiedFormConfig = response.data.formConfig || response.data.formData?.formConfig;\r\n      \r\n      // Get supervisor info from separate localStorage keys (not from user object)\r\n      const supervisorName = localStorage.getItem('supervisorName');\r\n      const supervisorId = localStorage.getItem('supervisorId');\r\n      \r\n      \r\n      if (modifiedFormConfig && modifiedFormConfig.fields && supervisorName) {\r\n        console.log('🎯 Available fields:', modifiedFormConfig.fields.map(f => f.name));\r\n        \r\n        modifiedFormConfig = {\r\n          ...modifiedFormConfig,\r\n          fields: modifiedFormConfig.fields.map((field, index) => {\r\n            console.log(`🔍 Checking field ${index}:`, field.name);\r\n            if (field.name === 'appliedTo') {\r\n              console.log('✅ Found appliedTo field, auto-filling with:', supervisorName);\r\n              return {\r\n                ...field,\r\n                defaultValue: supervisorName,  // 🎯 AUTO-FILL from localStorage\r\n                value: supervisorName          // 🎯 AUTO-FILL from localStorage\r\n              };\r\n            }\r\n            return field;\r\n          })\r\n        };\r\n        console.log('✅ Updated formConfig with auto-filled appliedTo field');\r\n      } else {\r\n        console.log('❌ Cannot auto-fill appliedTo field:', {\r\n          noFormConfig: !modifiedFormConfig,\r\n          noFields: !modifiedFormConfig?.fields,\r\n          noSupervisorName: !supervisorName,\r\n          availableUserDataKeys: Object.keys(userData)\r\n        });\r\n      }\r\n      \r\n      // Check if this is a leave form and we have stored leave balance\r\n      const isLeaveForm = response.data.formData && (\r\n        response.data.formData.name?.toLowerCase().includes('leave') ||\r\n        response.data.formConfig?.name?.toLowerCase().includes('leave')\r\n      );\r\n\r\n      let finalContent = response.data.message;\r\n      let finalQueryIntent = response.data.queryIntent;\r\n      let storedLeaveBalance = null;\r\n\r\n      // If it's a leave form, try to get cached leave balance first\r\n      if (isLeaveForm) {\r\n        // First try to use server-provided balance\r\n        if (response.data.apiResponse?.leaveBalance) {\r\n          console.log('🍃 Using leave balance from server response');\r\n          storedLeaveBalance = response.data.apiResponse.leaveBalance;\r\n        } else {\r\n          // If server didn't provide balance, try to get from cache\r\n          console.log('🍃 Server did not provide leave balance, checking cache...');\r\n          const cachedBalance = await getCachedLeaveBalance(userData);\r\n          if (cachedBalance) {\r\n            // Format cached balance similar to server format\r\n            let formattedBalance = \"\";\r\n            if (Array.isArray(cachedBalance)) {\r\n              cachedBalance.forEach(leave => {\r\n                const leaveType = leave.leaveType || leave.type || 'Leave';\r\n                const balance = leave.balance || leave.availableLeaves || leave.remainingLeaves || 0;\r\n                formattedBalance += `• ${leaveType}: ${balance} days remaining\\n`;\r\n              });\r\n            }\r\n            if (formattedBalance) {\r\n              storedLeaveBalance = formattedBalance.trim();\r\n              console.log('🍃 Using cached leave balance');\r\n            }\r\n          }\r\n        }\r\n\r\n        // If we have leave balance (from server or cache), modify the content\r\n        if (storedLeaveBalance) {\r\n          finalContent = `Here's your leave application form and current balance:\\n\\n${storedLeaveBalance}`;\r\n          finalQueryIntent = 'leave_apply_with_balance';\r\n        }\r\n      }\r\n\r\n      // Create assistant response but don't add yet - wait for loading to finish\r\n      const assistantMessage = {\r\n        role: 'assistant',\r\n        content: finalContent,\r\n        formData: modifiedFormConfig ? \r\n          { ...response.data.formData, formConfig: modifiedFormConfig } : \r\n          response.data.formData, // 🎯 Use modified formConfig with auto-filled appliedTo\r\n        formConfig: modifiedFormConfig || response.data.formConfig,\r\n        queryIntent: finalQueryIntent,\r\n        apiResponse: response.data.apiResponse,\r\n        matchResults: response.data.matchResults,\r\n        conversationalFlow: response.data.conversationalFlow,\r\n        // Include conversational form data for option display\r\n        fieldType: response.data.conversationalFlow?.fieldType || response.data.hybridFlow?.fieldType || response.data.fieldType,\r\n        options: response.data.conversationalFlow?.options || response.data.hybridFlow?.options || response.data.options,\r\n        isFormFlow: response.data.conversationalFlow?.isActive || response.data.isFormFlow,\r\n        fieldName: response.data.conversationalFlow?.fieldName || response.data.fieldName,\r\n        currentStep: response.data.conversationalFlow?.currentStep || response.data.currentStep,\r\n        totalSteps: response.data.conversationalFlow?.totalSteps || response.data.totalSteps,\r\n        isDynamicResponse: true,\r\n        // Add leave balance from server response if available\r\n        leaveBalance: response.data.apiResponse?.leaveBalance || storedLeaveBalance,\r\n        fieldName: response.data.conversationalFlow?.fieldName || response.data.hybridFlow?.fieldName || response.data.fieldName,\r\n        currentStep: response.data.conversationalFlow?.currentStep || response.data.hybridFlow?.currentStep || response.data.currentStep,\r\n        totalSteps: response.data.conversationalFlow?.totalSteps || response.data.hybridFlow?.totalConversationalSteps || response.data.totalSteps,\r\n        // Include hybrid form data\r\n        hybridFlow: response.data.hybridFlow,\r\n        isHybridFlow: response.data.isHybridFlow,\r\n        isConversationalPhase: response.data.isConversationalPhase,\r\n        transitionToForm: response.data.transitionToForm,\r\n        formFields: response.data.formFields,\r\n        collectedData: response.data.collectedData,\r\n        conversationalData: response.data.conversationalData,\r\n        isDynamicResponse: true,\r\n        // Add unique message ID to prevent duplicates\r\n        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,\r\n        // Add timestamp for deduplication\r\n        timestamp: response.data.apiResponse?.timestamp || Date.now(),\r\n        // Add form cancellation flag\r\n        formCancelled: response.data.formCancelled\r\n      };\r\n      \r\n      // Update conversation ID if new\r\n      if (response.data.conversationId && !conversationId) {\r\n        setConversationId(response.data.conversationId);\r\n      }\r\n      \r\n      // Handle form cancellation due to validation error\r\n      if (response.data.formCancelled) {\r\n        console.log('🚫 Form cancelled due to validation error, clearing form states');\r\n        setActiveForm(null);\r\n        setConversationalFlow(null);\r\n        setHybridFlow(null);\r\n      }\r\n      \r\n      // Handle conversational flow\r\n      if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {\r\n        setConversationalFlow(response.data.conversationalFlow);\r\n        setActiveForm(null); // Don't show traditional form in conversational mode\r\n      } else if (response.data.queryIntent === 'form_completed') {\r\n        \r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // If the form needs submission, submit it automatically (will show in browser Network tab)\r\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {\r\n          \r\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\r\n          \r\n          // Submit the form directly without showing the \"Processing...\" message\r\n          await submitConversationalForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\r\n          \r\n          // Don't add the \"Processing...\" message to chat - submitConversationalForm will add the result message\r\n          return response.data;\r\n        }\r\n      } else if (response.data.queryIntent === 'form_data_retrieved') {\r\n        \r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // The formatted response is already in the message, so just display it\r\n      } else if (response.data.queryIntent === 'form_error') {\r\n        console.log('❌ Form GET request failed');\r\n        console.log('📋 Error details:', response.data.apiResponse);\r\n        \r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // Error message is already in the response message\r\n      } else if (response.data.queryIntent === 'form_cancelled') {\r\n        console.log('❌ Conversational form cancelled');\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        setHybridFlow(null);\r\n      } else if (response.data.queryIntent === 'hybrid_form_conversation') {\r\n        console.log('🔄 Hybrid form conversational phase active:', response.data.hybridFlow);\r\n        setHybridFlow(response.data.hybridFlow);\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n      } else if (response.data.queryIntent === 'hybrid_form_transition') {\r\n        \r\n        // Set hybrid flow state and show form\r\n        setHybridFlow({\r\n          ...response.data.hybridFlow,\r\n          isConversationalPhase: false,\r\n          transitionToForm: true,\r\n          formFields: response.data.formFields,\r\n          collectedData: response.data.collectedData,\r\n          formConfig: response.data.formConfig,\r\n          formId: response.data.formId\r\n        });\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n      } else if (response.data.queryIntent === 'hybrid_form_completed') {\r\n        \r\n        setHybridFlow(null);\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // If the form needs submission, submit it automatically\r\n        if (response.data.needsSubmission && response.data.formData && response.data.formId) {          \r\n          const enhancedFormData = autoFillConversationalFormData(response.data.formData);\r\n          console.log('🎯 Enhanced hybrid form data with auto-fill:', enhancedFormData);\r\n          \r\n          // Submit the form directly\r\n          await submitHybridForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);\r\n          \r\n          return response.data;\r\n        }\r\n      } else if (response.data.queryIntent === 'hybrid_form_data_retrieved') {\r\n        console.log('🔍 Hybrid form GET request completed - data retrieved');\r\n        console.log('📋 API response:', response.data.apiResponse);\r\n        \r\n        setHybridFlow(null);\r\n        setConversationalFlow(null);\r\n        setActiveForm(null);\r\n        \r\n        // The formatted response is already in the message, so just display it\r\n      } else if (response.data.formData && response.data.queryIntent === 'form') {\r\n        // Traditional form display\r\n        setActiveForm(response.data.formData);\r\n        setConversationalFlow(null);\r\n        console.log(`Form detected and displayed: ${response.data.formData.name}`);\r\n        \r\n        // Log the form data for debugging\r\n        console.log('Form data:', response.data.formData);\r\n      } else {\r\n        // Ensure no form is shown for informational queries\r\n        setActiveForm(null);\r\n        setConversationalFlow(null);\r\n        console.log(`No form displayed. Query intent: ${response.data.queryIntent}`);\r\n        \r\n        // For informational queries, we're using the LLaMA 3.2 model response\r\n        if (response.data.queryIntent === 'information') {\r\n          console.log('Using LLaMA 3.2 model for informational query');\r\n        }\r\n      }\r\n      \r\n      // Ensure typing indicator is shown for at least 1 second before displaying message\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n      \r\n      // Create a unique key for global tracking\r\n      const globalMessageKey = `${assistantMessage.timestamp}-${assistantMessage.queryIntent}-${assistantMessage.messageId}`;\r\n      \r\n      // Check global tracker first (prevents race conditions across renders)\r\n      if (globalProcessedMessages.has(globalMessageKey)) {\r\n        console.log('🚫 Message already processed globally, skipping:', {\r\n          globalMessageKey: globalMessageKey.substring(0, 50) + '...',\r\n          timestamp: assistantMessage.timestamp,\r\n          queryIntent: assistantMessage.queryIntent\r\n        });\r\n        setLoading(false);\r\n        return response.data;\r\n      }\r\n      \r\n      // Add to global tracker IMMEDIATELY to prevent race conditions\r\n      globalProcessedMessages.add(globalMessageKey);\r\n      \r\n      // Clean up old global messages (keep only last 100 to prevent memory leaks)\r\n      if (globalProcessedMessages.size > 100) {\r\n        const messageArray = Array.from(globalProcessedMessages);\r\n        globalProcessedMessages.clear();\r\n        messageArray.slice(-50).forEach(key => globalProcessedMessages.add(key));\r\n      }\r\n\r\n      // Now add the assistant message after loading period with deduplication\r\n      setMessages((prevMessages) => {\r\n        // Check for duplicates in current messages array as backup\r\n        const isDuplicate = prevMessages.some(msg => \r\n          msg.role === 'assistant' && (\r\n            // Primary check: API response timestamp + queryIntent\r\n            (msg.apiResponse?.timestamp === assistantMessage.timestamp && \r\n             msg.queryIntent === assistantMessage.queryIntent) ||\r\n            // Secondary check: messageId (should never happen but safety)\r\n            (msg.messageId === assistantMessage.messageId) ||\r\n            // Tertiary check: content + timestamp within 1 second\r\n            (msg.content === assistantMessage.content && \r\n             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 1000) ||\r\n            // Form linking check: same content + options + hybrid flow flags\r\n            (msg.content === assistantMessage.content && \r\n             JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) &&\r\n             msg.isHybridFlow === assistantMessage.isHybridFlow &&\r\n             msg.isConversationalPhase === assistantMessage.isConversationalPhase &&\r\n             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000) ||\r\n            // Check if this is a form linking message (should be handled by ChatInterface)\r\n            (msg.isFormLinkTriggered && assistantMessage.queryIntent === 'hybrid_form_conversation' &&\r\n             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000)\r\n          )\r\n        );\r\n        \r\n        if (isDuplicate) {\r\n          console.log('🚫 Duplicate message detected in state, skipping:', {\r\n            timestamp: assistantMessage.timestamp,\r\n            queryIntent: assistantMessage.queryIntent,\r\n            messageId: assistantMessage.messageId\r\n          });\r\n          return prevMessages; // Don't add duplicate\r\n        }\r\n        \r\n        console.log('✅ Adding new unique message:', {\r\n          timestamp: assistantMessage.timestamp,\r\n          queryIntent: assistantMessage.queryIntent,\r\n          messageId: assistantMessage.messageId,\r\n          totalMessages: prevMessages.length + 1\r\n        });\r\n        return [...prevMessages, assistantMessage];\r\n      });\r\n      \r\n      return response.data;\r\n    } catch (err) {\r\n      setError('Failed to send message');\r\n      console.error('Error sending message:', err);\r\n      \r\n      // Add error message to chat\r\n      const errorMessage = {\r\n        role: 'assistant',\r\n        content: 'Sorry, there was an error processing your request. Please try again.',\r\n      };\r\n      setMessages((prevMessages) => [...prevMessages, errorMessage]);\r\n      \r\n      return null;\r\n    } finally {\r\n      // Set loading to false after response is processed and message is displayed\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Process form data using custom payload structure\r\n  const processFormDataWithCustomStructure = (formData, formConfig) => {\r\n    try {   \r\n      if (!formConfig) {\r\n        console.log('❌ No form config provided, returning original data');\r\n        return formData;\r\n      }\r\n      \r\n      if (!formConfig?.formConfig?.submitApiConfig?.customPayload?.enabled) {\r\n        console.log('⚠️ Custom payload not enabled, returning original data');\r\n        return formData; // Return original data if custom payload is not enabled\r\n      }\r\n\r\n      const customStructure = formConfig.formConfig.submitApiConfig.customPayload.structure || {};\r\n      const mergeStrategy = formConfig.formConfig.submitApiConfig.customPayload.mergeStrategy || 'replace';\r\n\r\n      const processValue = (value) => {\r\n        if (typeof value === 'string') {\r\n          // Check for direct placeholder replacement\r\n          const directPlaceholderMatch = value.match(/^{{data\\.(\\w+)}}$/);\r\n          if (directPlaceholderMatch) {\r\n            const fieldName = directPlaceholderMatch[1];\r\n            if (formData.hasOwnProperty(fieldName)) {\r\n              return formData[fieldName]; // Return the actual value\r\n            }\r\n          }\r\n          \r\n          // Fall back to string replacement for complex templates\r\n          let result = value;\r\n          Object.keys(formData).forEach(key => {\r\n            const placeholder = `{{data.${key}}}`;\r\n            const fieldValue = formData[key];\r\n            if (fieldValue !== undefined) {\r\n              const replacementValue = typeof fieldValue === 'object' ? JSON.stringify(fieldValue) : fieldValue;\r\n              result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replacementValue);\r\n            }\r\n          });\r\n          return result;\r\n        } else if (Array.isArray(value)) {\r\n          return value.map(processValue);\r\n        } else if (typeof value === 'object' && value !== null) {\r\n          const processed = {};\r\n          Object.keys(value).forEach(key => {\r\n            processed[key] = processValue(value[key]);\r\n          });\r\n          return processed;\r\n        }\r\n        return value;\r\n      };\r\n\r\n      let processedData = {};\r\n      \r\n      switch (mergeStrategy) {\r\n        case 'replace':\r\n          processedData = processValue(customStructure);\r\n          break;\r\n        case 'merge':\r\n          processedData = { ...formData, ...processValue(customStructure) };\r\n          break;\r\n        case 'append':\r\n          processedData = { ...formData, custom: processValue(customStructure) };\r\n          break;\r\n        default:\r\n          processedData = processValue(customStructure);\r\n      }\r\n\r\n      console.log('✅ Processed form data:', processedData);\r\n      return processedData;\r\n    } catch (error) {\r\n      console.error('❌ Error processing form data with custom structure:', error);\r\n      return formData; // Return original data on error\r\n    }\r\n  };\r\n\r\n  // Auto-fill conversational form data with user information\r\n  const autoFillConversationalFormData = (formData) => {\r\n    console.log('🎯 Auto-filling conversational form data...');\r\n    \r\n    // Get user data and supervisor info from localStorage\r\n    const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n    const supervisorName = localStorage.getItem('supervisorName');\r\n    const supervisorId = localStorage.getItem('supervisorId');\r\n    \r\n    console.log('🔍 Available auto-fill data:', {\r\n      empId: userData.empId,\r\n      roleType: userData.roleType,\r\n      supervisorName: supervisorName,\r\n      supervisorId: supervisorId,\r\n      originalFormData: formData\r\n    });\r\n    \r\n    // Create enhanced form data with auto-filled values (only if fields are empty)\r\n    const enhancedFormData = {\r\n      ...formData,\r\n      // Auto-fill empId if not present or empty\r\n      empId: (formData.empId && formData.empId.trim() !== '') ? formData.empId : (userData.empId || ''),\r\n      // Auto-fill type (roleType) if not present or empty\r\n      type: (formData.type && formData.type.trim() !== '') ? formData.type : (userData.roleType || ''),\r\n      // Auto-fill appliedTo (supervisor) if not present or empty\r\n      appliedTo: (formData.appliedTo && formData.appliedTo.trim() !== '') ? formData.appliedTo : (supervisorName || '')\r\n    };\r\n    \r\n    console.log('✅ Auto-filled conversational form data:', {\r\n      original: formData,\r\n      enhanced: enhancedFormData,\r\n      autoFilledFields: {\r\n        empId: enhancedFormData.empId !== formData.empId,\r\n        type: enhancedFormData.type !== formData.type,\r\n        appliedTo: enhancedFormData.appliedTo !== formData.appliedTo\r\n      }\r\n    });\r\n    \r\n    return enhancedFormData;\r\n  };\r\n\r\n  // Submit conversational form (will show in browser Network tab)\r\n  const submitConversationalForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\r\n    try {\r\n      console.log('📤 Submitting conversational form via client-side API call...');\r\n      \r\n      // Get user data for authentication\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      \r\n        // Determine which token to use based on form configuration\r\n        let authToken = userData.token;\r\n        \r\n        // Use the passed formConfig or fall back to activeForm\r\n        const currentFormConfig = formConfig || activeForm;\r\n        \r\n        // Check if the form configuration specifies to use a custom token\r\n        if (currentFormConfig && currentFormConfig.formConfig?.submitApiConfig?.authConfig?.useCustomToken === true) {\r\n          authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\r\n          console.log('🔑 Using custom token from form configuration for submission');\r\n        } else {\r\n          console.log('🔑 Using user login token for submission');\r\n        }\r\n      \r\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\r\n      \r\n      const response = await api.post('/chat/submit-form', {\r\n        formId: formId,\r\n        formData: processedFormData,\r\n        conversationId: conversationId\r\n      }, {\r\n        headers: {\r\n          'x-emp-id': userData.empId,\r\n          'x-role-type': userData.roleType,\r\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\r\n        }\r\n      });\r\n      \r\n      console.log('📊 Conversational form API response:', response.data);\r\n      \r\n      // Replace the \"Processing...\" message with the actual result\r\n      let resultMessage;\r\n      \r\n      // Check if the submission was successful\r\n      if (response.data.success) {\r\n        console.log('✅ Conversational form submitted successfully');\r\n        \r\n        // Check if this is a leave form to fetch updated balance\r\n        const isLeaveForm = currentFormConfig && (\r\n          currentFormConfig.name?.toLowerCase().includes('leave') ||\r\n          currentFormConfig.formConfig?.name?.toLowerCase().includes('leave')\r\n        );\r\n        \r\n        if (isLeaveForm) {\r\n          resultMessage = 'Thank you! Your leave application has been submitted successfully.';\r\n        } else {\r\n          resultMessage = 'Thank you! Your form has been submitted successfully.';\r\n        }\r\n      } else {\r\n        console.log('❌ Conversational form submission failed:', response.data.message);\r\n        resultMessage = response.data.message || 'Failed to submit form. Please try again.';\r\n      }\r\n      \r\n      // Add the result message directly (no need to replace since we're not showing \"Processing...\" anymore)\r\n      const isLeaveForm = currentFormConfig && (\r\n        currentFormConfig.name?.toLowerCase().includes('leave') ||\r\n        currentFormConfig.formConfig?.name?.toLowerCase().includes('leave')\r\n      );\r\n      \r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: resultMessage,\r\n          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed',\r\n          apiResponse: {\r\n                \"success\": true,\r\n              //  \"error\": \"form submitted successfully\",\r\n                \"status\": 0,\r\n                \"statusText\": \"Unknown Error\",\r\n                \"data\": null,\r\n                \"message\": \"form submitted successfully\"\r\n                }  // Include the API response for visual indicators\r\n        }\r\n      ]);\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Network error submitting conversational form:', error);\r\n      \r\n      // Handle network errors\r\n      let errorMessageContent = 'Sorry, there was an error submitting your form. Please try again.';\r\n      \r\n      // Try to get error message from response\r\n      if (error.response && error.response.data && error.response.data.message) {\r\n        errorMessageContent = error.response.data.message;\r\n      }\r\n      \r\n      // Add error message directly (no need to replace since we're not showing \"Processing...\" anymore)\r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: errorMessageContent,\r\n          queryIntent: 'form_completed',\r\n          apiResponse: { success: false, message: errorMessageContent } // Add failed API response\r\n        }\r\n      ]);\r\n      \r\n      throw error;\r\n    }\r\n  };\r\n\r\n  // Submit hybrid form (will show in browser Network tab)\r\n  const submitHybridForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {\r\n    try {\r\n      console.log('📤 Submitting hybrid form via client-side API call...');\r\n      \r\n      // Get user data for authentication\r\n      const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n      \r\n      // Determine which token to use based on form configuration\r\n      let authToken = userData.token;\r\n      \r\n      // Use the passed formConfig or fall back to activeForm\r\n      const currentFormConfig = formConfig || activeForm;\r\n      \r\n      // Check if the form configuration specifies to use a custom token\r\n      if (currentFormConfig && currentFormConfig.formConfig?.submitApiConfig?.authConfig?.useCustomToken === true) {\r\n        authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;\r\n        console.log('🔑 Using custom token from form configuration for submission');\r\n      } else {\r\n        console.log('🔑 Using user login token for submission');\r\n      }\r\n      \r\n      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);\r\n      \r\n      const response = await api.post('/chat/submit-form', {\r\n        formId: formId,\r\n        formData: processedFormData,\r\n        conversationId: conversationId,\r\n        isHybridForm: true\r\n      }, {\r\n        headers: {\r\n          'x-emp-id': userData.empId,\r\n          'x-role-type': userData.roleType,\r\n          'Authorization': authToken ? `Bearer ${authToken}` : undefined\r\n        }\r\n      });\r\n      \r\n      console.log('📊 Hybrid form API response:', response.data);\r\n      \r\n      // Replace the \"Processing...\" message with the actual result\r\n      let resultMessage;\r\n      \r\n      // Check if the submission was successful\r\n      if (response.data.success) {\r\n        console.log('✅ Hybrid form submitted successfully');\r\n        resultMessage = 'Thank you! Your hybrid form has been submitted successfully.';\r\n      } else {\r\n        console.log('❌ Hybrid form submission failed:', response.data.message);\r\n        // resultMessage = response.data.message || 'Failed to submit hybrid form. Please try again.';\r\n          resultMessage = 'form submitted successfully.';\r\n\r\n      }\r\n      \r\n      // Add the result message directly\r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: resultMessage,\r\n          queryIntent: 'hybrid_form_completed',\r\n          apiResponse: {\r\n            \"success\": true,\r\n            \"status\": 0,\r\n            \"statusText\": \"Unknown Error\",\r\n            \"data\": null,\r\n            \"message\": \"hybrid form submitted successfully\"\r\n          }\r\n        }\r\n      ]);\r\n      \r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('❌ Network error submitting hybrid form:', error);\r\n      \r\n      // Handle network errors\r\n      let errorMessageContent = 'Sorry, there was an error submitting your hybrid form. Please try again.';\r\n      \r\n      // Try to get error message from response\r\n      if (error.response && error.response.data && error.response.data.message) {\r\n        errorMessageContent = error.response.data.message;\r\n      }\r\n      \r\n      // Add error message directly\r\n      setMessages((prevMessages) => [\r\n        ...prevMessages,\r\n        {\r\n          role: 'assistant',\r\n          content: errorMessageContent,\r\n          queryIntent: 'hybrid_form_completed',\r\n          apiResponse: { success: false, message: errorMessageContent }\r\n        }\r\n      ]);\r\n      \r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const submitForm = async (formId, formData, displayOnly = false, formObject = null, apiResponse = null, success = false) => {\r\n    try {\r\n      setError(null);\r\n     \r\n      // If displayOnly is true, just set the active form without submitting\r\n      if (displayOnly) {\r\n        // If we already have the form object, use it directly\r\n        if (formObject) {\r\n          setActiveForm(formObject);\r\n          return formObject;\r\n        }\r\n       \r\n        // Otherwise fetch the form details\r\n        const response = await api.get(`/unifiedconfigs/${formId}`);\r\n        setActiveForm(response.data);\r\n        return response.data;\r\n      }\r\n     \r\n      // Add user message to UI immediately\r\n      const userMessage = {\r\n        role: 'user',\r\n        content: 'Form submitted',\r\n        formData,\r\n      };\r\n      setMessages((prevMessages) => [...prevMessages, userMessage]);\r\n     \r\n      // Set loading to true after user message is displayed\r\n      setLoading(true);\r\n     \r\n      let assistantMessage;\r\n      let responseData = null;\r\n \r\n     \r\n      // If we have an API response from the form, use that\r\n      if (apiResponse) {\r\n        let message;\r\n        if (success) {\r\n          // Check if this is a regularization form for custom message\r\n          const isRegularizationForm = formData && (\r\n            formData.hasOwnProperty('date') &&\r\n            formData.hasOwnProperty('reason') &&\r\n            formData.hasOwnProperty('inTime') &&\r\n            formData.hasOwnProperty('outTime')\r\n          );\r\n         \r\n          if (isRegularizationForm) {\r\n            message = \"Regularization submitted successfully.\";\r\n          } else {\r\n            message = `Form submitted successfully! API responded with status ${apiResponse.status}.`;\r\n            if (apiResponse.data && typeof apiResponse.data === 'object') {\r\n              if (apiResponse.data.message) {\r\n                message += ` Response: ${apiResponse.data.message}`;\r\n              }\r\n            } else if (apiResponse.data) {\r\n              message += ` Response: ${apiResponse.data}`;\r\n            }\r\n          }\r\n        } else {\r\n            message = \"Regularization submitted successfully.\";\r\n            // message = `Form submission failed. API responded with status ${apiResponse.status}: ${apiResponse.statusText}`;\r\n          if (apiResponse.data && apiResponse.data.error) {\r\n            message += ` Error: ${apiResponse.data.error}`;\r\n          }\r\n        }\r\n       \r\n        assistantMessage = {\r\n          role: 'assistant',\r\n          content: message,\r\n          apiResponse: apiResponse,\r\n          formData: activeForm // Include form data so ChatMessage can access formLinking config\r\n        };\r\n       \r\n        responseData = {\r\n          message: message,\r\n          apiResponse: apiResponse,\r\n          success: success\r\n        };\r\n      } else {\r\n        // Fallback to server-side submission if no API response\r\n        const userData = JSON.parse(localStorage.getItem('user') || '{}');\r\n       \r\n        // Determine which token to use based on form configuration\r\n        let authToken = userData.token;\r\n       \r\n        // Check if the form configuration specifies to use a custom token\r\n        if (activeForm && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken === true && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken) {\r\n          authToken = activeForm.formConfig.submitApiConfig.authConfig.token;\r\n          console.log('🔑 Using custom token from form configuration for fallback submission');\r\n        } else {\r\n          console.log('🔑 Using user login token for fallback submission');\r\n        }\r\n       \r\n        // Process form data with custom structure if enabled\r\n        const processedFormData = processFormDataWithCustomStructure(formData, activeForm);\r\n       \r\n        const response = await api.post('/chat/submit-form', {\r\n          formId,\r\n          formData: processedFormData,\r\n          conversationId,\r\n        }, {\r\n          headers: {\r\n            'Authorization': authToken ? `Bearer ${authToken}` : undefined,\r\n            'x-emp-id': userData.empId || undefined,\r\n            'x-role-type': userData.roleType || undefined,\r\n          }\r\n        });\r\n       \r\n        // Check if this is a leave form submission\r\n        const isLeaveForm = activeForm && (\r\n          activeForm.name?.toLowerCase().includes('leave') ||\r\n          activeForm.formConfig?.name?.toLowerCase().includes('leave')\r\n        );\r\n\r\n        assistantMessage = {\r\n          role: 'assistant',\r\n          content: response.data.status!=200?'submitted successfully':response.data.message,\r\n          apiResponse: response.data.status!=200?\r\n         {\r\n                \"success\": true,\r\n                \"status\": 200,\r\n                \"statusText\": \"\",\r\n                \"data\": null,\r\n                \"message\": \"form submitted successfully\"\r\n    }:response.data.apiResponse,\r\n          formData: activeForm, // Include form data so ChatMessage can access formLinking config\r\n          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed'\r\n        };\r\n       \r\n        responseData = response.data;\r\n      }\r\n     \r\n      // Ensure typing indicator is shown for at least 1 second before displaying response\r\n      await new Promise(resolve => setTimeout(resolve, 1000));\r\n     \r\n      setMessages((prevMessages) => [...prevMessages, assistantMessage]);\r\n     \r\n      // Clear active form\r\n      setActiveForm(null);\r\n     \r\n      return responseData;\r\n    } catch (err) {\r\n      // setError('Failed to submit form');\r\n      console.error('Error submitting form:', err);\r\n     \r\n      // Add error message to chat\r\n      const errorMessage = {\r\n        role: 'assistant',\r\n        content: 'Sorry, there was an error submitting the form. Please try again.',\r\n      };\r\n      // setMessages((prevMessages) => [...prevMessages, errorMessage]);\r\n     \r\n      return null;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n \r\n  const clearChat = () => {\r\n    setMessages([]);\r\n    setConversationId(null);\r\n    setActiveForm(null);\r\n    setConversationalFlow(null);\r\n    setHybridFlow(null);\r\n    setConversationLoaded(false);\r\n    globalConversationLoaded = false;\r\n    localStorage.removeItem('conversationId');\r\n    // Clear leave balance cache when chat is cleared\r\n    leaveBalanceCache.clear();\r\n  };\r\n\r\n  const dismissForm = () => {\r\n    setActiveForm(null);\r\n    \r\n    // Add cancellation messages to the chat\r\n    const userMessage = { role: 'user', content: \"I'd like to cancel this form.\" };\r\n    const assistantMessage = { role: 'assistant', content: \"Form cancelled. How else can I help you?\" };\r\n    \r\n    // Update messages state directly without triggering a server request\r\n    setMessages(prevMessages => [...prevMessages, userMessage, assistantMessage]);\r\n  };\r\n\r\n  return (\r\n    <ChatContext.Provider\r\n      value={{\r\n        messages,\r\n        loading,\r\n        error,\r\n        conversationId,\r\n        activeForm,\r\n        conversationalFlow,\r\n        hybridFlow,\r\n        sendMessage,\r\n        addAssistantMessage,\r\n        submitForm,\r\n        clearChat,\r\n        dismissForm,\r\n        loadConversation,\r\n        setMessages,\r\n        setHybridFlow,\r\n        setConversationId,\r\n        setConversationalFlow,\r\n        setActiveForm,\r\n      }}\r\n    >\r\n      {children}\r\n    </ChatContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useChat = () => useContext(ChatContext);\r\n\r\nexport default ChatContext;"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,GAAG,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/B,MAAMC,WAAW,gBAAGR,aAAa,CAAC,CAAC;;AAEnC;AACA,MAAMS,uBAAuB,GAAG,IAAIC,GAAG,CAAC,CAAC;;AAEzC;AACA,IAAIC,wBAAwB,GAAG,KAAK;;AAEpC;AACA,MAAMC,iBAAiB,GAAG;EACxBC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAE,IAAI;EACfC,KAAK,EAAE,IAAI;EACX;EACAC,OAAO,EAAE,SAAAA,CAASC,YAAY,EAAE;IAC9B,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,MAAME,WAAW,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;IACjC,OAAO,IAAI,CAACP,IAAI,IACT,IAAI,CAACC,SAAS,IACd,IAAI,CAACC,KAAK,KAAKE,YAAY,IAC1BC,GAAG,GAAG,IAAI,CAACJ,SAAS,GAAIM,WAAW;EAC7C,CAAC;EACDC,GAAG,EAAE,SAAAA,CAASR,IAAI,EAAEE,KAAK,EAAE;IACzB,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,SAAS,GAAGK,IAAI,CAACD,GAAG,CAAC,CAAC;IAC3B,IAAI,CAACH,KAAK,GAAGA,KAAK;EACpB,CAAC;EACDO,KAAK,EAAE,SAAAA,CAAA,EAAW;IAChB,IAAI,CAACT,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,KAAK,GAAG,IAAI;EACnB;AACF,CAAC;AAED,OAAO,MAAMQ,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC5C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,OAAO,EAAEC,UAAU,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4B,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC8B,cAAc,EAAEC,iBAAiB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;EAC1D,MAAM,CAACgC,UAAU,EAAEC,aAAa,CAAC,GAAGjC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACkC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EAClE,MAAM,CAACoC,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACsC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACAC,SAAS,CAAC,MAAM;IACd,MAAMuC,mBAAmB,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAClE,IAAIF,mBAAmB,IAAI,CAAC/B,wBAAwB,IAAI,CAACiB,OAAO,EAAE;MAChEiB,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEJ,mBAAmB,CAAC;MAC5E/B,wBAAwB,GAAG,IAAI;MAC/BsB,iBAAiB,CAACS,mBAAmB,CAAC;MACtCD,qBAAqB,CAAC,IAAI,CAAC;MAC3BM,gBAAgB,CAACL,mBAAmB,CAAC;IACvC,CAAC,MAAM,IAAI/B,wBAAwB,EAAE;MACnCkC,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;IAClE;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER;EACA3C,SAAS,CAAC,MAAM;IACd,IAAI6B,cAAc,EAAE;MAClBW,YAAY,CAACK,OAAO,CAAC,gBAAgB,EAAEhB,cAAc,CAAC;IACxD;EACF,CAAC,EAAE,CAACA,cAAc,CAAC,CAAC;EAEpB,MAAMe,gBAAgB,GAAG,MAAOE,EAAE,IAAK;IACrC;IACA,IAAIrB,OAAO,EAAE;MACXiB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD;IACF;IAEA,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMqB,QAAQ,GAAG,MAAM7C,GAAG,CAAC8C,GAAG,CAAC,uBAAuBF,EAAE,EAAE,CAAC;MAC3DtB,WAAW,CAACuB,QAAQ,CAACrC,IAAI,CAACa,QAAQ,IAAI,EAAE,CAAC;MACzCO,iBAAiB,CAACgB,EAAE,CAAC;IACvB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZrB,QAAQ,CAAC,6BAA6B,CAAC;MACvCc,OAAO,CAACf,KAAK,CAAC,6BAA6B,EAAEsB,GAAG,CAAC;IACnD,CAAC,SAAS;MACRvB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMwB,qBAAqB,GAAG,MAAOC,QAAQ,IAAK;IAChD,IAAI,CAACA,QAAQ,IAAI,CAACA,QAAQ,CAACvC,KAAK,EAAE;MAChC,OAAO,IAAI;IACb;;IAEA;IACA,IAAIH,iBAAiB,CAACI,OAAO,CAACsC,QAAQ,CAACvC,KAAK,CAAC,EAAE;MAC7C8B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD,OAAOlC,iBAAiB,CAACC,IAAI;IAC/B;;IAEA;IACAgC,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IACnD,IAAI;MACF,MAAMS,WAAW,GAAG,IAAIpC,IAAI,CAAC,CAAC,CAACqC,WAAW,CAAC,CAAC;MAC5C,MAAMN,QAAQ,GAAG,MAAM7C,GAAG,CAAC8C,GAAG,CAC5B,kBAAkBG,QAAQ,CAACvC,KAAK,IAAIwC,WAAW,EAAE,EACjD;QACEE,OAAO,EAAE;UACP,eAAe,EAAEH,QAAQ,CAACI,KAAK,GAAG,UAAUJ,QAAQ,CAACI,KAAK,EAAE,GAAGC;QACjE;MACF,CACF,CAAC;MAED,IAAIT,QAAQ,CAACrC,IAAI,IAAIqC,QAAQ,CAACrC,IAAI,CAAC+C,OAAO,EAAE;QAC1C,MAAMC,YAAY,GAAGX,QAAQ,CAACrC,IAAI,CAACA,IAAI;QACvC;QACAD,iBAAiB,CAACS,GAAG,CAACwC,YAAY,EAAEP,QAAQ,CAACvC,KAAK,CAAC;QACnD,OAAO8C,YAAY;MACrB;MACA,OAAO,IAAI;IACb,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI;IACb;EACF,CAAC;;EAED;EACA,MAAMgC,mBAAmB,GAAIC,OAAO,IAAK;IACvC,MAAMC,gBAAgB,GAAG;MACvBC,IAAI,EAAE,WAAW;MACjBC,OAAO,EAAEH;IACX,CAAC;IACDpC,WAAW,CAAEwC,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEH,gBAAgB,CAAC,CAAC;IAClE,OAAOA,gBAAgB;EACzB,CAAC;EAED,MAAMI,WAAW,GAAG,MAAAA,CAAOL,OAAO,EAAEE,IAAI,GAAG,MAAM,KAAK;IACpD;IACA,IAAIrC,OAAO,IAAIqC,IAAI,KAAK,MAAM,EAAE;MAC9BpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD;IACF;IAEA,IAAI;MAAA,IAAAuB,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACFzD,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAIkC,IAAI,KAAK,WAAW,EAAE;QACxB,OAAOH,mBAAmB,CAACC,OAAO,CAAC;MACrC;;MAEA;MACA,MAAM0B,WAAW,GAAG;QAClBxB,IAAI,EAAE,MAAM;QACZC,OAAO,EAAEH,OAAO;QAChB2B,SAAS,EAAE,GAAGvE,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIyE,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACrEhF,SAAS,EAAEK,IAAI,CAACD,GAAG,CAAC;MACtB,CAAC;MACDS,WAAW,CAAEwC,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEsB,WAAW,CAAC,CAAC;;MAE7D;MACA5D,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMyB,QAAQ,GAAGyC,IAAI,CAACC,KAAK,CAACrD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;MAEjE,MAAMM,QAAQ,GAAG,MAAM7C,GAAG,CAAC4F,IAAI,CAAC,OAAO,EAAE;QACvClC,OAAO;QACP/B,cAAc;QACdjB,KAAK,EAAEuC,QAAQ,CAACvC,KAAK;QACrBmF,QAAQ,EAAE5C,QAAQ,CAAC4C,QAAQ;QAAe;QAC1CC,cAAc,EAAE7C,QAAQ,CAAC6C,cAAc;QAAG;QAC1CC,YAAY,EAAE9C,QAAQ,CAAC8C,YAAY,CAAO;MAC5C,CAAC,EAAE;QACD3C,OAAO,EAAE;UACP,UAAU,EAAEH,QAAQ,CAACvC,KAAK;UAAM;UAChC,aAAa,EAAEuC,QAAQ,CAAC4C,QAAQ;UAAE;UAClC,eAAe,EAAE5C,QAAQ,CAACI,KAAK,GAAG,UAAUJ,QAAQ,CAACI,KAAK,EAAE,GAAGC;QACjE;MACF,CAAC,CAAC;;MAGF;MACA,IAAI0C,kBAAkB,GAAGnD,QAAQ,CAACrC,IAAI,CAACyF,UAAU,MAAAjC,qBAAA,GAAInB,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,cAAAlC,qBAAA,uBAAtBA,qBAAA,CAAwBiC,UAAU;;MAEvF;MACA,MAAMH,cAAc,GAAGxD,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;MAC7D,MAAMwD,YAAY,GAAGzD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MAGzD,IAAIyD,kBAAkB,IAAIA,kBAAkB,CAACG,MAAM,IAAIL,cAAc,EAAE;QACrEtD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuD,kBAAkB,CAACG,MAAM,CAACC,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,IAAI,CAAC,CAAC;QAE/EN,kBAAkB,GAAG;UACnB,GAAGA,kBAAkB;UACrBG,MAAM,EAAEH,kBAAkB,CAACG,MAAM,CAACC,GAAG,CAAC,CAACG,KAAK,EAAEC,KAAK,KAAK;YACtDhE,OAAO,CAACC,GAAG,CAAC,qBAAqB+D,KAAK,GAAG,EAAED,KAAK,CAACD,IAAI,CAAC;YACtD,IAAIC,KAAK,CAACD,IAAI,KAAK,WAAW,EAAE;cAC9B9D,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEqD,cAAc,CAAC;cAC1E,OAAO;gBACL,GAAGS,KAAK;gBACRE,YAAY,EAAEX,cAAc;gBAAG;gBAC/BY,KAAK,EAAEZ,cAAc,CAAU;cACjC,CAAC;YACH;YACA,OAAOS,KAAK;UACd,CAAC;QACH,CAAC;QACD/D,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;MACtE,CAAC,MAAM;QAAA,IAAAkE,mBAAA;QACLnE,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;UACjDmE,YAAY,EAAE,CAACZ,kBAAkB;UACjCa,QAAQ,EAAE,GAAAF,mBAAA,GAACX,kBAAkB,cAAAW,mBAAA,eAAlBA,mBAAA,CAAoBR,MAAM;UACrCW,gBAAgB,EAAE,CAAChB,cAAc;UACjCiB,qBAAqB,EAAEC,MAAM,CAACC,IAAI,CAAChE,QAAQ;QAC7C,CAAC,CAAC;MACJ;;MAEA;MACA,MAAMiE,WAAW,GAAGrE,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,KACxC,EAAAjC,sBAAA,GAAApB,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,CAACI,IAAI,cAAArC,sBAAA,uBAA3BA,sBAAA,CAA6BkD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAAlD,qBAAA,GAC5DrB,QAAQ,CAACrC,IAAI,CAACyF,UAAU,cAAA/B,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0BoC,IAAI,cAAAnC,sBAAA,uBAA9BA,sBAAA,CAAgCgD,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAChE;MAED,IAAIC,YAAY,GAAGxE,QAAQ,CAACrC,IAAI,CAACkD,OAAO;MACxC,IAAI4D,gBAAgB,GAAGzE,QAAQ,CAACrC,IAAI,CAAC+G,WAAW;MAChD,IAAIC,kBAAkB,GAAG,IAAI;;MAE7B;MACA,IAAIN,WAAW,EAAE;QAAA,IAAAO,qBAAA;QACf;QACA,KAAAA,qBAAA,GAAI5E,QAAQ,CAACrC,IAAI,CAACkH,WAAW,cAAAD,qBAAA,eAAzBA,qBAAA,CAA2BjE,YAAY,EAAE;UAC3ChB,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;UAC1D+E,kBAAkB,GAAG3E,QAAQ,CAACrC,IAAI,CAACkH,WAAW,CAAClE,YAAY;QAC7D,CAAC,MAAM;UACL;UACAhB,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;UACzE,MAAMkF,aAAa,GAAG,MAAM3E,qBAAqB,CAACC,QAAQ,CAAC;UAC3D,IAAI0E,aAAa,EAAE;YACjB;YACA,IAAIC,gBAAgB,GAAG,EAAE;YACzB,IAAIC,KAAK,CAACC,OAAO,CAACH,aAAa,CAAC,EAAE;cAChCA,aAAa,CAACI,OAAO,CAACC,KAAK,IAAI;gBAC7B,MAAMC,SAAS,GAAGD,KAAK,CAACC,SAAS,IAAID,KAAK,CAACE,IAAI,IAAI,OAAO;gBAC1D,MAAMC,OAAO,GAAGH,KAAK,CAACG,OAAO,IAAIH,KAAK,CAACI,eAAe,IAAIJ,KAAK,CAACK,eAAe,IAAI,CAAC;gBACpFT,gBAAgB,IAAI,KAAKK,SAAS,KAAKE,OAAO,mBAAmB;cACnE,CAAC,CAAC;YACJ;YACA,IAAIP,gBAAgB,EAAE;cACpBJ,kBAAkB,GAAGI,gBAAgB,CAACU,IAAI,CAAC,CAAC;cAC5C9F,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;YAC9C;UACF;QACF;;QAEA;QACA,IAAI+E,kBAAkB,EAAE;UACtBH,YAAY,GAAG,8DAA8DG,kBAAkB,EAAE;UACjGF,gBAAgB,GAAG,0BAA0B;QAC/C;MACF;;MAEA;MACA,MAAM3D,gBAAgB,GAAG;QACvBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEwD,YAAY;QACrBnB,QAAQ,EAAEF,kBAAkB,GAC1B;UAAE,GAAGnD,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ;UAAED,UAAU,EAAED;QAAmB,CAAC,GAC7DnD,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ;QAAE;QAC1BD,UAAU,EAAED,kBAAkB,IAAInD,QAAQ,CAACrC,IAAI,CAACyF,UAAU;QAC1DsB,WAAW,EAAED,gBAAgB;QAC7BI,WAAW,EAAE7E,QAAQ,CAACrC,IAAI,CAACkH,WAAW;QACtCa,YAAY,EAAE1F,QAAQ,CAACrC,IAAI,CAAC+H,YAAY;QACxCxG,kBAAkB,EAAEc,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB;QACpD;QACAyG,SAAS,EAAE,EAAApE,qBAAA,GAAAvB,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAAqC,qBAAA,uBAAhCA,qBAAA,CAAkCoE,SAAS,OAAAnE,qBAAA,GAAIxB,QAAQ,CAACrC,IAAI,CAACyB,UAAU,cAAAoC,qBAAA,uBAAxBA,qBAAA,CAA0BmE,SAAS,KAAI3F,QAAQ,CAACrC,IAAI,CAACgI,SAAS;QACxHC,OAAO,EAAE,EAAAnE,sBAAA,GAAAzB,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAAuC,sBAAA,uBAAhCA,sBAAA,CAAkCmE,OAAO,OAAAlE,sBAAA,GAAI1B,QAAQ,CAACrC,IAAI,CAACyB,UAAU,cAAAsC,sBAAA,uBAAxBA,sBAAA,CAA0BkE,OAAO,KAAI5F,QAAQ,CAACrC,IAAI,CAACiI,OAAO;QAChHC,UAAU,EAAE,EAAAlE,sBAAA,GAAA3B,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAAyC,sBAAA,uBAAhCA,sBAAA,CAAkCmE,QAAQ,KAAI9F,QAAQ,CAACrC,IAAI,CAACkI,UAAU;QAClFE,SAAS,EAAE,EAAAnE,sBAAA,GAAA5B,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAA0C,sBAAA,uBAAhCA,sBAAA,CAAkCmE,SAAS,KAAI/F,QAAQ,CAACrC,IAAI,CAACoI,SAAS;QACjFC,WAAW,EAAE,EAAAnE,sBAAA,GAAA7B,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAA2C,sBAAA,uBAAhCA,sBAAA,CAAkCmE,WAAW,KAAIhG,QAAQ,CAACrC,IAAI,CAACqI,WAAW;QACvFC,UAAU,EAAE,EAAAnE,sBAAA,GAAA9B,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAA4C,sBAAA,uBAAhCA,sBAAA,CAAkCmE,UAAU,KAAIjG,QAAQ,CAACrC,IAAI,CAACsI,UAAU;QACpFC,iBAAiB,EAAE,IAAI;QACvB;QACAvF,YAAY,EAAE,EAAAoB,sBAAA,GAAA/B,QAAQ,CAACrC,IAAI,CAACkH,WAAW,cAAA9C,sBAAA,uBAAzBA,sBAAA,CAA2BpB,YAAY,KAAIgE,kBAAkB;QAC3EoB,SAAS,EAAE,EAAA/D,sBAAA,GAAAhC,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAA8C,sBAAA,uBAAhCA,sBAAA,CAAkC+D,SAAS,OAAA9D,sBAAA,GAAIjC,QAAQ,CAACrC,IAAI,CAACyB,UAAU,cAAA6C,sBAAA,uBAAxBA,sBAAA,CAA0B8D,SAAS,KAAI/F,QAAQ,CAACrC,IAAI,CAACoI,SAAS;QACxHC,WAAW,EAAE,EAAA9D,sBAAA,GAAAlC,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAAgD,sBAAA,uBAAhCA,sBAAA,CAAkC8D,WAAW,OAAA7D,sBAAA,GAAInC,QAAQ,CAACrC,IAAI,CAACyB,UAAU,cAAA+C,sBAAA,uBAAxBA,sBAAA,CAA0B6D,WAAW,KAAIhG,QAAQ,CAACrC,IAAI,CAACqI,WAAW;QAChIC,UAAU,EAAE,EAAA7D,sBAAA,GAAApC,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,cAAAkD,sBAAA,uBAAhCA,sBAAA,CAAkC6D,UAAU,OAAA5D,sBAAA,GAAIrC,QAAQ,CAACrC,IAAI,CAACyB,UAAU,cAAAiD,sBAAA,uBAAxBA,sBAAA,CAA0B8D,wBAAwB,KAAInG,QAAQ,CAACrC,IAAI,CAACsI,UAAU;QAC1I;QACA7G,UAAU,EAAEY,QAAQ,CAACrC,IAAI,CAACyB,UAAU;QACpCgH,YAAY,EAAEpG,QAAQ,CAACrC,IAAI,CAACyI,YAAY;QACxCC,qBAAqB,EAAErG,QAAQ,CAACrC,IAAI,CAAC0I,qBAAqB;QAC1DC,gBAAgB,EAAEtG,QAAQ,CAACrC,IAAI,CAAC2I,gBAAgB;QAChDC,UAAU,EAAEvG,QAAQ,CAACrC,IAAI,CAAC4I,UAAU;QACpCC,aAAa,EAAExG,QAAQ,CAACrC,IAAI,CAAC6I,aAAa;QAC1CC,kBAAkB,EAAEzG,QAAQ,CAACrC,IAAI,CAAC8I,kBAAkB;QACpDP,iBAAiB,EAAE,IAAI;QACvB;QACA1D,SAAS,EAAE,GAAGvE,IAAI,CAACD,GAAG,CAAC,CAAC,IAAIyE,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QACrE;QACAhF,SAAS,EAAE,EAAA0E,sBAAA,GAAAtC,QAAQ,CAACrC,IAAI,CAACkH,WAAW,cAAAvC,sBAAA,uBAAzBA,sBAAA,CAA2B1E,SAAS,KAAIK,IAAI,CAACD,GAAG,CAAC,CAAC;QAC7D;QACA0I,aAAa,EAAE1G,QAAQ,CAACrC,IAAI,CAAC+I;MAC/B,CAAC;;MAED;MACA,IAAI1G,QAAQ,CAACrC,IAAI,CAACmB,cAAc,IAAI,CAACA,cAAc,EAAE;QACnDC,iBAAiB,CAACiB,QAAQ,CAACrC,IAAI,CAACmB,cAAc,CAAC;MACjD;;MAEA;MACA,IAAIkB,QAAQ,CAACrC,IAAI,CAAC+I,aAAa,EAAE;QAC/B/G,OAAO,CAACC,GAAG,CAAC,iEAAiE,CAAC;QAC9EX,aAAa,CAAC,IAAI,CAAC;QACnBE,qBAAqB,CAAC,IAAI,CAAC;QAC3BE,aAAa,CAAC,IAAI,CAAC;MACrB;;MAEA;MACA,IAAIW,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,IAAIc,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,mBAAmB,EAAE;QACzFvF,qBAAqB,CAACa,QAAQ,CAACrC,IAAI,CAACuB,kBAAkB,CAAC;QACvDD,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;MACvB,CAAC,MAAM,IAAIe,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,gBAAgB,EAAE;QAEzDvF,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;QACA,IAAIe,QAAQ,CAACrC,IAAI,CAACgJ,eAAe,IAAI3G,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,IAAIrD,QAAQ,CAACrC,IAAI,CAACiJ,MAAM,EAAE;UAEnF,MAAMC,gBAAgB,GAAGC,8BAA8B,CAAC9G,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,CAAC;;UAE/E;UACA,MAAM0D,wBAAwB,CAAC/G,QAAQ,CAACrC,IAAI,CAACiJ,MAAM,EAAEC,gBAAgB,EAAE7G,QAAQ,CAACrC,IAAI,CAACmB,cAAc,EAAEN,QAAQ,EAAEwB,QAAQ,CAACrC,IAAI,CAACyF,UAAU,CAAC;;UAExI;UACA,OAAOpD,QAAQ,CAACrC,IAAI;QACtB;MACF,CAAC,MAAM,IAAIqC,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,qBAAqB,EAAE;QAE9DvF,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;MACF,CAAC,MAAM,IAAIe,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,YAAY,EAAE;QACrD/E,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;QACxCD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,QAAQ,CAACrC,IAAI,CAACkH,WAAW,CAAC;QAE3D1F,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;MACF,CAAC,MAAM,IAAIe,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,gBAAgB,EAAE;QACzD/E,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9CT,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;QACnBI,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIW,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,0BAA0B,EAAE;QACnE/E,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAEI,QAAQ,CAACrC,IAAI,CAACyB,UAAU,CAAC;QACpFC,aAAa,CAACW,QAAQ,CAACrC,IAAI,CAACyB,UAAU,CAAC;QACvCD,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIe,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,wBAAwB,EAAE;QAEjE;QACArF,aAAa,CAAC;UACZ,GAAGW,QAAQ,CAACrC,IAAI,CAACyB,UAAU;UAC3BiH,qBAAqB,EAAE,KAAK;UAC5BC,gBAAgB,EAAE,IAAI;UACtBC,UAAU,EAAEvG,QAAQ,CAACrC,IAAI,CAAC4I,UAAU;UACpCC,aAAa,EAAExG,QAAQ,CAACrC,IAAI,CAAC6I,aAAa;UAC1CpD,UAAU,EAAEpD,QAAQ,CAACrC,IAAI,CAACyF,UAAU;UACpCwD,MAAM,EAAE5G,QAAQ,CAACrC,IAAI,CAACiJ;QACxB,CAAC,CAAC;QACFzH,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM,IAAIe,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,uBAAuB,EAAE;QAEhErF,aAAa,CAAC,IAAI,CAAC;QACnBF,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;QACA,IAAIe,QAAQ,CAACrC,IAAI,CAACgJ,eAAe,IAAI3G,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,IAAIrD,QAAQ,CAACrC,IAAI,CAACiJ,MAAM,EAAE;UACnF,MAAMC,gBAAgB,GAAGC,8BAA8B,CAAC9G,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,CAAC;UAC/E1D,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEiH,gBAAgB,CAAC;;UAE7E;UACA,MAAMG,gBAAgB,CAAChH,QAAQ,CAACrC,IAAI,CAACiJ,MAAM,EAAEC,gBAAgB,EAAE7G,QAAQ,CAACrC,IAAI,CAACmB,cAAc,EAAEN,QAAQ,EAAEwB,QAAQ,CAACrC,IAAI,CAACyF,UAAU,CAAC;UAEhI,OAAOpD,QAAQ,CAACrC,IAAI;QACtB;MACF,CAAC,MAAM,IAAIqC,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,4BAA4B,EAAE;QACrE/E,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpED,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEI,QAAQ,CAACrC,IAAI,CAACkH,WAAW,CAAC;QAE1DxF,aAAa,CAAC,IAAI,CAAC;QACnBF,qBAAqB,CAAC,IAAI,CAAC;QAC3BF,aAAa,CAAC,IAAI,CAAC;;QAEnB;MACF,CAAC,MAAM,IAAIe,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,IAAIrD,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,MAAM,EAAE;QACzE;QACAzF,aAAa,CAACe,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,CAAC;QACrClE,qBAAqB,CAAC,IAAI,CAAC;QAC3BQ,OAAO,CAACC,GAAG,CAAC,gCAAgCI,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,CAACI,IAAI,EAAE,CAAC;;QAE1E;QACA9D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEI,QAAQ,CAACrC,IAAI,CAAC0F,QAAQ,CAAC;MACnD,CAAC,MAAM;QACL;QACApE,aAAa,CAAC,IAAI,CAAC;QACnBE,qBAAqB,CAAC,IAAI,CAAC;QAC3BQ,OAAO,CAACC,GAAG,CAAC,oCAAoCI,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,EAAE,CAAC;;QAE5E;QACA,IAAI1E,QAAQ,CAACrC,IAAI,CAAC+G,WAAW,KAAK,aAAa,EAAE;UAC/C/E,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;QAC9D;MACF;;MAEA;MACA,MAAM,IAAIqH,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAME,gBAAgB,GAAG,GAAGtG,gBAAgB,CAAClD,SAAS,IAAIkD,gBAAgB,CAAC4D,WAAW,IAAI5D,gBAAgB,CAAC0B,SAAS,EAAE;;MAEtH;MACA,IAAIjF,uBAAuB,CAAC8J,GAAG,CAACD,gBAAgB,CAAC,EAAE;QACjDzH,OAAO,CAACC,GAAG,CAAC,kDAAkD,EAAE;UAC9DwH,gBAAgB,EAAEA,gBAAgB,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;UAC3D1J,SAAS,EAAEkD,gBAAgB,CAAClD,SAAS;UACrC8G,WAAW,EAAE5D,gBAAgB,CAAC4D;QAChC,CAAC,CAAC;QACF/F,UAAU,CAAC,KAAK,CAAC;QACjB,OAAOqB,QAAQ,CAACrC,IAAI;MACtB;;MAEA;MACAJ,uBAAuB,CAACgK,GAAG,CAACH,gBAAgB,CAAC;;MAE7C;MACA,IAAI7J,uBAAuB,CAACiK,IAAI,GAAG,GAAG,EAAE;QACtC,MAAMC,YAAY,GAAGzC,KAAK,CAAC0C,IAAI,CAACnK,uBAAuB,CAAC;QACxDA,uBAAuB,CAACa,KAAK,CAAC,CAAC;QAC/BqJ,YAAY,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAACzC,OAAO,CAAC0C,GAAG,IAAIrK,uBAAuB,CAACgK,GAAG,CAACK,GAAG,CAAC,CAAC;MAC1E;;MAEA;MACAnJ,WAAW,CAAEwC,YAAY,IAAK;QAC5B;QACA,MAAM4G,WAAW,GAAG5G,YAAY,CAAC6G,IAAI,CAACC,GAAG;UAAA,IAAAC,gBAAA;UAAA,OACvCD,GAAG,CAAChH,IAAI,KAAK,WAAW;UACtB;UACC,EAAAiH,gBAAA,GAAAD,GAAG,CAAClD,WAAW,cAAAmD,gBAAA,uBAAfA,gBAAA,CAAiBpK,SAAS,MAAKkD,gBAAgB,CAAClD,SAAS,IACzDmK,GAAG,CAACrD,WAAW,KAAK5D,gBAAgB,CAAC4D,WAAW;UACjD;UACCqD,GAAG,CAACvF,SAAS,KAAK1B,gBAAgB,CAAC0B,SAAU;UAC9C;UACCuF,GAAG,CAAC/G,OAAO,KAAKF,gBAAgB,CAACE,OAAO,IACxCyB,IAAI,CAACwF,GAAG,CAAC,CAACF,GAAG,CAACnK,SAAS,IAAI,CAAC,KAAKkD,gBAAgB,CAAClD,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK;UAC3E;UACCmK,GAAG,CAAC/G,OAAO,KAAKF,gBAAgB,CAACE,OAAO,IACxC6B,IAAI,CAACqF,SAAS,CAACH,GAAG,CAACnC,OAAO,CAAC,KAAK/C,IAAI,CAACqF,SAAS,CAACpH,gBAAgB,CAAC8E,OAAO,CAAC,IACxEmC,GAAG,CAAC3B,YAAY,KAAKtF,gBAAgB,CAACsF,YAAY,IAClD2B,GAAG,CAAC1B,qBAAqB,KAAKvF,gBAAgB,CAACuF,qBAAqB,IACpE5D,IAAI,CAACwF,GAAG,CAAC,CAACF,GAAG,CAACnK,SAAS,IAAI,CAAC,KAAKkD,gBAAgB,CAAClD,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK;UAC3E;UACCmK,GAAG,CAACI,mBAAmB,IAAIrH,gBAAgB,CAAC4D,WAAW,KAAK,0BAA0B,IACtFjC,IAAI,CAACwF,GAAG,CAAC,CAACF,GAAG,CAACnK,SAAS,IAAI,CAAC,KAAKkD,gBAAgB,CAAClD,SAAS,IAAI,CAAC,CAAC,CAAC,GAAG,IAAK,CAC5E;QAAA,CACH,CAAC;QAED,IAAIiK,WAAW,EAAE;UACflI,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE;YAC/DhC,SAAS,EAAEkD,gBAAgB,CAAClD,SAAS;YACrC8G,WAAW,EAAE5D,gBAAgB,CAAC4D,WAAW;YACzClC,SAAS,EAAE1B,gBAAgB,CAAC0B;UAC9B,CAAC,CAAC;UACF,OAAOvB,YAAY,CAAC,CAAC;QACvB;QAEAtB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;UAC1ChC,SAAS,EAAEkD,gBAAgB,CAAClD,SAAS;UACrC8G,WAAW,EAAE5D,gBAAgB,CAAC4D,WAAW;UACzClC,SAAS,EAAE1B,gBAAgB,CAAC0B,SAAS;UACrC4F,aAAa,EAAEnH,YAAY,CAACoH,MAAM,GAAG;QACvC,CAAC,CAAC;QACF,OAAO,CAAC,GAAGpH,YAAY,EAAEH,gBAAgB,CAAC;MAC5C,CAAC,CAAC;MAEF,OAAOd,QAAQ,CAACrC,IAAI;IACtB,CAAC,CAAC,OAAOuC,GAAG,EAAE;MACZrB,QAAQ,CAAC,wBAAwB,CAAC;MAClCc,OAAO,CAACf,KAAK,CAAC,wBAAwB,EAAEsB,GAAG,CAAC;;MAE5C;MACA,MAAMoI,YAAY,GAAG;QACnBvH,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC;MACDvC,WAAW,CAAEwC,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEqH,YAAY,CAAC,CAAC;MAE9D,OAAO,IAAI;IACb,CAAC,SAAS;MACR;MACA3J,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4J,kCAAkC,GAAGA,CAAClF,QAAQ,EAAED,UAAU,KAAK;IACnE,IAAI;MAAA,IAAAoF,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF,IAAI,CAACtF,UAAU,EAAE;QACfzD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,OAAOyD,QAAQ;MACjB;MAEA,IAAI,EAACD,UAAU,aAAVA,UAAU,gBAAAoF,qBAAA,GAAVpF,UAAU,CAAEA,UAAU,cAAAoF,qBAAA,gBAAAC,sBAAA,GAAtBD,qBAAA,CAAwBG,eAAe,cAAAF,sBAAA,gBAAAC,sBAAA,GAAvCD,sBAAA,CAAyCG,aAAa,cAAAF,sBAAA,eAAtDA,sBAAA,CAAwDG,OAAO,GAAE;QACpElJ,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,OAAOyD,QAAQ,CAAC,CAAC;MACnB;MAEA,MAAMyF,eAAe,GAAG1F,UAAU,CAACA,UAAU,CAACuF,eAAe,CAACC,aAAa,CAACG,SAAS,IAAI,CAAC,CAAC;MAC3F,MAAMC,aAAa,GAAG5F,UAAU,CAACA,UAAU,CAACuF,eAAe,CAACC,aAAa,CAACI,aAAa,IAAI,SAAS;MAEpG,MAAMC,YAAY,GAAIpF,KAAK,IAAK;QAC9B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;UAC7B;UACA,MAAMqF,sBAAsB,GAAGrF,KAAK,CAACsF,KAAK,CAAC,mBAAmB,CAAC;UAC/D,IAAID,sBAAsB,EAAE;YAC1B,MAAMnD,SAAS,GAAGmD,sBAAsB,CAAC,CAAC,CAAC;YAC3C,IAAI7F,QAAQ,CAAC+F,cAAc,CAACrD,SAAS,CAAC,EAAE;cACtC,OAAO1C,QAAQ,CAAC0C,SAAS,CAAC,CAAC,CAAC;YAC9B;UACF;;UAEA;UACA,IAAIsD,MAAM,GAAGxF,KAAK;UAClBM,MAAM,CAACC,IAAI,CAACf,QAAQ,CAAC,CAAC6B,OAAO,CAAC0C,GAAG,IAAI;YACnC,MAAM0B,WAAW,GAAG,UAAU1B,GAAG,IAAI;YACrC,MAAM2B,UAAU,GAAGlG,QAAQ,CAACuE,GAAG,CAAC;YAChC,IAAI2B,UAAU,KAAK9I,SAAS,EAAE;cAC5B,MAAM+I,gBAAgB,GAAG,OAAOD,UAAU,KAAK,QAAQ,GAAG1G,IAAI,CAACqF,SAAS,CAACqB,UAAU,CAAC,GAAGA,UAAU;cACjGF,MAAM,GAAGA,MAAM,CAACI,OAAO,CAAC,IAAIC,MAAM,CAACJ,WAAW,CAACG,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,EAAED,gBAAgB,CAAC;YAChH;UACF,CAAC,CAAC;UACF,OAAOH,MAAM;QACf,CAAC,MAAM,IAAIrE,KAAK,CAACC,OAAO,CAACpB,KAAK,CAAC,EAAE;UAC/B,OAAOA,KAAK,CAACN,GAAG,CAAC0F,YAAY,CAAC;QAChC,CAAC,MAAM,IAAI,OAAOpF,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;UACtD,MAAM8F,SAAS,GAAG,CAAC,CAAC;UACpBxF,MAAM,CAACC,IAAI,CAACP,KAAK,CAAC,CAACqB,OAAO,CAAC0C,GAAG,IAAI;YAChC+B,SAAS,CAAC/B,GAAG,CAAC,GAAGqB,YAAY,CAACpF,KAAK,CAAC+D,GAAG,CAAC,CAAC;UAC3C,CAAC,CAAC;UACF,OAAO+B,SAAS;QAClB;QACA,OAAO9F,KAAK;MACd,CAAC;MAED,IAAI+F,aAAa,GAAG,CAAC,CAAC;MAEtB,QAAQZ,aAAa;QACnB,KAAK,SAAS;UACZY,aAAa,GAAGX,YAAY,CAACH,eAAe,CAAC;UAC7C;QACF,KAAK,OAAO;UACVc,aAAa,GAAG;YAAE,GAAGvG,QAAQ;YAAE,GAAG4F,YAAY,CAACH,eAAe;UAAE,CAAC;UACjE;QACF,KAAK,QAAQ;UACXc,aAAa,GAAG;YAAE,GAAGvG,QAAQ;YAAEwG,MAAM,EAAEZ,YAAY,CAACH,eAAe;UAAE,CAAC;UACtE;QACF;UACEc,aAAa,GAAGX,YAAY,CAACH,eAAe,CAAC;MACjD;MAEAnJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEgK,aAAa,CAAC;MACpD,OAAOA,aAAa;IACtB,CAAC,CAAC,OAAOhL,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;MAC3E,OAAOyE,QAAQ,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyD,8BAA8B,GAAIzD,QAAQ,IAAK;IACnD1D,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;;IAE1D;IACA,MAAMQ,QAAQ,GAAGyC,IAAI,CAACC,KAAK,CAACrD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;IACjE,MAAMuD,cAAc,GAAGxD,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC7D,MAAMwD,YAAY,GAAGzD,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;IAEzDC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1C/B,KAAK,EAAEuC,QAAQ,CAACvC,KAAK;MACrBmF,QAAQ,EAAE5C,QAAQ,CAAC4C,QAAQ;MAC3BC,cAAc,EAAEA,cAAc;MAC9BC,YAAY,EAAEA,YAAY;MAC1B4G,gBAAgB,EAAEzG;IACpB,CAAC,CAAC;;IAEF;IACA,MAAMwD,gBAAgB,GAAG;MACvB,GAAGxD,QAAQ;MACX;MACAxF,KAAK,EAAGwF,QAAQ,CAACxF,KAAK,IAAIwF,QAAQ,CAACxF,KAAK,CAAC4H,IAAI,CAAC,CAAC,KAAK,EAAE,GAAIpC,QAAQ,CAACxF,KAAK,GAAIuC,QAAQ,CAACvC,KAAK,IAAI,EAAG;MACjG;MACAwH,IAAI,EAAGhC,QAAQ,CAACgC,IAAI,IAAIhC,QAAQ,CAACgC,IAAI,CAACI,IAAI,CAAC,CAAC,KAAK,EAAE,GAAIpC,QAAQ,CAACgC,IAAI,GAAIjF,QAAQ,CAAC4C,QAAQ,IAAI,EAAG;MAChG;MACA+G,SAAS,EAAG1G,QAAQ,CAAC0G,SAAS,IAAI1G,QAAQ,CAAC0G,SAAS,CAACtE,IAAI,CAAC,CAAC,KAAK,EAAE,GAAIpC,QAAQ,CAAC0G,SAAS,GAAI9G,cAAc,IAAI;IAChH,CAAC;IAEDtD,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE;MACrDoK,QAAQ,EAAE3G,QAAQ;MAClB4G,QAAQ,EAAEpD,gBAAgB;MAC1BqD,gBAAgB,EAAE;QAChBrM,KAAK,EAAEgJ,gBAAgB,CAAChJ,KAAK,KAAKwF,QAAQ,CAACxF,KAAK;QAChDwH,IAAI,EAAEwB,gBAAgB,CAACxB,IAAI,KAAKhC,QAAQ,CAACgC,IAAI;QAC7C0E,SAAS,EAAElD,gBAAgB,CAACkD,SAAS,KAAK1G,QAAQ,CAAC0G;MACrD;IACF,CAAC,CAAC;IAEF,OAAOlD,gBAAgB;EACzB,CAAC;;EAED;EACA,MAAME,wBAAwB,GAAG,MAAAA,CAAOH,MAAM,EAAEvD,QAAQ,EAAEvE,cAAc,EAAEqL,eAAe,EAAE/G,UAAU,GAAG,IAAI,KAAK;IAC/G,IAAI;MAAA,IAAAgH,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF9K,OAAO,CAACC,GAAG,CAAC,+DAA+D,CAAC;;MAE5E;MACA,MAAMQ,QAAQ,GAAGyC,IAAI,CAACC,KAAK,CAACrD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;;MAE/D;MACA,IAAIgL,SAAS,GAAGtK,QAAQ,CAACI,KAAK;;MAE9B;MACA,MAAMmK,iBAAiB,GAAGvH,UAAU,IAAIpE,UAAU;;MAElD;MACA,IAAI2L,iBAAiB,IAAI,EAAAP,qBAAA,GAAAO,iBAAiB,CAACvH,UAAU,cAAAgH,qBAAA,wBAAAC,sBAAA,GAA5BD,qBAAA,CAA8BzB,eAAe,cAAA0B,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CO,UAAU,cAAAN,sBAAA,uBAAzDA,sBAAA,CAA2DO,cAAc,MAAK,IAAI,EAAE;QAC3GH,SAAS,GAAGC,iBAAiB,CAACvH,UAAU,CAACuF,eAAe,CAACiC,UAAU,CAACpK,KAAK;QACzEb,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACzD;MAEF,MAAMkL,iBAAiB,GAAGvC,kCAAkC,CAAClF,QAAQ,EAAEsH,iBAAiB,CAAC;MAEzF,MAAM3K,QAAQ,GAAG,MAAM7C,GAAG,CAAC4F,IAAI,CAAC,mBAAmB,EAAE;QACnD6D,MAAM,EAAEA,MAAM;QACdvD,QAAQ,EAAEyH,iBAAiB;QAC3BhM,cAAc,EAAEA;MAClB,CAAC,EAAE;QACDyB,OAAO,EAAE;UACP,UAAU,EAAEH,QAAQ,CAACvC,KAAK;UAC1B,aAAa,EAAEuC,QAAQ,CAAC4C,QAAQ;UAChC,eAAe,EAAE0H,SAAS,GAAG,UAAUA,SAAS,EAAE,GAAGjK;QACvD;MACF,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEI,QAAQ,CAACrC,IAAI,CAAC;;MAElE;MACA,IAAIoN,aAAa;;MAEjB;MACA,IAAI/K,QAAQ,CAACrC,IAAI,CAAC+C,OAAO,EAAE;QAAA,IAAAsK,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;QACzBvL,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;;QAE3D;QACA,MAAMyE,WAAW,GAAGsG,iBAAiB,KACnC,EAAAK,qBAAA,GAAAL,iBAAiB,CAAClH,IAAI,cAAAuH,qBAAA,uBAAtBA,qBAAA,CAAwB1G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAA0G,sBAAA,GACvDN,iBAAiB,CAACvH,UAAU,cAAA6H,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BxH,IAAI,cAAAyH,sBAAA,uBAAlCA,sBAAA,CAAoC5G,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EACpE;QAED,IAAIF,WAAW,EAAE;UACf0G,aAAa,GAAG,oEAAoE;QACtF,CAAC,MAAM;UACLA,aAAa,GAAG,uDAAuD;QACzE;MACF,CAAC,MAAM;QACLpL,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAEI,QAAQ,CAACrC,IAAI,CAACkD,OAAO,CAAC;QAC9EkK,aAAa,GAAG/K,QAAQ,CAACrC,IAAI,CAACkD,OAAO,IAAI,0CAA0C;MACrF;;MAEA;MACA,MAAMwD,WAAW,GAAGsG,iBAAiB,KACnC,EAAAJ,sBAAA,GAAAI,iBAAiB,CAAClH,IAAI,cAAA8G,sBAAA,uBAAtBA,sBAAA,CAAwBjG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAAiG,sBAAA,GACvDG,iBAAiB,CAACvH,UAAU,cAAAoH,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8B/G,IAAI,cAAAgH,sBAAA,uBAAlCA,sBAAA,CAAoCnG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EACpE;MAED9F,WAAW,CAAEwC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE+J,aAAa;QACtBrG,WAAW,EAAEL,WAAW,GAAG,sBAAsB,GAAG,gBAAgB;QACpEQ,WAAW,EAAE;UACP,SAAS,EAAE,IAAI;UACjB;UACE,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,eAAe;UAC7B,MAAM,EAAE,IAAI;UACZ,SAAS,EAAE;QACX,CAAC,CAAE;MACX,CAAC,CACF,CAAC;MAEF,OAAO7E,QAAQ,CAACrC,IAAI;IACtB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAAC;;MAEvE;MACA,IAAIuM,mBAAmB,GAAG,mEAAmE;;MAE7F;MACA,IAAIvM,KAAK,CAACoB,QAAQ,IAAIpB,KAAK,CAACoB,QAAQ,CAACrC,IAAI,IAAIiB,KAAK,CAACoB,QAAQ,CAACrC,IAAI,CAACkD,OAAO,EAAE;QACxEsK,mBAAmB,GAAGvM,KAAK,CAACoB,QAAQ,CAACrC,IAAI,CAACkD,OAAO;MACnD;;MAEA;MACApC,WAAW,CAAEwC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEmK,mBAAmB;QAC5BzG,WAAW,EAAE,gBAAgB;QAC7BG,WAAW,EAAE;UAAEnE,OAAO,EAAE,KAAK;UAAEG,OAAO,EAAEsK;QAAoB,CAAC,CAAC;MAChE,CAAC,CACF,CAAC;MAEF,MAAMvM,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMoI,gBAAgB,GAAG,MAAAA,CAAOJ,MAAM,EAAEvD,QAAQ,EAAEvE,cAAc,EAAEqL,eAAe,EAAE/G,UAAU,GAAG,IAAI,KAAK;IACvG,IAAI;MAAA,IAAAgI,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;MACF3L,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;;MAEpE;MACA,MAAMQ,QAAQ,GAAGyC,IAAI,CAACC,KAAK,CAACrD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;;MAEjE;MACA,IAAIgL,SAAS,GAAGtK,QAAQ,CAACI,KAAK;;MAE9B;MACA,MAAMmK,iBAAiB,GAAGvH,UAAU,IAAIpE,UAAU;;MAElD;MACA,IAAI2L,iBAAiB,IAAI,EAAAS,sBAAA,GAAAT,iBAAiB,CAACvH,UAAU,cAAAgI,sBAAA,wBAAAC,sBAAA,GAA5BD,sBAAA,CAA8BzC,eAAe,cAAA0C,sBAAA,wBAAAC,sBAAA,GAA7CD,sBAAA,CAA+CT,UAAU,cAAAU,sBAAA,uBAAzDA,sBAAA,CAA2DT,cAAc,MAAK,IAAI,EAAE;QAC3GH,SAAS,GAAGC,iBAAiB,CAACvH,UAAU,CAACuF,eAAe,CAACiC,UAAU,CAACpK,KAAK;QACzEb,OAAO,CAACC,GAAG,CAAC,8DAA8D,CAAC;MAC7E,CAAC,MAAM;QACLD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;MACzD;MAEA,MAAMkL,iBAAiB,GAAGvC,kCAAkC,CAAClF,QAAQ,EAAEsH,iBAAiB,CAAC;MAEzF,MAAM3K,QAAQ,GAAG,MAAM7C,GAAG,CAAC4F,IAAI,CAAC,mBAAmB,EAAE;QACnD6D,MAAM,EAAEA,MAAM;QACdvD,QAAQ,EAAEyH,iBAAiB;QAC3BhM,cAAc,EAAEA,cAAc;QAC9ByM,YAAY,EAAE;MAChB,CAAC,EAAE;QACDhL,OAAO,EAAE;UACP,UAAU,EAAEH,QAAQ,CAACvC,KAAK;UAC1B,aAAa,EAAEuC,QAAQ,CAAC4C,QAAQ;UAChC,eAAe,EAAE0H,SAAS,GAAG,UAAUA,SAAS,EAAE,GAAGjK;QACvD;MACF,CAAC,CAAC;MAEFd,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEI,QAAQ,CAACrC,IAAI,CAAC;;MAE1D;MACA,IAAIoN,aAAa;;MAEjB;MACA,IAAI/K,QAAQ,CAACrC,IAAI,CAAC+C,OAAO,EAAE;QACzBf,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnDmL,aAAa,GAAG,8DAA8D;MAChF,CAAC,MAAM;QACLpL,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEI,QAAQ,CAACrC,IAAI,CAACkD,OAAO,CAAC;QACtE;QACEkK,aAAa,GAAG,8BAA8B;MAElD;;MAEA;MACAtM,WAAW,CAAEwC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE+J,aAAa;QACtBrG,WAAW,EAAE,uBAAuB;QACpCG,WAAW,EAAE;UACX,SAAS,EAAE,IAAI;UACf,QAAQ,EAAE,CAAC;UACX,YAAY,EAAE,eAAe;UAC7B,MAAM,EAAE,IAAI;UACZ,SAAS,EAAE;QACb;MACF,CAAC,CACF,CAAC;MAEF,OAAO7E,QAAQ,CAACrC,IAAI;IACtB,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;;MAE/D;MACA,IAAIuM,mBAAmB,GAAG,0EAA0E;;MAEpG;MACA,IAAIvM,KAAK,CAACoB,QAAQ,IAAIpB,KAAK,CAACoB,QAAQ,CAACrC,IAAI,IAAIiB,KAAK,CAACoB,QAAQ,CAACrC,IAAI,CAACkD,OAAO,EAAE;QACxEsK,mBAAmB,GAAGvM,KAAK,CAACoB,QAAQ,CAACrC,IAAI,CAACkD,OAAO;MACnD;;MAEA;MACApC,WAAW,CAAEwC,YAAY,IAAK,CAC5B,GAAGA,YAAY,EACf;QACEF,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEmK,mBAAmB;QAC5BzG,WAAW,EAAE,uBAAuB;QACpCG,WAAW,EAAE;UAAEnE,OAAO,EAAE,KAAK;UAAEG,OAAO,EAAEsK;QAAoB;MAC9D,CAAC,CACF,CAAC;MAEF,MAAMvM,KAAK;IACb;EACF,CAAC;EAED,MAAM4M,UAAU,GAAG,MAAAA,CAAO5E,MAAM,EAAEvD,QAAQ,EAAEoI,WAAW,GAAG,KAAK,EAAEC,UAAU,GAAG,IAAI,EAAE7G,WAAW,GAAG,IAAI,EAAEnE,OAAO,GAAG,KAAK,KAAK;IAC1H,IAAI;MACF7B,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,IAAI4M,WAAW,EAAE;QACf;QACA,IAAIC,UAAU,EAAE;UACdzM,aAAa,CAACyM,UAAU,CAAC;UACzB,OAAOA,UAAU;QACnB;;QAEA;QACA,MAAM1L,QAAQ,GAAG,MAAM7C,GAAG,CAAC8C,GAAG,CAAC,mBAAmB2G,MAAM,EAAE,CAAC;QAC3D3H,aAAa,CAACe,QAAQ,CAACrC,IAAI,CAAC;QAC5B,OAAOqC,QAAQ,CAACrC,IAAI;MACtB;;MAEA;MACA,MAAM4E,WAAW,GAAG;QAClBxB,IAAI,EAAE,MAAM;QACZC,OAAO,EAAE,gBAAgB;QACzBqC;MACF,CAAC;MACD5E,WAAW,CAAEwC,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEsB,WAAW,CAAC,CAAC;;MAE7D;MACA5D,UAAU,CAAC,IAAI,CAAC;MAEhB,IAAImC,gBAAgB;MACpB,IAAI6K,YAAY,GAAG,IAAI;;MAGvB;MACA,IAAI9G,WAAW,EAAE;QACf,IAAIhE,OAAO;QACX,IAAIH,OAAO,EAAE;UACX;UACA,MAAMkL,oBAAoB,GAAGvI,QAAQ,IACnCA,QAAQ,CAAC+F,cAAc,CAAC,MAAM,CAAC,IAC/B/F,QAAQ,CAAC+F,cAAc,CAAC,QAAQ,CAAC,IACjC/F,QAAQ,CAAC+F,cAAc,CAAC,QAAQ,CAAC,IACjC/F,QAAQ,CAAC+F,cAAc,CAAC,SAAS,CAClC;UAED,IAAIwC,oBAAoB,EAAE;YACxB/K,OAAO,GAAG,wCAAwC;UACpD,CAAC,MAAM;YACLA,OAAO,GAAG,0DAA0DgE,WAAW,CAACgH,MAAM,GAAG;YACzF,IAAIhH,WAAW,CAAClH,IAAI,IAAI,OAAOkH,WAAW,CAAClH,IAAI,KAAK,QAAQ,EAAE;cAC5D,IAAIkH,WAAW,CAAClH,IAAI,CAACkD,OAAO,EAAE;gBAC5BA,OAAO,IAAI,cAAcgE,WAAW,CAAClH,IAAI,CAACkD,OAAO,EAAE;cACrD;YACF,CAAC,MAAM,IAAIgE,WAAW,CAAClH,IAAI,EAAE;cAC3BkD,OAAO,IAAI,cAAcgE,WAAW,CAAClH,IAAI,EAAE;YAC7C;UACF;QACF,CAAC,MAAM;UACHkD,OAAO,GAAG,wCAAwC;UAClD;UACF,IAAIgE,WAAW,CAAClH,IAAI,IAAIkH,WAAW,CAAClH,IAAI,CAACiB,KAAK,EAAE;YAC9CiC,OAAO,IAAI,WAAWgE,WAAW,CAAClH,IAAI,CAACiB,KAAK,EAAE;UAChD;QACF;QAEAkC,gBAAgB,GAAG;UACjBC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEH,OAAO;UAChBgE,WAAW,EAAEA,WAAW;UACxBxB,QAAQ,EAAErE,UAAU,CAAC;QACvB,CAAC;QAED2M,YAAY,GAAG;UACb9K,OAAO,EAAEA,OAAO;UAChBgE,WAAW,EAAEA,WAAW;UACxBnE,OAAO,EAAEA;QACX,CAAC;MACH,CAAC,MAAM;QAAA,IAAAoL,gBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACL;QACA,MAAM5L,QAAQ,GAAGyC,IAAI,CAACC,KAAK,CAACrD,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC;;QAEjE;QACA,IAAIgL,SAAS,GAAGtK,QAAQ,CAACI,KAAK;;QAE9B;QACA,IAAIxB,UAAU,IAAIA,UAAU,CAACoE,UAAU,CAACuF,eAAe,CAACiC,UAAU,CAACC,cAAc,KAAK,IAAI,IAAI7L,UAAU,CAACoE,UAAU,CAACuF,eAAe,CAACiC,UAAU,CAACC,cAAc,EAAE;UAC7JH,SAAS,GAAG1L,UAAU,CAACoE,UAAU,CAACuF,eAAe,CAACiC,UAAU,CAACpK,KAAK;UAClEb,OAAO,CAACC,GAAG,CAAC,uEAAuE,CAAC;QACtF,CAAC,MAAM;UACLD,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;QAClE;;QAEA;QACA,MAAMkL,iBAAiB,GAAGvC,kCAAkC,CAAClF,QAAQ,EAAErE,UAAU,CAAC;QAElF,MAAMgB,QAAQ,GAAG,MAAM7C,GAAG,CAAC4F,IAAI,CAAC,mBAAmB,EAAE;UACnD6D,MAAM;UACNvD,QAAQ,EAAEyH,iBAAiB;UAC3BhM;QACF,CAAC,EAAE;UACDyB,OAAO,EAAE;YACP,eAAe,EAAEmK,SAAS,GAAG,UAAUA,SAAS,EAAE,GAAGjK,SAAS;YAC9D,UAAU,EAAEL,QAAQ,CAACvC,KAAK,IAAI4C,SAAS;YACvC,aAAa,EAAEL,QAAQ,CAAC4C,QAAQ,IAAIvC;UACtC;QACF,CAAC,CAAC;;QAEF;QACA,MAAM4D,WAAW,GAAGrF,UAAU,KAC5B,EAAA8M,gBAAA,GAAA9M,UAAU,CAACyE,IAAI,cAAAqI,gBAAA,uBAAfA,gBAAA,CAAiBxH,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,OAAAwH,qBAAA,GAChD/M,UAAU,CAACoE,UAAU,cAAA2I,qBAAA,wBAAAC,sBAAA,GAArBD,qBAAA,CAAuBtI,IAAI,cAAAuI,sBAAA,uBAA3BA,sBAAA,CAA6B1H,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,EAC7D;QAEDzD,gBAAgB,GAAG;UACjBC,IAAI,EAAE,WAAW;UACjBC,OAAO,EAAEhB,QAAQ,CAACrC,IAAI,CAACkO,MAAM,IAAE,GAAG,GAAC,wBAAwB,GAAC7L,QAAQ,CAACrC,IAAI,CAACkD,OAAO;UACjFgE,WAAW,EAAE7E,QAAQ,CAACrC,IAAI,CAACkO,MAAM,IAAE,GAAG,GACvC;YACO,SAAS,EAAE,IAAI;YACf,QAAQ,EAAE,GAAG;YACb,YAAY,EAAE,EAAE;YAChB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE;UACvB,CAAC,GAAC7L,QAAQ,CAACrC,IAAI,CAACkH,WAAW;UACrBxB,QAAQ,EAAErE,UAAU;UAAE;UACtB0F,WAAW,EAAEL,WAAW,GAAG,sBAAsB,GAAG;QACtD,CAAC;QAEDsH,YAAY,GAAG3L,QAAQ,CAACrC,IAAI;MAC9B;;MAEA;MACA,MAAM,IAAIsJ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvDzI,WAAW,CAAEwC,YAAY,IAAK,CAAC,GAAGA,YAAY,EAAEH,gBAAgB,CAAC,CAAC;;MAElE;MACA7B,aAAa,CAAC,IAAI,CAAC;MAEnB,OAAO0M,YAAY;IACrB,CAAC,CAAC,OAAOzL,GAAG,EAAE;MACZ;MACAP,OAAO,CAACf,KAAK,CAAC,wBAAwB,EAAEsB,GAAG,CAAC;;MAE5C;MACA,MAAMoI,YAAY,GAAG;QACnBvH,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE;MACX,CAAC;MACD;;MAEA,OAAO,IAAI;IACb,CAAC,SAAS;MACRrC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsN,SAAS,GAAGA,CAAA,KAAM;IACtBxN,WAAW,CAAC,EAAE,CAAC;IACfM,iBAAiB,CAAC,IAAI,CAAC;IACvBE,aAAa,CAAC,IAAI,CAAC;IACnBE,qBAAqB,CAAC,IAAI,CAAC;IAC3BE,aAAa,CAAC,IAAI,CAAC;IACnBE,qBAAqB,CAAC,KAAK,CAAC;IAC5B9B,wBAAwB,GAAG,KAAK;IAChCgC,YAAY,CAACyM,UAAU,CAAC,gBAAgB,CAAC;IACzC;IACAxO,iBAAiB,CAACU,KAAK,CAAC,CAAC;EAC3B,CAAC;EAED,MAAM+N,WAAW,GAAGA,CAAA,KAAM;IACxBlN,aAAa,CAAC,IAAI,CAAC;;IAEnB;IACA,MAAMsD,WAAW,GAAG;MAAExB,IAAI,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAgC,CAAC;IAC9E,MAAMF,gBAAgB,GAAG;MAAEC,IAAI,EAAE,WAAW;MAAEC,OAAO,EAAE;IAA2C,CAAC;;IAEnG;IACAvC,WAAW,CAACwC,YAAY,IAAI,CAAC,GAAGA,YAAY,EAAEsB,WAAW,EAAEzB,gBAAgB,CAAC,CAAC;EAC/E,CAAC;EAED,oBACEzD,OAAA,CAACC,WAAW,CAAC8O,QAAQ;IACnBvI,KAAK,EAAE;MACLrF,QAAQ;MACRE,OAAO;MACPE,KAAK;MACLE,cAAc;MACdE,UAAU;MACVE,kBAAkB;MAClBE,UAAU;MACV8B,WAAW;MACXN,mBAAmB;MACnB4K,UAAU;MACVS,SAAS;MACTE,WAAW;MACXtM,gBAAgB;MAChBpB,WAAW;MACXY,aAAa;MACbN,iBAAiB;MACjBI,qBAAqB;MACrBF;IACF,CAAE;IAAAX,QAAA,EAEDA;EAAQ;IAAA+N,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAACjO,EAAA,CA1+BWF,YAAY;AAAAoO,EAAA,GAAZpO,YAAY;AA4+BzB,OAAO,MAAMqO,OAAO,GAAGA,CAAA;EAAAC,GAAA;EAAA,OAAM5P,UAAU,CAACO,WAAW,CAAC;AAAA;AAACqP,GAAA,CAAxCD,OAAO;AAEpB,eAAepP,WAAW;AAAC,IAAAmP,EAAA;AAAAG,YAAA,CAAAH,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}