{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatMessage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatMessage = ({\n  message,\n  onOptionSelect,\n  onFormLinkTriggered\n}) => {\n  _s();\n  const isUser = message.role === 'user';\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\n\n  // Handle checkbox selection\n  const handleCheckboxToggle = option => {\n    const newSelected = selectedCheckboxes.includes(option) ? selectedCheckboxes.filter(item => item !== option) : [...selectedCheckboxes, option];\n    setSelectedCheckboxes(newSelected);\n  };\n\n  // Handle checkbox submission\n  const handleCheckboxSubmit = () => {\n    if (selectedCheckboxes.length > 0) {\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\n      setSelectedCheckboxes([]);\n    }\n  };\n\n  // Render leave balance with form linking buttons\n  const renderLeaveBalanceWithButtons = (apiResponse, formLinkingConfig) => {\n    var _message$formData, _message$formConfig;\n    const formId = ((_message$formData = message.formData) === null || _message$formData === void 0 ? void 0 : _message$formData._id) || ((_message$formConfig = message.formConfig) === null || _message$formConfig === void 0 ? void 0 : _message$formConfig._id);\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 bg-blue-50 rounded-md mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"whitespace-pre-wrap text-sm text-blue-800\",\n          children: apiResponse.leaveBalance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), (formLinkingConfig === null || formLinkingConfig === void 0 ? void 0 : formLinkingConfig.enabled) && formLinkingConfig.recordActions && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n          data: apiResponse.data || [] // Use the actual data from API response\n          ,\n          formId: formId,\n          formLinkingConfig: formLinkingConfig,\n          onFormLinkTriggered: onFormLinkTriggered,\n          showOnlyButtons: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Handle single option selection (for radio/select)\n  const handleSingleOptionSelect = option => {\n    onOptionSelect && onOptionSelect(option);\n  };\n  // Helper function to check if data is empty (comprehensive check)\n  const checkIfDataIsEmpty = data => {\n    if (!data) return true;\n    if (Array.isArray(data)) return data.length === 0;\n    if (typeof data === 'object' && data !== null) {\n      if (Object.keys(data).length === 0) return true;\n      // Check nested data structure\n      if (data.data) return checkIfDataIsEmpty(data.data);\n      // Check common array fields\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n      for (const field of arrayFields) {\n        if (data[field] && Array.isArray(data[field])) {\n          return data[field].length === 0;\n        }\n      }\n      // Check if all values are empty\n      return Object.values(data).every(val => val === null || val === undefined || val === '' || Array.isArray(val) && val.length === 0 || typeof val === 'object' && val !== null && Object.keys(val).length === 0);\n    }\n    return false;\n  };\n\n  // Helper function to render \"No data available\" message\n  const renderNoDataMessage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\",\n    children: /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-blue-800 text-sm font-medium\",\n      children: \"No data available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 5\n  }, this);\n\n  // Format API response for display\n  const formatApiResponse = apiResponse => {\n    var _message$formData2, _message$formData2$fo, _message$formConfig2, _message$formData3, _message$formConfig3, _message$formConfig3$, _message$apiResponse, _formLinkingConfig$re4;\n    if (!apiResponse) return null;\n\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\n    const formLinkingConfig = ((_message$formData2 = message.formData) === null || _message$formData2 === void 0 ? void 0 : (_message$formData2$fo = _message$formData2.formConfig) === null || _message$formData2$fo === void 0 ? void 0 : _message$formData2$fo.formLinking) || ((_message$formConfig2 = message.formConfig) === null || _message$formConfig2 === void 0 ? void 0 : _message$formConfig2.formLinking) || ((_message$formData3 = message.formData) === null || _message$formData3 === void 0 ? void 0 : _message$formData3.formLinking) || ((_message$formConfig3 = message.formConfig) === null || _message$formConfig3 === void 0 ? void 0 : (_message$formConfig3$ = _message$formConfig3.formConfig) === null || _message$formConfig3$ === void 0 ? void 0 : _message$formConfig3$.formLinking);\n\n    // Special handling for leave balance API responses\n    if ((_message$apiResponse = message.apiResponse) !== null && _message$apiResponse !== void 0 && _message$apiResponse.leaveBalance && !formLinkingConfig) {\n      var _message$formConfig4;\n      console.log('🍃 Leave balance detected, checking for form linking config');\n      // Try to find form linking config in the message structure\n      const leaveFormLinkingConfig = (_message$formConfig4 = message.formConfig) === null || _message$formConfig4 === void 0 ? void 0 : _message$formConfig4.formLinking;\n      if (leaveFormLinkingConfig !== null && leaveFormLinkingConfig !== void 0 && leaveFormLinkingConfig.enabled) {\n        console.log('🔗 Found form linking config for leave balance:', leaveFormLinkingConfig);\n        return renderLeaveBalanceWithButtons(message.apiResponse, leaveFormLinkingConfig);\n      }\n    }\n\n    // For successful responses with data, check if we should display records with actions\n    if (apiResponse.success && apiResponse.data) {\n      var _message$conversation, _message$hybridFlow, _formLinkingConfig$re, _formLinkingConfig$re3;\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\n      const hasActiveConversationalFlow = message.isConversationalPhase || message.isHybridFlow || ((_message$conversation = message.conversationalFlow) === null || _message$conversation === void 0 ? void 0 : _message$conversation.isActive) || ((_message$hybridFlow = message.hybridFlow) === null || _message$hybridFlow === void 0 ? void 0 : _message$hybridFlow.isActive) || message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow);\n\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\n      if (formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re = formLinkingConfig.recordActions) === null || _formLinkingConfig$re === void 0 ? void 0 : _formLinkingConfig$re.length) > 0 && !hasActiveConversationalFlow) {\n        var _apiResponse$data, _formLinkingConfig$re2, _message$formData4, _message$formConfig5;\n        // Extract the actual data from the API response\n        const actualData = ((_apiResponse$data = apiResponse.data) === null || _apiResponse$data === void 0 ? void 0 : _apiResponse$data.data) || apiResponse.data;\n\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\n        const hasDisabledAutoTrigger = (_formLinkingConfig$re2 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re2 === void 0 ? void 0 : _formLinkingConfig$re2.some(action => {\n          var _action$autoTrigger;\n          return !((_action$autoTrigger = action.autoTrigger) !== null && _action$autoTrigger !== void 0 && _action$autoTrigger.enabled);\n        });\n        const formId = ((_message$formData4 = message.formData) === null || _message$formData4 === void 0 ? void 0 : _message$formData4._id) || ((_message$formConfig5 = message.formConfig) === null || _message$formConfig5 === void 0 ? void 0 : _message$formConfig5._id);\n\n        // Function to render records with interleaved apply buttons\n        const renderRecordsWithButtons = () => {\n          let records = [];\n\n          // Extract records from actualData\n          if (Array.isArray(actualData)) {\n            records = actualData;\n          } else if (actualData && typeof actualData === 'object') {\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n            for (const field of arrayFields) {\n              if (actualData[field] && Array.isArray(actualData[field])) {\n                records = actualData[field];\n                break;\n              }\n            }\n            if (records.length === 0) {\n              records = [actualData];\n            }\n          }\n\n          // Check if records are empty using helper function\n          if (checkIfDataIsEmpty(records)) {\n            return renderNoDataMessage();\n          }\n\n          // Split formatted response by \"Record N:\" pattern\n          let formattedRecords = [];\n          if (apiResponse.formattedResponse) {\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\n            formattedRecords = recordSections.filter(section => section.trim());\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [records.map((record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [formattedRecords[index] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: formattedRecords[index].trim()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 171,\n                columnNumber: 21\n              }, this), (formLinkingConfig === null || formLinkingConfig === void 0 ? void 0 : formLinkingConfig.enabled) && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2 ml-3\",\n                children: /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                  data: [record] // Pass only this specific record\n                  ,\n                  formId: formId,\n                  formLinkingConfig: formLinkingConfig,\n                  onFormLinkTriggered: onFormLinkTriggered,\n                  showOnlyButtons: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 17\n            }, this)), formattedRecords.length === 0 && apiResponse.formattedResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: apiResponse.formattedResponse\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                data: actualData,\n                formId: formId,\n                formLinkingConfig: formLinkingConfig,\n                onFormLinkTriggered: onFormLinkTriggered,\n                showOnlyButtons: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this);\n        };\n        return renderRecordsWithButtons();\n      }\n\n      // If we have a formatted response but no form linking, display just the formatted response\n      if (apiResponse.formattedResponse && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re3 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re3 === void 0 ? void 0 : _formLinkingConfig$re3.length) > 0)) {\n        // Check if the underlying data is empty using helper function\n        if (checkIfDataIsEmpty(apiResponse.data)) {\n          return renderNoDataMessage();\n        }\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 p-3 bg-blue-50 rounded-md\",\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: \"whitespace-pre-wrap text-sm text-blue-800\",\n            children: apiResponse.formattedResponse\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 228,\n          columnNumber: 11\n        }, this);\n      }\n    }\n\n    // Check if data is empty (array with no items or empty object)\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re4 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re4 === void 0 ? void 0 : _formLinkingConfig$re4.length) > 0)) {\n      // Check if data is empty using helper function\n      if (checkIfDataIsEmpty(apiResponse.data)) {\n        return renderNoDataMessage();\n      }\n\n      // Display data if not empty\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"whitespace-pre-wrap text-sm text-gray-700\",\n          children: JSON.stringify(apiResponse.data, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Handle successful response but no data property at all\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\n      return renderNoDataMessage();\n    }\n\n    // Error display\n    if (apiResponse.error) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-red-50 rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-sm\",\n          children: apiResponse.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 262,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-[80%] p-3 rounded-lg ${isUser ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-200 text-gray-800 rounded-bl-none'}`,\n      children: [!(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"whitespace-pre-wrap\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 286,\n        columnNumber: 11\n      }, this), !isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 314,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 307,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 341,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 339,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-gray-500\",\n          children: [\"Step \", message.currentStep, \" of \", message.totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 11\n      }, this), !isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 384,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 383,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 377,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 365,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 411,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalConversationalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-blue-600\",\n          children: [\"Conversational Step \", message.currentStep, \" of \", message.totalConversationalSteps, message.totalFormSteps > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-500\",\n            children: [\"(\", message.totalFormSteps, \" form field\", message.totalFormSteps !== 1 ? 's' : '', \" remaining)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this), \"Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 439,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-3 h-3 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 13l4 4L19 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), \"Leave Application Submitted Successfully\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 13\n        }, this), message.updatedLeaveBalance && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 bg-blue-50 rounded-lg border border-blue-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2 text-blue-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 463,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-blue-800\",\n              children: \"Updated Leave Balance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-blue-700 whitespace-pre-wrap\",\n            children: message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 460,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this), \"Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 477,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_cancelled' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 491,\n            columnNumber: 15\n          }, this), \"Form Cancelled\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 489,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 504,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 503,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 502,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 515,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Hybrid Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 514,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }, this), message.apiResponse && formatApiResponse(message.apiResponse)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatMessage, \"cE9qr9Un/J6bIavRReyYdfaIaEo=\");\n_c = ChatMessage;\nexport default ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");", "map": {"version": 3, "names": ["React", "useState", "RecordDisplayWithActions", "jsxDEV", "_jsxDEV", "ChatMessage", "message", "onOptionSelect", "onFormLinkTriggered", "_s", "isUser", "role", "selectedCheckboxes", "setSelectedCheckboxes", "handleCheckboxToggle", "option", "newSelected", "includes", "filter", "item", "handleCheckboxSubmit", "length", "join", "renderLeaveBalanceWithButtons", "apiResponse", "formLinkingConfig", "_message$formData", "_message$formConfig", "formId", "formData", "_id", "formConfig", "className", "children", "leaveBalance", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "enabled", "recordActions", "data", "showOnlyButtons", "handleSingleOptionSelect", "checkIfDataIsEmpty", "Array", "isArray", "Object", "keys", "arrayFields", "field", "values", "every", "val", "undefined", "renderNoDataMessage", "formatApiResponse", "_message$formData2", "_message$formData2$fo", "_message$formConfig2", "_message$formData3", "_message$formConfig3", "_message$formConfig3$", "_message$apiResponse", "_formLinkingConfig$re4", "formLinking", "_message$formConfig4", "console", "log", "leaveFormLinkingConfig", "success", "_message$conversation", "_message$hybridFlow", "_formLinkingConfig$re", "_formLinkingConfig$re3", "hasActiveConversationalFlow", "isConversationalPhase", "isHybridFlow", "conversationalFlow", "isActive", "hybridFlow", "options", "_apiResponse$data", "_formLinkingConfig$re2", "_message$formData4", "_message$formConfig5", "actualData", "hasDisabledAutoTrigger", "some", "action", "_action$autoTrigger", "autoTrigger", "renderRecordsWithButtons", "records", "formattedRecords", "formattedResponse", "recordSections", "split", "section", "trim", "map", "record", "index", "JSON", "stringify", "error", "content", "fieldType", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "currentStep", "totalSteps", "totalConversationalSteps", "totalFormSteps", "queryIntent", "updatedLeaveBalance", "replace", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatMessage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\r\n\r\nconst ChatMessage = ({ message, onOptionSelect, onFormLinkTriggered }) => {\r\n  const isUser = message.role === 'user';\r\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\r\n\r\n  // Handle checkbox selection\r\n  const handleCheckboxToggle = (option) => {\r\n    const newSelected = selectedCheckboxes.includes(option)\r\n      ? selectedCheckboxes.filter(item => item !== option)\r\n      : [...selectedCheckboxes, option];\r\n    setSelectedCheckboxes(newSelected);\r\n  };\r\n\r\n  // Handle checkbox submission\r\n  const handleCheckboxSubmit = () => {\r\n    if (selectedCheckboxes.length > 0) {\r\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\r\n      setSelectedCheckboxes([]);\r\n    }\r\n  };\r\n\r\n  // Render leave balance with form linking buttons\r\n  const renderLeaveBalanceWithButtons = (apiResponse, formLinkingConfig) => {\r\n    const formId = message.formData?._id || message.formConfig?._id;\r\n\r\n    return (\r\n      <div className=\"mt-3\">\r\n        {/* Display the leave balance content */}\r\n        <div className=\"p-3 bg-blue-50 rounded-md mb-3\">\r\n          <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n            {apiResponse.leaveBalance}\r\n          </pre>\r\n        </div>\r\n\r\n        {/* Display form linking buttons */}\r\n        {formLinkingConfig?.enabled && formLinkingConfig.recordActions && (\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            <RecordDisplayWithActions\r\n              data={apiResponse.data || []} // Use the actual data from API response\r\n              formId={formId}\r\n              formLinkingConfig={formLinkingConfig}\r\n              onFormLinkTriggered={onFormLinkTriggered}\r\n              showOnlyButtons={true}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Handle single option selection (for radio/select)\r\n  const handleSingleOptionSelect = (option) => {\r\n    onOptionSelect && onOptionSelect(option);\r\n  };\r\n  // Helper function to check if data is empty (comprehensive check)\r\n  const checkIfDataIsEmpty = (data) => {\r\n    if (!data) return true;\r\n    if (Array.isArray(data)) return data.length === 0;\r\n    if (typeof data === 'object' && data !== null) {\r\n      if (Object.keys(data).length === 0) return true;\r\n      // Check nested data structure\r\n      if (data.data) return checkIfDataIsEmpty(data.data);\r\n      // Check common array fields\r\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n      for (const field of arrayFields) {\r\n        if (data[field] && Array.isArray(data[field])) {\r\n          return data[field].length === 0;\r\n        }\r\n      }\r\n      // Check if all values are empty\r\n      return Object.values(data).every(val => \r\n        val === null || val === undefined || val === '' || \r\n        (Array.isArray(val) && val.length === 0) ||\r\n        (typeof val === 'object' && val !== null && Object.keys(val).length === 0)\r\n      );\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // Helper function to render \"No data available\" message\r\n  const renderNoDataMessage = () => (\r\n    <div className=\"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\">\r\n      <p className=\"text-blue-800 text-sm font-medium\">No data available</p>\r\n    </div>\r\n  );\r\n\r\n  // Format API response for display\r\n  const formatApiResponse = (apiResponse) => {\r\n    if (!apiResponse) return null;\r\n    \r\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\r\n    const formLinkingConfig = message.formData?.formConfig?.formLinking ||\r\n                             message.formConfig?.formLinking ||\r\n                             message.formData?.formLinking ||\r\n                             message.formConfig?.formConfig?.formLinking;\r\n\r\n    // Special handling for leave balance API responses\r\n    if (message.apiResponse?.leaveBalance && !formLinkingConfig) {\r\n      console.log('🍃 Leave balance detected, checking for form linking config');\r\n      // Try to find form linking config in the message structure\r\n      const leaveFormLinkingConfig = message.formConfig?.formLinking;\r\n      if (leaveFormLinkingConfig?.enabled) {\r\n        console.log('🔗 Found form linking config for leave balance:', leaveFormLinkingConfig);\r\n        return renderLeaveBalanceWithButtons(message.apiResponse, leaveFormLinkingConfig);\r\n      }\r\n    }\r\n    \r\n    // For successful responses with data, check if we should display records with actions\r\n    if (apiResponse.success && apiResponse.data) {\r\n      \r\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\r\n      const hasActiveConversationalFlow = message.isConversationalPhase || \r\n                                         message.isHybridFlow || \r\n                                         message.conversationalFlow?.isActive ||\r\n                                         message.hybridFlow?.isActive ||\r\n                                         (message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow));\r\n      \r\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\r\n      if (formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0 && !hasActiveConversationalFlow) {\r\n       \r\n        // Extract the actual data from the API response\r\n        const actualData = apiResponse.data?.data || apiResponse.data;\r\n        \r\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\r\n        const hasDisabledAutoTrigger = formLinkingConfig.recordActions?.some(action => \r\n          !action.autoTrigger?.enabled\r\n        );\r\n        \r\n        const formId = message.formData?._id || message.formConfig?._id;\r\n        \r\n        // Function to render records with interleaved apply buttons\r\n        const renderRecordsWithButtons = () => {\r\n          let records = [];\r\n          \r\n          // Extract records from actualData\r\n          if (Array.isArray(actualData)) {\r\n            records = actualData;\r\n          } else if (actualData && typeof actualData === 'object') {\r\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n            for (const field of arrayFields) {\r\n              if (actualData[field] && Array.isArray(actualData[field])) {\r\n                records = actualData[field];\r\n                break;\r\n              }\r\n            }\r\n            if (records.length === 0) {\r\n              records = [actualData];\r\n            }\r\n          }\r\n          \r\n          // Check if records are empty using helper function\r\n          if (checkIfDataIsEmpty(records)) {\r\n            return renderNoDataMessage();\r\n          }\r\n\r\n          // Split formatted response by \"Record N:\" pattern\r\n          let formattedRecords = [];\r\n          if (apiResponse.formattedResponse) {\r\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\r\n            formattedRecords = recordSections.filter(section => section.trim());\r\n          }\r\n\r\n          return (\r\n            <div className=\"mt-3\">\r\n              {records.map((record, index) => (\r\n                <div key={index} className=\"mb-4\">\r\n                  {/* Display the formatted response for this record */}\r\n                  {formattedRecords[index] && (\r\n                    <div className=\"mb-2 p-3 bg-blue-50 rounded-md\">\r\n                      <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                        {formattedRecords[index].trim()}\r\n                      </pre>\r\n                    </div>\r\n                  )}\r\n                  \r\n                  {/* Apply button for this specific record */}\r\n                  {formLinkingConfig?.enabled && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && (\r\n                    <div className=\"flex flex-wrap gap-2 ml-3\">\r\n                      <RecordDisplayWithActions\r\n                        data={[record]} // Pass only this specific record\r\n                        formId={formId}\r\n                        formLinkingConfig={formLinkingConfig}\r\n                        onFormLinkTriggered={onFormLinkTriggered}\r\n                        showOnlyButtons={true}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n              \r\n              {/* Fallback: if we can't split the formatted response properly */}\r\n              {formattedRecords.length === 0 && apiResponse.formattedResponse && (\r\n                <div>\r\n                  <div className=\"mb-3 p-3 bg-blue-50 rounded-md\">\r\n                    <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                      {apiResponse.formattedResponse}\r\n                    </pre>\r\n                  </div>\r\n                  {/* Only show Apply buttons if the response is not \"No data available\" */}\r\n                  {!apiResponse.formattedResponse.includes('No data available') && (\r\n                    <RecordDisplayWithActions\r\n                      data={actualData}\r\n                      formId={formId}\r\n                      formLinkingConfig={formLinkingConfig}\r\n                      onFormLinkTriggered={onFormLinkTriggered}\r\n                      showOnlyButtons={true}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          );\r\n        };\r\n\r\n        return renderRecordsWithButtons();\r\n      }\r\n        \r\n      // If we have a formatted response but no form linking, display just the formatted response\r\n      if (apiResponse.formattedResponse && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n        // Check if the underlying data is empty using helper function\r\n        if (checkIfDataIsEmpty(apiResponse.data)) {\r\n          return renderNoDataMessage();\r\n        }\r\n        \r\n        return (\r\n          <div className=\"mt-3 p-3 bg-blue-50 rounded-md\">\r\n            <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n              {apiResponse.formattedResponse}\r\n            </pre>\r\n          </div>\r\n        );\r\n      }\r\n    }\r\n    \r\n    // Check if data is empty (array with no items or empty object)\r\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n      // Check if data is empty using helper function\r\n      if (checkIfDataIsEmpty(apiResponse.data)) {\r\n        return renderNoDataMessage();\r\n      }\r\n      \r\n      // Display data if not empty\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\">\r\n          <pre className=\"whitespace-pre-wrap text-sm text-gray-700\">\r\n            {JSON.stringify(apiResponse.data, null, 2)}\r\n          </pre>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    // Handle successful response but no data property at all\r\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\r\n      return renderNoDataMessage();\r\n    }\r\n    \r\n    // Error display\r\n    if (apiResponse.error) {\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-red-50 rounded-md\">\r\n          <p className=\"text-red-600 text-sm\">{apiResponse.error}</p>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`flex ${\r\n        isUser ? 'justify-end' : 'justify-start'\r\n      } mb-4`}\r\n    >\r\n      <div\r\n        className={`max-w-[80%] p-3 rounded-lg ${\r\n          isUser\r\n            ? 'bg-blue-500 text-white rounded-br-none'\r\n            : 'bg-gray-200 text-gray-800 rounded-bl-none'\r\n        }`}\r\n      >\r\n        {/* Display message content only if we don't have a formatted API response */}\r\n        {!(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <p className=\"whitespace-pre-wrap\">{message.content}</p>\r\n        )}\r\n        \r\n        {/* Display conversational form options as buttons */}\r\n        {!isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalSteps && (\r\n              <div className=\"mt-2 text-xs text-gray-500\">\r\n                Step {message.currentStep} of {message.totalSteps}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form options for conversational phase */}\r\n        {!isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalConversationalSteps && (\r\n              <div className=\"mt-2 text-xs text-blue-600\">\r\n                Conversational Step {message.currentStep} of {message.totalConversationalSteps}\r\n                {message.totalFormSteps > 0 && (\r\n                  <span className=\"ml-2 text-gray-500\">\r\n                    ({message.totalFormSteps} form field{message.totalFormSteps !== 1 ? 's' : ''} remaining)\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n\r\n              \r\n        {/* Display form completion indicator - only show success if form was actually submitted successfully */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display leave form completion with updated balance */}\r\n        {!isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2\">\r\n            <div className=\"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\">\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n                Leave Application Submitted Successfully\r\n              </div>\r\n            </div>\r\n            {message.updatedLeaveBalance && (\r\n              <div className=\"p-3 bg-blue-50 rounded-lg border border-blue-200\">\r\n                <div className=\"flex items-center mb-2\">\r\n                  <svg className=\"w-4 h-4 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                  </svg>\r\n                  <h4 className=\"text-sm font-medium text-blue-800\">Updated Leave Balance</h4>\r\n                </div>\r\n                <div className=\"text-sm text-blue-700 whitespace-pre-wrap\">\r\n                  {message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Display form cancellation indicator */}\r\n        {!isUser && message.queryIntent === 'form_cancelled' && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Cancelled\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form completion indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              🔄 Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              🔄 Hybrid Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n\r\n        \r\n        {/* Leave balance is now included in the message content, no separate display needed */}\r\n\r\n        {/* Display API response if available */}\r\n        {message.apiResponse && formatApiResponse(message.apiResponse)}\r\n        \r\n        {/* Display form data if available (for debugging) */}\r\n        {/* {message.formData && process.env.NODE_ENV === 'development' && (\r\n          <div className=\"mt-2 p-2 bg-gray-100 rounded text-xs text-gray-500\">\r\n            <p>Form data submitted</p>\r\n          </div>\r\n        )} */}\r\n        \r\n        {/* Display query intent if available (for debugging) */}\r\n        {/* {message.queryIntent && process.env.NODE_ENV === 'development' && (\r\n          <div className={`mt-2 p-2 rounded text-xs ${\r\n            message.queryIntent === 'form' \r\n              ? 'bg-blue-100 text-blue-700' \r\n              : 'bg-green-100 text-green-700'\r\n          }`}>\r\n            <p>Query intent: <strong>{message.queryIntent}</strong></p>\r\n            {message.formData && message.queryIntent === 'form' && (\r\n              <p>Form detected: {message.formData.name}</p>\r\n            )}\r\n          </div>\r\n        )} */}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatMessage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,wBAAwB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,cAAc;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,IAAI,KAAK,MAAM;EACtC,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAMa,oBAAoB,GAAIC,MAAM,IAAK;IACvC,MAAMC,WAAW,GAAGJ,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GACnDH,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKJ,MAAM,CAAC,GAClD,CAAC,GAAGH,kBAAkB,EAAEG,MAAM,CAAC;IACnCF,qBAAqB,CAACG,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIR,kBAAkB,CAACS,MAAM,GAAG,CAAC,EAAE;MACjCd,cAAc,IAAIA,cAAc,CAACK,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/DT,qBAAqB,CAAC,EAAE,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMU,6BAA6B,GAAGA,CAACC,WAAW,EAAEC,iBAAiB,KAAK;IAAA,IAAAC,iBAAA,EAAAC,mBAAA;IACxE,MAAMC,MAAM,GAAG,EAAAF,iBAAA,GAAApB,OAAO,CAACuB,QAAQ,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBI,GAAG,OAAAH,mBAAA,GAAIrB,OAAO,CAACyB,UAAU,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBG,GAAG;IAE/D,oBACE1B,OAAA;MAAK4B,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnB7B,OAAA;QAAK4B,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C7B,OAAA;UAAK4B,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvDT,WAAW,CAACU;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGL,CAAAb,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEc,OAAO,KAAId,iBAAiB,CAACe,aAAa,iBAC5DpC,OAAA;QAAK4B,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnC7B,OAAA,CAACF,wBAAwB;UACvBuC,IAAI,EAAEjB,WAAW,CAACiB,IAAI,IAAI,EAAG,CAAC;UAAA;UAC9Bb,MAAM,EAAEA,MAAO;UACfH,iBAAiB,EAAEA,iBAAkB;UACrCjB,mBAAmB,EAAEA,mBAAoB;UACzCkC,eAAe,EAAE;QAAK;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAEV,CAAC;;EAED;EACA,MAAMK,wBAAwB,GAAI5B,MAAM,IAAK;IAC3CR,cAAc,IAAIA,cAAc,CAACQ,MAAM,CAAC;EAC1C,CAAC;EACD;EACA,MAAM6B,kBAAkB,GAAIH,IAAI,IAAK;IACnC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IACtB,IAAII,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE,OAAOA,IAAI,CAACpB,MAAM,KAAK,CAAC;IACjD,IAAI,OAAOoB,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;MAC7C,IAAIM,MAAM,CAACC,IAAI,CAACP,IAAI,CAAC,CAACpB,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC/C;MACA,IAAIoB,IAAI,CAACA,IAAI,EAAE,OAAOG,kBAAkB,CAACH,IAAI,CAACA,IAAI,CAAC;MACnD;MACA,MAAMQ,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;MAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;QAC/B,IAAIR,IAAI,CAACS,KAAK,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACL,IAAI,CAACS,KAAK,CAAC,CAAC,EAAE;UAC7C,OAAOT,IAAI,CAACS,KAAK,CAAC,CAAC7B,MAAM,KAAK,CAAC;QACjC;MACF;MACA;MACA,OAAO0B,MAAM,CAACI,MAAM,CAACV,IAAI,CAAC,CAACW,KAAK,CAACC,GAAG,IAClCA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,IAC9CR,KAAK,CAACC,OAAO,CAACO,GAAG,CAAC,IAAIA,GAAG,CAAChC,MAAM,KAAK,CAAE,IACvC,OAAOgC,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIN,MAAM,CAACC,IAAI,CAACK,GAAG,CAAC,CAAChC,MAAM,KAAK,CAC1E,CAAC;IACH;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMkC,mBAAmB,GAAGA,CAAA,kBAC1BnD,OAAA;IAAK4B,SAAS,EAAC,uDAAuD;IAAAC,QAAA,eACpE7B,OAAA;MAAG4B,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnE,CACN;;EAED;EACA,MAAMkB,iBAAiB,GAAIhC,WAAW,IAAK;IAAA,IAAAiC,kBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,kBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,sBAAA;IACzC,IAAI,CAACxC,WAAW,EAAE,OAAO,IAAI;;IAE7B;IACA,MAAMC,iBAAiB,GAAG,EAAAgC,kBAAA,GAAAnD,OAAO,CAACuB,QAAQ,cAAA4B,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkB1B,UAAU,cAAA2B,qBAAA,uBAA5BA,qBAAA,CAA8BO,WAAW,OAAAN,oBAAA,GAC1CrD,OAAO,CAACyB,UAAU,cAAA4B,oBAAA,uBAAlBA,oBAAA,CAAoBM,WAAW,OAAAL,kBAAA,GAC/BtD,OAAO,CAACuB,QAAQ,cAAA+B,kBAAA,uBAAhBA,kBAAA,CAAkBK,WAAW,OAAAJ,oBAAA,GAC7BvD,OAAO,CAACyB,UAAU,cAAA8B,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoB9B,UAAU,cAAA+B,qBAAA,uBAA9BA,qBAAA,CAAgCG,WAAW;;IAEpE;IACA,IAAI,CAAAF,oBAAA,GAAAzD,OAAO,CAACkB,WAAW,cAAAuC,oBAAA,eAAnBA,oBAAA,CAAqB7B,YAAY,IAAI,CAACT,iBAAiB,EAAE;MAAA,IAAAyC,oBAAA;MAC3DC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;MAC1E;MACA,MAAMC,sBAAsB,IAAAH,oBAAA,GAAG5D,OAAO,CAACyB,UAAU,cAAAmC,oBAAA,uBAAlBA,oBAAA,CAAoBD,WAAW;MAC9D,IAAII,sBAAsB,aAAtBA,sBAAsB,eAAtBA,sBAAsB,CAAE9B,OAAO,EAAE;QACnC4B,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEC,sBAAsB,CAAC;QACtF,OAAO9C,6BAA6B,CAACjB,OAAO,CAACkB,WAAW,EAAE6C,sBAAsB,CAAC;MACnF;IACF;;IAEA;IACA,IAAI7C,WAAW,CAAC8C,OAAO,IAAI9C,WAAW,CAACiB,IAAI,EAAE;MAAA,IAAA8B,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAE3C;MACA,MAAMC,2BAA2B,GAAGrE,OAAO,CAACsE,qBAAqB,IAC9BtE,OAAO,CAACuE,YAAY,MAAAN,qBAAA,GACpBjE,OAAO,CAACwE,kBAAkB,cAAAP,qBAAA,uBAA1BA,qBAAA,CAA4BQ,QAAQ,OAAAP,mBAAA,GACpClE,OAAO,CAAC0E,UAAU,cAAAR,mBAAA,uBAAlBA,mBAAA,CAAoBO,QAAQ,KAC3BzE,OAAO,CAAC2E,OAAO,IAAI3E,OAAO,CAAC2E,OAAO,CAAC5D,MAAM,GAAG,CAAC,KAAKf,OAAO,CAACsE,qBAAqB,IAAItE,OAAO,CAACuE,YAAY,CAAE;;MAE7I;MACA,IAAIpD,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEc,OAAO,IAAI,EAAAkC,qBAAA,GAAAhD,iBAAiB,CAACe,aAAa,cAAAiC,qBAAA,uBAA/BA,qBAAA,CAAiCpD,MAAM,IAAG,CAAC,IAAI,CAACsD,2BAA2B,EAAE;QAAA,IAAAO,iBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,oBAAA;QAE7G;QACA,MAAMC,UAAU,GAAG,EAAAJ,iBAAA,GAAA1D,WAAW,CAACiB,IAAI,cAAAyC,iBAAA,uBAAhBA,iBAAA,CAAkBzC,IAAI,KAAIjB,WAAW,CAACiB,IAAI;;QAE7D;QACA,MAAM8C,sBAAsB,IAAAJ,sBAAA,GAAG1D,iBAAiB,CAACe,aAAa,cAAA2C,sBAAA,uBAA/BA,sBAAA,CAAiCK,IAAI,CAACC,MAAM;UAAA,IAAAC,mBAAA;UAAA,OACzE,GAAAA,mBAAA,GAACD,MAAM,CAACE,WAAW,cAAAD,mBAAA,eAAlBA,mBAAA,CAAoBnD,OAAO;QAAA,CAC9B,CAAC;QAED,MAAMX,MAAM,GAAG,EAAAwD,kBAAA,GAAA9E,OAAO,CAACuB,QAAQ,cAAAuD,kBAAA,uBAAhBA,kBAAA,CAAkBtD,GAAG,OAAAuD,oBAAA,GAAI/E,OAAO,CAACyB,UAAU,cAAAsD,oBAAA,uBAAlBA,oBAAA,CAAoBvD,GAAG;;QAE/D;QACA,MAAM8D,wBAAwB,GAAGA,CAAA,KAAM;UACrC,IAAIC,OAAO,GAAG,EAAE;;UAEhB;UACA,IAAIhD,KAAK,CAACC,OAAO,CAACwC,UAAU,CAAC,EAAE;YAC7BO,OAAO,GAAGP,UAAU;UACtB,CAAC,MAAM,IAAIA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;YACvD,MAAMrC,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;YAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;cAC/B,IAAIqC,UAAU,CAACpC,KAAK,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACwC,UAAU,CAACpC,KAAK,CAAC,CAAC,EAAE;gBACzD2C,OAAO,GAAGP,UAAU,CAACpC,KAAK,CAAC;gBAC3B;cACF;YACF;YACA,IAAI2C,OAAO,CAACxE,MAAM,KAAK,CAAC,EAAE;cACxBwE,OAAO,GAAG,CAACP,UAAU,CAAC;YACxB;UACF;;UAEA;UACA,IAAI1C,kBAAkB,CAACiD,OAAO,CAAC,EAAE;YAC/B,OAAOtC,mBAAmB,CAAC,CAAC;UAC9B;;UAEA;UACA,IAAIuC,gBAAgB,GAAG,EAAE;UACzB,IAAItE,WAAW,CAACuE,iBAAiB,EAAE;YACjC,MAAMC,cAAc,GAAGxE,WAAW,CAACuE,iBAAiB,CAACE,KAAK,CAAC,iBAAiB,CAAC;YAC7EH,gBAAgB,GAAGE,cAAc,CAAC9E,MAAM,CAACgF,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;UACrE;UAEA,oBACE/F,OAAA;YAAK4B,SAAS,EAAC,MAAM;YAAAC,QAAA,GAClB4D,OAAO,CAACO,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzBlG,OAAA;cAAiB4B,SAAS,EAAC,MAAM;cAAAC,QAAA,GAE9B6D,gBAAgB,CAACQ,KAAK,CAAC,iBACtBlG,OAAA;gBAAK4B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7C7B,OAAA;kBAAK4B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvD6D,gBAAgB,CAACQ,KAAK,CAAC,CAACH,IAAI,CAAC;gBAAC;kBAAAhE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAAAb,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEc,OAAO,KAAId,iBAAiB,CAACe,aAAa,IAAI,CAAChB,WAAW,CAACuE,iBAAiB,CAAC9E,QAAQ,CAAC,mBAAmB,CAAC,iBAC5Hb,OAAA;gBAAK4B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxC7B,OAAA,CAACF,wBAAwB;kBACvBuC,IAAI,EAAE,CAAC4D,MAAM,CAAE,CAAC;kBAAA;kBAChBzE,MAAM,EAAEA,MAAO;kBACfH,iBAAiB,EAAEA,iBAAkB;kBACrCjB,mBAAmB,EAAEA,mBAAoB;kBACzCkC,eAAe,EAAE;gBAAK;kBAAAP,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA,GArBOgE,KAAK;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBV,CACN,CAAC,EAGDwD,gBAAgB,CAACzE,MAAM,KAAK,CAAC,IAAIG,WAAW,CAACuE,iBAAiB,iBAC7D3F,OAAA;cAAA6B,QAAA,gBACE7B,OAAA;gBAAK4B,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7C7B,OAAA;kBAAK4B,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvDT,WAAW,CAACuE;gBAAiB;kBAAA5D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,CAACd,WAAW,CAACuE,iBAAiB,CAAC9E,QAAQ,CAAC,mBAAmB,CAAC,iBAC3Db,OAAA,CAACF,wBAAwB;gBACvBuC,IAAI,EAAE6C,UAAW;gBACjB1D,MAAM,EAAEA,MAAO;gBACfH,iBAAiB,EAAEA,iBAAkB;gBACrCjB,mBAAmB,EAAEA,mBAAoB;gBACzCkC,eAAe,EAAE;cAAK;gBAAAP,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC;QAED,OAAOsD,wBAAwB,CAAC,CAAC;MACnC;;MAEA;MACA,IAAIpE,WAAW,CAACuE,iBAAiB,IAAI,EAAEtE,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEc,OAAO,IAAI,EAAAmC,sBAAA,GAAAjD,iBAAiB,CAACe,aAAa,cAAAkC,sBAAA,uBAA/BA,sBAAA,CAAiCrD,MAAM,IAAG,CAAC,CAAC,EAAE;QACjH;QACA,IAAIuB,kBAAkB,CAACpB,WAAW,CAACiB,IAAI,CAAC,EAAE;UACxC,OAAOc,mBAAmB,CAAC,CAAC;QAC9B;QAEA,oBACEnD,OAAA;UAAK4B,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7C7B,OAAA;YAAK4B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACvDT,WAAW,CAACuE;UAAiB;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;IACF;;IAEA;IACA,IAAId,WAAW,CAAC8C,OAAO,IAAI9C,WAAW,CAACiB,IAAI,KAAKa,SAAS,IAAI,EAAE7B,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEc,OAAO,IAAI,EAAAyB,sBAAA,GAAAvC,iBAAiB,CAACe,aAAa,cAAAwB,sBAAA,uBAA/BA,sBAAA,CAAiC3C,MAAM,IAAG,CAAC,CAAC,EAAE;MACzI;MACA,IAAIuB,kBAAkB,CAACpB,WAAW,CAACiB,IAAI,CAAC,EAAE;QACxC,OAAOc,mBAAmB,CAAC,CAAC;MAC9B;;MAEA;MACA,oBACEnD,OAAA;QAAK4B,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpE7B,OAAA;UAAK4B,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvDsE,IAAI,CAACC,SAAS,CAAChF,WAAW,CAACiB,IAAI,EAAE,IAAI,EAAE,CAAC;QAAC;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;;IAEA;IACA,IAAId,WAAW,CAAC8C,OAAO,IAAI,CAAC9C,WAAW,CAACiB,IAAI,IAAI,CAACjB,WAAW,CAACuE,iBAAiB,IAAI,CAACvE,WAAW,CAACiF,KAAK,EAAE;MACpG,OAAOlD,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;IACA,IAAI/B,WAAW,CAACiF,KAAK,EAAE;MACrB,oBACErG,OAAA;QAAK4B,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5C7B,OAAA;UAAG4B,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAET,WAAW,CAACiF;QAAK;UAAAtE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEV;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACElC,OAAA;IACE4B,SAAS,EAAE,QACTtB,MAAM,GAAG,aAAa,GAAG,eAAe,OAClC;IAAAuB,QAAA,eAER7B,OAAA;MACE4B,SAAS,EAAE,8BACTtB,MAAM,GACF,wCAAwC,GACxC,2CAA2C,EAC9C;MAAAuB,QAAA,GAGF,EAAE3B,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACuE,iBAAiB,CAAC,iBAC9D3F,OAAA;QAAG4B,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAE3B,OAAO,CAACoG;MAAO;QAAAvE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CACxD,EAGA,CAAC5B,MAAM,IAAIJ,OAAO,CAAC2E,OAAO,IAAIpC,KAAK,CAACC,OAAO,CAACxC,OAAO,CAAC2E,OAAO,CAAC,IAAI3E,OAAO,CAAC2E,OAAO,CAAC5D,MAAM,GAAG,CAAC,IAAI,CAACf,OAAO,CAACuE,YAAY,IAAI,CAACvE,OAAO,CAACsE,qBAAqB,IAAI,EAAEtE,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACuE,iBAAiB,CAAC,iBACvN3F,OAAA;QAAK4B,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClB3B,OAAO,CAACqG,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAvG,OAAA;UAAA6B,QAAA,gBACE7B,OAAA;YAAK4B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClD3B,OAAO,CAAC2E,OAAO,CAACmB,GAAG,CAAC,CAACrF,MAAM,EAAEuF,KAAK,kBACjClG,OAAA;cAEEwG,OAAO,EAAEA,CAAA,KAAM9F,oBAAoB,CAACC,MAAM,CAAE;cAC5CiB,SAAS,EAAE,kHACTpB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAAkB,QAAA,eAEH7B,OAAA;gBAAK4B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC7B,OAAA;kBAAK4B,SAAS,EAAE,kEACdpB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAAkB,QAAA,EACArB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,iBAClCX,OAAA;oBAAK4B,SAAS,EAAC,wBAAwB;oBAAC6E,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAA9E,QAAA,eAC3F7B,OAAA;sBAAM4G,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACLvB,MAAM;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDgE,KAAK;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACL1B,kBAAkB,CAACS,MAAM,GAAG,CAAC,iBAC5BjB,OAAA;YAAK4B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB7B,OAAA;cACEwG,OAAO,EAAExF,oBAAqB;cAC9BY,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAACrB,kBAAkB,CAACS,MAAM,EAAC,GAC/C;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDlC,OAAA;YAAK4B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACAlC,OAAA;UAAK4B,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7C3B,OAAO,CAAC2E,OAAO,CAACmB,GAAG,CAAC,CAACrF,MAAM,EAAEuF,KAAK,kBACjClG,OAAA;YAEEwG,OAAO,EAAEA,CAAA,KAAMjE,wBAAwB,CAAC5B,MAAM,CAAE;YAChDiB,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/MlB;UAAM,GAJFuF,KAAK;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhC,OAAO,CAAC8G,WAAW,IAAI9G,OAAO,CAAC+G,UAAU,iBACxCjH,OAAA;UAAK4B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,OACrC,EAAC3B,OAAO,CAAC8G,WAAW,EAAC,MAAI,EAAC9G,OAAO,CAAC+G,UAAU;QAAA;UAAAlF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAAC5B,MAAM,IAAIJ,OAAO,CAACuE,YAAY,IAAIvE,OAAO,CAACsE,qBAAqB,IAAItE,OAAO,CAAC2E,OAAO,IAAIpC,KAAK,CAACC,OAAO,CAACxC,OAAO,CAAC2E,OAAO,CAAC,IAAI3E,OAAO,CAAC2E,OAAO,CAAC5D,MAAM,GAAG,CAAC,IAAI,EAAEf,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACuE,iBAAiB,CAAC,iBACrN3F,OAAA;QAAK4B,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClB3B,OAAO,CAACqG,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAvG,OAAA;UAAA6B,QAAA,gBACE7B,OAAA;YAAK4B,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClD3B,OAAO,CAAC2E,OAAO,CAACmB,GAAG,CAAC,CAACrF,MAAM,EAAEuF,KAAK,kBACjClG,OAAA;cAEEwG,OAAO,EAAEA,CAAA,KAAM9F,oBAAoB,CAACC,MAAM,CAAE;cAC5CiB,SAAS,EAAE,kHACTpB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAAkB,QAAA,eAEH7B,OAAA;gBAAK4B,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC7B,OAAA;kBAAK4B,SAAS,EAAE,kEACdpB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAAkB,QAAA,EACArB,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,iBAClCX,OAAA;oBAAK4B,SAAS,EAAC,wBAAwB;oBAAC6E,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAA9E,QAAA,eAC3F7B,OAAA;sBAAM4G,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAhF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACLvB,MAAM;cAAA;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDgE,KAAK;cAAAnE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACL1B,kBAAkB,CAACS,MAAM,GAAG,CAAC,iBAC5BjB,OAAA;YAAK4B,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB7B,OAAA;cACEwG,OAAO,EAAExF,oBAAqB;cAC9BY,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAACrB,kBAAkB,CAACS,MAAM,EAAC,GAC/C;YAAA;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACDlC,OAAA;YAAK4B,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACAlC,OAAA;UAAK4B,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7C3B,OAAO,CAAC2E,OAAO,CAACmB,GAAG,CAAC,CAACrF,MAAM,EAAEuF,KAAK,kBACjClG,OAAA;YAEEwG,OAAO,EAAEA,CAAA,KAAMjE,wBAAwB,CAAC5B,MAAM,CAAE;YAChDiB,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/MlB;UAAM,GAJFuF,KAAK;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACAhC,OAAO,CAAC8G,WAAW,IAAI9G,OAAO,CAACgH,wBAAwB,iBACtDlH,OAAA;UAAK4B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,sBACtB,EAAC3B,OAAO,CAAC8G,WAAW,EAAC,MAAI,EAAC9G,OAAO,CAACgH,wBAAwB,EAC7EhH,OAAO,CAACiH,cAAc,GAAG,CAAC,iBACzBnH,OAAA;YAAM4B,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,GAClC,EAAC3B,OAAO,CAACiH,cAAc,EAAC,aAAW,EAACjH,OAAO,CAACiH,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,aAC/E;UAAA;YAAApF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAKA,CAAC5B,MAAM,IAAIJ,OAAO,CAACkH,WAAW,KAAK,gBAAgB,IAAIlH,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAAC8C,OAAO,iBACxGlE,OAAA;QAAK4B,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAC6E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA9E,QAAA,eACjF7B,OAAA;cAAM4G,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,+BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAC5B,MAAM,IAAIJ,OAAO,CAACkH,WAAW,KAAK,sBAAsB,IAAIlH,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAAC8C,OAAO,iBAC9GlE,OAAA;QAAK4B,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB7B,OAAA;UAAK4B,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnE7B,OAAA;YAAK4B,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC7B,OAAA;cAAK4B,SAAS,EAAC,cAAc;cAAC6E,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA9E,QAAA,eACjF7B,OAAA;gBAAM4G,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgB;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,4CAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLhC,OAAO,CAACmH,mBAAmB,iBAC1BrH,OAAA;UAAK4B,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/D7B,OAAA;YAAK4B,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC7B,OAAA;cAAK4B,SAAS,EAAC,4BAA4B;cAAC6E,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA9E,QAAA,eAC/F7B,OAAA;gBAAM4G,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAAhF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL,CAAC,eACNlC,OAAA;cAAI4B,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACNlC,OAAA;YAAK4B,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACvD3B,OAAO,CAACmH,mBAAmB,CAACC,OAAO,CAAC,6CAA6C,EAAE,EAAE;UAAC;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAAC5B,MAAM,IAAIJ,OAAO,CAACkH,WAAW,KAAK,gBAAgB,IAAIlH,OAAO,CAACkB,WAAW,IAAI,CAAClB,OAAO,CAACkB,WAAW,CAAC8C,OAAO,iBACzGlE,OAAA;QAAK4B,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAC6E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA9E,QAAA,eACjF7B,OAAA;cAAM4G,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,0BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAC5B,MAAM,IAAIJ,OAAO,CAACkH,WAAW,KAAK,gBAAgB,iBAClDpH,OAAA;QAAK4B,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAC6E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA9E,QAAA,eACjF7B,OAAA;cAAM4G,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,kBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAC5B,MAAM,IAAIJ,OAAO,CAACkH,WAAW,KAAK,uBAAuB,IAAIlH,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAAC8C,OAAO,iBAC/GlE,OAAA;QAAK4B,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAC6E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA9E,QAAA,eACjF7B,OAAA;cAAM4G,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,4CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAAC5B,MAAM,IAAIJ,OAAO,CAACkH,WAAW,KAAK,uBAAuB,IAAIlH,OAAO,CAACkB,WAAW,IAAI,CAAClB,OAAO,CAACkB,WAAW,CAAC8C,OAAO,iBAChHlE,OAAA;QAAK4B,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D7B,OAAA;UAAK4B,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC7B,OAAA;YAAK4B,SAAS,EAAC,cAAc;YAAC6E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA9E,QAAA,eACjF7B,OAAA;cAAM4G,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAhF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,8CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAOAhC,OAAO,CAACkB,WAAW,IAAIgC,iBAAiB,CAAClD,OAAO,CAACkB,WAAW,CAAC;IAAA;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsB3D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CAriBIJ,WAAW;AAAAsH,EAAA,GAAXtH,WAAW;AAuiBjB,eAAeA,WAAW;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}