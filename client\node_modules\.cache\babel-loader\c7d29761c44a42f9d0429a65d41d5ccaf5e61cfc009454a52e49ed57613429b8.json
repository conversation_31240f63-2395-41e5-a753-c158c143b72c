{"ast": null, "code": "var _jsxFileName = \"E:\\\\kumaran\\\\botnexus\\\\client\\\\src\\\\components\\\\ChatMessage.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatMessage = ({\n  message,\n  onOptionSelect,\n  onFormLinkTriggered\n}) => {\n  _s();\n  const isUser = message.role === 'user';\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\n\n  // Handle checkbox selection\n  const handleCheckboxToggle = option => {\n    const newSelected = selectedCheckboxes.includes(option) ? selectedCheckboxes.filter(item => item !== option) : [...selectedCheckboxes, option];\n    setSelectedCheckboxes(newSelected);\n  };\n\n  // Handle checkbox submission\n  const handleCheckboxSubmit = () => {\n    if (selectedCheckboxes.length > 0) {\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\n      setSelectedCheckboxes([]);\n    }\n  };\n\n  // Render leave balance with leave type buttons\n  const renderLeaveBalanceWithButtons = (apiResponse, formLinkingConfig = null) => {\n    var _message$formData, _message$formConfig;\n    const formId = ((_message$formData = message.formData) === null || _message$formData === void 0 ? void 0 : _message$formData._id) || ((_message$formConfig = message.formConfig) === null || _message$formConfig === void 0 ? void 0 : _message$formConfig._id);\n\n    // Default leave type configuration if none provided\n    const defaultLeaveConfig = {\n      enabled: true,\n      recordActions: [{\n        buttonText: 'Sick leave',\n        targetFormName: 'Leave Application',\n        fieldMapping: {\n          leaveType: 'sick leave'\n        },\n        conditions: [],\n        buttonStyle: 'primary',\n        autoSubmitOnClick: false,\n        autoTrigger: {\n          enabled: false,\n          delaySeconds: 2\n        }\n      }, {\n        buttonText: 'Casual leave',\n        targetFormName: 'Leave Application',\n        fieldMapping: {\n          leaveType: 'casual leave'\n        },\n        conditions: [],\n        buttonStyle: 'primary',\n        autoSubmitOnClick: false,\n        autoTrigger: {\n          enabled: false,\n          delaySeconds: 2\n        }\n      }, {\n        buttonText: 'Privilege Leave',\n        targetFormName: 'Leave Application',\n        fieldMapping: {\n          leaveType: 'Privilege Leave'\n        },\n        conditions: [],\n        buttonStyle: 'primary',\n        autoSubmitOnClick: false,\n        autoTrigger: {\n          enabled: false,\n          delaySeconds: 2\n        }\n      }]\n    };\n\n    // Use provided config or default\n    const activeConfig = formLinkingConfig || defaultLeaveConfig;\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-3\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"p-3 bg-blue-50 rounded-md mb-3\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"whitespace-pre-wrap text-sm text-blue-800\",\n          children: apiResponse.leaveBalance\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-wrap gap-2\",\n        children: activeConfig.recordActions.map((action, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleLeaveTypeClick(action),\n          className: \"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\",\n          children: action.buttonText\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 75,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Handle leave type button clicks\n  const handleLeaveTypeClick = action => {\n    console.log('🍃 Leave type clicked:', action.buttonText);\n\n    // Trigger the leave application form with pre-filled leave type\n    if (onFormLinkTriggered) {\n      onFormLinkTriggered({\n        targetForm: {\n          name: action.targetFormName\n        },\n        prefillData: action.fieldMapping,\n        buttonText: action.buttonText,\n        buttonStyle: action.buttonStyle\n      });\n    }\n  };\n\n  // Handle single option selection (for radio/select)\n  const handleSingleOptionSelect = option => {\n    onOptionSelect && onOptionSelect(option);\n  };\n  // Helper function to check if data is empty (comprehensive check)\n  const checkIfDataIsEmpty = data => {\n    if (!data) return true;\n    if (Array.isArray(data)) return data.length === 0;\n    if (typeof data === 'object' && data !== null) {\n      if (Object.keys(data).length === 0) return true;\n      // Check nested data structure\n      if (data.data) return checkIfDataIsEmpty(data.data);\n      // Check common array fields\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n      for (const field of arrayFields) {\n        if (data[field] && Array.isArray(data[field])) {\n          return data[field].length === 0;\n        }\n      }\n      // Check if all values are empty\n      return Object.values(data).every(val => val === null || val === undefined || val === '' || Array.isArray(val) && val.length === 0 || typeof val === 'object' && val !== null && Object.keys(val).length === 0);\n    }\n    return false;\n  };\n\n  // Helper function to render \"No data available\" message\n  const renderNoDataMessage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\",\n    children: /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-blue-800 text-sm font-medium\",\n      children: \"No data available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 136,\n    columnNumber: 5\n  }, this);\n\n  // Format API response for display\n  const formatApiResponse = apiResponse => {\n    var _message$formData2, _message$formData2$fo, _message$formConfig2, _message$formData3, _message$formConfig3, _message$formConfig3$, _message$apiResponse, _formLinkingConfig$re4;\n    if (!apiResponse) return null;\n\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\n    const formLinkingConfig = ((_message$formData2 = message.formData) === null || _message$formData2 === void 0 ? void 0 : (_message$formData2$fo = _message$formData2.formConfig) === null || _message$formData2$fo === void 0 ? void 0 : _message$formData2$fo.formLinking) || ((_message$formConfig2 = message.formConfig) === null || _message$formConfig2 === void 0 ? void 0 : _message$formConfig2.formLinking) || ((_message$formData3 = message.formData) === null || _message$formData3 === void 0 ? void 0 : _message$formData3.formLinking) || ((_message$formConfig3 = message.formConfig) === null || _message$formConfig3 === void 0 ? void 0 : (_message$formConfig3$ = _message$formConfig3.formConfig) === null || _message$formConfig3$ === void 0 ? void 0 : _message$formConfig3$.formLinking);\n\n    // Special handling for leave balance API responses - ALWAYS show leave types\n    if ((_message$apiResponse = message.apiResponse) !== null && _message$apiResponse !== void 0 && _message$apiResponse.leaveBalance) {\n      var _message$formConfig4;\n      console.log('🍃 Leave balance detected, showing with leave type buttons');\n      // Try to find form linking config in the message structure, but use default if not found\n      const leaveFormLinkingConfig = (_message$formConfig4 = message.formConfig) === null || _message$formConfig4 === void 0 ? void 0 : _message$formConfig4.formLinking;\n      return renderLeaveBalanceWithButtons(message.apiResponse, leaveFormLinkingConfig);\n    }\n\n    // For successful responses with data, check if we should display records with actions\n    if (apiResponse.success && apiResponse.data) {\n      var _message$conversation, _message$hybridFlow, _formLinkingConfig$re, _formLinkingConfig$re3;\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\n      const hasActiveConversationalFlow = message.isConversationalPhase || message.isHybridFlow || ((_message$conversation = message.conversationalFlow) === null || _message$conversation === void 0 ? void 0 : _message$conversation.isActive) || ((_message$hybridFlow = message.hybridFlow) === null || _message$hybridFlow === void 0 ? void 0 : _message$hybridFlow.isActive) || message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow);\n\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\n      if (formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re = formLinkingConfig.recordActions) === null || _formLinkingConfig$re === void 0 ? void 0 : _formLinkingConfig$re.length) > 0 && !hasActiveConversationalFlow) {\n        var _apiResponse$data, _formLinkingConfig$re2, _message$formData4, _message$formConfig5;\n        // Extract the actual data from the API response\n        const actualData = ((_apiResponse$data = apiResponse.data) === null || _apiResponse$data === void 0 ? void 0 : _apiResponse$data.data) || apiResponse.data;\n\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\n        const hasDisabledAutoTrigger = (_formLinkingConfig$re2 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re2 === void 0 ? void 0 : _formLinkingConfig$re2.some(action => {\n          var _action$autoTrigger;\n          return !((_action$autoTrigger = action.autoTrigger) !== null && _action$autoTrigger !== void 0 && _action$autoTrigger.enabled);\n        });\n        const formId = ((_message$formData4 = message.formData) === null || _message$formData4 === void 0 ? void 0 : _message$formData4._id) || ((_message$formConfig5 = message.formConfig) === null || _message$formConfig5 === void 0 ? void 0 : _message$formConfig5._id);\n\n        // Function to render records with interleaved apply buttons\n        const renderRecordsWithButtons = () => {\n          let records = [];\n\n          // Extract records from actualData\n          if (Array.isArray(actualData)) {\n            records = actualData;\n          } else if (actualData && typeof actualData === 'object') {\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\n            for (const field of arrayFields) {\n              if (actualData[field] && Array.isArray(actualData[field])) {\n                records = actualData[field];\n                break;\n              }\n            }\n            if (records.length === 0) {\n              records = [actualData];\n            }\n          }\n\n          // Check if records are empty using helper function\n          if (checkIfDataIsEmpty(records)) {\n            return renderNoDataMessage();\n          }\n\n          // Split formatted response by \"Record N:\" pattern\n          let formattedRecords = [];\n          if (apiResponse.formattedResponse) {\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\n            formattedRecords = recordSections.filter(section => section.trim());\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3\",\n            children: [records.map((record, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-4\",\n              children: [formattedRecords[index] && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-2 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: formattedRecords[index].trim()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 21\n              }, this), (formLinkingConfig === null || formLinkingConfig === void 0 ? void 0 : formLinkingConfig.enabled) && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2 ml-3\",\n                children: /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                  data: [record] // Pass only this specific record\n                  ,\n                  formId: formId,\n                  formLinkingConfig: formLinkingConfig,\n                  onFormLinkTriggered: onFormLinkTriggered,\n                  showOnlyButtons: true\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 21\n              }, this)]\n            }, index, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 17\n            }, this)), formattedRecords.length === 0 && apiResponse.formattedResponse && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mb-3 p-3 bg-blue-50 rounded-md\",\n                children: /*#__PURE__*/_jsxDEV(\"pre\", {\n                  className: \"whitespace-pre-wrap text-sm text-blue-800\",\n                  children: apiResponse.formattedResponse\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 19\n              }, this), !apiResponse.formattedResponse.includes('No data available') && /*#__PURE__*/_jsxDEV(RecordDisplayWithActions, {\n                data: actualData,\n                formId: formId,\n                formLinkingConfig: formLinkingConfig,\n                onFormLinkTriggered: onFormLinkTriggered,\n                showOnlyButtons: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 252,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this);\n        };\n        return renderRecordsWithButtons();\n      }\n\n      // If we have a formatted response but no form linking, display just the formatted response\n      if (apiResponse.formattedResponse && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re3 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re3 === void 0 ? void 0 : _formLinkingConfig$re3.length) > 0)) {\n        // Check if the underlying data is empty using helper function\n        if (checkIfDataIsEmpty(apiResponse.data)) {\n          return renderNoDataMessage();\n        }\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-3 p-3 bg-blue-50 rounded-md\",\n          children: /*#__PURE__*/_jsxDEV(\"pre\", {\n            className: \"whitespace-pre-wrap text-sm text-blue-800\",\n            children: apiResponse.formattedResponse\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 278,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this);\n      }\n    }\n\n    // Check if data is empty (array with no items or empty object)\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig !== null && formLinkingConfig !== void 0 && formLinkingConfig.enabled && ((_formLinkingConfig$re4 = formLinkingConfig.recordActions) === null || _formLinkingConfig$re4 === void 0 ? void 0 : _formLinkingConfig$re4.length) > 0)) {\n      // Check if data is empty using helper function\n      if (checkIfDataIsEmpty(apiResponse.data)) {\n        return renderNoDataMessage();\n      }\n\n      // Display data if not empty\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\",\n        children: /*#__PURE__*/_jsxDEV(\"pre\", {\n          className: \"whitespace-pre-wrap text-sm text-gray-700\",\n          children: JSON.stringify(apiResponse.data, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 296,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 295,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Handle successful response but no data property at all\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\n      return renderNoDataMessage();\n    }\n\n    // Error display\n    if (apiResponse.error) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-3 bg-red-50 rounded-md\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-sm\",\n          children: apiResponse.error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-[80%] p-3 rounded-lg ${isUser ? 'bg-blue-500 text-white rounded-br-none' : 'bg-gray-200 text-gray-800 rounded-bl-none'}`,\n      children: [!(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"whitespace-pre-wrap\",\n        children: message.content\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this), !isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 363,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 362,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 356,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 346,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 374,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 388,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-gray-500\",\n          children: [\"Step \", message.currentStep, \" of \", message.totalSteps]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this), !isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3\",\n        children: [message.fieldType === 'checkbox' ?\n        /*#__PURE__*/\n        // Checkbox field - allow multiple selections\n        _jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-2 max-w-xs mb-3\",\n            children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => handleCheckboxToggle(option),\n              className: `px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${selectedCheckboxes.includes(option) ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700' : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'}`,\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${selectedCheckboxes.includes(option) ? 'bg-white border-white' : 'bg-transparent border-gray-400'}`,\n                  children: selectedCheckboxes.includes(option) && /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 text-green-500\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M5 13l4 4L19 7\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 433,\n                      columnNumber: 31\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 432,\n                    columnNumber: 29\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 426,\n                  columnNumber: 25\n                }, this), option]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 23\n              }, this)\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 17\n          }, this), selectedCheckboxes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleCheckboxSubmit,\n              className: \"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\",\n              children: [\"Submit Selection (\", selectedCheckboxes.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 21\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Select multiple options and click Submit, or select one option and Submit\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 452,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 15\n        }, this) :\n        /*#__PURE__*/\n        // Radio/Select field - single selection\n        _jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 gap-2 max-w-xs\",\n          children: message.options.map((option, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleSingleOptionSelect(option),\n            className: \"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\",\n            children: option\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 19\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 458,\n          columnNumber: 15\n        }, this), message.currentStep && message.totalConversationalSteps && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"mt-2 text-xs text-blue-600\",\n          children: [\"Conversational Step \", message.currentStep, \" of \", message.totalConversationalSteps, message.totalFormSteps > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"ml-2 text-gray-500\",\n            children: [\"(\", message.totalFormSteps, \" form field\", message.totalFormSteps !== 1 ? 's' : '', \" remaining)\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 474,\n            columnNumber: 19\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 489,\n            columnNumber: 15\n          }, this), \"Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 488,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 487,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-3 h-3 mr-1\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 13l4 4L19 7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 503,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 17\n            }, this), \"Leave Application Submitted Successfully\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this), message.updatedLeaveBalance && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-3 bg-blue-50 rounded-lg border border-blue-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center mb-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4 mr-2 text-blue-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 512,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-blue-800\",\n              children: \"Updated Leave Balance\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 514,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-sm text-blue-700 whitespace-pre-wrap\",\n            children: message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 499,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 529,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 528,\n            columnNumber: 15\n          }, this), \"Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 527,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 526,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'form_cancelled' && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 15\n          }, this), \"Form Cancelled\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 538,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M5 13l4 4L19 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 553,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 552,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Form Submitted Successfully\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 551,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 11\n      }, this), !isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-3 h-3 mr-1\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 564,\n            columnNumber: 15\n          }, this), \"\\uD83D\\uDD04 Hybrid Form Submission Failed\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 563,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 562,\n        columnNumber: 11\n      }, this), message.apiResponse && formatApiResponse(message.apiResponse)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 326,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 321,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatMessage, \"cE9qr9Un/J6bIavRReyYdfaIaEo=\");\n_c = ChatMessage;\nexport default ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");", "map": {"version": 3, "names": ["React", "useState", "RecordDisplayWithActions", "jsxDEV", "_jsxDEV", "ChatMessage", "message", "onOptionSelect", "onFormLinkTriggered", "_s", "isUser", "role", "selectedCheckboxes", "setSelectedCheckboxes", "handleCheckboxToggle", "option", "newSelected", "includes", "filter", "item", "handleCheckboxSubmit", "length", "join", "renderLeaveBalanceWithButtons", "apiResponse", "formLinkingConfig", "_message$formData", "_message$formConfig", "formId", "formData", "_id", "formConfig", "defaultLeaveConfig", "enabled", "recordActions", "buttonText", "targetFormName", "fieldMapping", "leaveType", "conditions", "buttonStyle", "autoSubmitOnClick", "autoTrigger", "delaySeconds", "activeConfig", "className", "children", "leaveBalance", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "action", "index", "onClick", "handleLeaveTypeClick", "console", "log", "targetForm", "name", "prefillData", "handleSingleOptionSelect", "checkIfDataIsEmpty", "data", "Array", "isArray", "Object", "keys", "arrayFields", "field", "values", "every", "val", "undefined", "renderNoDataMessage", "formatApiResponse", "_message$formData2", "_message$formData2$fo", "_message$formConfig2", "_message$formData3", "_message$formConfig3", "_message$formConfig3$", "_message$apiResponse", "_formLinkingConfig$re4", "formLinking", "_message$formConfig4", "leaveFormLinkingConfig", "success", "_message$conversation", "_message$hybridFlow", "_formLinkingConfig$re", "_formLinkingConfig$re3", "hasActiveConversationalFlow", "isConversationalPhase", "isHybridFlow", "conversationalFlow", "isActive", "hybridFlow", "options", "_apiResponse$data", "_formLinkingConfig$re2", "_message$formData4", "_message$formConfig5", "actualData", "hasDisabledAutoTrigger", "some", "_action$autoTrigger", "renderRecordsWithButtons", "records", "formattedRecords", "formattedResponse", "recordSections", "split", "section", "trim", "record", "showOnlyButtons", "JSON", "stringify", "error", "content", "fieldType", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "currentStep", "totalSteps", "totalConversationalSteps", "totalFormSteps", "queryIntent", "updatedLeaveBalance", "replace", "_c", "$RefreshReg$"], "sources": ["E:/kumaran/botnexus/client/src/components/ChatMessage.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport RecordDisplayWithActions from './RecordDisplayWithActions';\r\n\r\nconst ChatMessage = ({ message, onOptionSelect, onFormLinkTriggered }) => {\r\n  const isUser = message.role === 'user';\r\n  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);\r\n\r\n  // Handle checkbox selection\r\n  const handleCheckboxToggle = (option) => {\r\n    const newSelected = selectedCheckboxes.includes(option)\r\n      ? selectedCheckboxes.filter(item => item !== option)\r\n      : [...selectedCheckboxes, option];\r\n    setSelectedCheckboxes(newSelected);\r\n  };\r\n\r\n  // Handle checkbox submission\r\n  const handleCheckboxSubmit = () => {\r\n    if (selectedCheckboxes.length > 0) {\r\n      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));\r\n      setSelectedCheckboxes([]);\r\n    }\r\n  };\r\n\r\n  // Render leave balance with leave type buttons\r\n  const renderLeaveBalanceWithButtons = (apiResponse, formLinkingConfig = null) => {\r\n    const formId = message.formData?._id || message.formConfig?._id;\r\n\r\n    // Default leave type configuration if none provided\r\n    const defaultLeaveConfig = {\r\n      enabled: true,\r\n      recordActions: [\r\n        {\r\n          buttonText: 'Sick leave',\r\n          targetFormName: 'Leave Application',\r\n          fieldMapping: { leaveType: 'sick leave' },\r\n          conditions: [],\r\n          buttonStyle: 'primary',\r\n          autoSubmitOnClick: false,\r\n          autoTrigger: { enabled: false, delaySeconds: 2 }\r\n        },\r\n        {\r\n          buttonText: 'Casual leave',\r\n          targetFormName: 'Leave Application',\r\n          fieldMapping: { leaveType: 'casual leave' },\r\n          conditions: [],\r\n          buttonStyle: 'primary',\r\n          autoSubmitOnClick: false,\r\n          autoTrigger: { enabled: false, delaySeconds: 2 }\r\n        },\r\n        {\r\n          buttonText: 'Privilege Leave',\r\n          targetFormName: 'Leave Application',\r\n          fieldMapping: { leaveType: 'Privilege Leave' },\r\n          conditions: [],\r\n          buttonStyle: 'primary',\r\n          autoSubmitOnClick: false,\r\n          autoTrigger: { enabled: false, delaySeconds: 2 }\r\n        }\r\n      ]\r\n    };\r\n\r\n    // Use provided config or default\r\n    const activeConfig = formLinkingConfig || defaultLeaveConfig;\r\n\r\n    return (\r\n      <div className=\"mt-3\">\r\n        {/* Display the leave balance content */}\r\n        <div className=\"p-3 bg-blue-50 rounded-md mb-3\">\r\n          <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n            {apiResponse.leaveBalance}\r\n          </pre>\r\n        </div>\r\n\r\n        {/* Always display leave type buttons */}\r\n        <div className=\"flex flex-wrap gap-2\">\r\n          {activeConfig.recordActions.map((action, index) => (\r\n            <button\r\n              key={index}\r\n              onClick={() => handleLeaveTypeClick(action)}\r\n              className=\"px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\r\n            >\r\n              {action.buttonText}\r\n            </button>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    );\r\n  };\r\n\r\n  // Handle leave type button clicks\r\n  const handleLeaveTypeClick = (action) => {\r\n    console.log('🍃 Leave type clicked:', action.buttonText);\r\n\r\n    // Trigger the leave application form with pre-filled leave type\r\n    if (onFormLinkTriggered) {\r\n      onFormLinkTriggered({\r\n        targetForm: { name: action.targetFormName },\r\n        prefillData: action.fieldMapping,\r\n        buttonText: action.buttonText,\r\n        buttonStyle: action.buttonStyle\r\n      });\r\n    }\r\n  };\r\n\r\n  // Handle single option selection (for radio/select)\r\n  const handleSingleOptionSelect = (option) => {\r\n    onOptionSelect && onOptionSelect(option);\r\n  };\r\n  // Helper function to check if data is empty (comprehensive check)\r\n  const checkIfDataIsEmpty = (data) => {\r\n    if (!data) return true;\r\n    if (Array.isArray(data)) return data.length === 0;\r\n    if (typeof data === 'object' && data !== null) {\r\n      if (Object.keys(data).length === 0) return true;\r\n      // Check nested data structure\r\n      if (data.data) return checkIfDataIsEmpty(data.data);\r\n      // Check common array fields\r\n      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n      for (const field of arrayFields) {\r\n        if (data[field] && Array.isArray(data[field])) {\r\n          return data[field].length === 0;\r\n        }\r\n      }\r\n      // Check if all values are empty\r\n      return Object.values(data).every(val => \r\n        val === null || val === undefined || val === '' || \r\n        (Array.isArray(val) && val.length === 0) ||\r\n        (typeof val === 'object' && val !== null && Object.keys(val).length === 0)\r\n      );\r\n    }\r\n    return false;\r\n  };\r\n\r\n  // Helper function to render \"No data available\" message\r\n  const renderNoDataMessage = () => (\r\n    <div className=\"mt-2 p-3 bg-blue-50 rounded-md border border-blue-200\">\r\n      <p className=\"text-blue-800 text-sm font-medium\">No data available</p>\r\n    </div>\r\n  );\r\n\r\n  // Format API response for display\r\n  const formatApiResponse = (apiResponse) => {\r\n    if (!apiResponse) return null;\r\n    \r\n    // Try multiple paths to find form linking config (define at top level for access throughout function)\r\n    const formLinkingConfig = message.formData?.formConfig?.formLinking ||\r\n                             message.formConfig?.formLinking ||\r\n                             message.formData?.formLinking ||\r\n                             message.formConfig?.formConfig?.formLinking;\r\n\r\n    // Special handling for leave balance API responses - ALWAYS show leave types\r\n    if (message.apiResponse?.leaveBalance) {\r\n      console.log('🍃 Leave balance detected, showing with leave type buttons');\r\n      // Try to find form linking config in the message structure, but use default if not found\r\n      const leaveFormLinkingConfig = message.formConfig?.formLinking;\r\n      return renderLeaveBalanceWithButtons(message.apiResponse, leaveFormLinkingConfig);\r\n    }\r\n    \r\n    // For successful responses with data, check if we should display records with actions\r\n    if (apiResponse.success && apiResponse.data) {\r\n      \r\n      // Check if this message has active conversational flow - if so, prioritize it over form linking\r\n      const hasActiveConversationalFlow = message.isConversationalPhase || \r\n                                         message.isHybridFlow || \r\n                                         message.conversationalFlow?.isActive ||\r\n                                         message.hybridFlow?.isActive ||\r\n                                         (message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow));\r\n      \r\n      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW\r\n      if (formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0 && !hasActiveConversationalFlow) {\r\n       \r\n        // Extract the actual data from the API response\r\n        const actualData = apiResponse.data?.data || apiResponse.data;\r\n        \r\n        // Check if any action has autoTrigger disabled - if so, show Apply buttons\r\n        const hasDisabledAutoTrigger = formLinkingConfig.recordActions?.some(action => \r\n          !action.autoTrigger?.enabled\r\n        );\r\n        \r\n        const formId = message.formData?._id || message.formConfig?._id;\r\n        \r\n        // Function to render records with interleaved apply buttons\r\n        const renderRecordsWithButtons = () => {\r\n          let records = [];\r\n          \r\n          // Extract records from actualData\r\n          if (Array.isArray(actualData)) {\r\n            records = actualData;\r\n          } else if (actualData && typeof actualData === 'object') {\r\n            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];\r\n            for (const field of arrayFields) {\r\n              if (actualData[field] && Array.isArray(actualData[field])) {\r\n                records = actualData[field];\r\n                break;\r\n              }\r\n            }\r\n            if (records.length === 0) {\r\n              records = [actualData];\r\n            }\r\n          }\r\n          \r\n          // Check if records are empty using helper function\r\n          if (checkIfDataIsEmpty(records)) {\r\n            return renderNoDataMessage();\r\n          }\r\n\r\n          // Split formatted response by \"Record N:\" pattern\r\n          let formattedRecords = [];\r\n          if (apiResponse.formattedResponse) {\r\n            const recordSections = apiResponse.formattedResponse.split(/(?=Record \\d+:)/);\r\n            formattedRecords = recordSections.filter(section => section.trim());\r\n          }\r\n\r\n          return (\r\n            <div className=\"mt-3\">\r\n              {records.map((record, index) => (\r\n                <div key={index} className=\"mb-4\">\r\n                  {/* Display the formatted response for this record */}\r\n                  {formattedRecords[index] && (\r\n                    <div className=\"mb-2 p-3 bg-blue-50 rounded-md\">\r\n                      <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                        {formattedRecords[index].trim()}\r\n                      </pre>\r\n                    </div>\r\n                  )}\r\n                  \r\n                  {/* Apply button for this specific record */}\r\n                  {formLinkingConfig?.enabled && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && (\r\n                    <div className=\"flex flex-wrap gap-2 ml-3\">\r\n                      <RecordDisplayWithActions\r\n                        data={[record]} // Pass only this specific record\r\n                        formId={formId}\r\n                        formLinkingConfig={formLinkingConfig}\r\n                        onFormLinkTriggered={onFormLinkTriggered}\r\n                        showOnlyButtons={true}\r\n                      />\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              ))}\r\n              \r\n              {/* Fallback: if we can't split the formatted response properly */}\r\n              {formattedRecords.length === 0 && apiResponse.formattedResponse && (\r\n                <div>\r\n                  <div className=\"mb-3 p-3 bg-blue-50 rounded-md\">\r\n                    <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n                      {apiResponse.formattedResponse}\r\n                    </pre>\r\n                  </div>\r\n                  {/* Only show Apply buttons if the response is not \"No data available\" */}\r\n                  {!apiResponse.formattedResponse.includes('No data available') && (\r\n                    <RecordDisplayWithActions\r\n                      data={actualData}\r\n                      formId={formId}\r\n                      formLinkingConfig={formLinkingConfig}\r\n                      onFormLinkTriggered={onFormLinkTriggered}\r\n                      showOnlyButtons={true}\r\n                    />\r\n                  )}\r\n                </div>\r\n              )}\r\n            </div>\r\n          );\r\n        };\r\n\r\n        return renderRecordsWithButtons();\r\n      }\r\n        \r\n      // If we have a formatted response but no form linking, display just the formatted response\r\n      if (apiResponse.formattedResponse && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n        // Check if the underlying data is empty using helper function\r\n        if (checkIfDataIsEmpty(apiResponse.data)) {\r\n          return renderNoDataMessage();\r\n        }\r\n        \r\n        return (\r\n          <div className=\"mt-3 p-3 bg-blue-50 rounded-md\">\r\n            <pre className=\"whitespace-pre-wrap text-sm text-blue-800\">\r\n              {apiResponse.formattedResponse}\r\n            </pre>\r\n          </div>\r\n        );\r\n      }\r\n    }\r\n    \r\n    // Check if data is empty (array with no items or empty object)\r\n    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {\r\n      // Check if data is empty using helper function\r\n      if (checkIfDataIsEmpty(apiResponse.data)) {\r\n        return renderNoDataMessage();\r\n      }\r\n      \r\n      // Display data if not empty\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60\">\r\n          <pre className=\"whitespace-pre-wrap text-sm text-gray-700\">\r\n            {JSON.stringify(apiResponse.data, null, 2)}\r\n          </pre>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    // Handle successful response but no data property at all\r\n    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {\r\n      return renderNoDataMessage();\r\n    }\r\n    \r\n    // Error display\r\n    if (apiResponse.error) {\r\n      return (\r\n        <div className=\"mt-2 p-3 bg-red-50 rounded-md\">\r\n          <p className=\"text-red-600 text-sm\">{apiResponse.error}</p>\r\n        </div>\r\n      );\r\n    }\r\n    \r\n    return null;\r\n  };\r\n\r\n  return (\r\n    <div\r\n      className={`flex ${\r\n        isUser ? 'justify-end' : 'justify-start'\r\n      } mb-4`}\r\n    >\r\n      <div\r\n        className={`max-w-[80%] p-3 rounded-lg ${\r\n          isUser\r\n            ? 'bg-blue-500 text-white rounded-br-none'\r\n            : 'bg-gray-200 text-gray-800 rounded-bl-none'\r\n        }`}\r\n      >\r\n        {/* Display message content only if we don't have a formatted API response */}\r\n        {!(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <p className=\"whitespace-pre-wrap\">{message.content}</p>\r\n        )}\r\n        \r\n        {/* Display conversational form options as buttons */}\r\n        {!isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalSteps && (\r\n              <div className=\"mt-2 text-xs text-gray-500\">\r\n                Step {message.currentStep} of {message.totalSteps}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form options for conversational phase */}\r\n        {!isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && (\r\n          <div className=\"mt-3\">\r\n            {message.fieldType === 'checkbox' ? (\r\n              // Checkbox field - allow multiple selections\r\n              <div>\r\n                <div className=\"grid grid-cols-1 gap-2 max-w-xs mb-3\">\r\n                  {message.options.map((option, index) => (\r\n                    <button\r\n                      key={index}\r\n                      onClick={() => handleCheckboxToggle(option)}\r\n                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${\r\n                        selectedCheckboxes.includes(option)\r\n                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'\r\n                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'\r\n                      }`}\r\n                    >\r\n                      <div className=\"flex items-center\">\r\n                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${\r\n                          selectedCheckboxes.includes(option)\r\n                            ? 'bg-white border-white'\r\n                            : 'bg-transparent border-gray-400'\r\n                        }`}>\r\n                          {selectedCheckboxes.includes(option) && (\r\n                            <svg className=\"w-3 h-3 text-green-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                            </svg>\r\n                          )}\r\n                        </div>\r\n                        {option}\r\n                      </div>\r\n                    </button>\r\n                  ))}\r\n                </div>\r\n                {selectedCheckboxes.length > 0 && (\r\n                  <div className=\"mb-2\">\r\n                    <button\r\n                      onClick={handleCheckboxSubmit}\r\n                      className=\"px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200\"\r\n                    >\r\n                      Submit Selection ({selectedCheckboxes.length})\r\n                    </button>\r\n                  </div>\r\n                )}\r\n                <div className=\"text-xs text-gray-500\">\r\n                  Select multiple options and click Submit, or select one option and Submit\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              // Radio/Select field - single selection\r\n              <div className=\"grid grid-cols-1 gap-2 max-w-xs\">\r\n                {message.options.map((option, index) => (\r\n                  <button\r\n                    key={index}\r\n                    onClick={() => handleSingleOptionSelect(option)}\r\n                    className=\"px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105\"\r\n                  >\r\n                    {option}\r\n                  </button>\r\n                ))}\r\n              </div>\r\n            )}\r\n            {message.currentStep && message.totalConversationalSteps && (\r\n              <div className=\"mt-2 text-xs text-blue-600\">\r\n                Conversational Step {message.currentStep} of {message.totalConversationalSteps}\r\n                {message.totalFormSteps > 0 && (\r\n                  <span className=\"ml-2 text-gray-500\">\r\n                    ({message.totalFormSteps} form field{message.totalFormSteps !== 1 ? 's' : ''} remaining)\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n\r\n              \r\n        {/* Display form completion indicator - only show success if form was actually submitted successfully */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display leave form completion with updated balance */}\r\n        {!isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2\">\r\n            <div className=\"p-2 bg-green-100 rounded text-xs text-green-700 mb-2\">\r\n              <div className=\"flex items-center\">\r\n                <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n                </svg>\r\n                Leave Application Submitted Successfully\r\n              </div>\r\n            </div>\r\n            {message.updatedLeaveBalance && (\r\n              <div className=\"p-3 bg-blue-50 rounded-lg border border-blue-200\">\r\n                <div className=\"flex items-center mb-2\">\r\n                  <svg className=\"w-4 h-4 mr-2 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\" />\r\n                  </svg>\r\n                  <h4 className=\"text-sm font-medium text-blue-800\">Updated Leave Balance</h4>\r\n                </div>\r\n                <div className=\"text-sm text-blue-700 whitespace-pre-wrap\">\r\n                  {message.updatedLeaveBalance.replace('Here\\'s your leave balance information:\\n\\n', '')}\r\n                </div>\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* Display form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n        \r\n        {/* Display form cancellation indicator */}\r\n        {!isUser && message.queryIntent === 'form_cancelled' && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              Form Cancelled\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form completion indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-green-100 rounded text-xs text-green-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\r\n              </svg>\r\n              🔄 Form Submitted Successfully\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Display hybrid form submission failure indicator */}\r\n        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && (\r\n          <div className=\"mt-2 p-2 bg-red-100 rounded text-xs text-red-700\">\r\n            <div className=\"flex items-center\">\r\n              <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n              </svg>\r\n              🔄 Hybrid Form Submission Failed\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n\r\n        \r\n        {/* Leave balance is now included in the message content, no separate display needed */}\r\n\r\n        {/* Display API response if available */}\r\n        {message.apiResponse && formatApiResponse(message.apiResponse)}\r\n        \r\n        {/* Display form data if available (for debugging) */}\r\n        {/* {message.formData && process.env.NODE_ENV === 'development' && (\r\n          <div className=\"mt-2 p-2 bg-gray-100 rounded text-xs text-gray-500\">\r\n            <p>Form data submitted</p>\r\n          </div>\r\n        )} */}\r\n        \r\n        {/* Display query intent if available (for debugging) */}\r\n        {/* {message.queryIntent && process.env.NODE_ENV === 'development' && (\r\n          <div className={`mt-2 p-2 rounded text-xs ${\r\n            message.queryIntent === 'form' \r\n              ? 'bg-blue-100 text-blue-700' \r\n              : 'bg-green-100 text-green-700'\r\n          }`}>\r\n            <p>Query intent: <strong>{message.queryIntent}</strong></p>\r\n            {message.formData && message.queryIntent === 'form' && (\r\n              <p>Form detected: {message.formData.name}</p>\r\n            )}\r\n          </div>\r\n        )} */}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ChatMessage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,wBAAwB,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,WAAW,GAAGA,CAAC;EAAEC,OAAO;EAAEC,cAAc;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACxE,MAAMC,MAAM,GAAGJ,OAAO,CAACK,IAAI,KAAK,MAAM;EACtC,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;;EAEhE;EACA,MAAMa,oBAAoB,GAAIC,MAAM,IAAK;IACvC,MAAMC,WAAW,GAAGJ,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GACnDH,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,KAAKJ,MAAM,CAAC,GAClD,CAAC,GAAGH,kBAAkB,EAAEG,MAAM,CAAC;IACnCF,qBAAqB,CAACG,WAAW,CAAC;EACpC,CAAC;;EAED;EACA,MAAMI,oBAAoB,GAAGA,CAAA,KAAM;IACjC,IAAIR,kBAAkB,CAACS,MAAM,GAAG,CAAC,EAAE;MACjCd,cAAc,IAAIA,cAAc,CAACK,kBAAkB,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC;MAC/DT,qBAAqB,CAAC,EAAE,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMU,6BAA6B,GAAGA,CAACC,WAAW,EAAEC,iBAAiB,GAAG,IAAI,KAAK;IAAA,IAAAC,iBAAA,EAAAC,mBAAA;IAC/E,MAAMC,MAAM,GAAG,EAAAF,iBAAA,GAAApB,OAAO,CAACuB,QAAQ,cAAAH,iBAAA,uBAAhBA,iBAAA,CAAkBI,GAAG,OAAAH,mBAAA,GAAIrB,OAAO,CAACyB,UAAU,cAAAJ,mBAAA,uBAAlBA,mBAAA,CAAoBG,GAAG;;IAE/D;IACA,MAAME,kBAAkB,GAAG;MACzBC,OAAO,EAAE,IAAI;MACbC,aAAa,EAAE,CACb;QACEC,UAAU,EAAE,YAAY;QACxBC,cAAc,EAAE,mBAAmB;QACnCC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAa,CAAC;QACzCC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,SAAS;QACtBC,iBAAiB,EAAE,KAAK;QACxBC,WAAW,EAAE;UAAET,OAAO,EAAE,KAAK;UAAEU,YAAY,EAAE;QAAE;MACjD,CAAC,EACD;QACER,UAAU,EAAE,cAAc;QAC1BC,cAAc,EAAE,mBAAmB;QACnCC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAe,CAAC;QAC3CC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,SAAS;QACtBC,iBAAiB,EAAE,KAAK;QACxBC,WAAW,EAAE;UAAET,OAAO,EAAE,KAAK;UAAEU,YAAY,EAAE;QAAE;MACjD,CAAC,EACD;QACER,UAAU,EAAE,iBAAiB;QAC7BC,cAAc,EAAE,mBAAmB;QACnCC,YAAY,EAAE;UAAEC,SAAS,EAAE;QAAkB,CAAC;QAC9CC,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,SAAS;QACtBC,iBAAiB,EAAE,KAAK;QACxBC,WAAW,EAAE;UAAET,OAAO,EAAE,KAAK;UAAEU,YAAY,EAAE;QAAE;MACjD,CAAC;IAEL,CAAC;;IAED;IACA,MAAMC,YAAY,GAAGnB,iBAAiB,IAAIO,kBAAkB;IAE5D,oBACE5B,OAAA;MAAKyC,SAAS,EAAC,MAAM;MAAAC,QAAA,gBAEnB1C,OAAA;QAAKyC,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAC7C1C,OAAA;UAAKyC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvDtB,WAAW,CAACuB;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN/C,OAAA;QAAKyC,SAAS,EAAC,sBAAsB;QAAAC,QAAA,EAClCF,YAAY,CAACV,aAAa,CAACkB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAC5ClD,OAAA;UAEEmD,OAAO,EAAEA,CAAA,KAAMC,oBAAoB,CAACH,MAAM,CAAE;UAC5CR,SAAS,EAAC,yJAAyJ;UAAAC,QAAA,EAElKO,MAAM,CAAClB;QAAU,GAJbmB,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKJ,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMK,oBAAoB,GAAIH,MAAM,IAAK;IACvCI,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEL,MAAM,CAAClB,UAAU,CAAC;;IAExD;IACA,IAAI3B,mBAAmB,EAAE;MACvBA,mBAAmB,CAAC;QAClBmD,UAAU,EAAE;UAAEC,IAAI,EAAEP,MAAM,CAACjB;QAAe,CAAC;QAC3CyB,WAAW,EAAER,MAAM,CAAChB,YAAY;QAChCF,UAAU,EAAEkB,MAAM,CAAClB,UAAU;QAC7BK,WAAW,EAAEa,MAAM,CAACb;MACtB,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMsB,wBAAwB,GAAI/C,MAAM,IAAK;IAC3CR,cAAc,IAAIA,cAAc,CAACQ,MAAM,CAAC;EAC1C,CAAC;EACD;EACA,MAAMgD,kBAAkB,GAAIC,IAAI,IAAK;IACnC,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;IACtB,IAAIC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE,OAAOA,IAAI,CAAC3C,MAAM,KAAK,CAAC;IACjD,IAAI,OAAO2C,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;MAC7C,IAAIG,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC3C,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;MAC/C;MACA,IAAI2C,IAAI,CAACA,IAAI,EAAE,OAAOD,kBAAkB,CAACC,IAAI,CAACA,IAAI,CAAC;MACnD;MACA,MAAMK,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;MAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;QAC/B,IAAIL,IAAI,CAACM,KAAK,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACF,IAAI,CAACM,KAAK,CAAC,CAAC,EAAE;UAC7C,OAAON,IAAI,CAACM,KAAK,CAAC,CAACjD,MAAM,KAAK,CAAC;QACjC;MACF;MACA;MACA,OAAO8C,MAAM,CAACI,MAAM,CAACP,IAAI,CAAC,CAACQ,KAAK,CAACC,GAAG,IAClCA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKC,SAAS,IAAID,GAAG,KAAK,EAAE,IAC9CR,KAAK,CAACC,OAAO,CAACO,GAAG,CAAC,IAAIA,GAAG,CAACpD,MAAM,KAAK,CAAE,IACvC,OAAOoD,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,IAAIN,MAAM,CAACC,IAAI,CAACK,GAAG,CAAC,CAACpD,MAAM,KAAK,CAC1E,CAAC;IACH;IACA,OAAO,KAAK;EACd,CAAC;;EAED;EACA,MAAMsD,mBAAmB,GAAGA,CAAA,kBAC1BvE,OAAA;IAAKyC,SAAS,EAAC,uDAAuD;IAAAC,QAAA,eACpE1C,OAAA;MAAGyC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACnE,CACN;;EAED;EACA,MAAMyB,iBAAiB,GAAIpD,WAAW,IAAK;IAAA,IAAAqD,kBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,kBAAA,EAAAC,oBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,sBAAA;IACzC,IAAI,CAAC5D,WAAW,EAAE,OAAO,IAAI;;IAE7B;IACA,MAAMC,iBAAiB,GAAG,EAAAoD,kBAAA,GAAAvE,OAAO,CAACuB,QAAQ,cAAAgD,kBAAA,wBAAAC,qBAAA,GAAhBD,kBAAA,CAAkB9C,UAAU,cAAA+C,qBAAA,uBAA5BA,qBAAA,CAA8BO,WAAW,OAAAN,oBAAA,GAC1CzE,OAAO,CAACyB,UAAU,cAAAgD,oBAAA,uBAAlBA,oBAAA,CAAoBM,WAAW,OAAAL,kBAAA,GAC/B1E,OAAO,CAACuB,QAAQ,cAAAmD,kBAAA,uBAAhBA,kBAAA,CAAkBK,WAAW,OAAAJ,oBAAA,GAC7B3E,OAAO,CAACyB,UAAU,cAAAkD,oBAAA,wBAAAC,qBAAA,GAAlBD,oBAAA,CAAoBlD,UAAU,cAAAmD,qBAAA,uBAA9BA,qBAAA,CAAgCG,WAAW;;IAEpE;IACA,KAAAF,oBAAA,GAAI7E,OAAO,CAACkB,WAAW,cAAA2D,oBAAA,eAAnBA,oBAAA,CAAqBpC,YAAY,EAAE;MAAA,IAAAuC,oBAAA;MACrC7B,OAAO,CAACC,GAAG,CAAC,4DAA4D,CAAC;MACzE;MACA,MAAM6B,sBAAsB,IAAAD,oBAAA,GAAGhF,OAAO,CAACyB,UAAU,cAAAuD,oBAAA,uBAAlBA,oBAAA,CAAoBD,WAAW;MAC9D,OAAO9D,6BAA6B,CAACjB,OAAO,CAACkB,WAAW,EAAE+D,sBAAsB,CAAC;IACnF;;IAEA;IACA,IAAI/D,WAAW,CAACgE,OAAO,IAAIhE,WAAW,CAACwC,IAAI,EAAE;MAAA,IAAAyB,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;MAE3C;MACA,MAAMC,2BAA2B,GAAGvF,OAAO,CAACwF,qBAAqB,IAC9BxF,OAAO,CAACyF,YAAY,MAAAN,qBAAA,GACpBnF,OAAO,CAAC0F,kBAAkB,cAAAP,qBAAA,uBAA1BA,qBAAA,CAA4BQ,QAAQ,OAAAP,mBAAA,GACpCpF,OAAO,CAAC4F,UAAU,cAAAR,mBAAA,uBAAlBA,mBAAA,CAAoBO,QAAQ,KAC3B3F,OAAO,CAAC6F,OAAO,IAAI7F,OAAO,CAAC6F,OAAO,CAAC9E,MAAM,GAAG,CAAC,KAAKf,OAAO,CAACwF,qBAAqB,IAAIxF,OAAO,CAACyF,YAAY,CAAE;;MAE7I;MACA,IAAItE,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEQ,OAAO,IAAI,EAAA0D,qBAAA,GAAAlE,iBAAiB,CAACS,aAAa,cAAAyD,qBAAA,uBAA/BA,qBAAA,CAAiCtE,MAAM,IAAG,CAAC,IAAI,CAACwE,2BAA2B,EAAE;QAAA,IAAAO,iBAAA,EAAAC,sBAAA,EAAAC,kBAAA,EAAAC,oBAAA;QAE7G;QACA,MAAMC,UAAU,GAAG,EAAAJ,iBAAA,GAAA5E,WAAW,CAACwC,IAAI,cAAAoC,iBAAA,uBAAhBA,iBAAA,CAAkBpC,IAAI,KAAIxC,WAAW,CAACwC,IAAI;;QAE7D;QACA,MAAMyC,sBAAsB,IAAAJ,sBAAA,GAAG5E,iBAAiB,CAACS,aAAa,cAAAmE,sBAAA,uBAA/BA,sBAAA,CAAiCK,IAAI,CAACrD,MAAM;UAAA,IAAAsD,mBAAA;UAAA,OACzE,GAAAA,mBAAA,GAACtD,MAAM,CAACX,WAAW,cAAAiE,mBAAA,eAAlBA,mBAAA,CAAoB1E,OAAO;QAAA,CAC9B,CAAC;QAED,MAAML,MAAM,GAAG,EAAA0E,kBAAA,GAAAhG,OAAO,CAACuB,QAAQ,cAAAyE,kBAAA,uBAAhBA,kBAAA,CAAkBxE,GAAG,OAAAyE,oBAAA,GAAIjG,OAAO,CAACyB,UAAU,cAAAwE,oBAAA,uBAAlBA,oBAAA,CAAoBzE,GAAG;;QAE/D;QACA,MAAM8E,wBAAwB,GAAGA,CAAA,KAAM;UACrC,IAAIC,OAAO,GAAG,EAAE;;UAEhB;UACA,IAAI5C,KAAK,CAACC,OAAO,CAACsC,UAAU,CAAC,EAAE;YAC7BK,OAAO,GAAGL,UAAU;UACtB,CAAC,MAAM,IAAIA,UAAU,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;YACvD,MAAMnC,WAAW,GAAG,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC;YAC7E,KAAK,MAAMC,KAAK,IAAID,WAAW,EAAE;cAC/B,IAAImC,UAAU,CAAClC,KAAK,CAAC,IAAIL,KAAK,CAACC,OAAO,CAACsC,UAAU,CAAClC,KAAK,CAAC,CAAC,EAAE;gBACzDuC,OAAO,GAAGL,UAAU,CAAClC,KAAK,CAAC;gBAC3B;cACF;YACF;YACA,IAAIuC,OAAO,CAACxF,MAAM,KAAK,CAAC,EAAE;cACxBwF,OAAO,GAAG,CAACL,UAAU,CAAC;YACxB;UACF;;UAEA;UACA,IAAIzC,kBAAkB,CAAC8C,OAAO,CAAC,EAAE;YAC/B,OAAOlC,mBAAmB,CAAC,CAAC;UAC9B;;UAEA;UACA,IAAImC,gBAAgB,GAAG,EAAE;UACzB,IAAItF,WAAW,CAACuF,iBAAiB,EAAE;YACjC,MAAMC,cAAc,GAAGxF,WAAW,CAACuF,iBAAiB,CAACE,KAAK,CAAC,iBAAiB,CAAC;YAC7EH,gBAAgB,GAAGE,cAAc,CAAC9F,MAAM,CAACgG,OAAO,IAAIA,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC;UACrE;UAEA,oBACE/G,OAAA;YAAKyC,SAAS,EAAC,MAAM;YAAAC,QAAA,GAClB+D,OAAO,CAACzD,GAAG,CAAC,CAACgE,MAAM,EAAE9D,KAAK,kBACzBlD,OAAA;cAAiByC,SAAS,EAAC,MAAM;cAAAC,QAAA,GAE9BgE,gBAAgB,CAACxD,KAAK,CAAC,iBACtBlD,OAAA;gBAAKyC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7C1C,OAAA;kBAAKyC,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvDgE,gBAAgB,CAACxD,KAAK,CAAC,CAAC6D,IAAI,CAAC;gBAAC;kBAAAnE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CACN,EAGA,CAAA1B,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAEQ,OAAO,KAAIR,iBAAiB,CAACS,aAAa,IAAI,CAACV,WAAW,CAACuF,iBAAiB,CAAC9F,QAAQ,CAAC,mBAAmB,CAAC,iBAC5Hb,OAAA;gBAAKyC,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,eACxC1C,OAAA,CAACF,wBAAwB;kBACvB8D,IAAI,EAAE,CAACoD,MAAM,CAAE,CAAC;kBAAA;kBAChBxF,MAAM,EAAEA,MAAO;kBACfH,iBAAiB,EAAEA,iBAAkB;kBACrCjB,mBAAmB,EAAEA,mBAAoB;kBACzC6G,eAAe,EAAE;gBAAK;kBAAArE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CACN;YAAA,GArBOG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBV,CACN,CAAC,EAGD2D,gBAAgB,CAACzF,MAAM,KAAK,CAAC,IAAIG,WAAW,CAACuF,iBAAiB,iBAC7D3G,OAAA;cAAA0C,QAAA,gBACE1C,OAAA;gBAAKyC,SAAS,EAAC,gCAAgC;gBAAAC,QAAA,eAC7C1C,OAAA;kBAAKyC,SAAS,EAAC,2CAA2C;kBAAAC,QAAA,EACvDtB,WAAW,CAACuF;gBAAiB;kBAAA/D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3B;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,EAEL,CAAC3B,WAAW,CAACuF,iBAAiB,CAAC9F,QAAQ,CAAC,mBAAmB,CAAC,iBAC3Db,OAAA,CAACF,wBAAwB;gBACvB8D,IAAI,EAAEwC,UAAW;gBACjB5E,MAAM,EAAEA,MAAO;gBACfH,iBAAiB,EAAEA,iBAAkB;gBACrCjB,mBAAmB,EAAEA,mBAAoB;gBACzC6G,eAAe,EAAE;cAAK;gBAAArE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB,CACF;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAEV,CAAC;QAED,OAAOyD,wBAAwB,CAAC,CAAC;MACnC;;MAEA;MACA,IAAIpF,WAAW,CAACuF,iBAAiB,IAAI,EAAEtF,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEQ,OAAO,IAAI,EAAA2D,sBAAA,GAAAnE,iBAAiB,CAACS,aAAa,cAAA0D,sBAAA,uBAA/BA,sBAAA,CAAiCvE,MAAM,IAAG,CAAC,CAAC,EAAE;QACjH;QACA,IAAI0C,kBAAkB,CAACvC,WAAW,CAACwC,IAAI,CAAC,EAAE;UACxC,OAAOW,mBAAmB,CAAC,CAAC;QAC9B;QAEA,oBACEvE,OAAA;UAAKyC,SAAS,EAAC,gCAAgC;UAAAC,QAAA,eAC7C1C,OAAA;YAAKyC,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACvDtB,WAAW,CAACuF;UAAiB;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV;IACF;;IAEA;IACA,IAAI3B,WAAW,CAACgE,OAAO,IAAIhE,WAAW,CAACwC,IAAI,KAAKU,SAAS,IAAI,EAAEjD,iBAAiB,aAAjBA,iBAAiB,eAAjBA,iBAAiB,CAAEQ,OAAO,IAAI,EAAAmD,sBAAA,GAAA3D,iBAAiB,CAACS,aAAa,cAAAkD,sBAAA,uBAA/BA,sBAAA,CAAiC/D,MAAM,IAAG,CAAC,CAAC,EAAE;MACzI;MACA,IAAI0C,kBAAkB,CAACvC,WAAW,CAACwC,IAAI,CAAC,EAAE;QACxC,OAAOW,mBAAmB,CAAC,CAAC;MAC9B;;MAEA;MACA,oBACEvE,OAAA;QAAKyC,SAAS,EAAC,uDAAuD;QAAAC,QAAA,eACpE1C,OAAA;UAAKyC,SAAS,EAAC,2CAA2C;UAAAC,QAAA,EACvDwE,IAAI,CAACC,SAAS,CAAC/F,WAAW,CAACwC,IAAI,EAAE,IAAI,EAAE,CAAC;QAAC;UAAAhB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;;IAEA;IACA,IAAI3B,WAAW,CAACgE,OAAO,IAAI,CAAChE,WAAW,CAACwC,IAAI,IAAI,CAACxC,WAAW,CAACuF,iBAAiB,IAAI,CAACvF,WAAW,CAACgG,KAAK,EAAE;MACpG,OAAO7C,mBAAmB,CAAC,CAAC;IAC9B;;IAEA;IACA,IAAInD,WAAW,CAACgG,KAAK,EAAE;MACrB,oBACEpH,OAAA;QAAKyC,SAAS,EAAC,+BAA+B;QAAAC,QAAA,eAC5C1C,OAAA;UAAGyC,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAEtB,WAAW,CAACgG;QAAK;UAAAxE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD,CAAC;IAEV;IAEA,OAAO,IAAI;EACb,CAAC;EAED,oBACE/C,OAAA;IACEyC,SAAS,EAAE,QACTnC,MAAM,GAAG,aAAa,GAAG,eAAe,OAClC;IAAAoC,QAAA,eAER1C,OAAA;MACEyC,SAAS,EAAE,8BACTnC,MAAM,GACF,wCAAwC,GACxC,2CAA2C,EAC9C;MAAAoC,QAAA,GAGF,EAAExC,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACuF,iBAAiB,CAAC,iBAC9D3G,OAAA;QAAGyC,SAAS,EAAC,qBAAqB;QAAAC,QAAA,EAAExC,OAAO,CAACmH;MAAO;QAAAzE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CACxD,EAGA,CAACzC,MAAM,IAAIJ,OAAO,CAAC6F,OAAO,IAAIlC,KAAK,CAACC,OAAO,CAAC5D,OAAO,CAAC6F,OAAO,CAAC,IAAI7F,OAAO,CAAC6F,OAAO,CAAC9E,MAAM,GAAG,CAAC,IAAI,CAACf,OAAO,CAACyF,YAAY,IAAI,CAACzF,OAAO,CAACwF,qBAAqB,IAAI,EAAExF,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACuF,iBAAiB,CAAC,iBACvN3G,OAAA;QAAKyC,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClBxC,OAAO,CAACoH,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAtH,OAAA;UAAA0C,QAAA,gBACE1C,OAAA;YAAKyC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDxC,OAAO,CAAC6F,OAAO,CAAC/C,GAAG,CAAC,CAACrC,MAAM,EAAEuC,KAAK,kBACjClD,OAAA;cAEEmD,OAAO,EAAEA,CAAA,KAAMzC,oBAAoB,CAACC,MAAM,CAAE;cAC5C8B,SAAS,EAAE,kHACTjC,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAA+B,QAAA,eAEH1C,OAAA;gBAAKyC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC1C,OAAA;kBAAKyC,SAAS,EAAE,kEACdjC,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAA+B,QAAA,EACAlC,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,iBAClCX,OAAA;oBAAKyC,SAAS,EAAC,wBAAwB;oBAAC8E,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAA/E,QAAA,eAC3F1C,OAAA;sBAAM0H,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACLpC,MAAM;cAAA;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLvC,kBAAkB,CAACS,MAAM,GAAG,CAAC,iBAC5BjB,OAAA;YAAKyC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB1C,OAAA;cACEmD,OAAO,EAAEnC,oBAAqB;cAC9ByB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAAClC,kBAAkB,CAACS,MAAM,EAAC,GAC/C;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACD/C,OAAA;YAAKyC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACA/C,OAAA;UAAKyC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7CxC,OAAO,CAAC6F,OAAO,CAAC/C,GAAG,CAAC,CAACrC,MAAM,EAAEuC,KAAK,kBACjClD,OAAA;YAEEmD,OAAO,EAAEA,CAAA,KAAMO,wBAAwB,CAAC/C,MAAM,CAAE;YAChD8B,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/M/B;UAAM,GAJFuC,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACA7C,OAAO,CAAC4H,WAAW,IAAI5H,OAAO,CAAC6H,UAAU,iBACxC/H,OAAA;UAAKyC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,OACrC,EAACxC,OAAO,CAAC4H,WAAW,EAAC,MAAI,EAAC5H,OAAO,CAAC6H,UAAU;QAAA;UAAAnF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAACzC,MAAM,IAAIJ,OAAO,CAACyF,YAAY,IAAIzF,OAAO,CAACwF,qBAAqB,IAAIxF,OAAO,CAAC6F,OAAO,IAAIlC,KAAK,CAACC,OAAO,CAAC5D,OAAO,CAAC6F,OAAO,CAAC,IAAI7F,OAAO,CAAC6F,OAAO,CAAC9E,MAAM,GAAG,CAAC,IAAI,EAAEf,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACuF,iBAAiB,CAAC,iBACrN3G,OAAA;QAAKyC,SAAS,EAAC,MAAM;QAAAC,QAAA,GAClBxC,OAAO,CAACoH,SAAS,KAAK,UAAU;QAAA;QAC/B;QACAtH,OAAA;UAAA0C,QAAA,gBACE1C,OAAA;YAAKyC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAClDxC,OAAO,CAAC6F,OAAO,CAAC/C,GAAG,CAAC,CAACrC,MAAM,EAAEuC,KAAK,kBACjClD,OAAA;cAEEmD,OAAO,EAAEA,CAAA,KAAMzC,oBAAoB,CAACC,MAAM,CAAE;cAC5C8B,SAAS,EAAE,kHACTjC,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,2FAA2F,GAC3F,0FAA0F,EAC7F;cAAA+B,QAAA,eAEH1C,OAAA;gBAAKyC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChC1C,OAAA;kBAAKyC,SAAS,EAAE,kEACdjC,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,GAC/B,uBAAuB,GACvB,gCAAgC,EACnC;kBAAA+B,QAAA,EACAlC,kBAAkB,CAACK,QAAQ,CAACF,MAAM,CAAC,iBAClCX,OAAA;oBAAKyC,SAAS,EAAC,wBAAwB;oBAAC8E,IAAI,EAAC,MAAM;oBAACC,MAAM,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAA/E,QAAA,eAC3F1C,OAAA;sBAAM0H,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACC,CAAC,EAAC;oBAAgB;sBAAAjF,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrF;gBACN;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC,EACLpC,MAAM;cAAA;gBAAAiC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC,GArBDG,KAAK;cAAAN,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAsBJ,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EACLvC,kBAAkB,CAACS,MAAM,GAAG,CAAC,iBAC5BjB,OAAA;YAAKyC,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnB1C,OAAA;cACEmD,OAAO,EAAEnC,oBAAqB;cAC9ByB,SAAS,EAAC,kHAAkH;cAAAC,QAAA,GAC7H,oBACmB,EAAClC,kBAAkB,CAACS,MAAM,EAAC,GAC/C;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,eACD/C,OAAA;YAAKyC,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAEvC;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;QAAA;QAEN;QACA/C,OAAA;UAAKyC,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAC7CxC,OAAO,CAAC6F,OAAO,CAAC/C,GAAG,CAAC,CAACrC,MAAM,EAAEuC,KAAK,kBACjClD,OAAA;YAEEmD,OAAO,EAAEA,CAAA,KAAMO,wBAAwB,CAAC/C,MAAM,CAAE;YAChD8B,SAAS,EAAC,sMAAsM;YAAAC,QAAA,EAE/M/B;UAAM,GAJFuC,KAAK;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKJ,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EACA7C,OAAO,CAAC4H,WAAW,IAAI5H,OAAO,CAAC8H,wBAAwB,iBACtDhI,OAAA;UAAKyC,SAAS,EAAC,4BAA4B;UAAAC,QAAA,GAAC,sBACtB,EAACxC,OAAO,CAAC4H,WAAW,EAAC,MAAI,EAAC5H,OAAO,CAAC8H,wBAAwB,EAC7E9H,OAAO,CAAC+H,cAAc,GAAG,CAAC,iBACzBjI,OAAA;YAAMyC,SAAS,EAAC,oBAAoB;YAAAC,QAAA,GAAC,GAClC,EAACxC,OAAO,CAAC+H,cAAc,EAAC,aAAW,EAAC/H,OAAO,CAAC+H,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,EAAE,EAAC,aAC/E;UAAA;YAAArF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACP;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAKA,CAACzC,MAAM,IAAIJ,OAAO,CAACgI,WAAW,KAAK,gBAAgB,IAAIhI,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACgE,OAAO,iBACxGpF,OAAA;QAAKyC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE1C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAC8E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA/E,QAAA,eACjF1C,OAAA;cAAM0H,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,+BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACzC,MAAM,IAAIJ,OAAO,CAACgI,WAAW,KAAK,sBAAsB,IAAIhI,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACgE,OAAO,iBAC9GpF,OAAA;QAAKyC,SAAS,EAAC,MAAM;QAAAC,QAAA,gBACnB1C,OAAA;UAAKyC,SAAS,EAAC,sDAAsD;UAAAC,QAAA,eACnE1C,OAAA;YAAKyC,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChC1C,OAAA;cAAKyC,SAAS,EAAC,cAAc;cAAC8E,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA/E,QAAA,eACjF1C,OAAA;gBAAM0H,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAgB;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrF,CAAC,4CAER;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACL7C,OAAO,CAACiI,mBAAmB,iBAC1BnI,OAAA;UAAKyC,SAAS,EAAC,kDAAkD;UAAAC,QAAA,gBAC/D1C,OAAA;YAAKyC,SAAS,EAAC,wBAAwB;YAAAC,QAAA,gBACrC1C,OAAA;cAAKyC,SAAS,EAAC,4BAA4B;cAAC8E,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA/E,QAAA,eAC/F1C,OAAA;gBAAM0H,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6G;gBAAAjF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClL,CAAC,eACN/C,OAAA;cAAIyC,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAqB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzE,CAAC,eACN/C,OAAA;YAAKyC,SAAS,EAAC,2CAA2C;YAAAC,QAAA,EACvDxC,OAAO,CAACiI,mBAAmB,CAACC,OAAO,CAAC,6CAA6C,EAAE,EAAE;UAAC;YAAAxF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,EAGA,CAACzC,MAAM,IAAIJ,OAAO,CAACgI,WAAW,KAAK,gBAAgB,IAAIhI,OAAO,CAACkB,WAAW,IAAI,CAAClB,OAAO,CAACkB,WAAW,CAACgE,OAAO,iBACzGpF,OAAA;QAAKyC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D1C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAC8E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA/E,QAAA,eACjF1C,OAAA;cAAM0H,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,0BAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACzC,MAAM,IAAIJ,OAAO,CAACgI,WAAW,KAAK,gBAAgB,iBAClDlI,OAAA;QAAKyC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D1C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAC8E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA/E,QAAA,eACjF1C,OAAA;cAAM0H,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,kBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACzC,MAAM,IAAIJ,OAAO,CAACgI,WAAW,KAAK,uBAAuB,IAAIhI,OAAO,CAACkB,WAAW,IAAIlB,OAAO,CAACkB,WAAW,CAACgE,OAAO,iBAC/GpF,OAAA;QAAKyC,SAAS,EAAC,sDAAsD;QAAAC,QAAA,eACnE1C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAC8E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA/E,QAAA,eACjF1C,OAAA;cAAM0H,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAgB;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrF,CAAC,4CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAGA,CAACzC,MAAM,IAAIJ,OAAO,CAACgI,WAAW,KAAK,uBAAuB,IAAIhI,OAAO,CAACkB,WAAW,IAAI,CAAClB,OAAO,CAACkB,WAAW,CAACgE,OAAO,iBAChHpF,OAAA;QAAKyC,SAAS,EAAC,kDAAkD;QAAAC,QAAA,eAC/D1C,OAAA;UAAKyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAChC1C,OAAA;YAAKyC,SAAS,EAAC,cAAc;YAAC8E,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAA/E,QAAA,eACjF1C,OAAA;cAAM0H,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAjF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F,CAAC,8CAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,EAOA7C,OAAO,CAACkB,WAAW,IAAIoD,iBAAiB,CAACtE,OAAO,CAACkB,WAAW,CAAC;IAAA;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAsB3D;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1C,EAAA,CAtlBIJ,WAAW;AAAAoI,EAAA,GAAXpI,WAAW;AAwlBjB,eAAeA,WAAW;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}