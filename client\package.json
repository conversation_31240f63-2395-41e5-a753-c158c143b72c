{"name": "client", "version": "0.1.0", "private": true, "homepage": "https://dev.cavinkare.in/botnexus", "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.10.0", "express": "^4.18.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build:cdn": "webpack --config webpack.cdn.config.js", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "^7.28.0", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "autoprefixer": "^10.4.16", "babel-loader": "^10.0.0", "css-loader": "^7.1.2", "html-webpack-plugin": "^5.6.3", "mini-css-extract-plugin": "^2.9.2", "postcss": "^8.4.31", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "tailwindcss": "^3.3.3", "webpack": "^5.99.9", "webpack-cli": "^6.0.1"}}