import React from 'react';
import { HashRouter as Router, Routes, Route } from 'react-router-dom';
import { ChatProvider } from './context/ChatContext';
import { FormProvider } from './context/FormContext';
import { AuthProvider } from './context/AuthContext';
import { ApiConfigProvider } from './context/ApiConfigContext';
import { MainLayout, SimpleLayout } from './layouts';
import ChatPage from './pages/ChatPage';
import FormsPage from './pages/FormsPage';
import DocumentsPage from './pages/DocumentsPage';
import ApiConfigPage from './pages/ApiConfigPage';
import ChatbotWidget from './components/ChatbotWidget';
import UnifiedConfigPage from './pages/UnifiedConfigPage';
import './App.css';

function App() {
  return (
    <Router>
      <AuthProvider>
        <FormProvider>
          <ApiConfigProvider>
            <ChatProvider>
              <Routes>
                {/* Route with SimpleLayout for chatbot */}
                <Route 
                  path="/chatbot" 
                  element={
                    <SimpleLayout>
                      <ChatbotWidget />
                    </SimpleLayout>
                  } 
                />
                
                {/* Routes with MainLayout */}
                {/* <Route 
                  path="/" 
                  element={
                    <MainLayout>
                      <ChatPage />
                    </MainLayout>
                  } 
                /> */}
                <Route 
                  path="/unifiedconfigs" 
                  element={
                    <MainLayout>
                      <FormsPage />
                    </MainLayout>
                  } 
                />
                <Route 
                  path="/api-config" 
                  element={
                    <MainLayout>
                      <ApiConfigPage />
                    </MainLayout>
                  } 
                />
                <Route 
                  path="/unified-configs" 
                  element={
                    <MainLayout>
                      <UnifiedConfigPage />
                    </MainLayout>
                  } 
                />
                <Route 
                  path="/documents" 
                  element={
                    <MainLayout>
                      <DocumentsPage />
                    </MainLayout>
                  } 
                />
              </Routes>
            </ChatProvider>
          </ApiConfigProvider>
        </FormProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;
