import React from 'react';
import ReactDOM from 'react-dom/client';
import '../index.css';
import { ChatProvider } from '../context/ChatContext';
import ChatbotWidget from '../components/ChatbotWidget';
import { AuthProvider } from '../context/AuthContext';
import { FormProvider } from '../context/FormContext';
import { ApiConfigProvider } from '../context/ApiConfigContext';

// Global initialization function
const BotNexusChatbot = {
  init: function (config = {}) {
    // Create container div if it doesn't exist
    let container = document.getElementById('botnexus-chatbot-container');
    if (!container) {
      container = document.createElement('div');
      container.id = 'botnexus-chatbot-container';
      document.body.appendChild(container);
    }

    // Create React root and render the widget wrapped with ChatProvider
    const root = ReactDOM.createRoot(container);
    root.render(
      React.createElement(AuthProvider, null,
        React.createElement(FormProvider, null,
          React.createElement(ApiConfigProvider, null,
            React.createElement(Chat<PERSON>rovider, null,
              React.createElement(ChatbotWidget, config)
            )
          )
        )
      )
    );
  }
};

// Expose globally
window.BotNexusChatbot = BotNexusChatbot;

// Auto-initialize if script tag has data attributes
function initializeFromScript() {
  const scripts = document.querySelectorAll('script[src*="chatbot-widget.js"]');

  scripts.forEach(script => {
    const config = {
      apiUrl: script.getAttribute('data-api-url') || process.env.REACT_APP_API_URL || 'http://localhost:5000',
      theme: script.getAttribute('data-theme') || 'default',
      position: script.getAttribute('data-position') || 'bottom-right',
      primaryColor: script.getAttribute('data-primary-color') || '#007bff',
      title: script.getAttribute('data-title') || 'Chat with us',
      subtitle: script.getAttribute('data-subtitle') || 'We\'re here to help!',
      autoInit: script.getAttribute('data-auto-init') !== 'false'
    };

    if (config.autoInit) {
      BotNexusChatbot.init(config);
    }
  });
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeFromScript);
} else {
  // DOM is already ready
  initializeFromScript();
}

// Export for module systems
export default BotNexusChatbot;