import React, { useState, useEffect } from 'react';
import { useApiConfig } from '../context/ApiConfigContext';

const ApiConfigForm = ({ initialConfig, onSave, onCancel }) => {
  const { createApiConfig, updateApiConfig } = useApiConfig();
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    endpoint: '',
    method: 'GET',
    headers: {},
    authType: 'none',
    authConfig: {},
    timeout: 30000,
    retryCount: 3,
    isActive: true
  });
  const [headers, setHeaders] = useState([{ key: '', value: '' }]);
  const [authData, setAuthData] = useState({
    apiKey: '',
    bearerToken: '',
    username: '',
    password: '',
    customHeader: '',
    customValue: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  useEffect(() => {
    if (initialConfig) {
      setFormData({
        name: initialConfig.name || '',
        description: initialConfig.description || '',
        endpoint: initialConfig.endpoint || '',
        method: initialConfig.method || 'GET',
        headers: initialConfig.headers || {},
        authType: initialConfig.authType || 'none',
        authConfig: initialConfig.authConfig || {},
        timeout: initialConfig.timeout || 30000,
        retryCount: initialConfig.retryCount || 3,
        isActive: initialConfig.isActive !== undefined ? initialConfig.isActive : true
      });
      
      // Convert headers object to array for easier editing
      const headersArray = Object.entries(initialConfig.headers || {}).map(([key, value]) => ({
        key,
        value
      }));
      if (headersArray.length === 0) {
        headersArray.push({ key: '', value: '' });
      }
      setHeaders(headersArray);
      
      // Set auth data based on auth config
      if (initialConfig.authConfig) {
        setAuthData({
          apiKey: initialConfig.authConfig.apiKey || '',
          bearerToken: initialConfig.authConfig.bearerToken || '',
          username: initialConfig.authConfig.username || '',
          password: initialConfig.authConfig.password || '',
          customHeader: initialConfig.authConfig.customHeader || '',
          customValue: initialConfig.authConfig.customValue || ''
        });
      }
    }
  }, [initialConfig]);

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.endpoint.trim()) {
      newErrors.endpoint = 'Endpoint URL is required';
    } else {
      try {
        new URL(formData.endpoint);
      } catch {
        newErrors.endpoint = 'Please enter a valid URL';
      }
    }
    
    if (formData.timeout < 1000 || formData.timeout > 300000) {
      newErrors.timeout = 'Timeout must be between 1000ms and 300000ms';
    }
    
    if (formData.retryCount < 0 || formData.retryCount > 10) {
      newErrors.retryCount = 'Retry count must be between 0 and 10';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleHeaderChange = (index, field, value) => {
    const newHeaders = [...headers];
    newHeaders[index][field] = value;
    setHeaders(newHeaders);
  };

  const addHeader = () => {
    setHeaders([...headers, { key: '', value: '' }]);
  };

  const removeHeader = (index) => {
    if (headers.length > 1) {
      setHeaders(headers.filter((_, i) => i !== index));
    }
  };

  const handleAuthChange = (e) => {
    const { name, value } = e.target;
    setAuthData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Convert headers array to object
      const headersObject = {};
      headers.forEach(({ key, value }) => {
        if (key.trim() && value.trim()) {
          headersObject[key.trim()] = value.trim();
        }
      });
      
      // Build auth config based on auth type
      let authConfig = {};
      switch (formData.authType) {
        case 'apiKey':
          if (authData.apiKey) {
            authConfig = { apiKey: authData.apiKey };
          }
          break;
        case 'bearer':
          if (authData.bearerToken) {
            authConfig = { bearerToken: authData.bearerToken };
          }
          break;
        case 'basic':
          if (authData.username && authData.password) {
            authConfig = { username: authData.username, password: authData.password };
          }
          break;
        case 'custom':
          if (authData.customHeader && authData.customValue) {
            authConfig = { customHeader: authData.customHeader, customValue: authData.customValue };
          }
          break;
      }
      
      const configData = {
        ...formData,
        headers: headersObject,
        authConfig,
        timeout: parseInt(formData.timeout),
        retryCount: parseInt(formData.retryCount)
      };
      
      let result;
      if (initialConfig?._id) {
        result = await updateApiConfig(initialConfig._id, configData);
      } else {
        result = await createApiConfig(configData);
      }
      
      if (result) {
        onSave();
      }
    } catch (error) {
      console.error('Error saving API configuration:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-6">
        {initialConfig ? 'Edit API Configuration' : 'Create New API Configuration'}
      </h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Configuration Name *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter a unique name for this API config"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              HTTP Method
            </label>
            <select
              name="method"
              value={formData.method}
              onChange={handleInputChange}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="GET">GET</option>
              <option value="POST">POST</option>
              <option value="PUT">PUT</option>
              <option value="DELETE">DELETE</option>
              <option value="PATCH">PATCH</option>
            </select>
          </div>
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Description
          </label>
          <textarea
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            rows={3}
            className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Describe what this API configuration is for"
          />
        </div>
        
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Endpoint URL *
          </label>
          <input
            type="url"
            name="endpoint"
            value={formData.endpoint}
            onChange={handleInputChange}
            className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
              errors.endpoint ? 'border-red-500' : 'border-gray-300'
            }`}
            placeholder="https://api.example.com/endpoint"
          />
          {errors.endpoint && <p className="text-red-500 text-sm mt-1">{errors.endpoint}</p>}
        </div>
        
        {/* Headers */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Headers
          </label>
          {headers.map((header, index) => (
            <div key={index} className="flex gap-2 mb-2">
              <input
                type="text"
                value={header.key}
                onChange={(e) => handleHeaderChange(index, 'key', e.target.value)}
                placeholder="Header name"
                className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="text"
                value={header.value}
                onChange={(e) => handleHeaderChange(index, 'value', e.target.value)}
                placeholder="Header value"
                className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <button
                type="button"
                onClick={() => removeHeader(index)}
                className="px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200"
                disabled={headers.length === 1}
              >
                Remove
              </button>
            </div>
          ))}
          <button
            type="button"
            onClick={addHeader}
            className="mt-2 px-3 py-2 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200"
          >
            Add Header
          </button>
        </div>
        
        {/* Authentication */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Authentication
          </label>
          <select
            name="authType"
            value={formData.authType}
            onChange={handleInputChange}
            className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 mb-3"
          >
            <option value="none">None</option>
            <option value="apiKey">API Key</option>
            <option value="bearer">Bearer Token</option>
            <option value="basic">Basic Auth</option>
            <option value="custom">Custom Header</option>
          </select>
          
          {formData.authType === 'apiKey' && (
            <input
              type="text"
              name="apiKey"
              value={authData.apiKey}
              onChange={handleAuthChange}
              placeholder="Enter API key"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          )}
          
          {formData.authType === 'bearer' && (
            <input
              type="text"
              name="bearerToken"
              value={authData.bearerToken}
              onChange={handleAuthChange}
              placeholder="Enter bearer token"
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          )}
          
          {formData.authType === 'basic' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="text"
                name="username"
                value={authData.username}
                onChange={handleAuthChange}
                placeholder="Username"
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="password"
                name="password"
                value={authData.password}
                onChange={handleAuthChange}
                placeholder="Password"
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
          
          {formData.authType === 'custom' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <input
                type="text"
                name="customHeader"
                value={authData.customHeader}
                onChange={handleAuthChange}
                placeholder="Header name"
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <input
                type="text"
                name="customValue"
                value={authData.customValue}
                onChange={handleAuthChange}
                placeholder="Header value"
                className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
        </div>
        
        {/* Advanced Settings */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Timeout (ms)
            </label>
            <input
              type="number"
              name="timeout"
              value={formData.timeout}
              onChange={handleInputChange}
              min="1000"
              max="300000"
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.timeout ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.timeout && <p className="text-red-500 text-sm mt-1">{errors.timeout}</p>}
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Retry Count
            </label>
            <input
              type="number"
              name="retryCount"
              value={formData.retryCount}
              onChange={handleInputChange}
              min="0"
              max="10"
              className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.retryCount ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.retryCount && <p className="text-red-500 text-sm mt-1">{errors.retryCount}</p>}
          </div>
          
          <div className="flex items-center">
            <label className="flex items-center">
              <input
                type="checkbox"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Active</span>
            </label>
          </div>
        </div>
        
        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : (initialConfig ? 'Update Configuration' : 'Create Configuration')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ApiConfigForm;