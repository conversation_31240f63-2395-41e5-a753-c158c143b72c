import React, { useState, useEffect } from 'react';
import api from '../utils/api';

const ApiConfigManager = () => {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [stats, setStats] = useState(null);

  // Fetch configurations
  useEffect(() => {
    fetchConfigs();
    fetchStats();
  }, [filterType]);

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const params = {};
      if (filterType !== 'all') params.type = filterType;
      
      const response = await api.get('/api-configs', { params });
      setConfigs(response.data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch configurations');
      console.error('Error fetching configs:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await api.get('/api-configs/stats/overview');
      setStats(response.data);
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  const handleSave = async (configData) => {
    try {
      if (editingConfig) {
        await api.put(`/api-configs/${editingConfig._id}`, configData);
      } else {
        await api.post('/api-configs', configData);
      }
      
      setShowBuilder(false);
      setEditingConfig(null);
      fetchConfigs();
      fetchStats();
    } catch (err) {
      console.error('Error saving config:', err);
      throw err;
    }
  };

  const handleDelete = async (configId) => {
    const config = configs.find(c => c._id === configId);
    if (window.confirm(`Are you sure you want to delete "${config?.name}"? This action cannot be undone.`)) {
      try {
        await api.delete(`/api-configs/${configId}`);
        fetchConfigs();
        fetchStats();
      } catch (err) {
        console.error('Error deleting config:', err);
        alert('Failed to delete configuration');
      }
    }
  };

  const handleTest = async (config) => {
    try {
      const response = await api.post(`/api-configs/${config._id}/test`, {
        testData: { userId: 'test-user' }
      });
      
      alert(`Test Result:\nSuccess: ${response.data.success}\nResponse Time: ${response.data.responseTime}ms\nData: ${JSON.stringify(response.data.data, null, 2)}`);
    } catch (err) {
      console.error('Error testing config:', err);
      alert('Failed to test configuration');
    }
  };

  const handleExecute = async (config) => {
    try {
      const response = await api.post(`/api-configs/${config._id}/execute`, {
        userData: { userId: 'current-user', empId: '12345' },
        inputData: { query: 'test execution' }
      });
      
      alert(`Execution Result:\nSuccess: ${response.data.success}\nResponse Time: ${response.data.responseTime}ms\nMessage: ${response.data.message}`);
    } catch (err) {
      console.error('Error executing config:', err);
      alert('Failed to execute configuration');
    }
  };

  const toggleStatus = async (config) => {
    try {
      await api.put(`/api-configs/${config._id}`, {
        ...config,
        isActive: !config.isActive
      });
      fetchConfigs();
      fetchStats();
    } catch (err) {
      console.error('Error updating config status:', err);
      alert('Failed to update configuration status');
    }
  };

  const filteredConfigs = configs.filter(config => {
    if (searchTerm) {
      return config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
             config.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
             config.keywords.some(k => k.toLowerCase().includes(searchTerm.toLowerCase()));
    }
    return true;
  });

  if (showBuilder) {
    return (
      <div className="min-h-screen bg-gray-50 py-6">
        <div className="max-w-5xl mx-auto px-4">
          <ApiConfigBuilder
            initialConfig={editingConfig}
            onSave={handleSave}
            onCancel={() => {
              setShowBuilder(false);
              setEditingConfig(null);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">API Configuration Manager</h1>
          <p className="text-gray-600 mt-2">Manage and trigger API configurations dynamically</p>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-blue-600">{stats.totalConfigs}</div>
              <div className="text-sm text-gray-600">Total Configs</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-green-600">{stats.totalApis}</div>
              <div className="text-sm text-gray-600">API Configs</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-purple-600">{stats.totalForms}</div>
              <div className="text-sm text-gray-600">Form Configs</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-emerald-600">{stats.activeConfigs}</div>
              <div className="text-sm text-gray-600">Active</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-red-600">{stats.inactiveConfigs}</div>
              <div className="text-sm text-gray-600">Inactive</div>
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="bg-white p-4 rounded-lg shadow mb-6">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex gap-4 items-center">
              <button
                onClick={() => setShowBuilder(true)}
                className="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                + New Configuration
              </button>
              
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Types</option>
                <option value="api">API Only</option>
                <option value="form">Form Only</option>
              </select>
            </div>
            
            <div className="flex gap-4 items-center">
              <input
                type="text"
                placeholder="Search configurations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="border border-gray-300 rounded-md px-3 py-2 w-64 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              
              <button
                onClick={fetchConfigs}
                className="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {/* Configurations List */}
        {loading ? (
          <div className="text-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <p className="mt-2 text-gray-600">Loading configurations...</p>
          </div>
        ) : filteredConfigs.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-gray-600">No configurations found.</p>
            <button
              onClick={() => setShowBuilder(true)}
              className="mt-4 bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600"
            >
              Create First Configuration
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredConfigs.map((config) => (
              <ConfigCard
                key={config._id}
                config={config}
                onEdit={(config) => {
                  setEditingConfig(config);
                  setShowBuilder(true);
                }}
                onDelete={() => handleDelete(config._id)}
                onTest={() => handleTest(config)}
                onExecute={() => handleExecute(config)}
                onToggleStatus={() => toggleStatus(config)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

// Configuration Card Component
const ConfigCard = ({ config, onEdit, onDelete, onTest, onExecute, onToggleStatus }) => {
  const getStatusColor = () => {
    return config.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getTypeColor = () => {
    return config.type === 'api' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800';
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{config.name}</h3>
          <p className="text-gray-600 text-sm mt-1">{config.description || 'No description'}</p>
        </div>
        <div className="flex gap-2">
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor()}`}>
            {config.type.toUpperCase()}
          </span>
          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor()}`}>
            {config.isActive ? 'Active' : 'Inactive'}
          </span>
        </div>
      </div>

      {/* Configuration Details */}
      <div className="space-y-2 mb-4">
        {config.type === 'api' && config.apiConfig && (
          <div className="text-sm">
            <span className="font-medium text-gray-700">Endpoint:</span>
            <span className="ml-2 text-gray-600 break-all">{config.apiConfig.endpoint}</span>
          </div>
        )}
        
        <div className="text-sm">
          <span className="font-medium text-gray-700">Keywords:</span>
          <span className="ml-2 text-gray-600">
            {config.keywords?.length > 0 ? config.keywords.join(', ') : 'None'}
          </span>
        </div>
        
        <div className="text-sm">
          <span className="font-medium text-gray-700">Trigger Phrases:</span>
          <span className="ml-2 text-gray-600">
            {config.triggerPhrases?.length > 0 ? config.triggerPhrases.join(', ') : 'None'}
          </span>
        </div>
        
        <div className="text-sm">
          <span className="font-medium text-gray-700">Priority:</span>
          <span className="ml-2 text-gray-600">{config.priority || 0}</span>
        </div>
      </div>

      {/* Statistics */}
      {config.stats && (
        <div className="bg-gray-50 rounded-lg p-3 mb-4">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-semibold text-gray-900">{config.stats.totalTriggers || 0}</div>
              <div className="text-xs text-gray-600">Total Triggers</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-green-600">{config.stats.successfulTriggers || 0}</div>
              <div className="text-xs text-gray-600">Successful</div>
            </div>
            <div>
              <div className="text-lg font-semibold text-red-600">{config.stats.failedTriggers || 0}</div>
              <div className="text-xs text-gray-600">Failed</div>
            </div>
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => onEdit(config)}
          className="bg-blue-500 text-white px-3 py-1 rounded-md text-sm hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Edit
        </button>
        
        {config.type === 'api' && (
          <button
            onClick={onTest}
            className="bg-green-500 text-white px-3 py-1 rounded-md text-sm hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500"
          >
            Test
          </button>
        )}
        
        <button
          onClick={onExecute}
          className="bg-purple-500 text-white px-3 py-1 rounded-md text-sm hover:bg-purple-600 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          Execute
        </button>
        
        <button
          onClick={onToggleStatus}
          className={`px-3 py-1 rounded-md text-sm focus:outline-none focus:ring-2 ${
            config.isActive
              ? 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500'
              : 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500'
          }`}
        >
          {config.isActive ? 'Deactivate' : 'Activate'}
        </button>
        
        <button
          onClick={onDelete}
          className="bg-red-500 text-white px-3 py-1 rounded-md text-sm hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500"
        >
          Delete
        </button>
      </div>
    </div>
  );
};

// Configuration Builder Component (simplified - you can expand this)
const ApiConfigBuilder = ({ initialConfig, onSave, onCancel }) => {
  const [config, setConfig] = useState({
    name: '',
    description: '',
    type: 'api',
    keywords: [],
    triggerPhrases: [],
    priority: 0,
    category: 'general',
    isActive: true,
    apiConfig: {
      endpoint: '',
      method: 'GET',
      headers: {},
      authType: 'none',
      authConfig: {},
      responseTemplate: '',
      timeout: 30000,
      retryConfig: {
        enabled: true,
        maxRetries: 3,
        retryDelay: 1000
      }
    },
    ...initialConfig
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationErrors = {};
    if (!config.name.trim()) validationErrors.name = 'Name is required';
    if (!config.type) validationErrors.type = 'Type is required';
    if (config.type === 'api' && !config.apiConfig.endpoint.trim()) {
      validationErrors.endpoint = 'API endpoint is required';
    }
    
    setErrors(validationErrors);
    
    if (Object.keys(validationErrors).length === 0) {
      setIsSubmitting(true);
      try {
        await onSave(config);
      } catch (error) {
        console.error('Error saving config:', error);
        alert('Failed to save configuration: ' + (error.response?.data?.message || error.message));
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-xl font-bold mb-6">
        {initialConfig ? 'Edit Configuration' : 'Create New Configuration'}
      </h2>
      
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">Name *</label>
            <input
              type="text"
              value={config.name}
              onChange={(e) => setConfig({ ...config, name: e.target.value })}
              className={`w-full p-2 border rounded-md ${errors.name ? 'border-red-500' : 'border-gray-300'}`}
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
          </div>
          
          <div>
            <label className="block text-gray-700 font-medium mb-2">Type *</label>
            <select
              value={config.type}
              onChange={(e) => setConfig({ ...config, type: e.target.value })}
              className="w-full p-2 border rounded-md border-gray-300"
            >
              <option value="api">API Configuration</option>
              <option value="form">Form Configuration</option>
            </select>
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-gray-700 font-medium mb-2">Description</label>
          <textarea
            value={config.description}
            onChange={(e) => setConfig({ ...config, description: e.target.value })}
            className="w-full p-2 border rounded-md border-gray-300"
            rows="3"
          />
        </div>

        {config.type === 'api' && (
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">API Endpoint *</label>
            <input
              type="url"
              value={config.apiConfig.endpoint}
              onChange={(e) => setConfig({
                ...config,
                apiConfig: { ...config.apiConfig, endpoint: e.target.value }
              })}
              className={`w-full p-2 border rounded-md ${errors.endpoint ? 'border-red-500' : 'border-gray-300'}`}
              placeholder="https://api.example.com/endpoint"
            />
            {errors.endpoint && <p className="text-red-500 text-sm mt-1">{errors.endpoint}</p>}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">Keywords</label>
            <input
              type="text"
              value={config.keywords.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
              })}
              className="w-full p-2 border rounded-md border-gray-300"
              placeholder="api, data, fetch"
            />
          </div>
          
          <div>
            <label className="block text-gray-700 font-medium mb-2">Trigger Phrases</label>
            <input
              type="text"
              value={config.triggerPhrases.join(', ')}
              onChange={(e) => setConfig({
                ...config,
                triggerPhrases: e.target.value.split(',').map(p => p.trim()).filter(p => p)
              })}
              className="w-full p-2 border rounded-md border-gray-300"
              placeholder="get data, fetch api"
            />
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4 border-t">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-4 py-2 bg-blue-500 text-white rounded-md ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'
            }`}
          >
            {isSubmitting ? 'Saving...' : 'Save Configuration'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default ApiConfigManager;