import React, { useState } from 'react';
import api from '../utils/api';

const ApiTriggerTest = () => {
  const [userInput, setUserInput] = useState('');
  const [userData, setUserData] = useState({
    userId: 'test-user',
    empId: '12345',
    token: 'sample-token',
    roleType: 'admin'
  });
  const [results, setResults] = useState(null);
  const [loading, setLoading] = useState(false);
  const [matchingConfigs, setMatchingConfigs] = useState([]);

  const handleFindMatches = async () => {
    if (!userInput.trim()) return;

    setLoading(true);
    try {
      const response = await api.post('/api-configs/match', {
        userInput: userInput.trim()
      });
      
      setMatchingConfigs(response.data.configurations);
      setResults({
        type: 'match',
        success: true,
        matches: response.data.matches,
        message: `Found ${response.data.matches} matching configuration(s)`
      });
    } catch (error) {
      console.error('Error finding matches:', error);
      setResults({
        type: 'match',
        success: false,
        error: error.response?.data?.message || error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const handleExecuteConfig = async (configId) => {
    setLoading(true);
    try {
      const response = await api.post(`/api-configs/${configId}/execute`, {
        userData: userData,
        inputData: { query: userInput }
      });
      
      setResults({
        type: 'execute',
        success: response.data.success,
        data: response.data.data,
        message: response.data.message,
        responseTime: response.data.responseTime,
        error: response.data.error
      });
    } catch (error) {
      console.error('Error executing config:', error);
      setResults({
        type: 'execute',
        success: false,
        error: error.response?.data?.message || error.message
      });
    } finally {
      setLoading(false);
    }
  };

  const handleProcessInput = async () => {
    if (!userInput.trim()) return;

    setLoading(true);
    try {
      // This would call a new endpoint that processes input and auto-executes
      const response = await api.post('/api-configs/process', {
        userInput: userInput.trim(),
        userData: userData
      });
      
      setResults({
        type: 'process',
        success: response.data.success,
        matches: response.data.matches,
        results: response.data.results,
        message: response.data.message
      });
    } catch (error) {
      console.error('Error processing input:', error);
      setResults({
        type: 'process',
        success: false,
        error: error.response?.data?.message || error.message
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-4xl mx-auto px-4">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">API Trigger Test Interface</h1>
          <p className="text-gray-600 mt-2">Test dynamic API configuration matching and execution</p>
        </div>

        {/* Input Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">User Input</h2>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">Enter your query or command:</label>
            <textarea
              value={userInput}
              onChange={(e) => setUserInput(e.target.value)}
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder="e.g., 'get user data', 'fetch weather info', 'create new record'"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div>
              <label className="block text-gray-700 font-medium mb-2">User ID:</label>
              <input
                type="text"
                value={userData.userId}
                onChange={(e) => setUserData({ ...userData, userId: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">Employee ID:</label>
              <input
                type="text"
                value={userData.empId}
                onChange={(e) => setUserData({ ...userData, empId: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">Token:</label>
              <input
                type="text"
                value={userData.token}
                onChange={(e) => setUserData({ ...userData, token: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-gray-700 font-medium mb-2">Role Type:</label>
              <select
                value={userData.roleType}
                onChange={(e) => setUserData({ ...userData, roleType: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="admin">Admin</option>
                <option value="user">User</option>
                <option value="manager">Manager</option>
                <option value="guest">Guest</option>
              </select>
            </div>
          </div>

          <div className="flex gap-4">
            <button
              onClick={handleFindMatches}
              disabled={loading || !userInput.trim()}
              className={`px-6 py-2 rounded-md text-white ${
                loading || !userInput.trim()
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-blue-500 hover:bg-blue-600'
              }`}
            >
              {loading ? 'Searching...' : 'Find Matches'}
            </button>
            
            <button
              onClick={handleProcessInput}
              disabled={loading || !userInput.trim()}
              className={`px-6 py-2 rounded-md text-white ${
                loading || !userInput.trim()
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-500 hover:bg-green-600'
              }`}
            >
              {loading ? 'Processing...' : 'Process & Execute'}
            </button>
          </div>
        </div>

        {/* Matching Configurations */}
        {matchingConfigs.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Matching Configurations</h2>
            
            <div className="space-y-4">
              {matchingConfigs.map((config) => (
                <div key={config._id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">{config.name}</h3>
                      <p className="text-gray-600 text-sm">{config.description}</p>
                    </div>
                    <div className="flex gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        config.type === 'api' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                      }`}>
                        {config.type.toUpperCase()}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        config.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {config.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Keywords:</span>
                      <span className="ml-2 text-sm text-gray-600">
                        {config.keywords?.join(', ') || 'None'}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-gray-700">Trigger Phrases:</span>
                      <span className="ml-2 text-sm text-gray-600">
                        {config.triggerPhrases?.join(', ') || 'None'}
                      </span>
                    </div>
                  </div>
                  
                  {config.type === 'api' && config.apiConfig && (
                    <div className="mb-3">
                      <span className="text-sm font-medium text-gray-700">Endpoint:</span>
                      <span className="ml-2 text-sm text-gray-600 font-mono bg-gray-100 px-2 py-1 rounded">
                        {config.apiConfig.method} {config.apiConfig.endpoint}
                      </span>
                    </div>
                  )}
                  
                  <button
                    onClick={() => handleExecuteConfig(config._id)}
                    disabled={loading}
                    className={`px-4 py-2 rounded-md text-white ${
                      loading
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-purple-500 hover:bg-purple-600'
                    }`}
                  >
                    {loading ? 'Executing...' : 'Execute This Config'}
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Results Section */}
        {results && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Results</h2>
            
            <div className={`p-4 rounded-lg mb-4 ${
              results.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
            }`}>
              <div className="flex items-center mb-2">
                <span className={`w-3 h-3 rounded-full mr-2 ${
                  results.success ? 'bg-green-500' : 'bg-red-500'
                }`}></span>
                <span className={`font-medium ${
                  results.success ? 'text-green-800' : 'text-red-800'
                }`}>
                  {results.success ? 'Success' : 'Failed'}
                </span>
                {results.responseTime && (
                  <span className="ml-4 text-sm text-gray-600">
                    Response Time: {results.responseTime}ms
                  </span>
                )}
              </div>
              
              {results.message && (
                <p className={`text-sm ${results.success ? 'text-green-700' : 'text-red-700'}`}>
                  {results.message}
                </p>
              )}
              
              {results.error && (
                <p className="text-sm text-red-700 mt-2">
                  <strong>Error:</strong> {results.error}
                </p>
              )}
            </div>

            {/* Detailed Results */}
            {results.data && (
              <div className="mb-4">
                <h3 className="text-lg font-medium mb-2">Response Data:</h3>
                <pre className="bg-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
                  {JSON.stringify(results.data, null, 2)}
                </pre>
              </div>
            )}

            {results.results && Array.isArray(results.results) && (
              <div className="mb-4">
                <h3 className="text-lg font-medium mb-2">Execution Results:</h3>
                <div className="space-y-2">
                  {results.results.map((result, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{result.configName}</span>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          result.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {result.success ? 'Success' : 'Failed'}
                        </span>
                      </div>
                      {result.data && (
                        <pre className="bg-gray-50 p-2 rounded text-xs overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      )}
                      {result.error && (
                        <p className="text-red-600 text-sm mt-1">{result.error}</p>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {results.matches !== undefined && (
              <div className="text-sm text-gray-600">
                <strong>Total Matches Found:</strong> {results.matches}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiTriggerTest;