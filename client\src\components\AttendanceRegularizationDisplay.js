import React from 'react';

const AttendanceRegularizationDisplay = ({ attendanceData, onApplyClick }) => {

  // Function to format date as "18 Jun 2025"
  const formatDate = (dateString) => {
    if (!dateString) return 'Invalid Date';
    
    try {
      const date = new Date(dateString);
      const options = { day: '2-digit', month: 'short', year: 'numeric' };
      return date.toLocaleDateString('en-GB', options);
    } catch (error) {
      console.error('Date formatting error:', error);
      return dateString;
    }
  };

  // Function to format time as "HH:MM AM/PM"
  const formatTime = (timeString) => {
    if (!timeString || timeString === 'N/A' || timeString === '00:00') return 'Not Punched';
    
    try {
      // Handle different time formats
      if (timeString.includes(':')) {
        const [hours, minutes] = timeString.split(':');
        const hour24 = parseInt(hours);
        
        // Handle edge case where time is 00:00
        if (hour24 === 0 && minutes === '00') {
          return 'Not Punched';
        }
        
        const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
        const ampm = hour24 >= 12 ? 'PM' : 'AM';
        return `${hour12}:${minutes} ${ampm}`;
      }
      return timeString;
    } catch (error) {
      console.error('Time formatting error:', error);
      return timeString;
    }
  };

  const handleApplyClick = (record, index) => {
    if (onApplyClick) {
      onApplyClick(record, index);
    }
  };

  // Debug logging
  console.log('AttendanceRegularizationDisplay received:', attendanceData);
  console.log('Is array:', Array.isArray(attendanceData));
  console.log('Length:', attendanceData ? attendanceData.length : 'undefined');

  if (!attendanceData || !Array.isArray(attendanceData) || attendanceData.length === 0) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <p className="text-sm text-yellow-700">
              No attendance records found for regularization.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden mb-4">
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3">
        <div className="flex items-center">
          <svg className="h-5 w-5 text-white mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 00-2 2m-3 7h3m0 0h3m-3 0v3m0-3V9" />
          </svg>
          <h3 className="text-white font-medium">Attendance Records for Regularization</h3>
        </div>
        <p className="text-blue-100 text-sm mt-1">
          Click "Apply" next to any date to submit a regularization request
        </p>
      </div>
      
      <div className="p-4">
        <div className="space-y-3">
          {attendanceData.map((record, index) => (
            <div 
              key={index} 
              className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 transition-colors"
            >
              <div className="flex-1">
                <div className="flex items-center space-x-6">
                  {/* Date */}
                  <div className="flex items-center space-x-2">
                    <svg className="h-4 w-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="font-medium text-gray-900">
                      {formatDate(record.attendanceDate || record.date)}
                    </span>
                  </div>
                  
                  {/* In Time */}
                  <div className="flex items-center space-x-2">
                    <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                    </svg>
                    <span className="text-sm text-gray-600">
                      In: <span className="font-medium text-green-600">
                        {formatTime(record.actualInTime !== '00:00' ? record.actualInTime : record.inTime)}
                      </span>
                    </span>
                  </div>
                  
                  {/* Out Time */}
                  <div className="flex items-center space-x-2">
                    <svg className="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                    </svg>
                    <span className="text-sm text-gray-600">
                      Out: <span className="font-medium text-red-600">
                        {formatTime(record.actualOutTime !== '00:00' ? record.actualOutTime : record.outTime)}
                      </span>
                    </span>
                  </div>
                  
                  {/* Status */}
                  {/* <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                      record.attendanceData === 'P' ? 'bg-green-100 text-green-800' :
                      record.attendanceData === 'A' ? 'bg-red-100 text-red-800' :
                      record.attendanceData === 'L' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {record.attendanceData === 'P' ? 'Present' :
                       record.attendanceData === 'A' ? 'Absent' :
                       record.attendanceData === 'L' ? 'Late' :
                       record.attendanceData || 'Unknown'}
                    </span>
                  </div> */}
                </div>
              </div>
              
              {/* Apply Button */}
              <div className="flex-shrink-0 ml-4">
                <button
                  onClick={() => handleApplyClick(record, index)}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4v16m8-8H4" />
                  </svg>
                  Apply
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Summary */}
      <div className="px-4 py-3 bg-gray-50 border-t border-gray-200">
        <p className="text-sm text-gray-600">
          <span className="font-medium">{attendanceData.length}</span> attendance record{attendanceData.length !== 1 ? 's' : ''} available for regularization
        </p>
      </div>
    </div>
  );
};

export default AttendanceRegularizationDisplay;