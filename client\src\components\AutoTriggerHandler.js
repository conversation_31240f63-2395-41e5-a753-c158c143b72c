import React, { useState, useEffect } from 'react';
import DynamicForm from './DynamicForm';
import api from '../utils/api';

/**
 * AutoTriggerHandler - Component to handle auto-trigger form linking
 * This component checks if any forms should be auto-triggered when data is displayed
 * and provides a modal for the auto-triggered form
 */
const AutoTriggerHandler = ({ 
  formId, 
  recordData, 
  onFormSubmit,
  enabled = true 
}) => {
  const [isChecking, setIsChecking] = useState(false);
  const [autoTriggerForm, setAutoTriggerForm] = useState(null);
  const [showAutoTriggerModal, setShowAutoTriggerModal] = useState(false);
  const [countdown, setCountdown] = useState(0);

  useEffect(() => {
    if (!enabled || !formId || !recordData) return;

    const checkAutoTrigger = async () => {
      setIsChecking(true);
      try {
        console.log('🔍 Checking auto-trigger for form:', formId);
        
        const response = await api.post(`/unifiedconfigs/${formId}/check-auto-trigger`, {
          recordData
        });

        if (response.data.success && response.data.shouldTrigger) {
          const triggerInfo = response.data;
          const delay = triggerInfo.delaySeconds || 0;
          
          console.log(`⏱️ Auto-triggering form in ${delay} seconds...`);
          
          if (delay > 0) {
            // Show countdown
            setCountdown(delay);
            const countdownInterval = setInterval(() => {
              setCountdown(prev => {
                if (prev <= 1) {
                  clearInterval(countdownInterval);
                  triggerForm(triggerInfo);
                  return 0;
                }
                return prev - 1;
              });
            }, 1000);
          } else {
            // Trigger immediately
            triggerForm(triggerInfo);
          }
        }
      } catch (error) {
        console.error('❌ Error checking auto-trigger:', error);
      } finally {
        setIsChecking(false);
      }
    };

    const triggerForm = async (triggerInfo) => {
      try {
        // Process the form linking to get the target form and prefilled data
        const linkingResponse = await api.post(`/unifiedconfigs/${formId}/form-link`, {
          recordData,
          parentData: {},
          actionIndex: triggerInfo.actionIndex
        });

        if (linkingResponse.data.success) {
          console.log('🚀 Auto-triggering form:', linkingResponse.data.targetForm.name);
          
          // Set the auto-triggered form data
          setAutoTriggerForm({
            ...linkingResponse.data.targetForm,
            prefillData: linkingResponse.data.prefillData,
            buttonText: linkingResponse.data.buttonText,
            isAutoTriggered: true
          });
          setShowAutoTriggerModal(true);
        }
      } catch (error) {
        console.error('❌ Error processing auto-trigger:', error);
      }
    };

    checkAutoTrigger();
  }, [formId, recordData, enabled]);

  const handleAutoTriggerFormSubmit = async (formId, formData) => {
    try {
      // Submit the form
      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);
      
      if (response.data.success) {
        setShowAutoTriggerModal(false);
        setAutoTriggerForm(null);
        
        // Notify parent component
        if (onFormSubmit) {
          onFormSubmit(formId, formData, response.data);
        }
      }
    } catch (error) {
      console.error('❌ Error submitting auto-triggered form:', error);
    }
  };

  const handleCloseAutoTriggerModal = () => {
    setShowAutoTriggerModal(false);
    setAutoTriggerForm(null);
    setCountdown(0);
  };

  return (
    <>
      {/* Countdown Indicator */}
      {countdown > 0 && (
        <div className="fixed bottom-4 right-4 bg-blue-500 text-white px-4 py-2 rounded-lg shadow-lg z-40">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span className="text-sm font-medium">
              Auto-opening form in {countdown}s
            </span>
          </div>
        </div>
      )}

      {/* Auto-Trigger Form Modal */}
      {showAutoTriggerModal && autoTriggerForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="bg-blue-500 text-white px-6 py-4 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">
                    🚀 Auto-Opened Form
                  </h2>
                  <p className="text-sm text-blue-100 mt-1">
                    {autoTriggerForm.name}
                  </p>
                </div>
                <button
                  onClick={handleCloseAutoTriggerModal}
                  className="text-white hover:text-gray-200 focus:outline-none"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            
            {/* Modal Content */}
            <div className="p-6">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">Auto-Triggered Form</h3>
                    <div className="mt-1 text-sm text-blue-700">
                      This form was automatically opened based on the data you're viewing. 
                      Some fields may be pre-filled based on the original record.
                    </div>
                  </div>
                </div>
              </div>
              
              <DynamicForm
                form={autoTriggerForm}
                onSubmit={handleAutoTriggerFormSubmit}
                onCancel={handleCloseAutoTriggerModal}
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default AutoTriggerHandler;