import React, { useState, useEffect } from 'react';
import <PERSON>TriggerHandler from './AutoTriggerHandler';
import api from '../utils/api';

/**
 * Component for displaying and interacting with forms in the chat interface
 */
const ChatFormDisplay = ({ form, onSubmit, onCancel, submitButtonText = 'Submit' }) => {
  const [formData, setFormData] = useState({});
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [apiResponse, setApiResponse] = useState(null);
  const [apiError, setApiError] = useState(null);
  const [formLinkingActions, setFormLinkingActions] = useState([]);
  const [linkedForm, setLinkedForm] = useState(null);
  const [showLinkedFormModal, setShowLinkedFormModal] = useState(false);
  const [isProcessingLink, setIsProcessingLink] = useState(false);
  
  // Initialize form data with pre-filled or empty values
  useEffect(() => {
    console.log('ChatFormDisplay received form:', form);
    console.log('Form has fields:', form && Array.isArray(form.fields));
    console.log('Fields count:', form && form.fields ? form.fields.length : 0);
    
    if (form && form.formConfig.fields && Array.isArray(form.formConfig.fields)) {
      const initialData = {};
      
      // Get user data from localStorage for auto-population
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const roleType = localStorage.getItem('roleType');
      // Filter to only process required fields
      const requiredFields = form.formConfig.fields.filter(field => field.required);
      console.log('formmmmm:', form.formConfig.fields);
      console.log('Required fields only:', requiredFields);
      requiredFields.forEach(field => {
        let fieldValue = '';
        
        // Check if this field should be auto-populated
        if (field.name === 'empId' && userData.empId) {
          fieldValue = userData.empId;
        } else if (field.name === 'type') {
          // Prioritize prefill data over roleType for 'type' field
          fieldValue = form.prefillData && form.prefillData[field.name] 
            ? form.prefillData[field.name]
            : roleType || field.defaultValue || field.value || '';
        } else if (field.name === 'appliedTo') {
          // Get supervisor name from localStorage for appliedTo field
          const supervisorName = localStorage.getItem('supervisorName');
          fieldValue = supervisorName || field.defaultValue || field.value || '';
          console.log('🎯 Auto-filling appliedTo field:', {
            supervisorName: supervisorName,
            defaultValue: field.defaultValue,
            value: field.value,
            finalValue: fieldValue
          });
        } else {
          // Use pre-filled value if available, otherwise use defaultValue, otherwise empty string
          fieldValue = form.prefillData && form.prefillData[field.name] 
            ? form.prefillData[field.name] 
            : (field.defaultValue !== undefined && field.defaultValue !== null && field.defaultValue !== '') 
            ? field.defaultValue 
            : '';
        }

        console.log(`🔍 Field "${field.name}" initialization:`, {
          prefillData: form.prefillData?.[field.name],
          defaultValue: field.defaultValue,
          value: field.value,
          finalFieldValue: fieldValue
        });
        
        initialData[field.name] = fieldValue;

      });
      setFormData(initialData);
    }
  }, [form]);

  // Check for form linking configuration
  useEffect(() => {
    if (!form || !formData) return;

    const checkFormLinking = () => {
      const formLinkingConfig = form.formConfig?.formLinking;
      if (!formLinkingConfig?.enabled || !formLinkingConfig.recordActions?.length) {
        return;
      }

      const availableActions = formLinkingConfig.recordActions.map((action, index) => ({
        ...action,
        actionIndex: index,
        shouldShow: shouldShowButton(formData, action.conditions)
      })).filter(action => action.shouldShow);

      setFormLinkingActions(availableActions);
    };

    checkFormLinking();
  }, [form, formData]);

  // Helper function to check if button should be shown
  const shouldShowButton = (record, conditions = []) => {
    if (!conditions || conditions.length === 0) {
      return true;
    }
    
    return conditions.every(condition => {
      const fieldValue = record[condition.field];
      const conditionValue = condition.value;
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === conditionValue;
        case 'not_equals':
          return fieldValue !== conditionValue;
        case 'contains':
          return fieldValue && fieldValue.toString().includes(conditionValue);
        case 'not_contains':
          return !fieldValue || !fieldValue.toString().includes(conditionValue);
        case 'exists':
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        case 'not_exists':
          return fieldValue === undefined || fieldValue === null || fieldValue === '';
        default:
          return true;
      }
    });
  };

  // Handle form linking button click
  const handleFormLink = async (action) => {
    setIsProcessingLink(true);
    setApiError(null);
    
    try {
      const response = await api.post(`/unifiedconfigs/${form._id}/form-link`, {
        recordData: formData,
        parentData: {},
        actionIndex: action.actionIndex
      });

      if (response.data.success) {
        setLinkedForm({
          ...response.data.targetForm,
          prefillData: response.data.prefillData,
          buttonText: response.data.buttonText,
          isManuallyTriggered: true
        });
        setShowLinkedFormModal(true);
      } else {
        setApiError(response.data.message || 'Failed to process form linking');
      }
    } catch (error) {
      console.error('Error processing form linking:', error);
      setApiError('Failed to process form linking');
    } finally {
      setIsProcessingLink(false);
    }
  };

  // Handle auto-trigger form submission
  const handleAutoTriggerSubmit = async (formId, formData, responseData) => {
    console.log('Auto-triggered form submitted:', { formId, formData, responseData });
    // You can add additional logic here if needed
  };
  
  // Helper function to check if a field is auto-populated
  const isAutoPopulated = (fieldName) => {
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    const roleType = localStorage.getItem('roleType');
    const supervisorName = localStorage.getItem('supervisorName');
    
    return (fieldName === 'empId' && userData.empId) || 
           (fieldName === 'type' && roleType) ||
           (fieldName === 'appliedTo' && supervisorName);
  };

  // Handle field value changes
  const handleChange = (e) => {
    const { name, value, type, checked, files } = e.target;
    
    let fieldValue;
    if (type === 'checkbox') {
      fieldValue = checked;
    } else if (type === 'file') {
      // For file inputs, store the file name or empty string
      fieldValue = files && files.length > 0 ? files[0].name : "";
    } else {
      fieldValue = value;
    }
    
    setFormData({
      ...formData,
      [name]: fieldValue
    });
    
    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null
      });
    }
  };
  
  // Validate form data
  const validateForm = () => {
    const newErrors = {};
    
    if (form && form.formConfig && form.formConfig.fields) {
      // Only validate required fields
      const requiredFields = form.formConfig.fields.filter(field => field.required);
      requiredFields.forEach(field => {
        // Check required fields
        if (field.required && !formData[field.name]) {
          newErrors[field.name] = `${field.label} is required`;
        }
        
        // Add more validation as needed (email format, etc.)
        if (field.type === 'email' && formData[field.name] && 
            !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData[field.name])) {
          newErrors[field.name] = 'Invalid email address';
        }
      });
    }
    
    return newErrors;
  };
  
  // Replace placeholders in strings with actual values
  const replacePlaceholders = (str, data = formData) => {
    if (!str) return str;
    
    // Get authentication token from localStorage
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    const authToken = userData.token;
    
    // Debug token retrieval
    console.log('🔍 Debug - userData:', userData);
    console.log('🔍 Debug - authToken:', authToken);
    console.log('🔍 Debug - authToken type:', typeof authToken);
    console.log('🔍 Debug - authToken length:', authToken ? authToken.length : 'N/A');
    
    // Create enhanced data object with form data + auth data
    const enhancedData = {
      ...data,
      token: authToken && authToken !== '<token>' ? authToken : '',
      authToken: authToken && authToken !== '<token>' ? authToken : '',
      bearerToken: authToken && authToken !== '<token>' ? authToken : '',
      empId: userData.empId || '',
      userId: userData.empId || userData.id || '',
      // Add other common placeholders
      supervisorId: localStorage.getItem('supervisorId') || '',
      supervisorName: localStorage.getItem('supervisorName') || '',
      reviewerId: localStorage.getItem('reviewerId') || '',
      reviewerName: localStorage.getItem('reviewerName') || '',
      roleType: localStorage.getItem('roleType') || ''
    };
    
    console.log('🔧 Replacing placeholders with enhanced data:', {
      availablePlaceholders: Object.keys(enhancedData),
      originalString: str
    });
    
    const result = str.replace(/\{([^}]+)\}/g, (match, fieldName) => {
      const replacement = enhancedData[fieldName];
      if (replacement !== undefined && replacement !== '') {
        console.log(`🔧 Replaced {${fieldName}} with: ${fieldName.includes('token') ? replacement.substring(0, 20) + '...' : replacement}`);
        return replacement;
      }
      console.warn(`⚠️ Placeholder {${fieldName}} not found in data`);
      return match;
    });
    
    console.log('🔧 Final result after placeholder replacement:', result);
    return result;
  };
  
  // Prepare API request with form data
  const prepareApiRequest = (data = formData) => {
    if (!form || !form.apiConfig) return null;
    
    const { method, endpoint, headers, authType, authDetails = {} } = form.apiConfig;
    
    // Prepare request object
    const request = {
      method: method || 'GET',
      headers: { 'Content-Type': 'application/json' },
      body: method !== 'GET' ? JSON.stringify(data) : undefined
    };
    
    // Add custom headers with replaced placeholders
    if (headers && Object.keys(headers).length > 0) {
      Object.entries(headers).forEach(([key, value]) => {
        request.headers[key] = replacePlaceholders(value, data);
      });
    }
    
    // Add authentication
    if (authType && authType !== 'none') {
      switch (authType) {
        case 'basic':
          if (authDetails.username && authDetails.password) {
            const credentials = btoa(`${replacePlaceholders(authDetails.username, data)}:${replacePlaceholders(authDetails.password, data)}`);
            request.headers['Authorization'] = `Basic ${credentials}`;
          }
          break;
        case 'bearer':
          // Use token from authDetails, or fallback to localStorage user token
          let bearerToken = authDetails.token;
          
          // If no token in authDetails, try to get from localStorage user object
          if (!bearerToken) {
            const userData = JSON.parse(localStorage.getItem('user') || '{}');
            bearerToken = userData.token;
          }
          
          if (bearerToken && bearerToken !== '<token>') {
            request.headers['Authorization'] = `Bearer ${replacePlaceholders(bearerToken, data)}`;
          }
          break;
        case 'apiKey':
          if (authDetails.key && authDetails.value) {
            if (authDetails.in === 'header') {
              request.headers[authDetails.key] = replacePlaceholders(authDetails.value, data);
            } else if (authDetails.in === 'query') {
              // For query params, we'll handle this in the URL
            }
          }
          break;
        default:
          break;
      }
    }
    
    // Ensure Bearer token is added if no other auth is configured
    if (!authType || authType === 'none') {
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const token = userData.token;
      if (token && token !== '<token>' && !request.headers['Authorization']) {
        request.headers['Authorization'] = `Bearer ${token}`;
        console.log('🔐 Added default Bearer token to request');
      }
    }
    
    // Process endpoint (replace placeholders and add query params)
    let processedEndpoint = replacePlaceholders(endpoint, data);
    
    // Add query parameters for GET requests
    if (method === 'GET' && data && Object.keys(data).length > 0) {
      const queryParams = new URLSearchParams();
      Object.entries(data).forEach(([key, value]) => {
        if (value) queryParams.append(key, value);
      });
      
      // Add API key as query param if needed
      if (authType === 'apiKey' && authDetails.in === 'query') {
        queryParams.append(authDetails.key, replacePlaceholders(authDetails.value, data));
      }
      
      const queryString = queryParams.toString();
      if (queryString) {
        processedEndpoint += processedEndpoint.includes('?') ? '&' : '?';
        processedEndpoint += queryString;
      }
    }
    
    return { request, endpoint: processedEndpoint };
  };

  // Prepare API request specifically for regularization forms
  const prepareRegularizationApiRequest = (data, formConfig) => {
    if (!formConfig || !formConfig.apiConfig) return null;
    
    const { method, endpoint, headers, authType, authDetails = {} } = formConfig.apiConfig;
    
    // Helper function to ensure date is in YYYY-MM-DD format
    const formatDate = (dateValue) => {
      if (!dateValue) return '';
      
      // If already in YYYY-MM-DD format, return as is
      if (/^\d{4}-\d{2}-\d{2}$/.test(dateValue)) {
        return dateValue;
      }
      
      // Try to parse and format the date
      try {
        const date = new Date(dateValue);
        if (isNaN(date.getTime())) return dateValue; // Return original if invalid
        
        // Format as YYYY-MM-DD
        return date.toISOString().split('T')[0];
      } catch (error) {
        console.warn('Date formatting error:', error);
        return dateValue; // Return original if error
      }
    };

    // Create the regularization-specific request body format
    const regularizationRequestBody = {
      regularizationInfo: [
        {
          date: formatDate(data.date || data.attendanceDate),
          reason: data.reason || data.reasonForRegularization || '',
          inTime: data.inTime || data.checkIn || '',
          outTime: data.outTime || data.checkOut || ''
        }
      ],
      appliedTo: localStorage.getItem('supervisorId') || '',
      remark: data.remark || data.remarks || data.additionalRemarks || ''
    };
    
    console.log('🔧 Prepared regularization request body:', regularizationRequestBody);
  
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    const authToken = userData.token;
    
    // Enhanced debugging for regularization request
    console.log('🔐 Regularization Debug - Full userData:', userData);
    console.log('🔐 Regularization Debug - authToken:', authToken);
    console.log('🔐 Regularization Debug - authToken is placeholder?', authToken === '<token>');
    console.log('🔐 Regularization Debug - authToken is valid?', authToken && authToken !== '<token>' && authToken.length > 10);
    // Prepare request object with token validation
    const validToken = authToken && authToken !== '<token>' ? authToken : null;
    
    if (!validToken) {
      console.error('❌ No valid authentication token found!');
      console.error('❌ userData:', userData);
      console.error('❌ authToken:', authToken);
      throw new Error('Authentication token is missing or invalid. Please log in again.');
    }
    
    const request = {
      method: method || 'POST',
      headers: { 
        "Authorization": `Bearer ${validToken}`,
        'Content-Type': 'application/json' 
      },
      body: JSON.stringify(regularizationRequestBody)
    };    
  
    let processedEndpoint = replacePlaceholders(endpoint, data);
    
    return { request, endpoint: processedEndpoint };
  };
  
  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    
    setIsSubmitting(true);
    setApiResponse(null);
    setApiError(null);
    
    // Enhance formData with automatic values from localStorage
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    const roleType = localStorage.getItem('roleType');
    
    let enhancedFormData = { ...formData };
    
    // Add empId if not already present and available in localStorage
    if (form.name.toLowerCase().includes('leave') && !enhancedFormData.empId && userData.empId) {
      enhancedFormData.empId = userData.empId;
    }
    
    // Add type if not already present and roleType is available in localStorage
    if (form.name.toLowerCase().includes('leave') && !enhancedFormData.type && roleType) {
      enhancedFormData.type = roleType;
    }
  
    
    try {
      
      // Log the enhanced form data for debugging
      console.log('Form submission - Enhanced formData:', enhancedFormData);
      console.log('Form API Config:', form.formConfig.submitApiConfig.endpoint);
      
      // Make the actual API call using the form's configuration
      let apiResponse = null;
      
      if (form.formConfig && form.formConfig.submitApiConfig.endpoint) {
        try {
          // Check if this is a regularization form and format request accordingly
          const isRegularizationForm = form.name && form.name.toLowerCase().includes('regular');
          let apiRequest;
          
          console.log('🔍 Form detection:', {
            formName: form.name,
            isRegularizationForm,
            formTitle: form.formTitle || 'Not set'
          });
          
          
          if (apiRequest) {
            const { request, endpoint } = apiRequest;
            console.log('🚀 Making API call to:', endpoint);
            
            // Make the API call
            const response = await fetch(endpoint, request);
            const responseData = await response.text();
            
            // Try to parse as JSON, fallback to text
            let parsedData;
            try {
              parsedData = JSON.parse(responseData);
            } catch {
              parsedData = responseData;
            }
            
            apiResponse = {
              status: response.status,
              statusText: response.statusText,
              data: parsedData,
              success: response.ok
            };
            
            if (response.ok) {
              setApiResponse(apiResponse);
            } else {
              console.log("login response ----------- ", response)
              console.log("login response ----------- ", apiResponse.data.message)
              setApiError(`${apiResponse.data.message}`);
              // setApiError(`API Error: ${response.status} ${response.statusText} `);
            }
          }
        } catch (apiError) {
          console.error('API call failed:', apiError);
          setApiError(`API call failed: ${apiError.message}`);
          apiResponse = {
            status: 0,
            statusText: 'Network Error',
            data: { error: apiError.message },
            success: false
          };
        }
      }
      
      // Always call onSubmit to update the chat with the result
      if (onSubmit) {
        onSubmit({
          formData: enhancedFormData,
          formId: form._id,
          apiResponse: apiResponse,
          success: apiResponse?.success || false
        });
      }
      
      // Set a default success response if no API was configured
      if (!apiResponse) {
        setApiResponse({
          status: 200,
          statusText: 'OK',
          data: { message: 'Form submitted successfully (no API configured)' }
        });
      }
    } catch (error) {
      console.error('Form submission failed:', error);
      setApiError(error.message || 'Failed to submit form');
      
      // Call onSubmit with error information
      if (onSubmit) {
        onSubmit({
          formData: enhancedFormData || formData,
          formId: form._id,
          error: error.message || 'Failed to submit form',
          success: false
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (!form) return null;
  
  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden mb-4">
      <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3">
        <h3 className="text-white font-medium">{form.name}</h3>
        {form.description && (
          <p className="text-blue-100 text-sm mt-1">{form.description}</p>
        )}
        {/* Show required fields info */}
        {form && form.formConfig && form.formConfig.fields && (
          <div className="mt-2 text-blue-100 text-xs">
            <span className="bg-blue-400 bg-opacity-50 px-2 py-1 rounded">
              Required Fields Only: {form.formConfig.fields.filter(field => field.required).length} of {form.formConfig.fields.length} fields
            </span>
          </div>
        )}
      </div>
      
      <div className="p-4">
        {/* Form Fields */}
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {form && form.formConfig && form.formConfig.fields && Array.isArray(form.formConfig.fields) ? 
              form.formConfig.fields.filter(field => field.required).length > 0 ? 
                form.formConfig.fields.filter(field => field.required).map((field) => (

              <div key={field.name} className="form-field">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                  {isAutoPopulated(field.name) && (
                    <span className="text-blue-500 text-xs ml-2">(Auto-filled)</span>
                  )}
                </label>
                
                {field.type === 'text' && (
                  <input
                    type="text"
                    name={field.name}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                    placeholder={field.placeholder}
                    readOnly={field.readonly}
                    className={`w-full p-2 border rounded-md ${
                      errors[field.name] ? 'border-red-500' : 
                      field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' :
                      isAutoPopulated(field.name) ? 'border-blue-300 bg-blue-50' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                )}
                
                {field.type === 'email' && (
                  <input
                    type="email"
                    name={field.name}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                    placeholder={field.placeholder}
                    readOnly={field.readonly}
                    className={`w-full p-2 border rounded-md ${
                      errors[field.name] ? 'border-red-500' : 
                      field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' :
                      'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                )}
                
                {field.type === 'number' && (
                  <input
                    type="number"
                    name={field.name}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                    placeholder={field.placeholder}
                    readOnly={field.readonly}
                    className={`w-full p-2 border rounded-md ${
                      errors[field.name] ? 'border-red-500' : 
                      field.readonly ? 'border-gray-300 bg-gray-100 text-gray-700 cursor-not-allowed' :
                      'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                )}
                
                {field.type === 'textarea' && (
                  <textarea
                    name={field.name}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                    placeholder={field.placeholder}
                    rows="3"
                    className={`w-full p-2 border rounded-md ${
                      errors[field.name] ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                )}
                
                {field.type === 'select' && (
                  <select
                    name={field.name}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                    className={`w-full p-2 border rounded-md ${
                      errors[field.name] ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  >
                    <option value="">Select an option</option>
                    {field.options && field.options.map((option, index) => (
                      <option key={index} value={option}>
                        {option}
                      </option>
                    ))}
                  </select>
                )}
                
                {field.type === 'checkbox' && (
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      name={field.name}
                      checked={formData[field.name] || false}
                      onChange={handleChange}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-600">
                      {field.placeholder}
                    </span>
                  </div>
                )}
                
                {field.type === 'radio' && field.options && (
                  <div className="space-y-2">
                    {field.options.map((option, index) => (
                      <div key={index} className="flex items-center">
                        <input
                          type="radio"
                          id={`${field.name}-${index}`}
                          name={field.name}
                          value={option}
                          checked={formData[field.name] === option}
                          onChange={handleChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label
                          htmlFor={`${field.name}-${index}`}
                          className="ml-2 text-sm text-gray-600"
                        >
                          {option}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
                
                {field.type === 'date' && (
                  <input
                    type="date"
                    name={field.name}
                    value={formData[field.name] || ''}
                    onChange={handleChange}
                    className={`w-full p-2 border rounded-md ${
                      errors[field.name] ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                )}
                
                {field.type === 'file' && (
                  <input
                    type="file"
                    name={field.name}
                    onChange={handleChange}
                    className={`w-full p-2 border rounded-md ${
                      errors[field.name] ? 'border-red-500' : 'border-gray-300'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  />
                )}
                
                {errors[field.name] && (
                  <p className="mt-1 text-sm text-red-500">{errors[field.name]}</p>
                )}
              </div>
            )) : (
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <p className="text-yellow-700 font-medium">⚠️ No Required Fields</p>
                <p className="text-yellow-600 text-sm mt-1">
                  This form has no required fields to display. All fields are optional.
                </p>
              </div>
            ) : (
              <div className="p-4 bg-red-50 border border-red-200 rounded-md">
                <p className="text-red-700 font-medium">❌ Form Error</p>
                <p className="text-red-600 text-sm mt-1">
                  Form fields are not available. Please contact your administrator.
                </p>
                <p className="text-gray-600 text-xs mt-2">
                  Debug: form={form ? 'exists' : 'null'}, fields={form && form.formConfig && form.formConfig.fields ? 'exists' : 'null'}
                </p>
              </div>
            )}
          </div>
          
          {/* API Response Display */}
          {/* {apiResponse && (
            <div className="mt-4 p-3 bg-gray-50 rounded-md border border-gray-200">
              <h4 className="text-sm font-medium text-gray-700 mb-2">API Response</h4>
              <div className="bg-gray-800 text-green-400 p-3 rounded-md text-sm font-mono overflow-auto max-h-60">
                <pre>{JSON.stringify(apiResponse.data, null, 2)}</pre>
              </div>
            </div>
          )} */}
          
          {/* API Error Display */}
          {apiError && (
            <div className="mt-4 p-3 bg-red-50 rounded-md border border-red-200">
              {/* <h4 className="text-sm font-medium text-red-700 mb-1">Error</h4> */}
              <p className="text-sm text-red-600">{apiError}</p>
            </div>
          )}
          
          {/* Form Linking Buttons */}
          {formLinkingActions.length > 0 && (
            <div className="mt-4 bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="text-sm font-medium text-gray-700 mb-3">Available Actions</h3>
              <div className="flex flex-wrap gap-2">
                {formLinkingActions.map((action) => (
                  <button
                    key={action.actionIndex}
                    onClick={() => handleFormLink(action)}
                    disabled={isProcessingLink}
                    className={`px-4 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                      getButtonClasses(action.buttonStyle)
                    } ${isProcessingLink ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    {isProcessingLink ? 'Processing...' : action.buttonText}
                  </button>
                ))}
              </div>
            </div>
          )}
          
          {/* Form Actions */}
          <div className="mt-4 flex justify-end space-x-2">
            {onCancel && (
              <button
                type="button"
                onClick={onCancel}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
            )}
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-3 py-2 rounded-md text-sm font-medium text-white ${
                isSubmitting ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
              } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500`}
            >
              {isSubmitting ? (
                <span className="flex items-center">
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </span>
              ) : (
                submitButtonText
              )}
            </button>
          </div>
        </form>
      </div>

      {/* Auto-Trigger Handler */}
      <AutoTriggerHandler
        formId={form._id}
        recordData={formData}
        onFormSubmit={handleAutoTriggerSubmit}
        enabled={true}
      />

      {/* Linked Form Modal */}
      {showLinkedFormModal && linkedForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="bg-green-500 text-white px-6 py-4 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">
                    🔗 Linked Form
                  </h2>
                  <p className="text-sm text-green-100 mt-1">
                    {linkedForm.name}
                  </p>
                </div>
                <button
                  onClick={() => setShowLinkedFormModal(false)}
                  className="text-white hover:text-gray-200 focus:outline-none"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            
            {/* Modal Content */}
            <div className="p-6">
              <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">Linked Form</h3>
                    <div className="mt-1 text-sm text-green-700">
                      This form was opened through form linking. 
                      Some fields may be pre-filled based on the original record.
                    </div>
                  </div>
                </div>
              </div>
              
              <ChatFormDisplay
                form={linkedForm}
                onSubmit={async (formId, formData) => {
                  try {
                    await api.post(`/unifiedconfigs/${formId}/submit`, formData);
                    setShowLinkedFormModal(false);
                    setLinkedForm(null);
                  } catch (error) {
                    console.error('Error submitting linked form:', error);
                  }
                }}
                onCancel={() => {
                  setShowLinkedFormModal(false);
                  setLinkedForm(null);
                }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to get button classes based on style
const getButtonClasses = (style) => {
  switch (style) {
    case 'primary':
      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';
    case 'secondary':
      return 'bg-gray-500 text-white hover:bg-gray-600 focus:ring-gray-500';
    case 'success':
      return 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500';
    case 'warning':
      return 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500';
    case 'danger':
      return 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500';
    default:
      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';
  }
};

export default ChatFormDisplay;