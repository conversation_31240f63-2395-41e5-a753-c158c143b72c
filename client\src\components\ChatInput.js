import React, { useState } from 'react';

const ChatInput = ({ onSendMessage, loading, conversationalFlow, hybridFlow }) => {
  const [message, setMessage] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !loading) {
      onSendMessage(message);
      setMessage('');
    }
  };

  const handleCancel = () => {
    onSendMessage('cancel');
    setMessage('');
  };

  return (
    <div>      
      {/* Input form */}
      <form onSubmit={handleSubmit} className="flex items-center border-t border-gray-200 p-4">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder={
            (conversationalFlow && conversationalFlow.isActive) ? 
              "Type your answer or 'cancel' to exit the form..." : 
            (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) ? 
              "🔄 Type your answer or 'cancel' to exit the hybrid form..." : 
              "Ask a question..."
          }
          className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          disabled={loading}
        />
        {/* Show cancel button when in conversational flow or hybrid flow conversational phase */}
        {((conversationalFlow && conversationalFlow.isActive) || 
          (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase)) && (
          <button
            type="button"
            onClick={handleCancel}
            className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 transition-colors"
            disabled={loading}
          >
            Cancel
          </button>
        )}
        <button
          type="submit"
          className={`bg-blue-500 text-white p-2 ${
            (conversationalFlow && conversationalFlow.isActive) || 
            (hybridFlow && hybridFlow.isActive && hybridFlow.isConversationalPhase) ? 
              'rounded-r-md' : 'rounded-r-md'
          } ${
            loading ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'
          }`}
          disabled={loading}
        >
          {loading ? (
            <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </form>
    </div>
  );
};

export default ChatInput;