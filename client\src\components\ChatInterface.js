import React, { useState, useEffect, useRef } from 'react';
import { useForm } from '../context/FormContext';
import { useChat } from '../context/ChatContext';
import { useAuth } from '../context/AuthContext';
import api from '../utils/api';
import ChatInput from './ChatInput';
import ChatMessage from './ChatMessage';
import ChatFormDisplay from './ChatFormDisplay';
import HybridForm from './HybridForm';

import TypingIndicator from './TypingIndicator';
import AttendanceRegularizationDisplay from './AttendanceRegularizationDisplay';

const ChatInterface = () => {
  const { forms, getForms, getFormByName } = useForm();
  const { 
    messages, 
    loading, 
    activeForm, 
    conversationalFlow,
    hybridFlow,
    conversationId,
    sendMessage, 
    addAssistantMessage,
    submitForm, 
    dismissForm,
    clearChat,
    setHybridFlow,
    setMessages,
    setConversationId,
    setConversationalFlow,
    setActiveForm
  } = useChat();
  const { user, login, isLoggedIn, logout } = useAuth();
  
  const [loginForm, setLoginForm] = useState(null);
  const [loadingLoginForm, setLoadingLoginForm] = useState(true);
  const [hybridFormId, setHybridFormId] = useState(null);
  
  const messagesEndRef = useRef(null);
  
  // Fetch login form on component mount - only once
  useEffect(() => {
    const fetchLoginForm = async () => {
      // Only fetch if we don't already have the login form
      if (!loginForm) {
        try {
          setLoadingLoginForm(true);
          const form = await getFormByName('Login');
          setLoginForm(form);
        } catch (error) {
          console.error('Error fetching login form:', error);
        } finally {
          setLoadingLoginForm(false);
        }
      }
    };
    
    // Only fetch if user is not logged in and we don't have the form yet
    if (!isLoggedIn() && !loginForm) {
      fetchLoginForm();
    }
  }, [isLoggedIn, loginForm, getFormByName]);
  
  // Fetch forms on component mount
  useEffect(() => {
    if (isLoggedIn()) {
      getForms();
    }
  }, [getForms, isLoggedIn]);
  
  // Add welcome message when user logs in
  useEffect(() => {
    if (isLoggedIn() && messages.length === 0) {
      // Use firstName if available, otherwise fall back to username or empId
      const displayName = user.firstName || user.username || user.empId;
      addAssistantMessage(`Hey ${displayName}, how can I help you today?`);
    }
  }, [isLoggedIn, messages.length, addAssistantMessage, user]);
  
  // Scroll to bottom of messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);
  
  // Process user message using the ChatContext
  const processMessage = async (message) => {
    // Use the ChatContext to send the message to the server
    await sendMessage(message);
  };
  
  // Handle form submission using the ChatContext
  const handleFormSubmit = async (result) => {
    // If this is the login form, handle authentication
    if (activeForm && activeForm.name === 'Login') {
      try {
        // Call the server-side login endpoint (no CORS issues)
        const response = await api.post('/auth/login', {
          empId: result.formData.empId,
          password: result.formData.password
        });
        
        // Check if login was successful
        if (response.data && response.data.success === true && response.data.data) {
          const userData = response.data.data;
          
          // Store roleType in localStorage for form submissions
          if (userData.roleType) {
            localStorage.setItem('roleType', userData.roleType);
          }
          
          // Store user in localStorage and update auth context
          await login(userData);
          
          // Clear chat and dismiss the login form
          clearChat();
          // dismissForm();
          
          // Add welcome message using firstName
          addAssistantMessage(`Hey ${userData.firstName}, how can I help you today?`);
        } else {
          // Show error message in chat
          addAssistantMessage('Login failed. Please check your credentials and try again.');
        }
      } catch (error) {
        console.error('Login error:', error);
        
        // Show appropriate error message based on the error response
        if (error.response && error.response.status === 401) {
          addAssistantMessage('Invalid employee ID or password. Please try again.');
        } else if (error.response && error.response.status === 503) {
          addAssistantMessage('Login service is temporarily unavailable. Please try again later.');
        } else {
          addAssistantMessage('Login failed. Please try again later.');
        }
      }
    } else {
      // For other forms, use the normal submit handler with API response
      submitForm(activeForm._id, result.formData, false, null, result.apiResponse, result.success);
    }
  };
  
  // Handle form cancellation using the ChatContext
  const handleFormCancel = () => {
    // Use the ChatContext to dismiss the form
    // This will also add cancellation messages to the chat
    dismissForm();
  };

  // Handle conversational form option selection
  const handleOptionSelect = (option) => {
    // Send the selected option as a user message
    processMessage(option);
  };
  
  // Handle user logout
  const handleLogout = () => {
    logout();
    clearChat();
  };

  // Handle form linking (Apply button clicks)
  const handleFormLinkTriggered = async (linkData) => {
    try { 
      // Check if the target form has hybrid flow enabled
      const isHybridFlow = linkData.targetForm.formConfig?.hybridFlow?.enabled;
      
      if (isHybridFlow) {
        console.log('🔄 Starting hybrid flow for linked form without showing form name:', linkData.targetForm.name);
        
        // Store prefill data temporarily for the form
        localStorage.setItem('tempPrefillData', JSON.stringify(linkData.prefillData));
        
        // Instead of sending the form name as a message, directly start the conversational phase
        // by making a direct API call to start the form conversation
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        
        const response = await api.post('/chat', {
          message: `__START_FORM_DIRECT__${linkData.targetForm.name}`, // Special marker to start form directly
          conversationId,
          empId: userData.empId,
          roleType: userData.roleType,
          supervisorName: userData.supervisorName,
          supervisorId: userData.supervisorId,
          skipFormNameDisplay: true // Flag to skip form name display
        }, {
          headers: {
            'x-emp-id': userData.empId,
            'x-role-type': userData.roleType,
            'Authorization': userData.token ? `Bearer ${userData.token}` : undefined
          }
        });
        
        // Process the response similar to sendMessage but without adding the form name message
        // Construct formData from hybridFlow if not present
        const formData = response.data.formData || (response.data.hybridFlow ? {
          _id: response.data.hybridFlow.formId,
          name: response.data.hybridFlow.formName,
          formConfig: response.data.formConfig
        } : null);
        
        const assistantMessage = {
          role: 'assistant',
          content: response.data.message,
          formData: formData,
          formConfig: response.data.formConfig,
          queryIntent: response.data.queryIntent,
          apiResponse: response.data.apiResponse,
          matchResults: response.data.matchResults,
          conversationalFlow: response.data.conversationalFlow,
          fieldType: response.data.conversationalFlow?.fieldType || response.data.hybridFlow?.fieldType || response.data.fieldType,
          options: response.data.conversationalFlow?.options || response.data.hybridFlow?.options || response.data.options,
          isFormFlow: response.data.conversationalFlow?.isActive || response.data.isFormFlow,
          fieldName: response.data.conversationalFlow?.fieldName || response.data.hybridFlow?.fieldName || response.data.fieldName,
          currentStep: response.data.conversationalFlow?.currentStep || response.data.hybridFlow?.currentStep || response.data.currentStep,
          totalSteps: response.data.conversationalFlow?.totalSteps || response.data.hybridFlow?.totalConversationalSteps || response.data.totalSteps,
          hybridFlow: response.data.hybridFlow,
          isHybridFlow: response.data.isHybridFlow,
          isConversationalPhase: response.data.isConversationalPhase,
          transitionToForm: response.data.transitionToForm,
          formFields: response.data.formFields,
          collectedData: response.data.collectedData,
          conversationalData: response.data.conversationalData,
          isDynamicResponse: true,
          isFormLinkTriggered: true, // Flag to identify form linking messages
          messageId: `form-link-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`, // Unique prefix for form linking
          timestamp: response.data.apiResponse?.timestamp || Date.now()
        };
        
        // Update conversation ID if new
        if (response.data.conversationId && !conversationId) {
          setConversationId(response.data.conversationId);
        }
        
        // Handle conversational flow state
        if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {
          setConversationalFlow(response.data.conversationalFlow);
          setActiveForm(null);
        } else if (response.data.queryIntent === 'hybrid_form_conversation') {
          setHybridFlow(response.data.hybridFlow);
          setConversationalFlow(null);
          setActiveForm(null);
        } else if (response.data.queryIntent === 'hybrid_form_transition') {
          // Handle hybrid form transition (conversational to form phase)
          setHybridFlow(response.data.hybridFlow);
          setConversationalFlow(null);
          setActiveForm(null);
        }
        
        // Add the assistant message to chat (this will show the questions directly)
        // Use the same deduplication logic as in ChatContext
        setMessages((prevMessages) => {
          // For form linking messages, use a more specific duplicate check
          const isDuplicate = prevMessages.some(msg => 
            msg.role === 'assistant' && (
              // Check for same messageId (most specific)
              (msg.messageId === assistantMessage.messageId) ||
              // For form linking messages, check for same form ID and transition state
              (msg.isFormLinkTriggered && assistantMessage.isFormLinkTriggered &&
               msg.formData?._id === assistantMessage.formData?._id &&
               msg.transitionToForm === assistantMessage.transitionToForm &&
               Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 2000) ||
              // Check for same content + options + timestamp within 2 seconds (for regular messages)
              (!msg.isFormLinkTriggered && !assistantMessage.isFormLinkTriggered &&
               msg.content === assistantMessage.content && 
               JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) &&
               Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 2000)
            )
          );
          
          if (isDuplicate) {
            return prevMessages;
          }
          
          return [...prevMessages, assistantMessage];
        });
        
        // Clean up temporary storage
        localStorage.removeItem('tempPrefillData');
        
      } else {
        
        // For traditional forms, also skip the form name display
        // Create a form with prefilled data and show it directly
        const formWithPrefillData = {
          ...linkData.targetForm,
          prefillData: linkData.prefillData
        };
        
        // Show the target form with pre-filled data without the opening message
        submitForm(linkData.targetForm._id, {}, true, formWithPrefillData);
      }
      
    } catch (error) {
      console.error('Error handling form link:', error);
      addAssistantMessage('❌ Error opening linked form. Please try again.');
    }
  };

  // Handle regularization apply button click
  const handleRegularizationApply = async (attendanceRecord, index) => {
    try {
   
      
      // If forms are not loaded yet, try to load them first
      if (!forms || forms.length === 0) {
        console.log('⏳ Forms not loaded yet, attempting to fetch...');
        try {
          const fetchedForms = await getForms(); // This should fetch forms if not already loaded
          console.log('📋 Fetched forms:', fetchedForms);
        } catch (error) {
          console.error('❌ Failed to fetch forms:', error);
          addAssistantMessage('❌ Unable to load forms. Please try again or contact your administrator.');
          return;
        }
      }
      
      // Find the regularization form in the forms list
      let regularizationForm = null;
      
      // Try to find form by different possible names
      const possibleFormNames = [
        'Regularization',
        'regularization',
        'Attendance Regularization',
        'attendance regularization',
        'AttendanceRegularization',
        'attendance_regularization',
        'RegularizationForm',
        'regularization_form'
      ];
      
      // First try to find in the loaded forms array
      if (forms && Array.isArray(forms)) {
        for (const formName of possibleFormNames) {
          regularizationForm = forms.find(form => 
            form.name === formName || 
            form.formTitle === formName ||
            (form.name && form.name.toLowerCase() === formName.toLowerCase()) ||
            (form.formTitle && form.formTitle.toLowerCase() === formName.toLowerCase())
          );
          console.log(`🔍 Searching in loaded forms for "${formName}":`, regularizationForm ? 'Found' : 'Not found');
          if (regularizationForm) break;
        }
      }
      
      // If not found in loaded forms, try API calls
      if (!regularizationForm) {
        console.log('🌐 Form not found locally, trying API calls...');
        for (const formName of possibleFormNames) {
          try {
            regularizationForm = await getFormByName(formName);
            console.log(`🔍 API search for "${formName}":`, regularizationForm ? 'Found' : 'Not found');
            if (regularizationForm) break;
          } catch (error) {
            console.log(`❌ API error for "${formName}":`, error.message);
          }
        }
      }
      
      if (!regularizationForm && forms && Array.isArray(forms)) {
        // If no form found by name, try to find by checking form fields
        regularizationForm = forms.find(form => {
          if (!form || !form.fields || !Array.isArray(form.fields)) return false;
          const fieldNames = form.fields.map(field => field.name.toLowerCase());
          return fieldNames.includes('date') && 
                 (fieldNames.includes('intime') || fieldNames.includes('reason'));
        });
      }
      
      if (!regularizationForm) {
        console.log('❌ No regularization form found');
        console.log('📋 Available form names:', forms ? forms.map(f => f.formTitle || f.name || f._id) : 'No forms');
        addAssistantMessage('❌ Regularization form not found. Please contact your administrator.');
        return;
      }
      
      console.log('✅ Found regularization form:', regularizationForm.formTitle || regularizationForm.name);
      console.log('📋 Form fields:', regularizationForm.fields ? regularizationForm.fields.map(f => f.name) : 'No fields');
      
      // Prepare pre-filled data for the form
      // Use actual time if available (not 00:00), otherwise use scheduled time
      const actualInTime = attendanceRecord.actualInTime !== '00:00' ? attendanceRecord.actualInTime : attendanceRecord.inTime;
      const actualOutTime = attendanceRecord.actualOutTime !== '00:00' ? attendanceRecord.actualOutTime : attendanceRecord.outTime;
      
      const prefillData = {
        date: attendanceRecord.attendanceDate || attendanceRecord.date,
        inTime: actualInTime || attendanceRecord.inTime || attendanceRecord.checkIn,
        outTime: actualOutTime || attendanceRecord.outTime || attendanceRecord.checkOut,
        appliedTo: localStorage.getItem('supervisorName'),
        // Add any other common fields that might match
        attendanceDate: attendanceRecord.attendanceDate || attendanceRecord.date,
        checkIn: actualInTime || attendanceRecord.inTime || attendanceRecord.checkIn,
        checkOut: actualOutTime || attendanceRecord.outTime || attendanceRecord.checkOut
      };
      
      console.log('Pre-fill data prepared:', prefillData);
      
      // Create a copy of the form with prefilled data
      const formWithPrefillData = {
        ...regularizationForm,
        prefillData: prefillData
      };
      
      // Show the form with pre-filled data using ChatContext
      submitForm(regularizationForm._id, {}, true, formWithPrefillData);
      
      // Add a message to indicate the form is being prepared
      addAssistantMessage(`📝 Preparing regularization form for ${attendanceRecord.attendanceDate || attendanceRecord.date}. Please fill in the required details.`);
      
    } catch (error) {
      console.error('Error handling regularization apply:', error);
      addAssistantMessage('❌ Error opening regularization form. Please try again.');
    }
  };
  
  // Show login form when user is not logged in - with proper dependency tracking
  useEffect(() => {
    // Only set the login form as active if:
    // 1. User is not logged in
    // 2. We have the login form
    // 3. Either there's no active form or the active form is not the login form
    if (!isLoggedIn() && loginForm && (!activeForm || activeForm.name !== 'Login')) {
      // Set the login form as the active form, passing the form object directly to avoid an extra API call
      submitForm(loginForm._id, null, true, loginForm);
    }
  }, [isLoggedIn, loginForm, activeForm, submitForm]); // Include all dependencies
  
  return (
    <div className="flex flex-col h-full bg-white rounded-lg shadow-lg overflow-hidden">
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 p-4 text-white flex justify-between items-center">
        <h2 className="text-xl font-bold">AI Form Assistant</h2>
        {isLoggedIn() && (
          <div className="flex items-center">
            <span className="text-sm mr-3">
              Logged in as {user.firstName || user.username || user.empId}
            </span>
            <button 
              onClick={handleLogout}
              className="bg-blue-700 hover:bg-blue-800 text-white text-sm py-1 px-3 rounded"
            >
              Logout
            </button>
          </div>
        )}
      </div>
      
      <div className="flex-1 overflow-y-auto p-4">
        {!isLoggedIn() ? (
          <div className="flex items-center justify-center h-full">
            {loadingLoginForm ? (
              <div className="text-center">
                <TypingIndicator />
                <p className="mt-2 text-gray-600">Loading login form...</p>
              </div>
            ) : (
              <>
                {activeForm && activeForm.name === 'Login' ? (
                  <div className="w-full max-w-md">
                    <h2 className="text-2xl font-bold text-gray-800 mb-6 text-center">Login to Chat</h2>
                    <ChatFormDisplay 
                      form={activeForm} 
                      onSubmit={handleFormSubmit} 
                      submitButtonText="Login"
                    />
                  </div>
                ) : (
                  <div className="text-center">
                    <p className="text-gray-600">Login form not found. Please try again later.</p>
                  </div>
                )}
              </>
            )}
          </div>
        ) : (
          <>
            {messages.map((message, index) => (
              <div key={message.messageId || `message-${index}-${message.role}-${message.timestamp || index}`}>
                <ChatMessage 
                  message={message} 
                  onOptionSelect={handleOptionSelect} 
                  onFormLinkTriggered={handleFormLinkTriggered}
                />
                

                
                {/* Show legacy response indicator */}
                {message.isLegacyResponse && (
                  <div className="mt-2 p-2 bg-orange-50 border border-orange-200 rounded">
                    <div className="flex items-center text-orange-700 text-xs">
                      <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      Legacy Response (will be replaced by dynamic APIs)
                    </div>
                  </div>
                )}
                
                {/* Show attendance regularization display if this message contains attendance info */}
                {message.isRegularizationData && message.attendanceInfo && (
                  <div className="mt-2">
                    {console.log('🎨 Rendering AttendanceRegularizationDisplay with:', message.attendanceInfo)}
                    <AttendanceRegularizationDisplay 
                      attendanceData={message.attendanceInfo}
                      onApplyClick={handleRegularizationApply}
                    />
                  </div>
                )}
                {message.isRegularizationData && !message.attendanceInfo && (
                  <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded">
                    {console.log('⚠️ Regularization data found but no attendanceInfo:', message)}
                    <p className="text-yellow-700">Regularization data received but no attendance info available.</p>
                  </div>
                )}
              </div>
            ))}
            
            {/* Show typing indicator when loading */}
            {loading && <TypingIndicator />}
            
            {/* Display active form if available and not the login form */}
            {activeForm && activeForm.name !== 'Login' && (
              <div className="my-4">
                <ChatFormDisplay 
                  form={activeForm} 
                  onSubmit={handleFormSubmit} 
                  onCancel={handleFormCancel}
                />
              </div>
            )}

            {/* Display hybrid form if transitioning to form phase */}
            {hybridFlow && hybridFlow.transitionToForm && (
              <div className="my-4">
                <HybridForm 
                  hybridFlow={hybridFlow}
                  message={{ content: hybridFlow.completionMessage }}
                  formId={hybridFlow.formId}
                  onCancel={() => {
                    // Cancel hybrid form
                    sendMessage('cancel');
                  }}
                  onSubmit={(response) => {
                    // Handle form submission response
                    console.log('🔄 ChatInterface: Handling hybrid form submission response:', response);
                    
                    // Add the response message to chat
                    if (response.message) {
                      console.log('➕ Adding message to chat:', response.message);
                      addAssistantMessage(response.message);
                    }
                    
                    // Check if we're continuing to a conversational phase
                    if (response.isConversationalPhase && response.hybridFlow) {
                      console.log('🔄 Continuing to next conversational phase');
                      setHybridFlow({
                        ...response.hybridFlow,
                        transitionToForm: false,
                        isConversationalPhase: true
                      });
                    } else {
                      // Form completed or other state
                      console.log('✅ Hybrid form completed');
                      setHybridFlow(null);
                    }
                  }}
                />
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </>
        )}
      </div>
      
      {isLoggedIn() && (
        <ChatInput 
          onSendMessage={processMessage} 
          loading={loading}
          conversationalFlow={conversationalFlow}
          hybridFlow={hybridFlow}
        />
      )}
    </div>
  );
};

export default ChatInterface;
