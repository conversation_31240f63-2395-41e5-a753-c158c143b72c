import React, { useState } from 'react';
import RecordDisplayWithActions from './RecordDisplayWithActions';

const ChatMessage = ({ message, onOptionSelect, onFormLinkTriggered }) => {
  const isUser = message.role === 'user';
  const [selectedCheckboxes, setSelectedCheckboxes] = useState([]);

  // Handle checkbox selection
  const handleCheckboxToggle = (option) => {
    const newSelected = selectedCheckboxes.includes(option)
      ? selectedCheckboxes.filter(item => item !== option)
      : [...selectedCheckboxes, option];
    setSelectedCheckboxes(newSelected);
  };

  // Handle checkbox submission
  const handleCheckboxSubmit = () => {
    if (selectedCheckboxes.length > 0) {
      onOptionSelect && onOptionSelect(selectedCheckboxes.join(', '));
      setSelectedCheckboxes([]);
    }
  };

  // Handle single option selection (for radio/select)
  const handleSingleOptionSelect = (option) => {
    onOptionSelect && onOptionSelect(option);
  };
  // Helper function to check if data is empty (comprehensive check)
  const checkIfDataIsEmpty = (data) => {
    if (!data) return true;
    if (Array.isArray(data)) return data.length === 0;
    if (typeof data === 'object' && data !== null) {
      if (Object.keys(data).length === 0) return true;
      // Check nested data structure
      if (data.data) return checkIfDataIsEmpty(data.data);
      // Check common array fields
      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];
      for (const field of arrayFields) {
        if (data[field] && Array.isArray(data[field])) {
          return data[field].length === 0;
        }
      }
      // Check if all values are empty
      return Object.values(data).every(val => 
        val === null || val === undefined || val === '' || 
        (Array.isArray(val) && val.length === 0) ||
        (typeof val === 'object' && val !== null && Object.keys(val).length === 0)
      );
    }
    return false;
  };

  // Helper function to render "No data available" message
  const renderNoDataMessage = () => (
    <div className="mt-2 p-3 bg-blue-50 rounded-md border border-blue-200">
      <p className="text-blue-800 text-sm font-medium">No data available</p>
    </div>
  );

  // Helper function to check if data is leave balance related
  const isLeaveBalanceData = (data) => {
    if (!data) return false;

    // Check if data contains leave balance information
    if (Array.isArray(data)) {
      return data.some(item =>
        item.hasOwnProperty('leaveTypeName') ||
        item.hasOwnProperty('leaveType') ||
        item.hasOwnProperty('balance')
      );
    }

    if (typeof data === 'object') {
      return data.hasOwnProperty('leaveTypeName') ||
             data.hasOwnProperty('leaveType') ||
             data.hasOwnProperty('balance') ||
             (data.data && isLeaveBalanceData(data.data));
    }

    return false;
  };

  // Format API response for display
  const formatApiResponse = (apiResponse) => {
    if (!apiResponse) return null;

    // Try multiple paths to find form linking config (define at top level for access throughout function)
    const formLinkingConfig = message.formData?.formConfig?.formLinking ||
                             message.formConfig?.formLinking ||
                             message.formData?.formLinking ||
                             message.formConfig?.formConfig?.formLinking;

    // For successful responses with data, check if we should display records with actions
    if (apiResponse.success && apiResponse.data) {

      // Check if this message has active conversational flow - if so, prioritize it over form linking
      const hasActiveConversationalFlow = message.isConversationalPhase ||
                                         message.isHybridFlow ||
                                         message.conversationalFlow?.isActive ||
                                         message.hybridFlow?.isActive ||
                                         (message.options && message.options.length > 0 && (message.isConversationalPhase || message.isHybridFlow));

      // If form linking is enabled and we have record actions, BUT NO ACTIVE CONVERSATIONAL FLOW
      if (formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0 && !hasActiveConversationalFlow) {

        // Extract the actual data from the API response
        const actualData = apiResponse.data?.data || apiResponse.data;
        
        const formId = message.formData?._id || message.formConfig?._id;
        
        // Function to render records with interleaved apply buttons
        const renderRecordsWithButtons = () => {
          let records = [];
          
          // Extract records from actualData
          if (Array.isArray(actualData)) {
            records = actualData;
          } else if (actualData && typeof actualData === 'object') {
            const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];
            for (const field of arrayFields) {
              if (actualData[field] && Array.isArray(actualData[field])) {
                records = actualData[field];
                break;
              }
            }
            if (records.length === 0) {
              records = [actualData];
            }
          }
          
          // Check if records are empty using helper function
          if (checkIfDataIsEmpty(records)) {
            return renderNoDataMessage();
          }

          // Split formatted response by "Record N:" pattern
          let formattedRecords = [];
          if (apiResponse.formattedResponse) {
            const recordSections = apiResponse.formattedResponse.split(/(?=Record \d+:)/);
            formattedRecords = recordSections.filter(section => section.trim());
          }

          return (
            <div className="mt-3">
              {records.map((record, index) => (
                <div key={index} className="mb-4">
                  {/* Display the formatted response for this record */}
                  {formattedRecords[index] && (
                    <div className="mb-2 p-3 bg-blue-50 rounded-md">
                      <pre className="whitespace-pre-wrap text-sm text-blue-800">
                        {formattedRecords[index].trim()}
                      </pre>
                    </div>
                  )}
                  
                  {/* Apply button for this specific record */}
                  {formLinkingConfig?.enabled && formLinkingConfig.recordActions && !apiResponse.formattedResponse.includes('No data available') && (
                    <div className="flex flex-wrap gap-2 ml-3">
                      <RecordDisplayWithActions
                        data={[record]} // Pass only this specific record
                        formId={formId}
                        formLinkingConfig={formLinkingConfig}
                        onFormLinkTriggered={onFormLinkTriggered}
                        showOnlyButtons={true}
                        autoTriggerOptions={{
                          enabled: false, // Disable auto-trigger to prevent unwanted form opening
                          skipLeaveBalanceAutoTrigger: true // But still show Apply buttons
                        }}
                      />
                    </div>
                  )}
                </div>
              ))}
              
              {/* Fallback: if we can't split the formatted response properly */}
              {formattedRecords.length === 0 && apiResponse.formattedResponse && (
                <div>
                  <div className="mb-3 p-3 bg-blue-50 rounded-md">
                    <pre className="whitespace-pre-wrap text-sm text-blue-800">
                      {apiResponse.formattedResponse}
                    </pre>
                  </div>
                  {/* Only show Apply buttons if the response is not "No data available" */}
                  {!apiResponse.formattedResponse.includes('No data available') && (
                    <RecordDisplayWithActions
                      data={actualData}
                      formId={formId}
                      formLinkingConfig={formLinkingConfig}
                      onFormLinkTriggered={onFormLinkTriggered}
                      showOnlyButtons={true}
                      autoTriggerOptions={{
                        enabled: false, // Disable auto-trigger to prevent unwanted form opening
                        skipLeaveBalanceAutoTrigger: true // But still show Apply buttons
                      }}
                    />
                  )}
                </div>
              )}
            </div>
          );
        };

        return renderRecordsWithButtons();
      }
        
      // If we have a formatted response but no form linking, display just the formatted response
      if (apiResponse.formattedResponse && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {
        // Check if the underlying data is empty using helper function
        if (checkIfDataIsEmpty(apiResponse.data)) {
          return renderNoDataMessage();
        }
        
        return (
          <div className="mt-3 p-3 bg-blue-50 rounded-md">
            <pre className="whitespace-pre-wrap text-sm text-blue-800">
              {apiResponse.formattedResponse}
            </pre>
          </div>
        );
      }
    }
    
    // Check if data is empty (array with no items or empty object)
    if (apiResponse.success && apiResponse.data !== undefined && !(formLinkingConfig?.enabled && formLinkingConfig.recordActions?.length > 0)) {
      // Check if data is empty using helper function
      if (checkIfDataIsEmpty(apiResponse.data)) {
        return renderNoDataMessage();
      }
      
      // Display data if not empty
      return (
        <div className="mt-2 p-3 bg-gray-50 rounded-md overflow-auto max-h-60">
          <pre className="whitespace-pre-wrap text-sm text-gray-700">
            {JSON.stringify(apiResponse.data, null, 2)}
          </pre>
        </div>
      );
    }
    
    // Handle successful response but no data property at all
    if (apiResponse.success && !apiResponse.data && !apiResponse.formattedResponse && !apiResponse.error) {
      return renderNoDataMessage();
    }
    
    // Error display
    if (apiResponse.error) {
      return (
        <div className="mt-2 p-3 bg-red-50 rounded-md">
          <p className="text-red-600 text-sm">{apiResponse.error}</p>
        </div>
      );
    }
    
    return null;
  };

  return (
    <div
      className={`flex ${
        isUser ? 'justify-end' : 'justify-start'
      } mb-4`}
    >
      <div
        className={`max-w-[80%] p-3 rounded-lg ${
          isUser
            ? 'bg-blue-500 text-white rounded-br-none'
            : 'bg-gray-200 text-gray-800 rounded-bl-none'
        }`}
      >
        {/* Display message content only if we don't have a formatted API response */}
        {!(message.apiResponse && message.apiResponse.formattedResponse) && (
          <p className="whitespace-pre-wrap">{message.content}</p>
        )}
        
        {/* Display conversational form options as buttons */}
        {!isUser && message.options && Array.isArray(message.options) && message.options.length > 0 && !message.isHybridFlow && !message.isConversationalPhase && !(message.apiResponse && message.apiResponse.formattedResponse) && (
          <div className="mt-3">
            {message.fieldType === 'checkbox' ? (
              // Checkbox field - allow multiple selections
              <div>
                <div className="grid grid-cols-1 gap-2 max-w-xs mb-3">
                  {message.options.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => handleCheckboxToggle(option)}
                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${
                        selectedCheckboxes.includes(option)
                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${
                          selectedCheckboxes.includes(option)
                            ? 'bg-white border-white'
                            : 'bg-transparent border-gray-400'
                        }`}>
                          {selectedCheckboxes.includes(option) && (
                            <svg className="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </div>
                        {option}
                      </div>
                    </button>
                  ))}
                </div>
                {selectedCheckboxes.length > 0 && (
                  <div className="mb-2">
                    <button
                      onClick={handleCheckboxSubmit}
                      className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      Submit Selection ({selectedCheckboxes.length})
                    </button>
                  </div>
                )}
                <div className="text-xs text-gray-500">
                  Select multiple options and click Submit, or select one option and Submit
                </div>
              </div>
            ) : (
              // Radio/Select field - single selection
              <div className="grid grid-cols-1 gap-2 max-w-xs">
                {message.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleSingleOptionSelect(option)}
                    className="px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105"
                  >
                    {option}
                  </button>
                ))}
              </div>
            )}
            {message.currentStep && message.totalSteps && (
              <div className="mt-2 text-xs text-gray-500">
                Step {message.currentStep} of {message.totalSteps}
              </div>
            )}
          </div>
        )}

        {/* Display hybrid form options for conversational phase */}
        {!isUser && message.isHybridFlow && message.isConversationalPhase && message.options && Array.isArray(message.options) && message.options.length > 0 && !(message.apiResponse && message.apiResponse.formattedResponse) && (
          <div className="mt-3">
            {message.fieldType === 'checkbox' ? (
              // Checkbox field - allow multiple selections
              <div>
                <div className="grid grid-cols-1 gap-2 max-w-xs mb-3">
                  {message.options.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => handleCheckboxToggle(option)}
                      className={`px-4 py-3 rounded-lg text-left transition-all duration-200 shadow-sm hover:shadow-md transform hover:scale-105 ${
                        selectedCheckboxes.includes(option)
                          ? 'bg-green-500 hover:bg-green-600 text-white border border-green-600 hover:border-green-700'
                          : 'bg-gray-100 hover:bg-gray-200 text-gray-800 border border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      <div className="flex items-center">
                        <div className={`w-4 h-4 rounded border-2 mr-3 flex items-center justify-center ${
                          selectedCheckboxes.includes(option)
                            ? 'bg-white border-white'
                            : 'bg-transparent border-gray-400'
                        }`}>
                          {selectedCheckboxes.includes(option) && (
                            <svg className="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </div>
                        {option}
                      </div>
                    </button>
                  ))}
                </div>
                {selectedCheckboxes.length > 0 && (
                  <div className="mb-2">
                    <button
                      onClick={handleCheckboxSubmit}
                      className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-sm font-medium transition-colors duration-200"
                    >
                      Submit Selection ({selectedCheckboxes.length})
                    </button>
                  </div>
                )}
                <div className="text-xs text-gray-500">
                  Select multiple options and click Submit, or select one option and Submit
                </div>
              </div>
            ) : (
              // Radio/Select field - single selection
              <div className="grid grid-cols-1 gap-2 max-w-xs">
                {message.options.map((option, index) => (
                  <button
                    key={index}
                    onClick={() => handleSingleOptionSelect(option)}
                    className="px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg text-left transition-all duration-200 border border-blue-600 hover:border-blue-700 shadow-sm hover:shadow-md transform hover:scale-105"
                  >
                    {option}
                  </button>
                ))}
              </div>
            )}
            {message.currentStep && message.totalConversationalSteps && (
              <div className="mt-2 text-xs text-blue-600">
                Conversational Step {message.currentStep} of {message.totalConversationalSteps}
                {message.totalFormSteps > 0 && (
                  <span className="ml-2 text-gray-500">
                    ({message.totalFormSteps} form field{message.totalFormSteps !== 1 ? 's' : ''} remaining)
                  </span>
                )}
              </div>
            )}
          </div>
        )}


              
        {/* Display form completion indicator - only show success if form was actually submitted successfully */}
        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && message.apiResponse.success && (
          <div className="mt-2 p-2 bg-green-100 rounded text-xs text-green-700">
            <div className="flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Form Submitted Successfully
            </div>
          </div>
        )}

        {/* Display leave form completion with updated balance */}
        {!isUser && message.queryIntent === 'leave_form_submitted' && message.apiResponse && message.apiResponse.success && (
          <div className="mt-2">
            <div className="p-2 bg-green-100 rounded text-xs text-green-700 mb-2">
              <div className="flex items-center">
                <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Leave Application Submitted Successfully
              </div>
            </div>
            {message.updatedLeaveBalance && (
              <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-center mb-2">
                  <svg className="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  <h4 className="text-sm font-medium text-blue-800">Updated Leave Balance</h4>
                </div>
                <div className="text-sm text-blue-700 whitespace-pre-wrap">
                  {message.updatedLeaveBalance.replace('Here\'s your leave balance information:\n\n', '')}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Display form submission failure indicator */}
        {!isUser && message.queryIntent === 'form_completed' && message.apiResponse && !message.apiResponse.success && (
          <div className="mt-2 p-2 bg-red-100 rounded text-xs text-red-700">
            <div className="flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Form Submission Failed
            </div>
          </div>
        )}
        
        {/* Display form cancellation indicator */}
        {!isUser && message.queryIntent === 'form_cancelled' && (
          <div className="mt-2 p-2 bg-red-100 rounded text-xs text-red-700">
            <div className="flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Form Cancelled
            </div>
          </div>
        )}

        {/* Display hybrid form completion indicator */}
        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && message.apiResponse.success && (
          <div className="mt-2 p-2 bg-green-100 rounded text-xs text-green-700">
            <div className="flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              🔄 Form Submitted Successfully
            </div>
          </div>
        )}

        {/* Display hybrid form submission failure indicator */}
        {!isUser && message.queryIntent === 'hybrid_form_completed' && message.apiResponse && !message.apiResponse.success && (
          <div className="mt-2 p-2 bg-red-100 rounded text-xs text-red-700">
            <div className="flex items-center">
              <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              🔄 Hybrid Form Submission Failed
            </div>
          </div>
        )}


        
        {/* Leave balance is now included in the message content, no separate display needed */}

        {/* Display API response if available (but not for leave balance) */}
        {message.apiResponse && !message.apiResponse.leaveBalance && formatApiResponse(message.apiResponse)}
        
        {/* Display form data if available (for debugging) */}
        {/* {message.formData && process.env.NODE_ENV === 'development' && (
          <div className="mt-2 p-2 bg-gray-100 rounded text-xs text-gray-500">
            <p>Form data submitted</p>
          </div>
        )} */}
        
        {/* Display query intent if available (for debugging) */}
        {/* {message.queryIntent && process.env.NODE_ENV === 'development' && (
          <div className={`mt-2 p-2 rounded text-xs ${
            message.queryIntent === 'form' 
              ? 'bg-blue-100 text-blue-700' 
              : 'bg-green-100 text-green-700'
          }`}>
            <p>Query intent: <strong>{message.queryIntent}</strong></p>
            {message.formData && message.queryIntent === 'form' && (
              <p>Form detected: {message.formData.name}</p>
            )}
          </div>
        )} */}
      </div>
    </div>
  );
};

export default ChatMessage;