import React, { useState, useRef, useEffect } from 'react';
import ChatPage from '../pages/ChatPage';

const ChatbotWidget = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([
    {
      type: 'bot',
      text: '👋 Hi! I provide comprehensive details about:\n• Employee Directory & Information\n• Attendance Records & Analysis\n• Leave Management & Balances\n• Project Overview & Statistics\n\nAsk me anything for full detailed responses!'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <>
      {/* Chat <PERSON> */}
      <div
        onClick={() => setIsOpen(!isOpen)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          backgroundColor: '#007bff',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer',
          boxShadow: '0 4px 12px rgba(0,123,255,0.3)',
          zIndex: 1000,
          transition: 'all 0.3s ease'
        }}
      >
        <span style={{ color: 'white', fontSize: '24px' }}>
          {isOpen ? '✕' : '💬'}
        </span>
      </div>
      {isOpen && (
        <ChatPage />
      )}

      
    </>
  );
};

export default ChatbotWidget;