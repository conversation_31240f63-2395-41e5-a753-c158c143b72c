import React, { useState, useEffect } from 'react';

const DynamicForm = ({ form, onSubmit, onCancel }) => {
  const [formValues, setFormValues] = useState({});
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form values
  useEffect(() => {
    if (form && form.fields) {
      // Filter to show only required fields
      const requiredFields = form.fields.filter(field => field.required);
      
      console.log('🔍 Form initialization debug:', {
        formName: form.name,
        totalFields: form.fields.length,
        requiredFields: requiredFields.length,
        fields: requiredFields.map(f => ({
          name: f.name,
          defaultValue: f.defaultValue,
          type: f.type,
          required: f.required
        })),
        prefillData: form.prefillData
      });
      
      const initialValues = {};
      requiredFields.forEach((field) => {
        // Check if there's prefilled data for this field
        const prefillValue = form.prefillData && form.prefillData[field.name];
        
        if (prefillValue !== undefined && prefillValue !== null) {
          initialValues[field.name] = prefillValue;
          console.log(`✅ Using prefill value for ${field.name}:`, prefillValue);
        } else if (field.defaultValue !== undefined && field.defaultValue !== null && field.defaultValue !== '') {
          // Use the field's default value if no prefill data
          initialValues[field.name] = field.defaultValue;
          console.log(`✅ Using default value for ${field.name}:`, field.defaultValue);
        } else {
          initialValues[field.name] = field.type === 'checkbox' ? false : '';
          console.log(`⚠️ Using empty value for ${field.name}`);
        }
      });
      
      console.log('🎯 Final initial values:', initialValues);
      setFormValues(initialValues);
    }
  }, [form]);

  const validateField = (field, value) => {
    if (field.required && (!value || value === '')) {
      return `${field.label} is required`;
    }

    if (field.validation) {
      // Email validation
      if (field.type === 'email' && value) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return 'Please enter a valid email address';
        }
      }

      // Number validation
      if (field.type === 'number' && value) {
        const { min, max } = field.validation;
        const numValue = Number(value);
        
        if (min !== undefined && numValue < min) {
          return `Value must be at least ${min}`;
        }
        
        if (max !== undefined && numValue > max) {
          return `Value must be at most ${max}`;
        }
      }

      // Pattern validation
      if (field.validation.pattern && value) {
        const regex = new RegExp(field.validation.pattern);
        if (!regex.test(value)) {
          return field.validation.message || 'Invalid format';
        }
      }
    }

    return null;
  };

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    const fieldValue = type === 'checkbox' ? checked : value;
    
    setFormValues({
      ...formValues,
      [name]: fieldValue,
    });

    // Find the field definition among required fields
    const field = form.fields.find((f) => f.name === name && f.required);
    if (field) {
      const error = validateField(field, fieldValue);
      setErrors({
        ...errors,
        [name]: error,
      });
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    // Validate only required fields
    const newErrors = {};
    let hasErrors = false;
    
    form.fields.filter(field => field.required).forEach((field) => {
      const error = validateField(field, formValues[field.name]);
      if (error) {
        newErrors[field.name] = error;
        hasErrors = true;
      }
    });
    
    setErrors(newErrors);
    
    if (!hasErrors) {
      setIsSubmitting(true);
      
      // Only submit values for required fields
      const requiredFieldsValues = {};
      form.fields.filter(field => field.required).forEach(field => {
        requiredFieldsValues[field.name] = formValues[field.name];
      });
      
      onSubmit(form._id, requiredFieldsValues);
    }
  };

  if (!form) return null;

  // Check if there are any required fields
  const requiredFields = form.fields.filter(field => field.required);
  
  if (requiredFields.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6 mb-4">
        <h2 className="text-xl font-bold mb-4">{form.name}</h2>
        {form.description && <p className="text-gray-600 mb-4">{form.description}</p>}
        
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-4">
          <p className="text-yellow-800">
            <span className="font-medium">No Required Fields:</span> This form has no required fields to display.
          </p>
        </div>
        
        <div className="flex justify-end space-x-2">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6 mb-4">
      <h2 className="text-xl font-bold mb-4">{form.name}</h2>
      {form.description && <p className="text-gray-600 mb-4">{form.description}</p>}
      
      {/* Show required fields info */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-4">
        <p className="text-sm text-blue-800">
          <span className="font-medium">Required Fields Only:</span> Showing {form.fields.filter(field => field.required).length} required fields out of {form.fields.length} total fields
        </p>
      </div>
      
      <form onSubmit={handleSubmit}>
        {form.fields.filter(field => field.required).map((field) => (
          <div key={field.name} className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            
            {field.type === 'text' || field.type === 'email' || field.type === 'password' || field.type === 'number' || field.type === 'date' ? (
              <input
                type={field.type}
                name={field.name}
                value={formValues[field.name] || ''}
                onChange={handleChange}
                placeholder={field.placeholder || ''}
                className={`w-full p-2 border rounded-md ${
                  errors[field.name] ? 'border-red-500' : 'border-gray-300'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            ) : field.type === 'textarea' ? (
              <textarea
                name={field.name}
                value={formValues[field.name] || ''}
                onChange={handleChange}
                placeholder={field.placeholder || ''}
                rows="4"
                className={`w-full p-2 border rounded-md ${
                  errors[field.name] ? 'border-red-500' : 'border-gray-300'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
            ) : field.type === 'select' ? (
              <select
                name={field.name}
                value={formValues[field.name] || ''}
                onChange={handleChange}
                className={`w-full p-2 border rounded-md ${
                  errors[field.name] ? 'border-red-500' : 'border-gray-300'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="">Select an option</option>
                {field.options.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>
            ) : field.type === 'checkbox' ? (
              field.options && field.options.length > 0 ? (
                // Multiple checkbox options
                <div className="space-y-2">
                  {field.options.map((option) => (
                    <div key={option} className="flex items-center">
                      <input
                        type="checkbox"
                        name={field.name}
                        value={option}
                        checked={Array.isArray(formValues[field.name]) 
                          ? formValues[field.name].includes(option)
                          : false}
                        onChange={(e) => {
                          const currentValues = Array.isArray(formValues[field.name]) 
                            ? formValues[field.name] 
                            : [];
                          const newValues = e.target.checked
                            ? [...currentValues, option]
                            : currentValues.filter(val => val !== option);
                          handleChange({
                            target: {
                              name: field.name,
                              value: newValues
                            }
                          });
                        }}
                        className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-gray-700">{option}</span>
                    </div>
                  ))}
                </div>
              ) : (
                // Single checkbox
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    name={field.name}
                    checked={formValues[field.name] || false}
                    onChange={handleChange}
                    className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-gray-700">{field.placeholder || ''}</span>
                </div>
              )
            ) : field.type === 'radio' ? (
              <div className="space-y-2">
                {field.options.map((option) => (
                  <div key={option} className="flex items-center">
                    <input
                      type="radio"
                      name={field.name}
                      value={option}
                      checked={formValues[field.name] === option}
                      onChange={handleChange}
                      className="h-4 w-4 text-blue-500 focus:ring-blue-500 border-gray-300"
                    />
                    <span className="ml-2 text-gray-700">{option}</span>
                  </div>
                ))}
              </div>
            ) : null}
            
            {errors[field.name] && (
              <p className="text-red-500 text-sm mt-1">{errors[field.name]}</p>
            )}
          </div>
        ))}
        
        <div className="flex justify-end space-x-2 mt-6">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className={`px-4 py-2 bg-blue-500 text-white rounded-md ${
              isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-600'
            } focus:outline-none focus:ring-2 focus:ring-blue-500`}
          >
            {isSubmitting ? 'Submitting...' : 'Submit'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default DynamicForm;