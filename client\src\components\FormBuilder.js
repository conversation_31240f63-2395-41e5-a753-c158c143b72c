import React, { useState, useEffect } from 'react';
import { useForm } from '../context/FormContext';

const FormBuilder = ({ initialForm = null, onSave, onCancel }) => {
  const { createForm, updateForm, error, clearError } = useForm();
  
  // Debug logging
  console.log('🔧 FormBuilder rendered with:', {
    hasInitialForm: !!initialForm,
    formName: initialForm?.name
  });
  
  const [form, setForm] = useState({
    name: '',
    description: '',
    prompt: '',
    keywords: [],
    fields: [],
    conversationalFlow: {
      enabled: false,
      completionMessage: 'Thank you! Your form has been submitted successfully.'
    },
    apiConfig: {
      method: 'GET',
      endpoint: '',
      headers: {},
      authType: 'none',
      authDetails: {},
    },
  });
  
  const [currentField, setCurrentField] = useState({
    name: '',
    label: '',
    type: 'text',
    placeholder: '',
    required: false,
    readonly: false,
    options: [],
    validation: {},
    conversationalPrompt: '',
    skipInConversation: false,
  });
  
  const [editingFieldIndex, setEditingFieldIndex] = useState(-1);
  const [showFieldForm, setShowFieldForm] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Initialize form if editing an existing one
  useEffect(() => {
    if (initialForm) {
      // Get user data from localStorage for auto-population
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      
      // Ensure the form has all required default properties
      const authDetails = initialForm.apiConfig?.authDetails || {};
      
      // Auto-populate Bearer token if authType is bearer and no token is set
      if (initialForm.apiConfig?.authType === 'bearer' && !authDetails.token && userData.token) {
        authDetails.token = userData.token;
      }
      
      // Ensure all fields have readonly property with default false
      const fieldsWithDefaults = (initialForm.fields || []).map(field => ({
        ...field,
        readonly: field.hasOwnProperty('readonly') ? field.readonly : false
      }));

      const formWithDefaults = {
        name: initialForm.name || '',
        description: initialForm.description || '',
        prompt: initialForm.prompt || '',
        keywords: initialForm.keywords || [],
        fields: fieldsWithDefaults,
        conversationalFlow: {
          enabled: initialForm.conversationalFlow?.enabled || false,
          completionMessage: initialForm.conversationalFlow?.completionMessage || 'Thank you! Your form has been submitted successfully.'
        },
        apiConfig: {
          method: initialForm.apiConfig?.method || 'GET',
          endpoint: initialForm.apiConfig?.endpoint || '',
          headers: initialForm.apiConfig?.headers || {},
          authType: initialForm.apiConfig?.authType || 'none',
          authDetails: authDetails,
        },
      };
      setForm(formWithDefaults);
    }
  }, [initialForm]);
  
  const validateForm = () => {
    const errors = {};
    
    if (!form.name.trim()) {
      errors.name = 'Form name is required';
    }
    
    if (!form.apiConfig.endpoint.trim()) {
      errors.endpoint = 'API endpoint is required';
    }
    
    if (form.fields.length === 0) {
      errors.fields = 'At least one field is required';
    }
    
    // Validate headers - no empty header names
    const headerKeys = Object.keys(form.apiConfig.headers);
    if (headerKeys.some(key => key === '')) {
      errors.headers = 'All headers must have a name';
    }
    
    return errors;
  };
  
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setForm({
      ...form,
      [name]: value,
    });
  };
  
  const handleApiConfigChange = (e) => {
    const { name, value } = e.target;
    setForm({
      ...form,
      apiConfig: {
        ...form.apiConfig,
        [name]: value,
      },
    });
  };
  
  const handleAuthTypeChange = (e) => {
    const { value } = e.target;
    
    // Prepare authDetails based on the selected auth type
    let authDetails = {};
    
    // If switching to bearer auth, auto-populate token from localStorage
    if (value === 'bearer') {
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      if (userData.token) {
        authDetails.token = userData.token;
      }
    }
    
    setForm({
      ...form,
      apiConfig: {
        ...form.apiConfig,
        authType: value,
        authDetails: authDetails,
      },
    });
  };
  
  const handleAuthDetailsChange = (e) => {
    const { name, value } = e.target;
    setForm({
      ...form,
      apiConfig: {
        ...form.apiConfig,
        authDetails: {
          ...form.apiConfig.authDetails,
          [name]: value,
        },
      },
    });
  };
  
  const handleHeaderChange = (index, key, value) => {
    const headers = { ...form.apiConfig.headers };
    const headerKeys = Object.keys(headers);
    const oldKey = headerKeys[index];
    
    // If both key and value are empty, remove the header
    if (key === '' && value === '') {
      delete headers[oldKey];
    } else {
      // Update existing header
      if (oldKey !== undefined) {
        // If key changed, remove old key and add new one
        if (oldKey !== key && key !== '') {
          delete headers[oldKey];
          headers[key] = value;
        } else {
          // Just update the value for the existing key
          headers[oldKey] = value;
        }
      }
      // We don't add new headers here - that's only done via addHeader
    }
    
    setForm({
      ...form,
      apiConfig: {
        ...form.apiConfig,
        headers,
      },
    });
  };
  
  const addHeader = () => {
    // Only add a new header if there isn't already an empty one
    const headerKeys = Object.keys(form.apiConfig.headers);
    const hasEmptyHeader = headerKeys.some(key => key === '');
    
    if (!hasEmptyHeader) {
      setForm({
        ...form,
        apiConfig: {
          ...form.apiConfig,
          headers: {
            ...form.apiConfig.headers,
            '': '',
          },
        },
      });
    }
  };
  
  const removeHeader = (index) => {
    const headers = { ...form.apiConfig.headers };
    const headerKeys = Object.keys(headers);
    
    if (index >= 0 && index < headerKeys.length) {
      delete headers[headerKeys[index]];
      
      setForm({
        ...form,
        apiConfig: {
          ...form.apiConfig,
          headers,
        },
      });
    }
  };
  
  const handleFieldChange = (e) => {
    const { name, value, type, checked } = e.target;
    setCurrentField({
      ...currentField,
      [name]: type === 'checkbox' ? checked : value,
    });
  };
  
  const handleOptionChange = (index, value) => {
    const newOptions = [...currentField.options];
    newOptions[index] = value;
    setCurrentField({
      ...currentField,
      options: newOptions,
    });
  };
  
  const addOption = () => {
    setCurrentField({
      ...currentField,
      options: [...currentField.options, ''],
    });
  };
  
  const removeOption = (index) => {
    const newOptions = [...currentField.options];
    newOptions.splice(index, 1);
    setCurrentField({
      ...currentField,
      options: newOptions,
    });
  };
  
  const addField = () => {
    // Generate a unique field name if not provided
    let fieldName = currentField.name.trim();
    if (!fieldName) {
      fieldName = `field_${Date.now()}`;
    }
    
    const newField = {
      ...currentField,
      name: fieldName,
    };
    
    if (editingFieldIndex >= 0) {
      // Update existing field
      const newFields = [...form.fields];
      newFields[editingFieldIndex] = newField;
      setForm({
        ...form,
        fields: newFields,
      });
    } else {
      // Add new field
      setForm({
        ...form,
        fields: [...form.fields, newField],
      });
    }
    
    // Reset current field
    setCurrentField({
      name: '',
      label: '',
      type: 'text',
      placeholder: '',
      required: false,
      readonly: false,
      options: [],
      validation: {},
      conversationalPrompt: '',
      skipInConversation: false,
    });
    
    setEditingFieldIndex(-1);
    setShowFieldForm(false);
  };
  
  const editField = (index) => {
    setCurrentField(form.fields[index]);
    setEditingFieldIndex(index);
    setShowFieldForm(true);
  };
  
  const removeField = (index) => {
    const newFields = [...form.fields];
    newFields.splice(index, 1);
    setForm({
      ...form,
      fields: newFields,
    });
  };
  
  // Clean up empty headers
  const cleanupHeaders = (formData) => {
    const cleanForm = { ...formData };
    const headers = { ...cleanForm.apiConfig.headers };
    
    // Remove any headers with empty keys
    Object.keys(headers).forEach(key => {
      if (key === '') {
        delete headers[key];
      }
    });
    
    cleanForm.apiConfig.headers = headers;
    return cleanForm;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    // Validate form
    const errors = validateForm();
    setFormErrors(errors);
    
    if (Object.keys(errors).length === 0) {
      setIsSubmitting(true);
      
      try {
        // Clean up form data before submitting
        const cleanedForm = cleanupHeaders(form);
        
        let result;
        
        if (initialForm && initialForm._id) {
          // Update existing form
          result = await updateForm(initialForm._id, cleanedForm);
        } else {
          // Create new form
          result = await createForm(cleanedForm);
        }
        
        if (result) {
          onSave(result);
        }
      } catch (err) {
        console.error('Error saving form:', err);
      } finally {
        setIsSubmitting(false);
      }
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
      <div className="bg-gradient-to-r from-blue-600 to-blue-800 px-6 py-4 text-white">
        <h2 className="text-xl font-bold">
          {initialForm ? 'Edit Form' : 'Create New Form'}
        </h2>
        <p className="text-blue-100 mt-1">
          {initialForm 
            ? 'Update your form configuration and fields' 
            : 'Configure your form with fields and API settings'}
        </p>
      </div>
      
      <div className="p-6">
        {error && (
          <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded-r">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-red-700 text-sm font-medium">{error}</p>
              </div>
              <div className="ml-auto pl-3">
                <div className="-mx-1.5 -my-1.5">
                  <button
                    onClick={clearError}
                    className="inline-flex rounded-md p-1.5 text-red-500 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                  >
                    <span className="sr-only">Dismiss</span>
                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      
      <form onSubmit={handleSubmit}>
        {/* Form Basic Info */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">Basic Information</h3>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              Form Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="name"
              value={form.name}
              onChange={handleFormChange}
              className={`w-full p-2 border rounded-md ${
                formErrors.name ? 'border-red-500' : 'border-gray-300'
              } focus:outline-none focus:ring-2 focus:ring-blue-500`}
            />
            {formErrors.name && (
              <p className="text-red-500 text-sm mt-1">{formErrors.name}</p>
            )}
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              Description
            </label>
            <textarea
              name="description"
              value={form.description}
              onChange={handleFormChange}
              rows="3"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              AI Prompt
              <span className="text-gray-500 text-sm font-normal ml-2">(For dynamic matching)</span>
            </label>
            <textarea
              name="prompt"
              value={form.prompt}
              onChange={handleFormChange}
              rows="4"
              placeholder="Enter the AI prompt that will help match user queries to this form..."
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-gray-500 text-sm mt-1">
              🤖 This prompt helps the system understand when to show this form. Include keywords and phrases users might use.
            </p>
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              Keywords
              <span className="text-gray-500 text-sm font-normal ml-2">(Comma-separated)</span>
            </label>
            <input
              type="text"
              name="keywords"
              value={Array.isArray(form.keywords) ? form.keywords.join(', ') : form.keywords}
              onChange={(e) => {
                const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                setForm({ ...form, keywords });
              }}
              placeholder="leave, vacation, time off, absence, pto"
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            <p className="text-gray-500 text-sm mt-1">
              🏷️ Keywords help match user queries to this form. Separate multiple keywords with commas.
            </p>
          </div>
        </div>
        
        {/* Conversational Flow Configuration */}
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="text-lg font-semibold mb-3 text-yellow-800">🗣️ Conversational Flow</h3>
          
          <div className="mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={form.conversationalFlow?.enabled || false}
                onChange={(e) => {
                  console.log('🗣️ Conversational flow toggled:', e.target.checked);
                  setForm({
                    ...form,
                    conversationalFlow: {
                      ...form.conversationalFlow,
                      enabled: e.target.checked
                    }
                  });
                }}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="ml-2 text-gray-700 font-medium">Enable Conversational Flow</span>
            </label>
            <p className="text-gray-500 text-sm mt-1">
              🗣️ When enabled, the AI will ask users questions one by one instead of showing the full form
            </p>
          </div>
          
          {form.conversationalFlow?.enabled && (
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">
                Completion Message
              </label>
              <textarea
                value={form.conversationalFlow?.completionMessage || ''}
                onChange={(e) => setForm({
                  ...form,
                  conversationalFlow: {
                    ...form.conversationalFlow,
                    completionMessage: e.target.value
                  }
                })}
                rows="2"
                placeholder="Thank you! Your form has been submitted successfully."
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                Message shown when the conversational form is completed
              </p>
            </div>
          )}
        </div>
        
        {/* API Configuration */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">API Configuration</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                HTTP Method <span className="text-red-500">*</span>
              </label>
              <select
                name="method"
                value={form.apiConfig.method}
                onChange={handleApiConfigChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="GET">GET</option>
                <option value="POST">POST</option>
                <option value="PUT">PUT</option>
                <option value="DELETE">DELETE</option>
                <option value="PATCH">PATCH</option>
              </select>
            </div>
            
            <div>
              <label className="block text-gray-700 font-medium mb-2">
                API Endpoint <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                name="endpoint"
                value={form.apiConfig.endpoint}
                onChange={handleApiConfigChange}
                placeholder="https://api.example.com/data"
                className={`w-full p-2 border rounded-md ${
                  formErrors.endpoint ? 'border-red-500' : 'border-gray-300'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              />
              {formErrors.endpoint && (
                <p className="text-red-500 text-sm mt-1">{formErrors.endpoint}</p>
              )}
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-gray-700 font-medium mb-2">
              Authentication Type
            </label>
            <select
              name="authType"
              value={form.apiConfig.authType}
              onChange={handleAuthTypeChange}
              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="none">None</option>
              <option value="basic">Basic Auth</option>
              <option value="bearer">Bearer Token</option>
              <option value="apiKey">API Key</option>
            </select>
          </div>
          
          {/* Auth Details based on selected auth type */}
          {form.apiConfig.authType === 'basic' && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Employee ID
                </label>
                <input
                  type="text"
                  name="empId"
                  value={form.apiConfig.authDetails?.empId || ''}
                  onChange={handleAuthDetailsChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Password
                </label>
                <input
                  type="password"
                  name="password"
                  value={form.apiConfig.authDetails?.password || ''}
                  onChange={handleAuthDetailsChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          )}
          
          {form.apiConfig.authType === 'bearer' && (
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">
                Bearer Token
              </label>
              <input
                type="text"
                name="token"
                value={form.apiConfig.authDetails?.token || ''}
                onChange={handleAuthDetailsChange}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}
          
          {form.apiConfig.authType === 'apiKey' && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Key Name
                </label>
                <input
                  type="text"
                  name="key"
                  value={form.apiConfig.authDetails?.key || ''}
                  onChange={handleAuthDetailsChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Value
                </label>
                <input
                  type="text"
                  name="value"
                  value={form.apiConfig.authDetails?.value || ''}
                  onChange={handleAuthDetailsChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Add In
                </label>
                <select
                  name="in"
                  value={form.apiConfig.authDetails?.in || 'header'}
                  onChange={handleAuthDetailsChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="header">Header</option>
                  <option value="query">Query Parameter</option>
                </select>
              </div>
            </div>
          )}
          
          {/* Headers */}
          <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex justify-between items-center mb-3">
              <div>
                <label className="block text-gray-700 font-medium text-lg">
                  Request Headers
                </label>
                {formErrors.headers && (
                  <p className="text-red-500 text-sm mt-1">{formErrors.headers}</p>
                )}
              </div>
              <button
                type="button"
                onClick={addHeader}
                className="flex items-center bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
                Add Header
              </button>
            </div>
            
            <div className="bg-white rounded-md shadow-sm">
              {Object.keys(form.apiConfig.headers).length > 0 ? (
                <div className="divide-y divide-gray-200">
                  {Object.entries(form.apiConfig.headers).map(([key, value], index) => (
                    <div key={index} className="p-3">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        <div>
                          <label className="block text-xs text-gray-500 mb-1">Header Name</label>
                          <input
                            type="text"
                            value={key}
                            onChange={(e) => handleHeaderChange(index, e.target.value, value)}
                            placeholder="Content-Type"
                            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          {key === '' && (
                            <p className="mt-1 text-xs text-amber-600">Enter a header name</p>
                          )}
                        </div>
                        <div className="flex flex-col">
                          <label className="block text-xs text-gray-500 mb-1">Value</label>
                          <div className="flex">
                            <input
                              type="text"
                              value={value}
                              onChange={(e) => handleHeaderChange(index, key, e.target.value)}
                              placeholder="application/json"
                              className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <button
                              type="button"
                              onClick={() => removeHeader(index)}
                              className="bg-red-500 text-white px-3 rounded-r-md hover:bg-red-600 transition-colors"
                              title="Remove header"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="p-6 text-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <p className="mt-2 text-gray-500">No headers added yet</p>
                  {/* <p className="text-sm text-gray-400 mt-1">Headers like Content-Type, Authorization, etc. will be sent with your request</p> */}
                  <button
                    type="button"
                    onClick={addHeader}
                    className="mt-4 inline-flex items-center px-3 py-2 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Add First Header
                  </button>
                </div>
              )}
            </div>
            
            {Object.keys(form.apiConfig.headers).length > 0 && (
              <div className="mt-3 text-right">
                <button
                  type="button"
                  onClick={addHeader}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-300 shadow-sm text-sm leading-4 font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                  Add Another Header
                </button>
              </div>
            )}
          </div>
        </div>
        
        {/* Form Fields */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex justify-between items-center mb-3">
            <h3 className="text-lg font-medium text-gray-700">Form Fields</h3>
            <button
              type="button"
              onClick={() => setShowFieldForm(true)}
              className="flex items-center bg-blue-500 text-white px-3 py-1 rounded-md hover:bg-blue-600 transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
              </svg>
              Add Field
            </button>
          </div>
          
          {formErrors.fields && (
            <div className="bg-red-50 border-l-4 border-red-500 p-3 mb-4">
              <p className="text-red-700 text-sm">{formErrors.fields}</p>
            </div>
          )}
          
          {form.fields.length > 0 ? (
            <div className="bg-white rounded-md shadow-sm">
              <div className="divide-y divide-gray-200">
                {form.fields.map((field, index) => (
                  <div
                    key={index}
                    className="p-4 hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center">
                          <p className="font-medium text-gray-800">{field.label}</p>
                          {field.required && (
                            <span className="ml-2 px-2 py-0.5 bg-red-100 text-red-800 text-xs rounded-full">
                              Required
                            </span>
                          )}
                        </div>
                        <div className="mt-1 flex items-center text-sm text-gray-500">
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800 mr-2">
                            {field.type}
                          </span>
                          <span>Name: {field.name}</span>
                          {field.placeholder && (
                            <span className="ml-2">Placeholder: {field.placeholder}</span>
                          )}
                        </div>
                        {field.options && field.options.length > 0 && (
                          <div className="mt-1 text-sm text-gray-500">
                            <span>Options: {field.options.join(', ')}</span>
                          </div>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <button
                          type="button"
                          onClick={() => editField(index)}
                          className="inline-flex items-center px-2 py-1 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                          Edit
                        </button>
                        <button
                          type="button"
                          onClick={() => removeField(index)}
                          className="inline-flex items-center px-2 py-1 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clipRule="evenodd" />
                          </svg>
                          Remove
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-md shadow-sm p-8 text-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="mt-2 text-gray-500">No form fields added yet</p>
              {/* <p className="mt-1 text-sm text-gray-400">Click the "Add Field" button to create your first form field</p> */}
            </div>
          )}
          
          {/* Field Form */}
          {showFieldForm && (
            <div className="mt-6 bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-4 py-3 text-white">
                <h4 className="text-lg font-medium">
                  {editingFieldIndex >= 0 ? 'Edit Field' : 'Add New Field'}
                </h4>
              </div>
              
              <div className="p-5">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      Field Name <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={currentField.name}
                      onChange={handleFieldChange}
                      placeholder="e.g. email, firstName"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      This will be used as the field identifier in the form data
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      Label <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      name="label"
                      value={currentField.label}
                      onChange={handleFieldChange}
                      placeholder="e.g. Email Address, First Name"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      This will be displayed to the user as the field label
                    </p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      Field Type <span className="text-red-500">*</span>
                    </label>
                    <select
                      name="type"
                      value={currentField.type}
                      onChange={handleFieldChange}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="text">Text</option>
                      <option value="number">Number</option>
                      <option value="email">Email</option>
                      <option value="password">Password</option>
                      <option value="select">Select (Dropdown)</option>
                      <option value="checkbox">Checkbox</option>
                      <option value="radio">Radio Buttons</option>
                      <option value="textarea">Text Area</option>
                      <option value="date">Date</option>
                      <option value="file">File Upload</option>
                    </select>
                    <p className="mt-1 text-xs text-gray-500">
                      Select the type of input field
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">
                      Placeholder
                    </label>
                    <input
                      type="text"
                      name="placeholder"
                      value={currentField.placeholder}
                      onChange={handleFieldChange}
                      placeholder="e.g. Enter your email"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <p className="mt-1 text-xs text-gray-500">
                      Hint text displayed inside the input field
                    </p>
                  </div>
                </div>
                
                <div className="mb-6">
                  <label className="flex items-center p-3 bg-gray-50 rounded-md border border-gray-200">
                    <input
                      type="checkbox"
                      name="required"
                      checked={currentField.required}
                      onChange={handleFieldChange}
                      className="h-5 w-5 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div className="ml-3">
                      <span className="text-gray-700 font-medium">Required field</span>
                      <p className="text-xs text-gray-500 mt-1">
                        Users must fill out this field to submit the form
                      </p>
                    </div>
                  </label>
                </div>
                
                <div className="mb-6">
                  <label className="flex items-center p-3 bg-gray-50 rounded-md border border-gray-200">
                    <input
                      type="checkbox"
                      name="readonly"
                      checked={currentField.readonly}
                      onChange={handleFieldChange}
                      className="h-5 w-5 text-blue-500 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <div className="ml-3">
                      <span className="text-gray-700 font-medium">Read-only field</span>
                      <p className="text-xs text-gray-500 mt-1">
                        Field is displayed but cannot be edited by users
                      </p>
                    </div>
                  </label>
                </div>
                
                {/* Conversational Flow Settings */}
                {form.conversationalFlow?.enabled && (
                  <div className="mb-6 p-4 bg-blue-50 rounded-md border border-blue-200">
                    <h5 className="text-sm font-medium text-blue-800 mb-3">🗣️ Conversational Flow Settings</h5>
                    
                    <div className="mb-4">
                      <label className="block text-gray-700 font-medium mb-2">
                        Custom AI Question
                      </label>
                      <textarea
                        name="conversationalPrompt"
                        value={currentField.conversationalPrompt}
                        onChange={handleFieldChange}
                        rows="2"
                        placeholder={`Leave empty to use default question: "Please provide ${currentField.label?.toLowerCase() || 'this field'}"`}
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <p className="text-gray-500 text-xs mt-1">
                        Custom question the AI will ask when collecting this field in conversational mode
                      </p>
                    </div>
                    
                    <div className="mb-4">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          name="skipInConversation"
                          checked={currentField.skipInConversation}
                          onChange={handleFieldChange}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <span className="ml-2 text-gray-700 font-medium">Skip in conversational flow</span>
                      </label>
                      <p className="text-gray-500 text-xs mt-1 ml-6">
                        If checked, this field will be auto-filled or skipped during conversational mode
                      </p>
                    </div>
                  </div>
                )}
                
                {/* Options for select, checkbox, or radio */}
                {(currentField.type === 'select' || currentField.type === 'radio' || currentField.type === 'checkbox') && (
                  <div className="mb-6 p-4 bg-gray-50 rounded-md border border-gray-200">
                    <div className="flex justify-between items-center mb-3">
                      <label className="block text-gray-700 font-medium">
                        Options <span className="text-red-500">*</span>
                      </label>
                      <button
                        type="button"
                        onClick={addOption}
                        className="flex items-center text-blue-600 hover:text-blue-800"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        Add Option
                      </button>
                    </div>
                    
                    {currentField.options.length > 0 ? (
                      <div className="space-y-2">
                        {currentField.options.map((option, index) => (
                          <div key={index} className="flex items-center">
                            <div className="mr-2 text-gray-500 text-sm">
                              {index + 1}.
                            </div>
                            <input
                              type="text"
                              value={option}
                              onChange={(e) => handleOptionChange(index, e.target.value)}
                              placeholder={`Option ${index + 1}`}
                              className="flex-1 p-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <button
                              type="button"
                              onClick={() => removeOption(index)}
                              className="bg-red-500 text-white p-2 rounded-r-md hover:bg-red-600 transition-colors"
                              title="Remove option"
                            >
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-4 bg-white rounded border border-dashed border-gray-300">
                        <p className="text-gray-500 text-sm">No options added yet</p>
                        <p className="text-xs text-gray-400 mt-1">Add at least one option for this field type</p>
                      </div>
                    )}
                  </div>
                )}
                
                <div className="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={() => {
                      setShowFieldForm(false);
                      setEditingFieldIndex(-1);
                      setCurrentField({
                        name: '',
                        label: '',
                        type: 'text',
                        placeholder: '',
                        required: false,
                        readonly: false,
                        options: [],
                        validation: {},
                        conversationalPrompt: '',
                        skipInConversation: false,
                      });
                    }}
                    className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    onClick={addField}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
                  >
                    {editingFieldIndex >= 0 ? (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                        </svg>
                        Update Field
                      </>
                    ) : (
                      <>
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                        Save Field
                      </>
                    )}
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Form Actions */}
        <div className="mt-8 pt-6 border-t border-gray-200">
          <div className="flex flex-col-reverse sm:flex-row sm:justify-between sm:items-center">
            <button
              type="button"
              onClick={onCancel}
              className="mt-3 sm:mt-0 w-full sm:w-auto px-5 py-2.5 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors shadow-sm"
            >
              Cancel
            </button>
            
            <div className="flex flex-col sm:flex-row sm:items-center">
              <p className="text-sm text-gray-500 mb-2 sm:mb-0 sm:mr-4">
                {initialForm ? 'Save your changes to update this form' : 'Ready to create your new form?'}
              </p>
              <button
                type="submit"
                disabled={isSubmitting}
                className={`w-full sm:w-auto px-5 py-2.5 bg-blue-600 text-white rounded-md shadow-sm ${
                  isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-blue-700'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors flex items-center justify-center`}
              >
                {isSubmitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : initialForm ? (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
                    </svg>
                    Update Form
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1.5" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                    Create Form
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </form>
      </div>
    </div>
  );
};

export default FormBuilder;