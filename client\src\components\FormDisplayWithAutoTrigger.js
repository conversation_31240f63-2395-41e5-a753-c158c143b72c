import React, { useState, useEffect } from 'react';
import DynamicForm from './DynamicForm';
import AutoTriggerHandler from './AutoTriggerHandler';
import api from '../utils/api';

/**
 * FormDisplayWithAutoTrigger - Enhanced form display component with auto-trigger functionality
 * This component displays form data, handles manual form linking buttons, and manages auto-trigger
 */
const FormDisplayWithAutoTrigger = ({
  form,
  data,
  onSubmit,
  onCancel,
  showAutoTrigger = true,
  showFormLinkingButtons = true,
  skipLeaveBalanceAutoTrigger = false
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [formLinkingActions, setFormLinkingActions] = useState([]);
  const [linkedForm, setLinkedForm] = useState(null);
  const [showLinkedFormModal, setShowLinkedFormModal] = useState(false);

  // Check for form linking configuration
  useEffect(() => {
    if (!form || !data || !showFormLinkingButtons) return;

    const checkFormLinking = () => {
      const formLinkingConfig = form.formConfig?.formLinking;
      if (!formLinkingConfig?.enabled || !formLinkingConfig.recordActions?.length) {
        return;
      }

      const availableActions = formLinkingConfig.recordActions.map((action, index) => ({
        ...action,
        actionIndex: index,
        shouldShow: shouldShowButton(data, action.conditions)
      })).filter(action => action.shouldShow);

      setFormLinkingActions(availableActions);
    };

    checkFormLinking();
  }, [form, data, showFormLinkingButtons]);

  // Helper function to check if button should be shown
  const shouldShowButton = (record, conditions = []) => {
    if (!conditions || conditions.length === 0) {
      return true;
    }
    
    return conditions.every(condition => {
      const fieldValue = record[condition.field];
      const conditionValue = condition.value;
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === conditionValue;
        case 'not_equals':
          return fieldValue !== conditionValue;
        case 'contains':
          return fieldValue && fieldValue.toString().includes(conditionValue);
        case 'not_contains':
          return !fieldValue || !fieldValue.toString().includes(conditionValue);
        case 'exists':
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        case 'not_exists':
          return fieldValue === undefined || fieldValue === null || fieldValue === '';
        default:
          return true;
      }
    });
  };

  // Handle form linking button click
  const handleFormLinkingClick = async (action) => {
    setIsLoading(true);
    setError(null);
    
    try {
      const response = await api.post(`/unifiedconfigs/${form._id}/form-link`, {
        recordData: data,
        parentData: {},
        actionIndex: action.actionIndex
      });

      if (response.data.success) {
        setLinkedForm({
          ...response.data.targetForm,
          prefillData: response.data.prefillData,
          buttonText: response.data.buttonText,
          isManuallyTriggered: true
        });
        setShowLinkedFormModal(true);
      } else {
        setError(response.data.message || 'Failed to process form linking');
      }
    } catch (error) {
      console.error('Error processing form linking:', error);
      setError('Failed to process form linking');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle linked form submission
  const handleLinkedFormSubmit = async (formId, formData) => {
    try {
      const response = await api.post(`/unifiedconfigs/${formId}/submit`, formData);
      
      if (response.data.success) {
        setShowLinkedFormModal(false);
        setLinkedForm(null);
        
        // Notify parent component
        if (onSubmit) {
          onSubmit(formId, formData, response.data);
        }
      }
    } catch (error) {
      console.error('Error submitting linked form:', error);
      setError('Failed to submit linked form');
    }
  };

  const handleCloseLinkedFormModal = () => {
    setShowLinkedFormModal(false);
    setLinkedForm(null);
  };

  const handleAutoTriggerSubmit = (formId, formData, responseData) => {
    console.log('Auto-triggered form submitted:', { formId, formData, responseData });
    // Handle auto-trigger form submission result
    if (onSubmit) {
      onSubmit(formId, formData, responseData);
    }
  };

  if (!form) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Error Display */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative">
          <span className="block sm:inline">{error}</span>
          <button
            className="absolute top-0 bottom-0 right-0 px-4 py-3"
            onClick={() => setError(null)}
          >
            <svg className="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
              <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
            </svg>
          </button>
        </div>
      )}

      {/* Main Form */}
      <DynamicForm
        form={form}
        onSubmit={onSubmit}
        onCancel={onCancel}
      />

      {/* Form Linking Action Buttons */}
      {showFormLinkingButtons && formLinkingActions.length > 0 && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Available Actions</h3>
          <div className="flex flex-wrap gap-2">
            {formLinkingActions.map((action) => (
              <button
                key={action.actionIndex}
                onClick={() => handleFormLinkingClick(action)}
                disabled={isLoading}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                  getButtonClasses(action.buttonStyle)
                } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isLoading ? 'Processing...' : action.buttonText}
              </button>
            ))}
          </div>
        </div>
      )}

      {/* Auto-Trigger Handler */}
      {showAutoTrigger && (
        <AutoTriggerHandler
          formId={form._id}
          recordData={data}
          onFormSubmit={handleAutoTriggerSubmit}
          enabled={true}
          skipLeaveBalanceAutoTrigger={skipLeaveBalanceAutoTrigger}
        />
      )}

      {/* Linked Form Modal */}
      {showLinkedFormModal && linkedForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="bg-green-500 text-white px-6 py-4 rounded-t-lg">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">
                    🔗 Linked Form
                  </h2>
                  <p className="text-sm text-green-100 mt-1">
                    {linkedForm.name}
                  </p>
                </div>
                <button
                  onClick={handleCloseLinkedFormModal}
                  className="text-white hover:text-gray-200 focus:outline-none"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </button>
              </div>
            </div>
            
            {/* Modal Content */}
            <div className="p-6">
              <div className="bg-green-50 border border-green-200 rounded-md p-3 mb-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">Linked Form</h3>
                    <div className="mt-1 text-sm text-green-700">
                      This form was opened through form linking. 
                      Some fields may be pre-filled based on the original record.
                    </div>
                  </div>
                </div>
              </div>
              
              <DynamicForm
                form={linkedForm}
                onSubmit={handleLinkedFormSubmit}
                onCancel={handleCloseLinkedFormModal}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to get button classes based on style
const getButtonClasses = (style) => {
  switch (style) {
    case 'primary':
      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';
    case 'secondary':
      return 'bg-gray-500 text-white hover:bg-gray-600 focus:ring-gray-500';
    case 'success':
      return 'bg-green-500 text-white hover:bg-green-600 focus:ring-green-500';
    case 'warning':
      return 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500';
    case 'danger':
      return 'bg-red-500 text-white hover:bg-red-600 focus:ring-red-500';
    default:
      return 'bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-500';
  }
};

export default FormDisplayWithAutoTrigger;