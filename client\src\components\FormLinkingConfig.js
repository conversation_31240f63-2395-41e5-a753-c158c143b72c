import React, { useState, useEffect } from 'react';
import api from '../utils/api';

const FormLinkingConfig = ({ 
  formLinking, 
  onFormLinkingChange, 
  isFormType = false 
}) => {
  const [availableForms, setAvailableForms] = useState([]);
  const [loading, setLoading] = useState(false);

  // Fetch available forms for linking
  useEffect(() => {
    const fetchAvailableForms = async () => {
      if (!isFormType) return;
      
      setLoading(true);
      try {
        const response = await api.get('/unifiedconfigs/forms-for-linking');
        if (response.data.success) {
          setAvailableForms(response.data.forms);
        }
      } catch (error) {
        console.error('Error fetching available forms:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAvailableForms();
  }, [isFormType]);

  // Handle form linking enabled/disabled
  const handleFormLinkingToggle = (enabled) => {
    onFormLinkingChange({
      ...formLinking,
      enabled: enabled
    });
  };

  // Handle adding a new record action
  const handleAddRecordAction = () => {
    const newAction = {
      buttonText: 'Apply',
      targetFormId: '',
      targetFormName: '',
      fieldMapping: {},
      conditions: [],
      buttonStyle: 'primary'
    };

    onFormLinkingChange({
      ...formLinking,
      recordActions: [...formLinking.recordActions, newAction]
    });
  };

  // Handle removing a record action
  const handleRemoveRecordAction = (index) => {
    const updatedActions = formLinking.recordActions.filter((_, i) => i !== index);
    onFormLinkingChange({
      ...formLinking,
      recordActions: updatedActions
    });
  };

  // Handle updating a record action
  const handleUpdateRecordAction = (index, updatedAction) => {
    const updatedActions = formLinking.recordActions.map((action, i) => 
      i === index ? updatedAction : action
    );
    onFormLinkingChange({
      ...formLinking,
      recordActions: updatedActions
    });
  };

  // Handle field mapping changes
  const handleFieldMappingChange = (actionIndex, sourceField, targetField) => {
    const updatedActions = formLinking.recordActions.map((action, i) => {
      if (i === actionIndex) {
        const newFieldMapping = { ...action.fieldMapping };
        if (targetField) {
          newFieldMapping[targetField] = sourceField;
        } else {
          delete newFieldMapping[sourceField];
        }
        return { ...action, fieldMapping: newFieldMapping };
      }
      return action;
    });

    onFormLinkingChange({
      ...formLinking,
      recordActions: updatedActions
    });
  };

  // Handle condition changes
  const handleConditionChange = (actionIndex, conditionIndex, updatedCondition) => {
    const updatedActions = formLinking.recordActions.map((action, i) => {
      if (i === actionIndex) {
        const newConditions = action.conditions.map((condition, j) => 
          j === conditionIndex ? updatedCondition : condition
        );
        return { ...action, conditions: newConditions };
      }
      return action;
    });

    onFormLinkingChange({
      ...formLinking,
      recordActions: updatedActions
    });
  };

  // Handle adding a condition
  const handleAddCondition = (actionIndex) => {
    const newCondition = {
      field: '',
      operator: 'equals',
      value: ''
    };

    const updatedActions = formLinking.recordActions.map((action, i) => {
      if (i === actionIndex) {
        return { ...action, conditions: [...action.conditions, newCondition] };
      }
      return action;
    });

    onFormLinkingChange({
      ...formLinking,
      recordActions: updatedActions
    });
  };

  // Handle removing a condition
  const handleRemoveCondition = (actionIndex, conditionIndex) => {
    const updatedActions = formLinking.recordActions.map((action, i) => {
      if (i === actionIndex) {
        const newConditions = action.conditions.filter((_, j) => j !== conditionIndex);
        return { ...action, conditions: newConditions };
      }
      return action;
    });

    onFormLinkingChange({
      ...formLinking,
      recordActions: updatedActions
    });
  };

  if (!isFormType) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-gray-900">Form Linking Configuration</h3>
      <p className="text-sm text-gray-600">
        Configure action buttons that appear on data records to link to other forms.
      </p>
      
      {/* Enable/Disable Form Linking */}
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="formLinkingEnabled"
          checked={formLinking.enabled}
          onChange={(e) => handleFormLinkingToggle(e.target.checked)}
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        />
        <label htmlFor="formLinkingEnabled" className="text-sm font-medium text-gray-700">
          Enable Form Linking
        </label>
      </div>

      {formLinking.enabled && (
        <div className="space-y-6">
          {/* Record Actions */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-800">Record Actions</h4>
              <button
                type="button"
                onClick={handleAddRecordAction}
                className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
              >
                Add Action
              </button>
            </div>

            {loading && (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                <p className="mt-2 text-sm text-gray-600">Loading available forms...</p>
              </div>
            )}

            {formLinking.recordActions.map((action, actionIndex) => (
              <div key={actionIndex} className="border border-gray-200 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-4">
                  <h5 className="text-sm font-medium text-gray-700">Action {actionIndex + 1}</h5>
                  <button
                    type="button"
                    onClick={() => handleRemoveRecordAction(actionIndex)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Button Text */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Button Text
                    </label>
                    <input
                      type="text"
                      value={action.buttonText}
                      onChange={(e) => handleUpdateRecordAction(actionIndex, { 
                        ...action, 
                        buttonText: e.target.value 
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Apply"
                    />
                  </div>

                  {/* Button Style */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Button Style
                    </label>
                    <select
                      value={action.buttonStyle}
                      onChange={(e) => handleUpdateRecordAction(actionIndex, { 
                        ...action, 
                        buttonStyle: e.target.value 
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="primary">Primary (Blue)</option>
                      <option value="secondary">Secondary (Gray)</option>
                      <option value="success">Success (Green)</option>
                      <option value="warning">Warning (Yellow)</option>
                      <option value="danger">Danger (Red)</option>
                    </select>
                  </div>
                </div>

                {/* Target Form Selection */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Target Form
                  </label>
                  <select
                    value={action.targetFormId}
                    onChange={(e) => {
                      const selectedForm = availableForms.find(f => f._id === e.target.value);
                      handleUpdateRecordAction(actionIndex, { 
                        ...action, 
                        targetFormId: e.target.value,
                        targetFormName: selectedForm ? selectedForm.name : ''
                      });
                    }}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a form...</option>
                    {availableForms.map(form => (
                      <option key={form._id} value={form._id}>
                        {form.name} ({form.category})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Field Mapping */}
                <div className="mt-4">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Field Mapping
                  </label>
                  <div className="text-xs text-gray-500 mb-2">
                    Map fields from record data to target form fields
                  </div>
                  
                  <div className="space-y-2">
                    {Object.entries(action.fieldMapping).map(([targetField, sourceField], mappingIndex) => (
                      <div key={mappingIndex} className="flex items-center space-x-2">
                        <input
                          type="text"
                          value={sourceField}
                          onChange={(e) => handleFieldMappingChange(actionIndex, e.target.value, targetField)}
                          placeholder="Source field"
                          className="flex-1 p-2 border border-gray-300 rounded-md text-sm"
                        />
                        <span className="text-gray-500">→</span>
                        <input
                          type="text"
                          value={targetField}
                          onChange={(e) => {
                            const oldMapping = { ...action.fieldMapping };
                            delete oldMapping[targetField];
                            if (e.target.value) {
                              oldMapping[e.target.value] = sourceField;
                            }
                            handleUpdateRecordAction(actionIndex, { 
                              ...action, 
                              fieldMapping: oldMapping 
                            });
                          }}
                          placeholder="Target field"
                          className="flex-1 p-2 border border-gray-300 rounded-md text-sm"
                        />
                        <button
                          type="button"
                          onClick={() => handleFieldMappingChange(actionIndex, sourceField, null)}
                          className="text-red-600 hover:text-red-800 text-sm"
                        >
                          Remove
                        </button>
                      </div>
                    ))}
                    <button
                      type="button"
                      onClick={() => handleFieldMappingChange(actionIndex, '', 'newField')}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      + Add Field Mapping
                    </button>
                  </div>
                </div>

                {/* Auto-Trigger Configuration */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Auto-Trigger Configuration
                    </label>
                  </div>
                  <div className="text-xs text-gray-500 mb-2">
                    Configure automatic opening of target form when conditions are met
                  </div>
                  
                  <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                    <div className="flex items-center space-x-3 mb-4">
                      <input
                        type="checkbox"
                        id={`autoTrigger-${actionIndex}`}
                        checked={action.autoTrigger?.enabled || false}
                        onChange={(e) => handleUpdateRecordAction(actionIndex, { 
                          ...action, 
                          autoTrigger: {
                            ...action.autoTrigger,
                            enabled: e.target.checked,
                            delaySeconds: action.autoTrigger?.delaySeconds || 0
                          }
                        })}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`autoTrigger-${actionIndex}`} className="text-sm font-medium text-gray-700">
                        Enable Auto-Trigger
                      </label>
                    </div>
                    
                    {action.autoTrigger?.enabled && (
                      <div className="space-y-3">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Delay (seconds)
                          </label>
                          <input
                            type="number"
                            min="0"
                            max="60"
                            value={action.autoTrigger?.delaySeconds || 0}
                            onChange={(e) => handleUpdateRecordAction(actionIndex, { 
                              ...action, 
                              autoTrigger: {
                                ...action.autoTrigger,
                                enabled: true,
                                delaySeconds: parseInt(e.target.value) || 0
                              }
                            })}
                            className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                            placeholder="0"
                          />
                          <div className="text-xs text-gray-500 mt-1">
                            Delay in seconds before auto-triggering (0 = immediate)
                          </div>
                        </div>
                        
                        <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                          <div className="flex items-start">
                            <div className="flex-shrink-0">
                              <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                              </svg>
                            </div>
                            <div className="ml-3">
                              <h3 className="text-sm font-medium text-blue-800">Auto-Trigger Enabled</h3>
                              <div className="mt-1 text-sm text-blue-700">
                                When enabled, the target form will automatically open after the specified delay when data is displayed and conditions are met.
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {/* Auto-Submit on Click (when auto-trigger is disabled) */}
                    {!action.autoTrigger?.enabled && (
                      <div className="mt-4">
                        <div className="flex items-center space-x-3 mb-2">
                          <input
                            type="checkbox"
                            id={`autoSubmitOnClick-${actionIndex}`}
                            checked={action.autoSubmitOnClick || false}
                            onChange={(e) => handleUpdateRecordAction(actionIndex, { 
                              ...action, 
                              autoSubmitOnClick: e.target.checked
                            })}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`autoSubmitOnClick-${actionIndex}`} className="text-sm font-medium text-gray-700">
                            Auto-Submit on Click
                          </label>
                        </div>
                        
                        {action.autoSubmitOnClick && (
                          <div className="bg-green-50 border border-green-200 rounded-md p-3">
                            <div className="flex items-start">
                              <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <h3 className="text-sm font-medium text-green-800">Auto-Submit Enabled</h3>
                                <div className="mt-1 text-sm text-green-700">
                                  When clicked, the button will automatically submit the form data to the API without displaying the form to the user.
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        
                        {!action.autoSubmitOnClick && (
                          <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
                            <div className="flex items-start">
                              <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                                </svg>
                              </div>
                              <div className="ml-3">
                                <h3 className="text-sm font-medium text-gray-800">Display Form</h3>
                                <div className="mt-1 text-sm text-gray-700">
                                  When clicked, the button will display the form for user input (default behavior).
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Conditions */}
                <div className="mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      Show Conditions
                    </label>
                    <button
                      type="button"
                      onClick={() => handleAddCondition(actionIndex)}
                      className="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      + Add Condition
                    </button>
                  </div>
                  <div className="text-xs text-gray-500 mb-2">
                    Define when this button should be visible (also affects auto-trigger)
                  </div>
                  
                  {action.conditions.map((condition, conditionIndex) => (
                    <div key={conditionIndex} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={condition.field}
                        onChange={(e) => handleConditionChange(actionIndex, conditionIndex, { 
                          ...condition, 
                          field: e.target.value 
                        })}
                        placeholder="Field name"
                        className="flex-1 p-2 border border-gray-300 rounded-md text-sm"
                      />
                      <select
                        value={condition.operator}
                        onChange={(e) => handleConditionChange(actionIndex, conditionIndex, { 
                          ...condition, 
                          operator: e.target.value 
                        })}
                        className="p-2 border border-gray-300 rounded-md text-sm"
                      >
                        <option value="equals">Equals</option>
                        <option value="not_equals">Not Equals</option>
                        <option value="contains">Contains</option>
                        <option value="not_contains">Not Contains</option>
                        <option value="exists">Exists</option>
                        <option value="not_exists">Not Exists</option>
                      </select>
                      <input
                        type="text"
                        value={condition.value}
                        onChange={(e) => handleConditionChange(actionIndex, conditionIndex, { 
                          ...condition, 
                          value: e.target.value 
                        })}
                        placeholder="Value"
                        className="flex-1 p-2 border border-gray-300 rounded-md text-sm"
                      />
                      <button
                        type="button"
                        onClick={() => handleRemoveCondition(actionIndex, conditionIndex)}
                        className="text-red-600 hover:text-red-800 text-sm"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default FormLinkingConfig;