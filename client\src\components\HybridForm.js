import React, { useState, useEffect } from 'react';
import api from '../utils/api';

const HybridForm = ({ hybridFlow, message, onCancel, onSubmit, formId }) => {
  const [formData, setFormData] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState({});

  // Initialize form data from hybridFlow
  useEffect(() => {
    if (hybridFlow && hybridFlow.formFields && hybridFlow.collectedData) {
      const initialData = {};
      
      // Pre-populate with collected data
      Object.keys(hybridFlow.collectedData).forEach(key => {
        initialData[key] = hybridFlow.collectedData[key];
      });
      
      setFormData(initialData);
    }
  }, [hybridFlow]);

  const handleInputChange = (fieldName, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
    
    // Clear error when user starts typing
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: null
      }));
    }
  };

  // Quick date selection handlers
  const handleTodayClick = () => {
    const today = new Date().toISOString().split('T')[0];
    
    // Get all date fields from the form
    const dateFields = hybridFlow?.formFields?.filter(field => field.type === 'date') || [];
    const newFormData = { ...formData };
    const newErrors = { ...errors };
    
    // Update all date fields
    dateFields.forEach(field => {
      newFormData[field.name] = today;
      newErrors[field.name] = null;
    });
    
    setFormData(newFormData);
    setErrors(newErrors);
  };

  const handleTomorrowClick = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    
    // Get all date fields from the form
    const dateFields = hybridFlow?.formFields?.filter(field => field.type === 'date') || [];
    const newFormData = { ...formData };
    const newErrors = { ...errors };
    
    // Update all date fields
    dateFields.forEach(field => {
      newFormData[field.name] = tomorrowStr;
      newErrors[field.name] = null;
    });
    
    setFormData(newFormData);
    setErrors(newErrors);
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (hybridFlow && hybridFlow.formFields) {
      hybridFlow.formFields.forEach(field => {
        if (field.required && !formData[field.name]) {
          newErrors[field.name] = `${field.label} is required`;
        }
      });
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Get conversation ID from localStorage or other source
      const conversationId = localStorage.getItem('conversationId') || 
                            sessionStorage.getItem('conversationId') || 
                            hybridFlow.conversationId;
      
      // Prepare final form data
      const finalFormData = {
        ...hybridFlow.collectedData,
        ...formData
      };
      
      console.log('🔄 Submitting hybrid form with data:', finalFormData);
      
      const response = await api.post('/chat/submit-form', {
        formId: formId || hybridFlow.formConfig?._id,
        formData: finalFormData,
        conversationId: conversationId,
        isHybridForm: true
      }, {
        headers: {
          'x-emp-id': localStorage.getItem('empId') || 'test123',
          'x-role-type': localStorage.getItem('roleType') || 'employee'
        }
      });
      
      console.log('✅ Hybrid form submitted successfully:', response.data);
      
      // Call the onSubmit callback to handle the response
      if (onSubmit) {
        onSubmit(response.data);
      } else {
        console.warn('⚠️ No onSubmit callback provided to handle form submission response');
      }
      
      // Add success message to chat
      const successMessage = response.data.success ? 
        response.data.message : 
        'Form submitted successfully!';
      
      console.log('✅ Hybrid form submission completed');
      
    } catch (error) {
      console.error('❌ Hybrid form submission failed:', error);
      
      let errorMessage = 'Sorry, there was an error submitting your hybrid form. Please try again.';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      // Call the onSubmit callback even for errors
      if (onSubmit) {
        onSubmit({
          success: false,
          message: errorMessage,
          error: error.response?.data || error.message
        });
      }
      
      // The error message will be handled by the server response
      console.log('❌ Hybrid form submission failed:', errorMessage);
      
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderFormField = (field) => {
    const fieldValue = formData[field.name] || '';
    const fieldError = errors[field.name];
    
    switch (field.type) {
      case 'text':
      case 'email':
      case 'number':
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type={field.type}
              value={fieldValue}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                fieldError ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={field.placeholder}
              required={field.required}
            />
            {fieldError && (
              <p className="mt-1 text-sm text-red-600">{fieldError}</p>
            )}
          </div>
        );
        
      case 'textarea':
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <textarea
              value={fieldValue}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                fieldError ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder={field.placeholder}
              rows={3}
              required={field.required}
            />
            {fieldError && (
              <p className="mt-1 text-sm text-red-600">{fieldError}</p>
            )}
          </div>
        );
        
      case 'select':
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <select
              value={fieldValue}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                fieldError ? 'border-red-300' : 'border-gray-300'
              }`}
              required={field.required}
            >
              <option value="">Select {field.label}</option>
              {field.options && field.options.map((option, index) => (
                <option key={index} value={option.value || option}>
                  {option.label || option}
                </option>
              ))}
            </select>
            {fieldError && (
              <p className="mt-1 text-sm text-red-600">{fieldError}</p>
            )}
          </div>
        );
        
      case 'date':
        return (
          <div key={field.name} className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label}
              {field.required && <span className="text-red-500 ml-1">*</span>}
            </label>
            <input
              type="date"
              value={fieldValue}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                fieldError ? 'border-red-300' : 'border-gray-300'
              }`}
              required={field.required}
            />
            {fieldError && (
              <p className="mt-1 text-sm text-red-600">{fieldError}</p>
            )}
          </div>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="bg-white border rounded-lg p-6 shadow-lg">
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-gray-800 mb-2">
          Complete Your Application
        </h3>
        {message && (
          <p className="text-gray-600 text-sm mb-4">{message.content}</p>
        )}
      </div>
      
      {/* Quick Date Selection Buttons - Moved to top */}
      {hybridFlow && hybridFlow.formFields && hybridFlow.formFields.some(field => 
        field.type === 'date'
      ) && (
        <div className="border-b pb-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Quick Date Selection:</span>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={handleTodayClick}
                className="px-3 py-1.5 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 hover:border-blue-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors"
              >
                Today
              </button>
              <button
                type="button"
                onClick={handleTomorrowClick}
                className="px-3 py-1.5 text-xs font-medium text-green-600 bg-green-50 border border-green-200 rounded-md hover:bg-green-100 hover:border-green-300 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-1 transition-colors"
              >
                Tomorrow
              </button>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-1">Click to automatically set both From Date and To Date</p>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
        {hybridFlow && hybridFlow.formFields && hybridFlow.formFields.map(field => 
          renderFormField(field)
        )}
        
        <div className="flex justify-end space-x-3 mt-6">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Form'}
          </button>
        </div>
      </form>
    </div>
  );
};

export default HybridForm;