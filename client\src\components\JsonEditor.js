import React, { useState, useEffect } from 'react';

const JsonEditor = ({ value, onChange, placeholder, rows = 10, className = '' }) => {
  const [jsonValue, setJsonValue] = useState('');
  const [isValid, setIsValid] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    try {
      const formatted = JSON.stringify(value, null, 2);
      setJsonValue(formatted);
      setIsValid(true);
      setError('');
    } catch (err) {
      setJsonValue(JSON.stringify(value || {}, null, 2));
      setIsValid(false);
      setError('Invalid JSON');
    }
  }, [value]);

  const handleChange = (e) => {
    const newValue = e.target.value;
    setJsonValue(newValue);

    try {
      const parsed = JSON.parse(newValue);
      setIsValid(true);
      setError('');
      onChange(parsed);
    } catch (err) {
      setIsValid(false);
      setError(err.message);
    }
  };

  const formatJson = () => {
    try {
      const parsed = JSON.parse(jsonValue);
      const formatted = JSON.stringify(parsed, null, 2);
      setJsonValue(formatted);
      setIsValid(true);
      setError('');
      onChange(parsed);
    } catch (err) {
      setError(err.message);
      setIsValid(false);
    }
  };

  const clearJson = () => {
    setJsonValue('{}');
    setIsValid(true);
    setError('');
    onChange({});
  };

  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${isValid ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span className={`text-sm ${isValid ? 'text-green-600' : 'text-red-600'}`}>
            {isValid ? 'Valid JSON' : 'Invalid JSON'}
          </span>
        </div>
        <div className="flex space-x-2">
          <button
            type="button"
            onClick={formatJson}
            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
          >
            Format
          </button>
          <button
            type="button"
            onClick={clearJson}
            className="px-3 py-1 bg-gray-500 text-white rounded-md hover:bg-gray-600 text-sm"
          >
            Clear
          </button>
        </div>
      </div>
      
      <textarea
        value={jsonValue}
        onChange={handleChange}
        rows={rows}
        placeholder={placeholder}
        className={`w-full p-3 border rounded-md focus:outline-none focus:ring-2 font-mono text-sm ${
          isValid 
            ? 'border-gray-300 focus:ring-blue-500' 
            : 'border-red-500 focus:ring-red-500'
        } ${className}`}
      />
      
      {error && (
        <p className="text-red-500 text-sm">
          Error: {error}
        </p>
      )}
    </div>
  );
};

export default JsonEditor;