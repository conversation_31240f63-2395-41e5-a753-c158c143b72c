import React, { useState } from 'react';

const PayloadPreview = ({ 
  isOpen, 
  onClose, 
  config, 
  isFormConfig = false,
  customPayload 
}) => {
  const [sampleData, setSampleData] = useState({});
  const [userData, setUserData] = useState({
    empId: 'EMP123',
    roleType: 'admin',
    token: 'sample-token'
  });
  const [preview, setPreview] = useState(null);
  const [loading, setLoading] = useState(false);

  if (!isOpen) return null;

  const generatePreview = async () => {
    setLoading(true);
    try {
      let previewPayload = {};
      
      if (customPayload && customPayload.enabled) {
        // Client-side preview using placeholders
        let previewString = JSON.stringify(customPayload.structure, null, 2);
        
        // Replace user placeholders
        Object.keys(userData).forEach(key => {
          const placeholder = `{{user.${key}}}`;
          previewString = previewString.replace(new RegExp(placeholder, 'g'), userData[key]);
        });
        
        // Replace data placeholders
        Object.keys(sampleData).forEach(key => {
          const placeholder = `{{data.${key}}}`;
          previewString = previewString.replace(new RegExp(placeholder, 'g'), sampleData[key]);
        });
        
        // Replace system placeholders
        previewString = previewString.replace(/\{\{system\.uuid\}\}/g, 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx');
        previewString = previewString.replace(/\{\{system\.timestamp\}\}/g, Date.now().toString());
        previewString = previewString.replace(/\{\{system\.datetime\}\}/g, new Date().toISOString());
        previewString = previewString.replace(/\{\{system\.date\}\}/g, new Date().toISOString().split('T')[0]);
        previewString = previewString.replace(/\{\{system\.year\}\}/g, new Date().getFullYear().toString());
        previewString = previewString.replace(/\{\{system\.month\}\}/g, (new Date().getMonth() + 1).toString().padStart(2, '0'));
        previewString = previewString.replace(/\{\{system\.day\}\}/g, new Date().getDate().toString().padStart(2, '0'));
        
        try {
          previewPayload = JSON.parse(previewString);
        } catch (err) {
          previewPayload = { error: 'Invalid JSON after placeholder replacement', rawPreview: previewString };
        }
      } else {
        // Default payload structure
        if (isFormConfig) {
          previewPayload = { ...sampleData };
        } else {
          previewPayload = { ...sampleData };
          if (config?.apiConfig?.dataInjection?.injectUserData) {
            if (config.apiConfig.dataInjection.autoFields?.empId) {
              previewPayload.empId = userData.empId;
            }
            if (config.apiConfig.dataInjection.autoFields?.roleType) {
              previewPayload.roleType = userData.roleType;
            }
          }
        }
      }
      
      setPreview(previewPayload);
    } catch (error) {
      setPreview({ error: error.message });
    } finally {
      setLoading(false);
    }
  };

  const addSampleField = () => {
    const fieldName = `field${Object.keys(sampleData).length + 1}`;
    setSampleData({
      ...sampleData,
      [fieldName]: `sample value ${Object.keys(sampleData).length + 1}`
    });
  };

  const removeSampleField = (fieldName) => {
    const newData = { ...sampleData };
    delete newData[fieldName];
    setSampleData(newData);
  };

  const updateSampleField = (oldKey, newKey, value) => {
    const newData = { ...sampleData };
    delete newData[oldKey];
    newData[newKey] = value;
    setSampleData(newData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full m-4 max-h-[90vh] overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600 to-blue-800 px-6 py-4 text-white">
          <h2 className="text-xl font-bold">Payload Preview</h2>
          <p className="text-blue-100 mt-1">
            {isFormConfig ? 'Form Submission' : 'API Request'} Payload Structure
          </p>
        </div>
        
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Configuration Panel */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Configuration</h3>
              
              {/* User Data */}
              <div>
                <label className="block text-gray-700 font-medium mb-2">User Data</label>
                <div className="space-y-2">
                  <input
                    type="text"
                    value={userData.empId}
                    onChange={(e) => setUserData({...userData, empId: e.target.value})}
                    placeholder="Employee ID"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    value={userData.roleType}
                    onChange={(e) => setUserData({...userData, roleType: e.target.value})}
                    placeholder="Role Type"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <input
                    type="text"
                    value={userData.token}
                    onChange={(e) => setUserData({...userData, token: e.target.value})}
                    placeholder="Token"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
              
              {/* Sample Data */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label className="block text-gray-700 font-medium">Sample Data</label>
                  <button
                    onClick={addSampleField}
                    className="px-3 py-1 bg-green-500 text-white rounded-md hover:bg-green-600 text-sm"
                  >
                    Add Field
                  </button>
                </div>
                <div className="space-y-2">
                  {Object.entries(sampleData).map(([key, value]) => (
                    <div key={key} className="flex space-x-2">
                      <input
                        type="text"
                        value={key}
                        onChange={(e) => updateSampleField(key, e.target.value, value)}
                        placeholder="Field name"
                        className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <input
                        type="text"
                        value={value}
                        onChange={(e) => updateSampleField(key, key, e.target.value)}
                        placeholder="Field value"
                        className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <button
                        onClick={() => removeSampleField(key)}
                        className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              </div>
              
              <button
                onClick={generatePreview}
                disabled={loading}
                className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
              >
                {loading ? 'Generating...' : 'Generate Preview'}
              </button>
            </div>
            
            {/* Preview Panel */}
            <div>
              <h3 className="text-lg font-semibold mb-2">Preview</h3>
              <div className="border border-gray-300 rounded-md p-4 bg-gray-50 min-h-[400px]">
                {preview ? (
                  <pre className="text-sm overflow-auto whitespace-pre-wrap">
                    {JSON.stringify(preview, null, 2)}
                  </pre>
                ) : (
                  <p className="text-gray-500 text-center">
                    Click "Generate Preview" to see the payload structure
                  </p>
                )}
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 px-6 py-4 border-t flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PayloadPreview;