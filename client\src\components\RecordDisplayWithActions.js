import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import api from '../utils/api';

/**
 * Component for displaying records with Apply buttons for form linking
 */
const RecordDisplayWithActions = ({ 
  data, 
  formId, 
  formLinkingConfig, 
  onFormLinkTriggered,
  hideRecordDisplay = false,
  showOnlyButtons = false,
  autoTriggerOptions = {} // New flexible auto-trigger options
}) => {
  const [loadingActions, setLoadingActions] = useState({});
  const [autoTriggerState, setAutoTriggerState] = useState({});
  const [executedTriggers, setExecutedTriggers] = useState(new Set());
  const componentId = useRef(Math.random().toString(36).substr(2, 9));
  const timeoutRefs = useRef({});

  // Auto-trigger configuration with defaults
  const autoTriggerConfig = useMemo(() => ({
    enabled: autoTriggerOptions.enabled ?? false,
    delay: autoTriggerOptions.delay ?? 2,
    showCountdown: autoTriggerOptions.showCountdown ?? true,
    triggerOnce: autoTriggerOptions.triggerOnce ?? true,
    resetOnDataChange: autoTriggerOptions.resetOnDataChange ?? true,
    triggerCondition: autoTriggerOptions.triggerCondition ?? 'first', // 'first', 'all', 'custom'
    customTriggerLogic: autoTriggerOptions.customTriggerLogic, // Custom function
    onBeforeTrigger: autoTriggerOptions.onBeforeTrigger, // Callback before trigger
    onAfterTrigger: autoTriggerOptions.onAfterTrigger, // Callback after trigger
    ...autoTriggerOptions
  }), [autoTriggerOptions]);

  // Generate stable data hash for change detection
  const dataHash = useMemo(() => JSON.stringify(data), [data]);

  // Process records from various data structures
  const processedRecords = useMemo(() => {
    if (!data) return [];
    
    if (Array.isArray(data)) return data;
    
    if (typeof data === 'object') {
      const arrayFields = ['attendanceInfo', 'records', 'items', 'data', 'results'];
      
      for (const field of arrayFields) {
        if (data[field] && Array.isArray(data[field])) {
          return data[field];
        }
      }
      
      return [data]; // Single object as record
    }
    
    return [];
  }, [data]);

  // Check if record meets action conditions
  const shouldShowApplyButton = useCallback((record, conditions = []) => {
    if (!conditions?.length) return true;
    
    const validConditions = conditions.filter(condition => 
      condition.field?.trim()
    );
    
    if (!validConditions.length) return true;
    
    return validConditions.every(condition => {
      const fieldValue = record[condition.field];
      const conditionValue = condition.value;
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === conditionValue;
        case 'not_equals':
          return fieldValue !== conditionValue;
        case 'contains':
          return fieldValue?.toString().includes(conditionValue);
        case 'not_contains':
          return !fieldValue?.toString().includes(conditionValue);
        case 'exists':
          return fieldValue != null && fieldValue !== '';
        case 'not_exists':
          return fieldValue == null || fieldValue === '';
        default:
          return true;
      }
    });
  }, []);

  // Get eligible records for auto-trigger
  const getEligibleRecords = useCallback(() => {
    if (!formLinkingConfig?.recordActions) return [];
    
    const eligible = [];
    
    formLinkingConfig.recordActions.forEach((action, actionIndex) => {
      const shouldAutoTrigger = action.autoTrigger?.enabled || autoTriggerConfig.enabled;
      
      if (shouldAutoTrigger) {
        processedRecords.forEach((record, recordIndex) => {
          if (shouldShowApplyButton(record, action.conditions)) {
            eligible.push({
              record,
              recordIndex,
              action,
              actionIndex,
              key: `${recordIndex}-${actionIndex}`
            });
          }
        });
      }
    });
    
    return eligible;
  }, [processedRecords, formLinkingConfig, autoTriggerConfig.enabled, shouldShowApplyButton]);

  // Determine which records to trigger based on condition
  const getRecordsToTrigger = useCallback((eligibleRecords) => {
    if (!eligibleRecords.length) return [];
    
    switch (autoTriggerConfig.triggerCondition) {
      case 'first':
        return [eligibleRecords[0]];
      case 'all':
        return eligibleRecords;
      case 'custom':
        return autoTriggerConfig.customTriggerLogic 
          ? autoTriggerConfig.customTriggerLogic(eligibleRecords)
          : [eligibleRecords[0]];
      default:
        return [eligibleRecords[0]];
    }
  }, [autoTriggerConfig]);

  // Clear all timeouts
  const clearAllTimeouts = useCallback(() => {
    Object.values(timeoutRefs.current).forEach(clearTimeout);
    timeoutRefs.current = {};
  }, []);

  // Reset auto-trigger state when data changes
  useEffect(() => {
    if (!autoTriggerConfig.resetOnDataChange) return;
    
    console.log(`🔄 [${componentId.current}] Data changed, resetting auto-trigger state`);
    
    clearAllTimeouts();
    setExecutedTriggers(new Set());
    setAutoTriggerState({});
  }, [dataHash, autoTriggerConfig.resetOnDataChange, clearAllTimeouts]);

  // Execute auto-trigger logic
  const executeAutoTrigger = useCallback(() => {
    const eligibleRecords = getEligibleRecords();
    const recordsToTrigger = getRecordsToTrigger(eligibleRecords);
    
    recordsToTrigger.forEach(({ record, actionIndex, key }) => {
      const triggerKey = `${dataHash}-${key}`;
      
      // Skip if already triggered and triggerOnce is enabled
      if (autoTriggerConfig.triggerOnce && executedTriggers.has(triggerKey)) {
        return;
      }
      
      // Skip if currently processing
      if (autoTriggerState[key]?.processing) {
        return;
      }
      
      const action = formLinkingConfig.recordActions[actionIndex];
      const delay = action.autoTrigger?.delaySeconds ?? autoTriggerConfig.delay;
      
      console.log(`⏰ [${componentId.current}] Auto-triggering in ${delay}s for key: ${key}`);
      
      // Call before trigger callback
      if (autoTriggerConfig.onBeforeTrigger) {
        autoTriggerConfig.onBeforeTrigger({ record, action, actionIndex });
      }
      
      // Mark as executed
      setExecutedTriggers(prev => new Set(prev).add(triggerKey));
      
      if (hideRecordDisplay || delay === 0) {
        // Immediate trigger
        handleApplyClick(record, actionIndex);
        
        if (autoTriggerConfig.onAfterTrigger) {
          autoTriggerConfig.onAfterTrigger({ record, action, actionIndex });
        }
      } else {
        // Delayed trigger with countdown
        setAutoTriggerState(prev => ({
          ...prev,
          [key]: { processing: true, countdown: delay }
        }));
        
        // Countdown logic
        if (autoTriggerConfig.showCountdown) {
          const countdownInterval = setInterval(() => {
            setAutoTriggerState(prev => {
              const current = prev[key];
              if (!current || current.countdown <= 1) {
                clearInterval(countdownInterval);
                return { ...prev, [key]: { ...current, countdown: 0 } };
              }
              return { ...prev, [key]: { ...current, countdown: current.countdown - 1 } };
            });
          }, 1000);
        }
        
        // Execute trigger after delay
        const timeoutId = setTimeout(() => {
          handleApplyClick(record, actionIndex);
          setAutoTriggerState(prev => ({ ...prev, [key]: undefined }));
          
          if (autoTriggerConfig.onAfterTrigger) {
            autoTriggerConfig.onAfterTrigger({ record, action, actionIndex });
          }
        }, delay * 1000);
        
        timeoutRefs.current[key] = timeoutId;
      }
    });
  }, [
    getEligibleRecords, 
    getRecordsToTrigger, 
    autoTriggerConfig, 
    executedTriggers, 
    autoTriggerState, 
    dataHash, 
    hideRecordDisplay,
    formLinkingConfig
  ]);

  // Auto-trigger effect
  useEffect(() => {
    if (!autoTriggerConfig.enabled && !formLinkingConfig?.recordActions?.some(action => action.autoTrigger?.enabled)) {
      return;
    }
    
    const timeoutId = setTimeout(executeAutoTrigger, 100);
    return () => clearTimeout(timeoutId);
  }, [executeAutoTrigger, autoTriggerConfig.enabled]);

  // Handle Apply button click
  const handleApplyClick = useCallback(async (record, actionIndex = 0) => {
    const actionKey = `${JSON.stringify(record)}-${actionIndex}`;
    
    if (loadingActions[actionKey]) return;
    
    setLoadingActions(prev => ({ ...prev, [actionKey]: true }));

    try {
      const action = formLinkingConfig?.recordActions?.[actionIndex];
      const buttonText = action?.buttonText || 'Apply';
      
      const response = await api.post(`/unifiedconfigs/${formId}/form-link`, {
        recordData: record,
        parentData: data,
        actionIndex,
        buttonText
      });

      if (response.data.success) {
        const { autoSubmitOnClick, targetForm, prefillData } = response.data;
        
        if (autoSubmitOnClick) {
          await handleAutoSubmitLinkingForm(
            targetForm._id, 
            prefillData,
            targetForm.name,
            actionKey
          );
        } else if (onFormLinkTriggered) {
          onFormLinkTriggered({
            targetForm,
            prefillData,
            buttonText: response.data.buttonText,
            buttonStyle: response.data.buttonStyle
          });
        }
      } else {
        throw new Error(response.data.message || 'Form linking failed');
      }
    } catch (error) {
      console.error('❌ Error during form linking:', error);
      alert('Error opening form: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));
    }
  }, [formLinkingConfig, formId, data, loadingActions, onFormLinkTriggered]);

  // Handle auto-submit linking form
  const handleAutoSubmitLinkingForm = useCallback(async (targetFormId, prefillData, formName, actionKey) => {
    try {
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const headers = {
        'Authorization': userData.token ? `Bearer ${userData.token}` : undefined,
        'x-emp-id': userData.empId || undefined,
        'x-role-type': userData.roleType || undefined,
      };
      
      const response = await api.post(`/unifiedconfigs/${targetFormId}/submit`, {
        formData: prefillData
      }, { headers });
      
      if (response.data.success) {
        alert(`✅ ${formName} submitted successfully!`);
      } else {
        throw new Error(response.data.message || 'Auto-submit failed');
      }
    } catch (error) {
      console.error('❌ Error auto-submitting linking form:', error);
      alert(`❌ Error submitting ${formName}: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoadingActions(prev => ({ ...prev, [actionKey]: false }));
    }
  }, []);

  // Get button style classes
  const getButtonClasses = (style, isLoading) => {
    const baseClasses = 'px-3 py-1 rounded text-sm font-medium transition-colors duration-200';
    
    if (isLoading) {
      return `${baseClasses} bg-gray-400 text-white cursor-not-allowed`;
    }

    const styles = {
      primary: 'bg-blue-500 hover:bg-blue-600 text-white',
      secondary: 'bg-gray-500 hover:bg-gray-600 text-white',
      success: 'bg-green-500 hover:bg-green-600 text-white',
      warning: 'bg-yellow-500 hover:bg-yellow-600 text-white',
      danger: 'bg-red-500 hover:bg-red-600 text-white'
    };

    return `${baseClasses} ${styles[style] || styles.primary}`;
  };

  // Format field value for display
  const formatFieldValue = (value) => {
    if (value == null) return '-';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'object') return JSON.stringify(value);
    return value.toString();
  };

  // Render action buttons for a record
  const renderActionButtons = (record, recordIndex) => {
    if (!formLinkingConfig?.enabled || !formLinkingConfig.recordActions) return null;

    return (
      <div className="flex flex-wrap gap-2">
        {formLinkingConfig.recordActions.map((action, actionIndex) => {
          if (!shouldShowApplyButton(record, action.conditions)) return null;
          
          const actionKey = `${JSON.stringify(record)}-${actionIndex}`;
          const stateKey = `${recordIndex}-${actionIndex}`;
          const isLoading = loadingActions[actionKey];
          const triggerState = autoTriggerState[stateKey];
          const shouldAutoTrigger = action.autoTrigger?.enabled || autoTriggerConfig.enabled;
          
          return (
            <div key={actionIndex} className="flex items-center gap-2">
              {/* Show button if not auto-triggering or if auto-trigger is disabled */}
              {!shouldAutoTrigger && (
                <button
                  onClick={() => handleApplyClick(record, actionIndex)}
                  disabled={isLoading}
                  className={getButtonClasses(action.buttonStyle, isLoading)}
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Loading...
                    </span>
                  ) : (
                    action.buttonText || 'Apply'
                  )}
                </button>
              )}
              
              {/* Auto-trigger countdown */}
              {triggerState?.countdown > 0 && autoTriggerConfig.showCountdown && (
                <div className="flex items-center text-sm text-blue-600 bg-blue-50 px-2 py-1 rounded">
                  <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Auto-triggering in {triggerState.countdown}s
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Render individual record
  const renderRecord = (record, index) => (
    <div key={index} className="mb-4">
      {!showOnlyButtons && (
        <>
          <div className="mb-2">
            <span className="font-medium text-gray-800">Record {index + 1}:</span>
          </div>
          
          <div className="mb-3 text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
            {(() => {
              const isLeaveData = record.hasOwnProperty('leaveTypeName') && record.hasOwnProperty('balance');
              const fieldsToDisplay = isLeaveData 
                ? [['leaveTypeName', record.leaveTypeName], ['balance', record.balance]]
                : Object.entries(record);
              
              return fieldsToDisplay.map(([key, value], fieldIndex) => (
                <span key={key}>
                  {fieldIndex > 0 && ' '}
                  <span className="font-medium">{key}:</span> {formatFieldValue(value)}
                  {fieldIndex < fieldsToDisplay.length - 1 && ' '}
                </span>
              ));
            })()}
          </div>
        </>
      )}
      
      {renderActionButtons(record, index)}
    </div>
  );

  // Early return for hidden display
  if (hideRecordDisplay) return null;

  // Render records
  if (!processedRecords.length) {
    return (
      <div className="text-gray-500 text-center py-4">
        No records to display
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {processedRecords.map(renderRecord)}
    </div>
  );
};

export default RecordDisplayWithActions;