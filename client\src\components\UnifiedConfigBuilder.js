import React, { useState, useEffect } from 'react';
import <PERSON>sonEditor from './JsonEditor';
import PayloadPreview from './PayloadPreview';
import FormLinkingConfig from './FormLinkingConfig';

const UnifiedConfigBuilder = ({ initialConfig = null, onSave, onCancel }) => {
  const [config, setConfig] = useState({
    type: 'api', // 'api' or 'form'
    name: '',
    description: '',
    prompt: '',
    keywords: [],
    triggerPhrases: [],
    priority: 0,
    category: 'general',
    tags: [],
    isActive: true,
    
    // API specific fields
    apiConfig: {
      endpoint: '',
      method: 'GET',
      headers: {},
      authType: 'none',
      authConfig: {},
      responseTemplate: '',
      timeout: 30000,
      retryCount: 3,
      dataInjection: {
        injectUserData: true,
        requiredFields: [],
        autoFields: {
          empId: false,
          token: false,
          roleType: false
        }
      },
      customPayload: {
        enabled: false,
        structure: {},
        mergeStrategy: 'replace',
        transformations: []
      }
    },
    
    // Form specific fields
    formConfig: {
      conversationalFlow: {
        enabled: false,
        completionMessage: 'Thank you! Your form has been submitted successfully.'
      },
      hybridFlow: {
        enabled: false,
        completionMessage: 'Please complete the remaining form fields below.'
      },
      fields: [],
      submitApiConfig: {
        endpoint: '',
        method: 'POST',
        headers: {},
        authType: 'bearer',
        authConfig: {},
        dataMapping: {},
        customPayload: {
          enabled: false,
          structure: {},
          mergeStrategy: 'replace',
          transformations: []
        },
        accessControl: {
          enabled: false,
          allowedRoles: []
        },
        successMessage: 'Form submitted successfully!',
        errorMessage: 'Failed to submit form. Please try again..',
        responseTemplate: ''
      },
      prefillData: {},
      formLinking: {
        enabled: false,
        recordActions: []
      }
    }
  });
  
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [previewModal, setPreviewModal] = useState({
    isOpen: false,
    isFormConfig: false
  });

  // Initialize form if editing existing config
  useEffect(() => {
    if (initialConfig) {
      setConfig({
        type: initialConfig.type || 'api',
        name: initialConfig.name || '',
        description: initialConfig.description || '',
        prompt: initialConfig.prompt || '',
        keywords: initialConfig.keywords || [],
        triggerPhrases: initialConfig.triggerPhrases || [],
        priority: initialConfig.priority || 0,
        category: initialConfig.category || 'general',
        tags: initialConfig.tags || [],
        isActive: initialConfig.isActive !== undefined ? initialConfig.isActive : true,
        
        apiConfig: {
          endpoint: initialConfig.apiConfig?.endpoint || '',
          method: initialConfig.apiConfig?.method || 'GET',
          headers: initialConfig.apiConfig?.headers || {},
          authType: initialConfig.apiConfig?.authType || 'none',
          authConfig: initialConfig.apiConfig?.authConfig || {},
          responseTemplate: initialConfig.apiConfig?.responseTemplate || '',
          timeout: initialConfig.apiConfig?.timeout || 30000,
          retryCount: initialConfig.apiConfig?.retryCount || 3,
          dataInjection: {
            injectUserData: initialConfig.apiConfig?.dataInjection?.injectUserData !== false,
            requiredFields: initialConfig.apiConfig?.dataInjection?.requiredFields || [],
            autoFields: {
              empId: initialConfig.apiConfig?.dataInjection?.autoFields?.empId || false,
              token: initialConfig.apiConfig?.dataInjection?.autoFields?.token || false,
              roleType: initialConfig.apiConfig?.dataInjection?.autoFields?.roleType || false
            }
          },
          customPayload: {
            enabled: initialConfig.apiConfig?.customPayload?.enabled || false,
            structure: initialConfig.apiConfig?.customPayload?.structure || {},
            mergeStrategy: initialConfig.apiConfig?.customPayload?.mergeStrategy || 'replace',
            transformations: initialConfig.apiConfig?.customPayload?.transformations || []
          }
        },
        
        formConfig: {
          conversationalFlow: {
            enabled: initialConfig.formConfig?.conversationalFlow?.enabled || false,
            completionMessage: initialConfig.formConfig?.conversationalFlow?.completionMessage || 'Thank you! Your form has been submitted successfully.'
          },
          hybridFlow: {
            enabled: initialConfig.formConfig?.hybridFlow?.enabled || false,
            completionMessage: initialConfig.formConfig?.hybridFlow?.completionMessage || 'Please complete the remaining form fields below.'
          },
          fields: initialConfig.formConfig?.fields || [],
          submitApiConfig: {
            endpoint: initialConfig.formConfig?.submitApiConfig?.endpoint || '',
            method: initialConfig.formConfig?.submitApiConfig?.method || 'POST',
            headers: initialConfig.formConfig?.submitApiConfig?.headers || {},
            authType: initialConfig.formConfig?.submitApiConfig?.authType || 'bearer',
            authConfig: initialConfig.formConfig?.submitApiConfig?.authConfig || {},
            dataMapping: initialConfig.formConfig?.submitApiConfig?.dataMapping || {},
            customPayload: {
              enabled: initialConfig.formConfig?.submitApiConfig?.customPayload?.enabled || false,
              structure: initialConfig.formConfig?.submitApiConfig?.customPayload?.structure || {},
              mergeStrategy: initialConfig.formConfig?.submitApiConfig?.customPayload?.mergeStrategy || 'replace',
              transformations: initialConfig.formConfig?.submitApiConfig?.customPayload?.transformations || []
            },
            accessControl: {
              enabled: initialConfig.formConfig?.submitApiConfig?.accessControl?.enabled || false,
              allowedRoles: initialConfig.formConfig?.submitApiConfig?.accessControl?.allowedRoles || []
            },
            successMessage: initialConfig.formConfig?.submitApiConfig?.successMessage || 'Form submitted successfully!',
            errorMessage: initialConfig.formConfig?.submitApiConfig?.errorMessage || 'Failed to submit form. Please try again.',
            responseTemplate: initialConfig.formConfig?.submitApiConfig?.responseTemplate || ''
          },
          prefillData: initialConfig.formConfig?.prefillData || {},
          formLinking: {
            enabled: initialConfig.formConfig?.formLinking?.enabled || false,
            recordActions: initialConfig.formConfig?.formLinking?.recordActions || []
          }
        }
      });
    }
  }, [initialConfig]);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setConfig({
      ...config,
      [name]: type === 'checkbox' ? checked : value,
    });
  };

  const handleApiConfigChange = (field, value) => {
    setConfig({
      ...config,
      apiConfig: {
        ...config.apiConfig,
        [field]: value
      }
    });
  };

  const handleFormConfigChange = (field, value) => {
    setConfig({
      ...config,
      formConfig: {
        ...config.formConfig,
        [field]: value
      }
    });
  };

  const handleFormLinkingChange = (formLinking) => {
    setConfig({
      ...config,
      formConfig: {
        ...config.formConfig,
        formLinking: formLinking
      }
    });
  };

  const handleDataInjectionChange = (field, value) => {
    setConfig({
      ...config,
      apiConfig: {
        ...config.apiConfig,
        dataInjection: {
          ...config.apiConfig.dataInjection,
          [field]: value
        }
      }
    });
  };

  const handleAutoFieldChange = (field, checked) => {
    setConfig({
      ...config,
      apiConfig: {
        ...config.apiConfig,
        dataInjection: {
          ...config.apiConfig.dataInjection,
          autoFields: {
            ...config.apiConfig.dataInjection.autoFields,
            [field]: checked
          }
        }
      }
    });
  };

  const handleFormAuthConfigChange = (field, value) => {
    setConfig({
      ...config,
      formConfig: {
        ...config.formConfig,
        submitApiConfig: {
          ...config.formConfig.submitApiConfig,
          authConfig: {
            ...config.formConfig.submitApiConfig.authConfig,
            [field]: value
          }
        }
      }
    });
  };

  const handleAccessControlChange = (field, value) => {
    setConfig({
      ...config,
      formConfig: {
        ...config.formConfig,
        submitApiConfig: {
          ...config.formConfig.submitApiConfig,
          accessControl: {
            ...config.formConfig.submitApiConfig.accessControl,
            [field]: value
          }
        }
      }
    });
  };

  // Custom payload management functions
  const handleCustomPayloadChange = (field, value, isFormConfig = false) => {
    if (isFormConfig) {
      setConfig({
        ...config,
        formConfig: {
          ...config.formConfig,
          submitApiConfig: {
            ...config.formConfig.submitApiConfig,
            customPayload: {
              ...config.formConfig.submitApiConfig.customPayload,
              [field]: value
            }
          }
        }
      });
    } else {
      setConfig({
        ...config,
        apiConfig: {
          ...config.apiConfig,
          customPayload: {
            ...config.apiConfig.customPayload,
            [field]: value
          }
        }
      });
    }
  };

  const handlePayloadStructureChange = (structure, isFormConfig = false) => {
    // JsonEditor already handles parsing, so we can pass the structure directly
    handleCustomPayloadChange('structure', structure, isFormConfig);
  };

  const addTransformation = (isFormConfig = false) => {
    const newTransformation = {
      field: '',
      operation: 'format',
      value: ''
    };
    
    const currentTransformations = isFormConfig 
      ? config.formConfig.submitApiConfig.customPayload.transformations 
      : config.apiConfig.customPayload.transformations;
    
    handleCustomPayloadChange('transformations', [...currentTransformations, newTransformation], isFormConfig);
  };

  const removeTransformation = (index, isFormConfig = false) => {
    const currentTransformations = isFormConfig 
      ? config.formConfig.submitApiConfig.customPayload.transformations 
      : config.apiConfig.customPayload.transformations;
    
    const updatedTransformations = currentTransformations.filter((_, i) => i !== index);
    handleCustomPayloadChange('transformations', updatedTransformations, isFormConfig);
  };

  const updateTransformation = (index, field, value, isFormConfig = false) => {
    const currentTransformations = isFormConfig 
      ? config.formConfig.submitApiConfig.customPayload.transformations 
      : config.apiConfig.customPayload.transformations;
    
    const updatedTransformations = [...currentTransformations];
    updatedTransformations[index] = { ...updatedTransformations[index], [field]: value };
    handleCustomPayloadChange('transformations', updatedTransformations, isFormConfig);
  };

  // Payload preview function
  const previewPayload = (isFormConfig = false) => {
    setPreviewModal({
      isOpen: true,
      isFormConfig
    });
  };

  const handleHeaderChange = (index, key, value, isFormConfig = false) => {
    const targetConfig = isFormConfig ? 'formConfig' : 'apiConfig';
    const targetPath = isFormConfig ? 'submitApiConfig' : '';
    
    const headers = isFormConfig 
      ? { ...config.formConfig.submitApiConfig.headers }
      : { ...config.apiConfig.headers };
    
    const headerKeys = Object.keys(headers);
    const oldKey = headerKeys[index];
    
    if (key === '' && value === '') {
      delete headers[oldKey];
    } else {
      if (oldKey !== undefined) {
        if (oldKey !== key && key !== '') {
          delete headers[oldKey];
          headers[key] = value;
        } else {
          headers[oldKey] = value;
        }
      }
    }
    
    if (isFormConfig) {
      setConfig({
        ...config,
        formConfig: {
          ...config.formConfig,
          submitApiConfig: {
            ...config.formConfig.submitApiConfig,
            headers
          }
        }
      });
    } else {
      handleApiConfigChange('headers', headers);
    }
  };

  const addHeader = (isFormConfig = false) => {
    const headers = isFormConfig 
      ? config.formConfig.submitApiConfig.headers
      : config.apiConfig.headers;
    
    const headerKeys = Object.keys(headers);
    const hasEmptyHeader = headerKeys.some(key => key === '');
    
    if (!hasEmptyHeader) {
      if (isFormConfig) {
        setConfig({
          ...config,
          formConfig: {
            ...config.formConfig,
            submitApiConfig: {
              ...config.formConfig.submitApiConfig,
              headers: { ...headers, '': '' }
            }
          }
        });
      } else {
        handleApiConfigChange('headers', { ...headers, '': '' });
      }
    }
  };

  const removeHeader = (index, isFormConfig = false) => {
    const headers = isFormConfig 
      ? { ...config.formConfig.submitApiConfig.headers }
      : { ...config.apiConfig.headers };
    
    const headerKeys = Object.keys(headers);
    
    if (index >= 0 && index < headerKeys.length) {
      delete headers[headerKeys[index]];
      
      if (isFormConfig) {
        setConfig({
          ...config,
          formConfig: {
            ...config.formConfig,
            submitApiConfig: {
              ...config.formConfig.submitApiConfig,
              headers
            }
          }
        });
      } else {
        handleApiConfigChange('headers', headers);
      }
    }
  };

  // Form field management
  const addField = () => {
    const newField = {
      name: '',
      label: '',
      type: 'text',
      required: false,
      readonly: false,
      placeholder: '',
      defaultValue: '',
      options: [],
      validation: {},
      conversationalPrompt: '',
      skipInConversation: false,
      displayMode: 'auto'
    };
    
    setConfig({
      ...config,
      formConfig: {
        ...config.formConfig,
        fields: [...config.formConfig.fields, newField]
      }
    });
  };

  const removeField = (index) => {
    const fields = config.formConfig.fields.filter((_, i) => i !== index);
    setConfig({
      ...config,
      formConfig: {
        ...config.formConfig,
        fields
      }
    });
  };

  const updateField = (index, field, value) => {
    const fields = [...config.formConfig.fields];
    fields[index] = { ...fields[index], [field]: value };
    setConfig({
      ...config,
      formConfig: {
        ...config.formConfig,
        fields
      }
    });
  };

  const validateConfig = () => {
    const newErrors = {};
    
    if (!config.name.trim()) {
      newErrors.name = 'Configuration name is required';
    }
    
    if (!config.type) {
      newErrors.type = 'Configuration type is required';
    }
    
    if (config.triggerPhrases.length === 0 && config.keywords.length === 0) {
      newErrors.matching = 'At least one trigger phrase or keyword is required for matching';
    }
    
    if (config.type === 'api') {
      if (!config.apiConfig.endpoint.trim()) {
        newErrors.endpoint = 'API endpoint is required';
      }
    } else if (config.type === 'form') {
      // For GET requests, fields are optional since they're used for data retrieval
      const isGetRequest = config.formConfig.submitApiConfig.method === 'GET';
      if (config.formConfig.fields.length === 0 && !isGetRequest) {
        newErrors.fields = 'At least one form field is required (except for GET requests)';
      }
    }
    
    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    console.log('UnifiedConfigBuilder: Form submitted');
    console.log('UnifiedConfigBuilder: Config data:', config);
    
    const validationErrors = validateConfig();
    console.log('UnifiedConfigBuilder: Validation errors:', validationErrors);
    setErrors(validationErrors);
    
    if (Object.keys(validationErrors).length === 0) {
      setIsSubmitting(true);
      console.log('UnifiedConfigBuilder: Calling onSave with config:', config);
      
      try {
        await onSave(config);
        console.log('UnifiedConfigBuilder: onSave completed successfully');
      } catch (error) {
        console.error('UnifiedConfigBuilder: Error saving configuration:', error);
        alert('Failed to save configuration: ' + (error.response?.data?.message || error.message));
      } finally {
        setIsSubmitting(false);
      }
    } else {
      console.log('UnifiedConfigBuilder: Form validation failed');
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 overflow-hidden">
      <div className="bg-gradient-to-r from-purple-600 to-purple-800 px-6 py-4 text-white">
        <h2 className="text-xl font-bold">
          {initialConfig ? `Edit ${config.type.toUpperCase()} Configuration` : 'Create New Configuration'}
        </h2>
        <p className="text-purple-100 mt-1">
          {initialConfig 
            ? `Update your ${config.type} configuration` 
            : 'Configure a new API endpoint or Form with dynamic matching'}
        </p>
      </div>
      
      <div className="p-6">
        <form onSubmit={handleSubmit}>
          {/* Basic Information */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Configuration Type <span className="text-red-500">*</span>
                </label>
                <select
                  name="type"
                  value={config.type}
                  onChange={handleChange}
                  className={`w-full p-2 border rounded-md ${
                    errors.type ? 'border-red-500' : 'border-gray-300'
                  } focus:outline-none focus:ring-2 focus:ring-purple-500`}
                >
                  <option value="api">API Configuration</option>
                  <option value="form">Form Configuration</option>
                </select>
                {errors.type && <p className="text-red-500 text-sm mt-1">{errors.type}</p>}
              </div>
              
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  Configuration Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={config.name}
                  onChange={handleChange}
                  className={`w-full p-2 border rounded-md ${
                    errors.name ? 'border-red-500' : 'border-gray-300'
                  } focus:outline-none focus:ring-2 focus:ring-purple-500`}
                />
                {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div>
                <label className="block text-gray-700 font-medium mb-2">Priority</label>
                <input
                  type="number"
                  name="priority"
                  value={config.priority}
                  onChange={handleChange}
                  min="0"
                  max="10"
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div>
                <label className="block text-gray-700 font-medium mb-2">Category</label>
                <input
                  type="text"
                  name="category"
                  value={config.category}
                  onChange={handleChange}
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              
              <div className="flex items-center">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    name="isActive"
                    checked={config.isActive}
                    onChange={handleChange}
                    className="mr-2"
                  />
                  <span className="text-gray-700">Active</span>
                </label>
              </div>
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">Description</label>
              <textarea
                name="description"
                value={config.description}
                onChange={handleChange}
                rows="2"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">Tags (comma-separated)</label>
              <input
                type="text"
                value={config.tags.join(', ')}
                onChange={(e) => {
                  const tags = e.target.value.split(',').map(t => t.trim()).filter(t => t);
                  setConfig({ ...config, tags });
                }}
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
            </div>
          </div>

          {/* Dynamic Matching Configuration */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-3">Dynamic Matching</h3>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">
                Trigger Phrases <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={config.triggerPhrases.join(', ')}
                onChange={(e) => {
                  const phrases = e.target.value.split(',').map(p => p.trim()).filter(p => p);
                  setConfig({ ...config, triggerPhrases: phrases });
                }}
                placeholder="check balance, get status, show info"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                Phrases that will trigger this configuration. Separate with commas.
              </p>
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">Keywords</label>
              <input
                type="text"
                value={config.keywords.join(', ')}
                onChange={(e) => {
                  const keywords = e.target.value.split(',').map(k => k.trim()).filter(k => k);
                  setConfig({ ...config, keywords });
                }}
                placeholder="balance, status, information, data"
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                Additional keywords for matching. Separate with commas.
              </p>
            </div>
            
            <div className="mb-4">
              <label className="block text-gray-700 font-medium mb-2">AI Prompt</label>
              <textarea
                name="prompt"
                value={config.prompt}
                onChange={handleChange}
                rows="3"
                placeholder="This configuration provides..."
                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <p className="text-gray-500 text-sm mt-1">
                Context for AI matching and response generation.
              </p>
            </div>
            
            {errors.matching && <p className="text-red-500 text-sm">{errors.matching}</p>}
          </div>

          {/* Type-specific Configuration */}
          {config.type === 'api' ? (
            // API Configuration
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">API Configuration</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    HTTP Method
                  </label>
                  <select
                    value={config.apiConfig.method}
                    onChange={(e) => handleApiConfigChange('method', e.target.value)}
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  >
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                  </select>
                </div>
                
                <div>
                  <label className="block text-gray-700 font-medium mb-2">
                    Timeout (ms)
                  </label>
                  <input
                    type="number"
                    value={config.apiConfig.timeout}
                    onChange={(e) => handleApiConfigChange('timeout', parseInt(e.target.value))}
                    min="1000"
                    max="300000"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>
              </div>
              
              <div className="mb-4">
                <label className="block text-gray-700 font-medium mb-2">
                  Endpoint URL <span className="text-red-500">*</span>
                </label>
                <input
                  type="url"
                  value={config.apiConfig.endpoint}
                  onChange={(e) => handleApiConfigChange('endpoint', e.target.value)}
                  placeholder="https://api.example.com/endpoint"
                  className={`w-full p-2 border rounded-md ${
                    errors.endpoint ? 'border-red-500' : 'border-gray-300'
                  } focus:outline-none focus:ring-2 focus:ring-purple-500`}
                />
                {errors.endpoint && <p className="text-red-500 text-sm mt-1">{errors.endpoint}</p>}
              </div>

              {/* Data Injection */}
              <div className="mb-4">
                <h4 className="text-md font-medium mb-2">Data Injection</h4>
                
                <div className="mb-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.apiConfig.dataInjection.injectUserData}
                      onChange={(e) => handleDataInjectionChange('injectUserData', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-gray-700">Automatically inject user data</span>
                  </label>
                </div>
                
                {config.apiConfig.dataInjection.injectUserData && (
                  <div className="ml-4 space-y-2">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.apiConfig.dataInjection.autoFields.empId}
                        onChange={(e) => handleAutoFieldChange('empId', e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-gray-700">Inject Employee ID</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.apiConfig.dataInjection.autoFields.token}
                        onChange={(e) => handleAutoFieldChange('token', e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-gray-700">Inject Auth Token</span>
                    </label>
                    
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.apiConfig.dataInjection.autoFields.roleType}
                        onChange={(e) => handleAutoFieldChange('roleType', e.target.checked)}
                        className="mr-2"
                      />
                      <span className="text-gray-700">Inject Role Type</span>
                    </label>
                  </div>
                )}
              </div>

              {/* Headers */}
              <div className="mb-4">
                <h4 className="text-md font-medium mb-2">Headers</h4>
                
                {Object.entries(config.apiConfig.headers).map(([key, value], index) => (
                  <div key={index} className="flex space-x-2 mb-2">
                    <input
                      type="text"
                      placeholder="Header Name"
                      value={key}
                      onChange={(e) => handleHeaderChange(index, e.target.value, value)}
                      className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    <input
                      type="text"
                      placeholder="Header Value"
                      value={value}
                      onChange={(e) => handleHeaderChange(index, key, e.target.value)}
                      className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    <button
                      type="button"
                      onClick={() => removeHeader(index)}
                      className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                    >
                      Remove
                    </button>
                  </div>
                ))}
                
                <button
                  type="button"
                  onClick={() => addHeader()}
                  className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
                >
                  Add Header
                </button>
              </div>

              {/* Custom Payload Configuration */}
              <div className="mb-6">
                <h4 className="text-md font-medium mb-3">🔧 Custom Payload Configuration</h4>
                
                <div className="mb-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.apiConfig.customPayload.enabled}
                      onChange={(e) => handleCustomPayloadChange('enabled', e.target.checked)}
                      className="mr-2"
                    />
                    <span className="text-gray-700 font-medium">Enable Custom Payload Structure</span>
                  </label>
                  <p className="text-gray-500 text-sm mt-1">
                    Override the default payload structure with custom JSON
                  </p>
                </div>

                {config.apiConfig.customPayload.enabled && (
                  <div className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                    {/* Merge Strategy */}
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Merge Strategy</label>
                      <select
                        value={config.apiConfig.customPayload.mergeStrategy}
                        onChange={(e) => handleCustomPayloadChange('mergeStrategy', e.target.value)}
                        className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <option value="replace">Replace - Use only custom structure</option>
                        <option value="merge">Merge - Combine with default structure</option>
                        <option value="append">Append - Add custom as 'custom' field</option>
                      </select>
                    </div>

                    {/* JSON Structure Editor */}
                    <div>
                      <label className="block text-gray-700 font-medium mb-2">Custom Payload Structure (JSON)</label>
                      <JsonEditor
                        value={config.apiConfig.customPayload.structure}
                        onChange={(structure) => handleCustomPayloadChange('structure', structure)}
                        placeholder={`{
  "requestId": "{{system.uuid}}",
  "timestamp": "{{system.timestamp}}",
  "user": {
    "id": "{{user.empId}}",
    "role": "{{user.roleType}}"
  },
  "data": {
    "param1": "{{data.param1}}",
    "param2": "{{data.param2}}"
  }
}`}
                        rows={10}
                      />
                      <p className="text-gray-500 text-sm mt-1">
                        Use placeholders: {"{user.field}"}, {"{data.field}"}, {"{system.timestamp}"}, {"{system.uuid}"}, etc.
                      </p>
                    </div>

                    {/* Transformations */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <label className="block text-gray-700 font-medium">Data Transformations</label>
                        <button
                          type="button"
                          onClick={() => addTransformation()}
                          className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                        >
                          Add Transformation
                        </button>
                      </div>
                      
                      {config.apiConfig.customPayload.transformations.map((transformation, index) => (
                        <div key={index} className="border border-gray-200 rounded-md p-3 mb-2 bg-white">
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-2">
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Field</label>
                              <input
                                type="text"
                                value={transformation.field}
                                onChange={(e) => updateTransformation(index, 'field', e.target.value)}
                                placeholder="fieldName"
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                              />
                            </div>
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Operation</label>
                              <select
                                value={transformation.operation}
                                onChange={(e) => updateTransformation(index, 'operation', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                              >
                                <option value="format">Format</option>
                                <option value="calculate">Calculate</option>
                                <option value="transform">Transform</option>
                                <option value="condition">Condition</option>
                              </select>
                            </div>
                            <div>
                              <label className="block text-xs text-gray-600 mb-1">Value</label>
                              <input
                                type="text"
                                value={transformation.value}
                                onChange={(e) => updateTransformation(index, 'value', e.target.value)}
                                placeholder="uppercase, lowercase, etc."
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                              />
                            </div>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeTransformation(index)}
                            className="px-2 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-xs"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                    </div>

                    {/* Preview Button */}
                    <div className="flex justify-end">
                      <button
                        type="button"
                        onClick={() => previewPayload()}
                        className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                      >
                        Preview Payload
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Response Template */}
              <div className="mb-4">
                <label className="block text-gray-700 font-medium mb-2">Response Template</label>
                <textarea
                  value={config.apiConfig.responseTemplate}
                  onChange={(e) => handleApiConfigChange('responseTemplate', e.target.value)}
                  rows="4"
                  placeholder="✅ **Success!**&#10;&#10;Status: {status}&#10;Data: {data.message}"
                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
                <p className="text-gray-500 text-sm mt-1">
                  Template for formatting API responses. Use {'{'}status{'}'}, {'{'}data.fieldName{'}'} for placeholders.
                </p>
              </div>
            </div>
          ) : (
            // Form Configuration
            <div className="mb-6">
              <h3 className="text-lg font-semibold mb-3">Form Configuration</h3>
              
              {/* Conversational Flow Configuration */}
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="text-lg font-semibold mb-3 text-yellow-800">🗣️ Conversational Flow</h4>
                
                <div className="mb-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.formConfig.conversationalFlow?.enabled || false}
                      onChange={(e) => {
                        console.log('🗣️ Conversational flow toggled:', e.target.checked);
                        setConfig({
                          ...config,
                          formConfig: {
                            ...config.formConfig,
                            conversationalFlow: {
                              ...config.formConfig.conversationalFlow,
                              enabled: e.target.checked
                            }
                          }
                        });
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-gray-700 font-medium">Enable Conversational Flow</span>
                  </label>
                  <p className="text-gray-500 text-sm mt-1">
                    🗣️ When enabled, the AI will ask users questions one by one instead of showing the full form
                  </p>
                </div>
                
                {config.formConfig.conversationalFlow?.enabled && (
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">
                      Completion Message
                    </label>
                    <textarea
                      value={config.formConfig.conversationalFlow?.completionMessage || ''}
                      onChange={(e) => setConfig({
                        ...config,
                        formConfig: {
                          ...config.formConfig,
                          conversationalFlow: {
                            ...config.formConfig.conversationalFlow,
                            completionMessage: e.target.value
                          }
                        }
                      })}
                      rows="2"
                      placeholder="Thank you! Your form has been submitted successfully."
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    <p className="text-gray-500 text-sm mt-1">
                      Message shown when the conversational form is completed
                    </p>
                  </div>
                )}
              </div>
              
              {/* Hybrid Flow Configuration */}
              <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 className="text-lg font-semibold mb-3 text-blue-800">🔄 Hybrid Flow</h4>
                
                <div className="mb-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={config.formConfig.hybridFlow?.enabled || false}
                      onChange={(e) => {
                        console.log('🔄 Hybrid flow toggled:', e.target.checked);
                        setConfig({
                          ...config,
                          formConfig: {
                            ...config.formConfig,
                            hybridFlow: {
                              ...config.formConfig.hybridFlow,
                              enabled: e.target.checked
                            },
                            // Auto-disable conversational flow if hybrid is enabled
                            conversationalFlow: {
                              ...config.formConfig.conversationalFlow,
                              enabled: e.target.checked ? false : config.formConfig.conversationalFlow?.enabled
                            }
                          }
                        });
                      }}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-gray-700 font-medium">Enable Hybrid Flow</span>
                  </label>
                  <p className="text-gray-500 text-sm mt-1">
                    🔄 Combines conversational questions with traditional form fields - some fields are asked by AI, others shown as form
                  </p>
                </div>
                
                {config.formConfig.hybridFlow?.enabled && (
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">
                      Transition Message
                    </label>
                    <textarea
                      value={config.formConfig.hybridFlow?.completionMessage || ''}
                      onChange={(e) => setConfig({
                        ...config,
                        formConfig: {
                          ...config.formConfig,
                          hybridFlow: {
                            ...config.formConfig.hybridFlow,
                            completionMessage: e.target.value
                          }
                        }
                      })}
                      rows="2"
                      placeholder="Please complete the remaining form fields below."
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    <p className="text-gray-500 text-sm mt-1">
                      Message shown when transitioning from conversational questions to form fields
                    </p>
                  </div>
                )}
              </div>
              
              {/* Form Fields */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-md font-medium">Form Fields</h4>
                  <button
                    type="button"
                    onClick={addField}
                    className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                  >
                    Add Field
                  </button>
                </div>
                
                {config.formConfig.fields.map((field, index) => (
                  <div key={index} className="border border-gray-200 rounded-md p-4 mb-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                      <div>
                        <label className="block text-gray-700 font-medium mb-1">Field Name</label>
                        <input
                          type="text"
                          value={field.name}
                          onChange={(e) => updateField(index, 'name', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-gray-700 font-medium mb-1">Label</label>
                        <input
                          type="text"
                          value={field.label}
                          onChange={(e) => updateField(index, 'label', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-gray-700 font-medium mb-1">Type</label>
                        <select
                          value={field.type}
                          onChange={(e) => updateField(index, 'type', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          <option value="text">Text</option>
                          <option value="email">Email</option>
                          <option value="password">Password</option>
                          <option value="number">Number</option>
                          <option value="date">Date</option>
                          <option value="time">Time</option>
                          <option value="textarea">Textarea</option>
                          <option value="select">Select</option>
                          <option value="radio">Radio</option>
                          <option value="checkbox">Checkbox</option>
                          <option value="file">File</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                      <div>
                        <label className="block text-gray-700 font-medium mb-1">Placeholder</label>
                        <input
                          type="text"
                          value={field.placeholder}
                          onChange={(e) => updateField(index, 'placeholder', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                      
                      <div>
                        <label className="block text-gray-700 font-medium mb-1">Default Value</label>
                        <input
                          type="text"
                          value={field.defaultValue}
                          onChange={(e) => updateField(index, 'defaultValue', e.target.value)}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-4 mb-3">
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={field.required}
                          onChange={(e) => updateField(index, 'required', e.target.checked)}
                          className="mr-2"
                        />
                        <span className="text-gray-700">Required</span>
                      </label>
                      
                      <label className="flex items-center">
                        <input
                          type="checkbox"
                          checked={field.readonly}
                          onChange={(e) => updateField(index, 'readonly', e.target.checked)}
                          className="mr-2"
                        />
                        <span className="text-gray-700">Read Only</span>
                      </label>
                    </div>
                    
                    {/* Conversational Flow Settings for this field */}
                    {config.formConfig.conversationalFlow?.enabled && (
                      <div className="mb-3 p-3 bg-blue-50 rounded-md border border-blue-200">
                        <h5 className="text-sm font-medium text-blue-800 mb-2">🗣️ Conversational Flow Settings</h5>
                        
                        <div className="mb-2">
                          <label className="block text-gray-700 font-medium mb-1">Custom AI Question</label>
                          <textarea
                            value={field.conversationalPrompt || ''}
                            onChange={(e) => updateField(index, 'conversationalPrompt', e.target.value)}
                            rows="2"
                            placeholder={`Leave empty to use default question: "Please provide ${field.label?.toLowerCase() || 'this field'}"`}
                            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                          <p className="text-gray-500 text-xs mt-1">
                            Custom question the AI will ask when collecting this field in conversational mode
                          </p>
                        </div>
                        
                        <div className="mb-2">
                          <label className="flex items-center">
                            <input
                              type="checkbox"
                              checked={field.skipInConversation || false}
                              onChange={(e) => updateField(index, 'skipInConversation', e.target.checked)}
                              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                            />
                            <span className="text-gray-700 font-medium text-sm">Skip in conversational flow</span>
                          </label>
                          <p className="text-gray-500 text-xs mt-1 ml-6">
                            If checked, this field will be auto-filled or skipped during conversational mode
                          </p>
                        </div>
                      </div>
                    )}
                    
                    {/* Hybrid Flow Settings for this field */}
                    {config.formConfig.hybridFlow?.enabled && (
                      <div className="mb-3 p-3 bg-green-50 rounded-md border border-green-200">
                        <h5 className="text-sm font-medium text-green-800 mb-2">🔄 Hybrid Flow Settings</h5>
                        
                        <div className="mb-2">
                          <label className="block text-gray-700 font-medium mb-1">Display Mode</label>
                          <select
                            value={field.displayMode || 'auto'}
                            onChange={(e) => updateField(index, 'displayMode', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                          >
                            <option value="auto">Auto (follows flow type)</option>
                            <option value="conversational">Always Conversational</option>
                            <option value="form">Always Form Field</option>
                          </select>
                          <p className="text-gray-500 text-xs mt-1">
                            • <strong>Auto:</strong> Conversational fields first, then form fields<br/>
                            • <strong>Always Conversational:</strong> AI will always ask this as a question<br/>
                            • <strong>Always Form Field:</strong> Always show as a form field
                          </p>
                        </div>
                        
                        {(field.displayMode === 'conversational' || field.displayMode === 'auto') && (
                          <div className="mb-2">
                            <label className="block text-gray-700 font-medium mb-1">Custom AI Question</label>
                            <textarea
                              value={field.conversationalPrompt || ''}
                              onChange={(e) => updateField(index, 'conversationalPrompt', e.target.value)}
                              rows="2"
                              placeholder={`Leave empty to use default question: "Please provide ${field.label?.toLowerCase() || 'this field'}"`}
                              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                            />
                            <p className="text-gray-500 text-xs mt-1">
                              Custom question the AI will ask when collecting this field in conversational mode
                            </p>
                          </div>
                        )}
                      </div>
                    )}
                    
                    {(field.type === 'select' || field.type === 'radio' || field.type === 'checkbox') && (
                      <div className="mb-3">
                        <label className="block text-gray-700 font-medium mb-1">Options (comma-separated)</label>
                        <input
                          type="text"
                          value={Array.isArray(field.options) ? field.options.join(', ') : field.options || ''}
                          onChange={(e) => {
                            const inputValue = e.target.value;
                            // Store the raw input value to preserve commas while typing
                            updateField(index, 'options', inputValue);
                          }}
                          onBlur={(e) => {
                            // Process and clean options when user finishes editing
                            const inputValue = e.target.value;
                            const options = inputValue.split(',').map(o => o.trim()).filter(o => o !== '');
                            updateField(index, 'options', options);
                          }}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                      </div>
                    )}
                    
                    <button
                      type="button"
                      onClick={() => removeField(index)}
                      className="px-3 py-1 bg-red-500 text-white rounded-md hover:bg-red-600"
                    >
                      Remove Field
                    </button>
                  </div>
                ))}
                
                {errors.fields && <p className="text-red-500 text-sm">{errors.fields}</p>}
              </div>

              {/* Form Submission API */}
              <div className="mb-4">
                <h4 className="text-md font-medium mb-3">Form Submission API</h4>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Submit Method</label>
                    <select
                      value={config.formConfig.submitApiConfig.method}
                      onChange={(e) => handleFormConfigChange('submitApiConfig', {
                        ...config.formConfig.submitApiConfig,
                        method: e.target.value
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="GET">GET</option>
                      <option value="POST">POST</option>
                      <option value="PUT">PUT</option>
                      <option value="PATCH">PATCH</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Auth Type</label>
                    <select
                      value={config.formConfig.submitApiConfig.authType}
                      onChange={(e) => handleFormConfigChange('submitApiConfig', {
                        ...config.formConfig.submitApiConfig,
                        authType: e.target.value
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    >
                      <option value="none">None</option>
                      <option value="bearer">Bearer Token</option>
                      <option value="apikey">API Key</option>
                      <option value="basic">Basic Auth</option>
                    </select>
                  </div>
                </div>

                {/* Authentication Configuration with Toggle */}
                {config.formConfig.submitApiConfig.authType !== 'none' && (
                  <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <h5 className="text-sm font-medium text-blue-800 mb-3">🔐 Authentication Configuration</h5>
                    <p className="text-sm text-blue-600 mb-4">
                      Configure authentication for both form submission and any API calls triggered by this form.
                    </p>
                    
                    {/* Toggle between auto-login and manual token */}
                    <div className="mb-4">
                      <div className="flex items-center space-x-4">
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="tokenMode"
                            value="auto"
                            checked={!config.formConfig.submitApiConfig.authConfig?.useCustomToken}
                            onChange={(e) => handleFormAuthConfigChange('useCustomToken', false)}
                            className="mr-2"
                          />
                          <span className="text-gray-700 font-medium">Use User Login Token</span>
                        </label>
                        <label className="flex items-center">
                          <input
                            type="radio"
                            name="tokenMode"
                            value="custom"
                            checked={config.formConfig.submitApiConfig.authConfig?.useCustomToken}
                            onChange={(e) => handleFormAuthConfigChange('useCustomToken', true)}
                            className="mr-2"
                          />
                          <span className="text-gray-700 font-medium">Use Custom Token</span>
                        </label>
                      </div>
                      <p className="text-gray-500 text-xs mt-2">
                        Choose whether to use the logged-in user's token or a custom token for this form
                      </p>
                    </div>

                    {/* Custom Token Fields - only show when custom token is selected */}
                    {config.formConfig.submitApiConfig.authConfig?.useCustomToken && (
                      <div className="p-3 bg-white rounded-md border border-gray-200">
                        {config.formConfig.submitApiConfig.authType === 'bearer' && (
                          <div className="mb-3">
                            <label className="block text-gray-700 font-medium mb-2">Custom Bearer Token</label>
                            <input
                              type="text"
                              value={config.formConfig.submitApiConfig.authConfig?.token || ''}
                              onChange={(e) => handleFormAuthConfigChange('token', e.target.value)}
                              placeholder="Enter your custom bearer token"
                              className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                            <p className="text-gray-500 text-xs mt-1">
                              This token will be used instead of the user's login token
                            </p>
                          </div>
                        )}

                        {config.formConfig.submitApiConfig.authType === 'apikey' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-gray-700 font-medium mb-2">API Key</label>
                              <input
                                type="text"
                                value={config.formConfig.submitApiConfig.authConfig?.apiKey || ''}
                                onChange={(e) => handleFormAuthConfigChange('apiKey', e.target.value)}
                                placeholder="Enter your API key"
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-gray-700 font-medium mb-2">API Key Header Name</label>
                              <input
                                type="text"
                                value={config.formConfig.submitApiConfig.authConfig?.headerName || 'X-API-Key'}
                                onChange={(e) => handleFormAuthConfigChange('headerName', e.target.value)}
                                placeholder="X-API-Key"
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                          </div>
                        )}

                        {config.formConfig.submitApiConfig.authType === 'basic' && (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <label className="block text-gray-700 font-medium mb-2">Username</label>
                              <input
                                type="text"
                                value={config.formConfig.submitApiConfig.authConfig?.username || ''}
                                onChange={(e) => handleFormAuthConfigChange('username', e.target.value)}
                                placeholder="Enter username"
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-gray-700 font-medium mb-2">Password</label>
                              <input
                                type="password"
                                value={config.formConfig.submitApiConfig.authConfig?.password || ''}
                                onChange={(e) => handleFormAuthConfigChange('password', e.target.value)}
                                placeholder="Enter password"
                                className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Access Control Configuration */}
                <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
                  <h4 className="text-lg font-semibold mb-3 text-orange-800">🔐 Access Authorization</h4>
                  <p className="text-gray-600 text-sm mb-4">
                    Configure authentication for both form submission and any API calls triggered by this form.
                  </p>
                  
                  <div className="mb-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.formConfig.submitApiConfig.accessControl?.enabled || false}
                        onChange={(e) => handleAccessControlChange('enabled', e.target.checked)}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                      />
                      <span className="ml-2 text-gray-700 font-medium">Enable Access Authorization</span>
                    </label>
                    <p className="text-gray-500 text-sm mt-1">
                      🔐 When enabled, only users with selected role types can access this form's submit endpoint
                    </p>
                  </div>

                  {config.formConfig.submitApiConfig.accessControl?.enabled && (
                    <div className="p-3 bg-white rounded-md border border-gray-200">
                      <label className="block text-gray-700 font-medium mb-3">
                        Select Authorized Role Types
                      </label>
                      
                      <div className="space-y-2">
                        {['Employee', 'Admin', 'Team leader',"Manager"].map((role) => (
                          <label key={role} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={config.formConfig.submitApiConfig.accessControl?.allowedRoles?.includes(role) || false}
                              onChange={(e) => {
                                const currentRoles = config.formConfig.submitApiConfig.accessControl?.allowedRoles || [];
                                const updatedRoles = e.target.checked
                                  ? [...currentRoles, role]
                                  : currentRoles.filter(r => r !== role);
                                handleAccessControlChange('allowedRoles', updatedRoles);
                              }}
                              className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                            />
                            <span className="ml-2 text-gray-700 capitalize">{role}</span>
                          </label>
                        ))}
                      </div>
                      
                      {config.formConfig.submitApiConfig.accessControl?.allowedRoles?.length === 0 && (
                        <p className="text-amber-600 text-sm mt-2 p-2 bg-amber-50 rounded border border-amber-200">
                          ⚠️ No roles selected. Please select at least one role type to enable access control.
                        </p>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="mb-4">
                  <label className="block text-gray-700 font-medium mb-2">Submit Endpoint</label>
                  <input
                    type="url"
                    value={config.formConfig.submitApiConfig.endpoint}
                    onChange={(e) => handleFormConfigChange('submitApiConfig', {
                      ...config.formConfig.submitApiConfig,
                      endpoint: e.target.value
                    })}
                    placeholder="https://api.example.com/submit"
                    className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>

                {/* Submit Headers */}
                <div className="mb-4">
                  <h5 className="text-sm font-medium mb-2">Submit Headers</h5>
                  
                  {Object.entries(config.formConfig.submitApiConfig.headers).map(([key, value], index) => (
                    <div key={index} className="flex space-x-2 mb-2">
                      <input
                        type="text"
                        placeholder="Header Name"
                        value={key}
                        onChange={(e) => handleHeaderChange(index, e.target.value, value, true)}
                        className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                      <input
                        type="text"
                        placeholder="Header Value"
                        value={value}
                        onChange={(e) => handleHeaderChange(index, key, e.target.value, true)}
                        className="flex-1 p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                      <button
                        type="button"
                        onClick={() => removeHeader(index, true)}
                        className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  
                  <button
                    type="button"
                    onClick={() => addHeader(true)}
                    className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600"
                  >
                    Add Header
                  </button>
                </div>

                {/* Custom Payload Configuration for Forms */}
                <div className="mb-6">
                  <h5 className="text-md font-medium mb-3">🔧 Custom Payload Configuration</h5>
                  
                  <div className="mb-4">
                    <label className="flex items-center">
                      <input
                        type="checkbox"
                        checked={config.formConfig.submitApiConfig.customPayload.enabled}
                        onChange={(e) => handleCustomPayloadChange('enabled', e.target.checked, true)}
                        className="mr-2"
                      />
                      <span className="text-gray-700 font-medium">Enable Custom Payload Structure</span>
                    </label>
                    <p className="text-gray-500 text-sm mt-1">
                      Override the default form submission payload with custom JSON
                    </p>
                  </div>

                  {config.formConfig.submitApiConfig.customPayload.enabled && (
                    <div className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                      {/* Merge Strategy */}
                      <div>
                        <label className="block text-gray-700 font-medium mb-2">Merge Strategy</label>
                        <select
                          value={config.formConfig.submitApiConfig.customPayload.mergeStrategy}
                          onChange={(e) => handleCustomPayloadChange('mergeStrategy', e.target.value, true)}
                          className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                        >
                          <option value="replace">Replace - Use only custom structure</option>
                          <option value="merge">Merge - Combine with form data</option>
                          <option value="append">Append - Add custom as 'custom' field</option>
                        </select>
                      </div>

                      {/* JSON Structure Editor */}
                      <div>
                        <label className="block text-gray-700 font-medium mb-2">Custom Payload Structure (JSON)</label>
                        <JsonEditor
                          value={config.formConfig.submitApiConfig.customPayload.structure}
                          onChange={(structure) => handleCustomPayloadChange('structure', structure, true)}
                          placeholder={`{
  "submissionId": "{{system.uuid}}",
  "submittedAt": "{{system.datetime}}",
  "submittedBy": {
    "empId": "{{user.empId}}",
    "role": "{{user.roleType}}"
  },
  "formData": {
    "field1": "{{data.field1}}",
    "field2": "{{data.field2}}"
  },
  "metadata": {
    "source": "web-form",
    "version": "1.0"
  }
}`}
                          rows={10}
                        />
                        <p className="text-gray-500 text-sm mt-1">
                          Use placeholders: {"{user.field}"}, {"{data.field}"}, {"{system.datetime}"}, {"{system.uuid}"}, etc.
                        </p>
                      </div>

                      {/* Transformations */}
                      <div>
                        <div className="flex items-center justify-between mb-2">
                          <label className="block text-gray-700 font-medium">Data Transformations</label>
                          <button
                            type="button"
                            onClick={() => addTransformation(true)}
                            className="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-sm"
                          >
                            Add Transformation
                          </button>
                        </div>
                        
                        {config.formConfig.submitApiConfig.customPayload.transformations.map((transformation, index) => (
                          <div key={index} className="border border-gray-200 rounded-md p-3 mb-2 bg-white">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 mb-2">
                              <div>
                                <label className="block text-xs text-gray-600 mb-1">Field</label>
                                <input
                                  type="text"
                                  value={transformation.field}
                                  onChange={(e) => updateTransformation(index, 'field', e.target.value, true)}
                                  placeholder="fieldName"
                                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                />
                              </div>
                              <div>
                                <label className="block text-xs text-gray-600 mb-1">Operation</label>
                                <select
                                  value={transformation.operation}
                                  onChange={(e) => updateTransformation(index, 'operation', e.target.value, true)}
                                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                >
                                  <option value="format">Format</option>
                                  <option value="calculate">Calculate</option>
                                  <option value="transform">Transform</option>
                                  <option value="condition">Condition</option>
                                </select>
                              </div>
                              <div>
                                <label className="block text-xs text-gray-600 mb-1">Value</label>
                                <input
                                  type="text"
                                  value={transformation.value}
                                  onChange={(e) => updateTransformation(index, 'value', e.target.value, true)}
                                  placeholder="uppercase, lowercase, etc."
                                  className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                                />
                              </div>
                            </div>
                            <button
                              type="button"
                              onClick={() => removeTransformation(index, true)}
                              className="px-2 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 text-xs"
                            >
                              Remove
                            </button>
                          </div>
                        ))}
                      </div>

                      {/* Preview Button */}
                      <div className="flex justify-end">
                        <button
                          type="button"
                          onClick={() => previewPayload(true)}
                          className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600"
                        >
                          Preview Payload
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Success Message</label>
                    <input
                      type="text"
                      value={config.formConfig.submitApiConfig.successMessage}
                      onChange={(e) => handleFormConfigChange('submitApiConfig', {
                        ...config.formConfig.submitApiConfig,
                        successMessage: e.target.value
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Error Message</label>
                    <input
                      type="text"
                      value={config.formConfig.submitApiConfig.errorMessage}
                      onChange={(e) => handleFormConfigChange('submitApiConfig', {
                        ...config.formConfig.submitApiConfig,
                        errorMessage: e.target.value
                      })}
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                  </div>
                </div>

                {/* Response Template for GET Requests */}
                {config.formConfig.submitApiConfig.method === 'GET' && (
                  <div className="mb-4">
                    <label className="block text-gray-700 font-medium mb-2">Response Template</label>
                    <textarea
                      value={config.formConfig.submitApiConfig.responseTemplate}
                      onChange={(e) => handleFormConfigChange('submitApiConfig', {
                        ...config.formConfig.submitApiConfig,
                        responseTemplate: e.target.value
                      })}
                      rows="4"
                      placeholder="✅ **Data Retrieved Successfully**&#10;&#10;**Employee ID:** {data.empId}&#10;**Name:** {data.name}&#10;**Status:** {data.status}&#10;&#10;_Response Status: {status}_"
                      className="w-full p-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    />
                    <p className="text-gray-500 text-sm mt-1">
                      Template for formatting GET request responses displayed in chat. Use {'{'}status{'}'}, {'{'}data.fieldName{'}'} for placeholders.
                    </p>
                  </div>
                )}
              </div>
              
              {/* Form Linking Configuration */}
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <FormLinkingConfig
                  formLinking={config.formConfig.formLinking}
                  onFormLinkingChange={handleFormLinkingChange}
                  isFormType={config.type === 'form'}
                />
              </div>
            </div>
          )}

          {/* Submit Buttons */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className={`px-4 py-2 bg-purple-500 text-white rounded-md ${
                isSubmitting ? 'opacity-50 cursor-not-allowed' : 'hover:bg-purple-600'
              } focus:outline-none focus:ring-2 focus:ring-purple-500`}
            >
              {isSubmitting ? 'Saving...' : 'Save Configuration'}
            </button>
          </div>
        </form>
      </div>
      
      {/* Payload Preview Modal */}
      <PayloadPreview
        isOpen={previewModal.isOpen}
        onClose={() => setPreviewModal({ isOpen: false, isFormConfig: false })}
        config={config}
        isFormConfig={previewModal.isFormConfig}
        customPayload={previewModal.isFormConfig 
          ? config.formConfig.submitApiConfig.customPayload 
          : config.apiConfig.customPayload}
      />
    </div>
  );
};

export default UnifiedConfigBuilder;