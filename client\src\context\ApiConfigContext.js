import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../utils/api';

const ApiConfigContext = createContext();

export const ApiConfigProvider = ({ children }) => {
  const [apiConfigs, setApiConfigs] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentApiConfig, setCurrentApiConfig] = useState(null);

  // Load API configurations on initial render
  useEffect(() => {
    fetchApiConfigs();
  }, []);

  const fetchApiConfigs = async () => {
    try {
      setLoading(true);
      console.log('🔄 Fetching API configurations from:', api.defaults.baseURL + '/api-configs');
      const response = await api.get('/api-configs');
      console.log('⚙️ API configs response:', response.data);
      setApiConfigs(response.data);
      return response.data;
    } catch (err) {
      setError('Failed to fetch API configurations');
      console.error('Error fetching API configurations:', err);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const getApiConfigById = async (id) => {
    try {
      setLoading(true);
      const response = await api.get(`/api-configs/${id}`);
      setCurrentApiConfig(response.data);
      return response.data;
    } catch (err) {
      setError('Failed to fetch API configuration');
      console.error('Error fetching API configuration:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const getApiConfigByName = async (name) => {
    try {
      setLoading(true);
      console.log(`🔍 Searching for API config by name: "${name}"`);
      const response = await api.get(`/api-configs/name/${name}`);
      console.log(`✅ Found API config by name "${name}":`, response.data);
      setCurrentApiConfig(response.data);
      return response.data;
    } catch (err) {
      setError('Failed to fetch API configuration');
      console.error(`❌ Error fetching API config "${name}":`, err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const createApiConfig = async (apiConfigData) => {
    try {
      setLoading(true);
      const response = await api.post('/api-configs', apiConfigData);
      setApiConfigs([...apiConfigs, response.data]);
      setCurrentApiConfig(response.data);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to create API configuration');
      console.error('Error creating API configuration:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateApiConfig = async (id, apiConfigData) => {
    try {
      setLoading(true);
      const response = await api.put(`/api-configs/${id}`, apiConfigData);
      setApiConfigs(apiConfigs.map(config => (config._id === id ? response.data : config)));
      setCurrentApiConfig(response.data);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update API configuration');
      console.error('Error updating API configuration:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteApiConfig = async (id) => {
    try {
      setLoading(true);
      await api.delete(`/api-configs/${id}`);
      setApiConfigs(apiConfigs.filter(config => config._id !== id));
      if (currentApiConfig && currentApiConfig._id === id) {
        setCurrentApiConfig(null);
      }
      return true;
    } catch (err) {
      setError('Failed to delete API configuration');
      console.error('Error deleting API configuration:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const clearCurrentApiConfig = () => {
    setCurrentApiConfig(null);
  };

  const clearError = () => {
    setError(null);
  };

  return (
    <ApiConfigContext.Provider
      value={{
        apiConfigs,
        loading,
        error,
        currentApiConfig,
        fetchApiConfigs,
        getApiConfigById,
        getApiConfigByName,
        createApiConfig,
        updateApiConfig,
        deleteApiConfig,
        clearCurrentApiConfig,
        clearError,
      }}
    >
      {children}
    </ApiConfigContext.Provider>
  );
};

export const useApiConfig = () => useContext(ApiConfigContext);

export default ApiConfigContext;