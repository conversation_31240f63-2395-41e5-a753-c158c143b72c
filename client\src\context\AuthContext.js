import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../utils/api';

const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch supervisor and reviewer information using dynamic API configuration
  const fetchSupervisorAndReviewerInfo = async (token) => {
    try {
      console.log('🔄 Fetching supervisor and reviewer information using dynamic config...');
      
      // Call backend endpoint that uses dynamic API configuration
      const response = await api.get('/auth/supervisor-reviewer-info', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('✅ Supervisor and reviewer info fetched via dynamic config:', response.data);

      if (response.data.success && response.data.data) {
        const result = response.data.data;
        
        // Check if the response has the expected structure
        if (result.status === true && result.data) {
          // Store supervisor information
          if (result.data.primary) {
            localStorage.setItem('supervisorName', result.data.primary.primaryManagerName);
            localStorage.setItem('supervisorId', result.data.primary.primaryManagerId);
            console.log('💾 Stored supervisor info:', {
              name: result.data.primary.primaryManagerName,
              id: result.data.primary.primaryManagerId
            });
          }

          // Store reviewer information
          if (result.data.reviewer) {
            localStorage.setItem('reviewerName', result.data.reviewer.reviewerManagerName);
            localStorage.setItem('reviewerId', result.data.reviewer.reviewerManagerId);
            console.log('💾 Stored reviewer info:', {
              name: result.data.reviewer.reviewerManagerName,
              id: result.data.reviewer.reviewerManagerId
            });
          }

          console.log('✅ Supervisor and reviewer information stored successfully via dynamic config');
        } else {
          console.log('⚠️ Unexpected response format from dynamic config:', result);
        }
      } else {
        console.log('⚠️ Backend response indicates failure:', response.data);
      }
    } catch (error) {
      console.error('❌ Error fetching supervisor and reviewer information via dynamic config:', error);
      
      // Log specific error details
      if (error.response) {
        console.error('Response status:', error.response.status);
        console.error('Response data:', error.response.data);
      } else if (error.request) {
        console.error('Network error - no response received');
      } else {
        console.error('Request setup error:', error.message);
      }
    }
  };

  // Check if user is already logged in on initial load
  useEffect(() => {
    const checkLoginStatus = async () => {
      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          setUser(userData);
          
          // Check if we need to fetch supervisor/reviewer info
          const supervisorName = localStorage.getItem('supervisorName');
          const reviewerName = localStorage.getItem('reviewerName');
          
          if (!supervisorName || !reviewerName) {
            console.log('🔄 Supervisor/reviewer info missing, fetching...');
            if (userData.token) {
              await fetchSupervisorAndReviewerInfo(userData.token);
            }
          } else {
            console.log('✅ Supervisor/reviewer info already available');
          }
        }
      } catch (error) {
        console.error('Error checking login status:', error);
        // Clear potentially corrupted data
        localStorage.removeItem('user');
      } finally {
        setLoading(false);
      }
    };

    checkLoginStatus();
  }, []);

  // Login function
  const login = async (userData) => {
    setUser(userData);
    localStorage.setItem('user', JSON.stringify(userData));
    
    // Fetch supervisor and reviewer information after login
    if (userData.token) {
      await fetchSupervisorAndReviewerInfo(userData.token);
    }
  };

  // Logout function
  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    // Clear supervisor and reviewer info on logout
    localStorage.removeItem('supervisorName');
    localStorage.removeItem('supervisorId');
    localStorage.removeItem('reviewerName');
    localStorage.removeItem('reviewerId');
    localStorage.removeItem('roleType');
  };

  // Check if user is logged in
  const isLoggedIn = () => {
    return !!user;
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        loading,
        login,
        logout,
        isLoggedIn
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);

export default AuthContext;