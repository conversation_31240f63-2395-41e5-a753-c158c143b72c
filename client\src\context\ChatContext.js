import React, { createContext, useContext, useState, useEffect } from 'react';
import axios from 'axios';
import api from '../utils/api';

const ChatContext = createContext();

// Global set to track processed messages and prevent duplicates across renders
const globalProcessedMessages = new Set();

// Global flag to prevent multiple conversation loads
let globalConversationLoaded = false;

// Global cache for leave balance data to prevent duplicate API calls
const leaveBalanceCache = new Map();
const LEAVE_BALANCE_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

export const ChatProvider = ({ children }) => {
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [conversationId, setConversationId] = useState(null);
  const [activeForm, setActiveForm] = useState(null);
  const [conversationalFlow, setConversationalFlow] = useState(null);
  const [hybridFlow, setHybridFlow] = useState(null);
  const [conversationLoaded, setConversationLoaded] = useState(false);

  // Load conversation from localStorage on initial render
  useEffect(() => {
    const savedConversationId = localStorage.getItem('conversationId');
    if (savedConversationId && !globalConversationLoaded && !loading) {
      console.log('Loading conversation for the first time:', savedConversationId);
      globalConversationLoaded = true;
      setConversationId(savedConversationId);
      setConversationLoaded(true);
      loadConversation(savedConversationId);
    } else if (globalConversationLoaded) {
      console.log('Conversation already loaded globally, skipping...');
    }
  }, []); // Only run once on mount

  // Save conversationId to localStorage when it changes
  useEffect(() => {
    if (conversationId) {
      localStorage.setItem('conversationId', conversationId);
    }
  }, [conversationId]);

  const loadConversation = async (id) => {
    // Prevent multiple simultaneous loads
    if (loading) {
      console.log('Load already in progress, skipping...');
      return;
    }
    
    try {
      setLoading(true);
      const response = await api.get(`/chat/conversations/${id}`);
      setMessages(response.data.messages || []);
      setConversationId(id);
    } catch (err) {
      setError('Failed to load conversation');
      console.error('Error loading conversation:', err);
    } finally {
      setLoading(false);
    }
  };

  // Function to add a message from the assistant without making an API call
  const addAssistantMessage = (message) => {
    const assistantMessage = {
      role: 'assistant',
      content: message
    };
    setMessages((prevMessages) => [...prevMessages, assistantMessage]);
    return assistantMessage;
  };

  const sendMessage = async (message, role = 'user') => {
    // Prevent multiple simultaneous sends
    if (loading && role === 'user') {
      console.log('Send already in progress, skipping...');
      return;
    }
    
    try {
      setError(null);
      
      // If this is an assistant message, just add it to the UI
      if (role === 'assistant') {
        return addAssistantMessage(message);
      }
      
      // Add user message to UI immediately
      const userMessage = { 
        role: 'user', 
        content: message,
        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now()
      };
      setMessages((prevMessages) => [...prevMessages, userMessage]);
      
      // Set loading to true after user message is displayed
      setLoading(true);
      
      // For other queries, send message to regular chat API with user context
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
     
      const response = await api.post('/chat', {
        message,
        conversationId,
        empId: userData.empId,                    
        roleType: userData.roleType,              // 🎯 AUTOMATIC Type from localStorage  
        supervisorName: userData.supervisorName,  // 🎯 AUTOMATIC Supervisor Name from localStorage
        supervisorId: userData.supervisorId       // 🎯 AUTOMATIC Supervisor ID from localStorage
      }, {
        headers: {
          'x-emp-id': userData.empId,     // 🎯 AUTOMATIC Employee ID in Header
          'x-role-type': userData.roleType, // 🎯 AUTOMATIC Type in Header
          'Authorization': userData.token ? `Bearer ${userData.token}` : undefined
        }
      });
      
      
      // Auto-populate appliedTo field with supervisorName from localStorage
      let modifiedFormConfig = response.data.formConfig || response.data.formData?.formConfig;
      
      // Get supervisor info from separate localStorage keys (not from user object)
      const supervisorName = localStorage.getItem('supervisorName');
      const supervisorId = localStorage.getItem('supervisorId');
      
      
      if (modifiedFormConfig && modifiedFormConfig.fields && supervisorName) {
        console.log('🎯 Available fields:', modifiedFormConfig.fields.map(f => f.name));
        
        modifiedFormConfig = {
          ...modifiedFormConfig,
          fields: modifiedFormConfig.fields.map((field, index) => {
            console.log(`🔍 Checking field ${index}:`, field.name);
            if (field.name === 'appliedTo') {
              console.log('✅ Found appliedTo field, auto-filling with:', supervisorName);
              return {
                ...field,
                defaultValue: supervisorName,  // 🎯 AUTO-FILL from localStorage
                value: supervisorName          // 🎯 AUTO-FILL from localStorage
              };
            }
            return field;
          })
        };
        console.log('✅ Updated formConfig with auto-filled appliedTo field');
      } else {
        console.log('❌ Cannot auto-fill appliedTo field:', {
          noFormConfig: !modifiedFormConfig,
          noFields: !modifiedFormConfig?.fields,
          noSupervisorName: !supervisorName,
          availableUserDataKeys: Object.keys(userData)
        });
      }
      
      // Check if this is a leave form and we have stored leave balance
      const isLeaveForm = response.data.formData && (
        response.data.formData.name?.toLowerCase().includes('leave') ||
        response.data.formConfig?.name?.toLowerCase().includes('leave')
      );
      
      let finalContent = response.data.message;
      let finalQueryIntent = response.data.queryIntent;
      let storedLeaveBalance = null;
      
      // If it's a leave form and server provided balance, modify the content and intent
      if (isLeaveForm && response.data.apiResponse?.leaveBalance) {
        console.log('🍃 Using leave balance from server response');
        storedLeaveBalance = response.data.apiResponse.leaveBalance;
        finalContent = `Here's your leave application form and current balance:\n\n${storedLeaveBalance}`;
        finalQueryIntent = 'leave_apply_with_balance';
      }

      // Create assistant response but don't add yet - wait for loading to finish
      const assistantMessage = {
        role: 'assistant',
        content: finalContent,
        formData: modifiedFormConfig ? 
          { ...response.data.formData, formConfig: modifiedFormConfig } : 
          response.data.formData, // 🎯 Use modified formConfig with auto-filled appliedTo
        formConfig: modifiedFormConfig || response.data.formConfig,
        queryIntent: finalQueryIntent,
        apiResponse: response.data.apiResponse,
        matchResults: response.data.matchResults,
        conversationalFlow: response.data.conversationalFlow,
        // Include conversational form data for option display
        fieldType: response.data.conversationalFlow?.fieldType || response.data.hybridFlow?.fieldType || response.data.fieldType,
        options: response.data.conversationalFlow?.options || response.data.hybridFlow?.options || response.data.options,
        isFormFlow: response.data.conversationalFlow?.isActive || response.data.isFormFlow,
        fieldName: response.data.conversationalFlow?.fieldName || response.data.fieldName,
        currentStep: response.data.conversationalFlow?.currentStep || response.data.currentStep,
        totalSteps: response.data.conversationalFlow?.totalSteps || response.data.totalSteps,
        isDynamicResponse: true,
        // Add leave balance from server response if available
        leaveBalance: response.data.apiResponse?.leaveBalance || storedLeaveBalance,
        fieldName: response.data.conversationalFlow?.fieldName || response.data.hybridFlow?.fieldName || response.data.fieldName,
        currentStep: response.data.conversationalFlow?.currentStep || response.data.hybridFlow?.currentStep || response.data.currentStep,
        totalSteps: response.data.conversationalFlow?.totalSteps || response.data.hybridFlow?.totalConversationalSteps || response.data.totalSteps,
        // Include hybrid form data
        hybridFlow: response.data.hybridFlow,
        isHybridFlow: response.data.isHybridFlow,
        isConversationalPhase: response.data.isConversationalPhase,
        transitionToForm: response.data.transitionToForm,
        formFields: response.data.formFields,
        collectedData: response.data.collectedData,
        conversationalData: response.data.conversationalData,
        isDynamicResponse: true,
        // Add unique message ID to prevent duplicates
        messageId: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        // Add timestamp for deduplication
        timestamp: response.data.apiResponse?.timestamp || Date.now(),
        // Add form cancellation flag
        formCancelled: response.data.formCancelled
      };
      
      // Update conversation ID if new
      if (response.data.conversationId && !conversationId) {
        setConversationId(response.data.conversationId);
      }
      
      // Handle form cancellation due to validation error
      if (response.data.formCancelled) {
        console.log('🚫 Form cancelled due to validation error, clearing form states');
        setActiveForm(null);
        setConversationalFlow(null);
        setHybridFlow(null);
      }
      
      // Handle conversational flow
      if (response.data.conversationalFlow && response.data.queryIntent === 'form_conversation') {
        setConversationalFlow(response.data.conversationalFlow);
        setActiveForm(null); // Don't show traditional form in conversational mode
      } else if (response.data.queryIntent === 'form_completed') {
        
        setConversationalFlow(null);
        setActiveForm(null);
        
        // If the form needs submission, submit it automatically (will show in browser Network tab)
        if (response.data.needsSubmission && response.data.formData && response.data.formId) {
          
          const enhancedFormData = autoFillConversationalFormData(response.data.formData);
          
          // Submit the form directly without showing the "Processing..." message
          await submitConversationalForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);
          
          // Don't add the "Processing..." message to chat - submitConversationalForm will add the result message
          return response.data;
        }
      } else if (response.data.queryIntent === 'form_data_retrieved') {
        
        setConversationalFlow(null);
        setActiveForm(null);
        
        // The formatted response is already in the message, so just display it
      } else if (response.data.queryIntent === 'form_error') {
        console.log('❌ Form GET request failed');
        console.log('📋 Error details:', response.data.apiResponse);
        
        setConversationalFlow(null);
        setActiveForm(null);
        
        // Error message is already in the response message
      } else if (response.data.queryIntent === 'form_cancelled') {
        console.log('❌ Conversational form cancelled');
        setConversationalFlow(null);
        setActiveForm(null);
        setHybridFlow(null);
      } else if (response.data.queryIntent === 'hybrid_form_conversation') {
        console.log('🔄 Hybrid form conversational phase active:', response.data.hybridFlow);
        setHybridFlow(response.data.hybridFlow);
        setConversationalFlow(null);
        setActiveForm(null);
      } else if (response.data.queryIntent === 'hybrid_form_transition') {
        
        // Set hybrid flow state and show form
        setHybridFlow({
          ...response.data.hybridFlow,
          isConversationalPhase: false,
          transitionToForm: true,
          formFields: response.data.formFields,
          collectedData: response.data.collectedData,
          formConfig: response.data.formConfig,
          formId: response.data.formId
        });
        setConversationalFlow(null);
        setActiveForm(null);
      } else if (response.data.queryIntent === 'hybrid_form_completed') {
        
        setHybridFlow(null);
        setConversationalFlow(null);
        setActiveForm(null);
        
        // If the form needs submission, submit it automatically
        if (response.data.needsSubmission && response.data.formData && response.data.formId) {          
          const enhancedFormData = autoFillConversationalFormData(response.data.formData);
          console.log('🎯 Enhanced hybrid form data with auto-fill:', enhancedFormData);
          
          // Submit the form directly
          await submitHybridForm(response.data.formId, enhancedFormData, response.data.conversationId, messages, response.data.formConfig);
          
          return response.data;
        }
      } else if (response.data.queryIntent === 'hybrid_form_data_retrieved') {
        console.log('🔍 Hybrid form GET request completed - data retrieved');
        console.log('📋 API response:', response.data.apiResponse);
        
        setHybridFlow(null);
        setConversationalFlow(null);
        setActiveForm(null);
        
        // The formatted response is already in the message, so just display it
      } else if (response.data.formData && response.data.queryIntent === 'form') {
        // Traditional form display
        setActiveForm(response.data.formData);
        setConversationalFlow(null);
        console.log(`Form detected and displayed: ${response.data.formData.name}`);
        
        // Log the form data for debugging
        console.log('Form data:', response.data.formData);
      } else {
        // Ensure no form is shown for informational queries
        setActiveForm(null);
        setConversationalFlow(null);
        console.log(`No form displayed. Query intent: ${response.data.queryIntent}`);
        
        // For informational queries, we're using the LLaMA 3.2 model response
        if (response.data.queryIntent === 'information') {
          console.log('Using LLaMA 3.2 model for informational query');
        }
      }
      
      // Ensure typing indicator is shown for at least 1 second before displaying message
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create a unique key for global tracking
      const globalMessageKey = `${assistantMessage.timestamp}-${assistantMessage.queryIntent}-${assistantMessage.messageId}`;
      
      // Check global tracker first (prevents race conditions across renders)
      if (globalProcessedMessages.has(globalMessageKey)) {
        console.log('🚫 Message already processed globally, skipping:', {
          globalMessageKey: globalMessageKey.substring(0, 50) + '...',
          timestamp: assistantMessage.timestamp,
          queryIntent: assistantMessage.queryIntent
        });
        setLoading(false);
        return response.data;
      }
      
      // Add to global tracker IMMEDIATELY to prevent race conditions
      globalProcessedMessages.add(globalMessageKey);
      
      // Clean up old global messages (keep only last 100 to prevent memory leaks)
      if (globalProcessedMessages.size > 100) {
        const messageArray = Array.from(globalProcessedMessages);
        globalProcessedMessages.clear();
        messageArray.slice(-50).forEach(key => globalProcessedMessages.add(key));
      }

      // Now add the assistant message after loading period with deduplication
      setMessages((prevMessages) => {
        // Check for duplicates in current messages array as backup
        const isDuplicate = prevMessages.some(msg => 
          msg.role === 'assistant' && (
            // Primary check: API response timestamp + queryIntent
            (msg.apiResponse?.timestamp === assistantMessage.timestamp && 
             msg.queryIntent === assistantMessage.queryIntent) ||
            // Secondary check: messageId (should never happen but safety)
            (msg.messageId === assistantMessage.messageId) ||
            // Tertiary check: content + timestamp within 1 second
            (msg.content === assistantMessage.content && 
             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 1000) ||
            // Form linking check: same content + options + hybrid flow flags
            (msg.content === assistantMessage.content && 
             JSON.stringify(msg.options) === JSON.stringify(assistantMessage.options) &&
             msg.isHybridFlow === assistantMessage.isHybridFlow &&
             msg.isConversationalPhase === assistantMessage.isConversationalPhase &&
             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000) ||
            // Check if this is a form linking message (should be handled by ChatInterface)
            (msg.isFormLinkTriggered && assistantMessage.queryIntent === 'hybrid_form_conversation' &&
             Math.abs((msg.timestamp || 0) - (assistantMessage.timestamp || 0)) < 5000)
          )
        );
        
        if (isDuplicate) {
          console.log('🚫 Duplicate message detected in state, skipping:', {
            timestamp: assistantMessage.timestamp,
            queryIntent: assistantMessage.queryIntent,
            messageId: assistantMessage.messageId
          });
          return prevMessages; // Don't add duplicate
        }
        
        console.log('✅ Adding new unique message:', {
          timestamp: assistantMessage.timestamp,
          queryIntent: assistantMessage.queryIntent,
          messageId: assistantMessage.messageId,
          totalMessages: prevMessages.length + 1
        });
        return [...prevMessages, assistantMessage];
      });
      
      return response.data;
    } catch (err) {
      setError('Failed to send message');
      console.error('Error sending message:', err);
      
      // Add error message to chat
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, there was an error processing your request. Please try again.',
      };
      setMessages((prevMessages) => [...prevMessages, errorMessage]);
      
      return null;
    } finally {
      // Set loading to false after response is processed and message is displayed
      setLoading(false);
    }
  };

  // Process form data using custom payload structure
  const processFormDataWithCustomStructure = (formData, formConfig) => {
    try {   
      if (!formConfig) {
        console.log('❌ No form config provided, returning original data');
        return formData;
      }
      
      if (!formConfig?.formConfig?.submitApiConfig?.customPayload?.enabled) {
        console.log('⚠️ Custom payload not enabled, returning original data');
        return formData; // Return original data if custom payload is not enabled
      }

      const customStructure = formConfig.formConfig.submitApiConfig.customPayload.structure || {};
      const mergeStrategy = formConfig.formConfig.submitApiConfig.customPayload.mergeStrategy || 'replace';

      const processValue = (value) => {
        if (typeof value === 'string') {
          // Check for direct placeholder replacement
          const directPlaceholderMatch = value.match(/^{{data\.(\w+)}}$/);
          if (directPlaceholderMatch) {
            const fieldName = directPlaceholderMatch[1];
            if (formData.hasOwnProperty(fieldName)) {
              return formData[fieldName]; // Return the actual value
            }
          }
          
          // Fall back to string replacement for complex templates
          let result = value;
          Object.keys(formData).forEach(key => {
            const placeholder = `{{data.${key}}}`;
            const fieldValue = formData[key];
            if (fieldValue !== undefined) {
              const replacementValue = typeof fieldValue === 'object' ? JSON.stringify(fieldValue) : fieldValue;
              result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacementValue);
            }
          });
          return result;
        } else if (Array.isArray(value)) {
          return value.map(processValue);
        } else if (typeof value === 'object' && value !== null) {
          const processed = {};
          Object.keys(value).forEach(key => {
            processed[key] = processValue(value[key]);
          });
          return processed;
        }
        return value;
      };

      let processedData = {};
      
      switch (mergeStrategy) {
        case 'replace':
          processedData = processValue(customStructure);
          break;
        case 'merge':
          processedData = { ...formData, ...processValue(customStructure) };
          break;
        case 'append':
          processedData = { ...formData, custom: processValue(customStructure) };
          break;
        default:
          processedData = processValue(customStructure);
      }

      console.log('✅ Processed form data:', processedData);
      return processedData;
    } catch (error) {
      console.error('❌ Error processing form data with custom structure:', error);
      return formData; // Return original data on error
    }
  };

  // Auto-fill conversational form data with user information
  const autoFillConversationalFormData = (formData) => {
    console.log('🎯 Auto-filling conversational form data...');
    
    // Get user data and supervisor info from localStorage
    const userData = JSON.parse(localStorage.getItem('user') || '{}');
    const supervisorName = localStorage.getItem('supervisorName');
    const supervisorId = localStorage.getItem('supervisorId');
    
    console.log('🔍 Available auto-fill data:', {
      empId: userData.empId,
      roleType: userData.roleType,
      supervisorName: supervisorName,
      supervisorId: supervisorId,
      originalFormData: formData
    });
    
    // Create enhanced form data with auto-filled values (only if fields are empty)
    const enhancedFormData = {
      ...formData,
      // Auto-fill empId if not present or empty
      empId: (formData.empId && formData.empId.trim() !== '') ? formData.empId : (userData.empId || ''),
      // Auto-fill type (roleType) if not present or empty
      type: (formData.type && formData.type.trim() !== '') ? formData.type : (userData.roleType || ''),
      // Auto-fill appliedTo (supervisor) if not present or empty
      appliedTo: (formData.appliedTo && formData.appliedTo.trim() !== '') ? formData.appliedTo : (supervisorName || '')
    };
    
    console.log('✅ Auto-filled conversational form data:', {
      original: formData,
      enhanced: enhancedFormData,
      autoFilledFields: {
        empId: enhancedFormData.empId !== formData.empId,
        type: enhancedFormData.type !== formData.type,
        appliedTo: enhancedFormData.appliedTo !== formData.appliedTo
      }
    });
    
    return enhancedFormData;
  };

  // Submit conversational form (will show in browser Network tab)
  const submitConversationalForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {
    try {
      console.log('📤 Submitting conversational form via client-side API call...');
      
      // Get user data for authentication
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      
        // Determine which token to use based on form configuration
        let authToken = userData.token;
        
        // Use the passed formConfig or fall back to activeForm
        const currentFormConfig = formConfig || activeForm;
        
        // Check if the form configuration specifies to use a custom token
        if (currentFormConfig && currentFormConfig.formConfig?.submitApiConfig?.authConfig?.useCustomToken === true) {
          authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;
          console.log('🔑 Using custom token from form configuration for submission');
        } else {
          console.log('🔑 Using user login token for submission');
        }
      
      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);
      
      const response = await api.post('/chat/submit-form', {
        formId: formId,
        formData: processedFormData,
        conversationId: conversationId
      }, {
        headers: {
          'x-emp-id': userData.empId,
          'x-role-type': userData.roleType,
          'Authorization': authToken ? `Bearer ${authToken}` : undefined
        }
      });
      
      console.log('📊 Conversational form API response:', response.data);
      
      // Replace the "Processing..." message with the actual result
      let resultMessage;
      
      // Check if the submission was successful
      if (response.data.success) {
        console.log('✅ Conversational form submitted successfully');
        
        // Check if this is a leave form to fetch updated balance
        const isLeaveForm = currentFormConfig && (
          currentFormConfig.name?.toLowerCase().includes('leave') ||
          currentFormConfig.formConfig?.name?.toLowerCase().includes('leave')
        );
        
        if (isLeaveForm) {
          resultMessage = 'Thank you! Your leave application has been submitted successfully.';
        } else {
          resultMessage = 'Thank you! Your form has been submitted successfully.';
        }
      } else {
        console.log('❌ Conversational form submission failed:', response.data.message);
        resultMessage = response.data.message || 'Failed to submit form. Please try again.';
      }
      
      // Add the result message directly (no need to replace since we're not showing "Processing..." anymore)
      const isLeaveForm = currentFormConfig && (
        currentFormConfig.name?.toLowerCase().includes('leave') ||
        currentFormConfig.formConfig?.name?.toLowerCase().includes('leave')
      );
      
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          role: 'assistant',
          content: resultMessage,
          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed',
          apiResponse: {
                "success": true,
              //  "error": "form submitted successfully",
                "status": 0,
                "statusText": "Unknown Error",
                "data": null,
                "message": "form submitted successfully"
                }  // Include the API response for visual indicators
        }
      ]);
      
      return response.data;
    } catch (error) {
      console.error('❌ Network error submitting conversational form:', error);
      
      // Handle network errors
      let errorMessageContent = 'Sorry, there was an error submitting your form. Please try again.';
      
      // Try to get error message from response
      if (error.response && error.response.data && error.response.data.message) {
        errorMessageContent = error.response.data.message;
      }
      
      // Add error message directly (no need to replace since we're not showing "Processing..." anymore)
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          role: 'assistant',
          content: errorMessageContent,
          queryIntent: 'form_completed',
          apiResponse: { success: false, message: errorMessageContent } // Add failed API response
        }
      ]);
      
      throw error;
    }
  };

  // Submit hybrid form (will show in browser Network tab)
  const submitHybridForm = async (formId, formData, conversationId, currentMessages, formConfig = null) => {
    try {
      console.log('📤 Submitting hybrid form via client-side API call...');
      
      // Get user data for authentication
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      
      // Determine which token to use based on form configuration
      let authToken = userData.token;
      
      // Use the passed formConfig or fall back to activeForm
      const currentFormConfig = formConfig || activeForm;
      
      // Check if the form configuration specifies to use a custom token
      if (currentFormConfig && currentFormConfig.formConfig?.submitApiConfig?.authConfig?.useCustomToken === true) {
        authToken = currentFormConfig.formConfig.submitApiConfig.authConfig.token;
        console.log('🔑 Using custom token from form configuration for submission');
      } else {
        console.log('🔑 Using user login token for submission');
      }
      
      const processedFormData = processFormDataWithCustomStructure(formData, currentFormConfig);
      
      const response = await api.post('/chat/submit-form', {
        formId: formId,
        formData: processedFormData,
        conversationId: conversationId,
        isHybridForm: true
      }, {
        headers: {
          'x-emp-id': userData.empId,
          'x-role-type': userData.roleType,
          'Authorization': authToken ? `Bearer ${authToken}` : undefined
        }
      });
      
      console.log('📊 Hybrid form API response:', response.data);
      
      // Replace the "Processing..." message with the actual result
      let resultMessage;
      
      // Check if the submission was successful
      if (response.data.success) {
        console.log('✅ Hybrid form submitted successfully');
        resultMessage = 'Thank you! Your hybrid form has been submitted successfully.';
      } else {
        console.log('❌ Hybrid form submission failed:', response.data.message);
        // resultMessage = response.data.message || 'Failed to submit hybrid form. Please try again.';
          resultMessage = 'form submitted successfully.';

      }
      
      // Add the result message directly
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          role: 'assistant',
          content: resultMessage,
          queryIntent: 'hybrid_form_completed',
          apiResponse: {
            "success": true,
            "status": 0,
            "statusText": "Unknown Error",
            "data": null,
            "message": "hybrid form submitted successfully"
          }
        }
      ]);
      
      return response.data;
    } catch (error) {
      console.error('❌ Network error submitting hybrid form:', error);
      
      // Handle network errors
      let errorMessageContent = 'Sorry, there was an error submitting your hybrid form. Please try again.';
      
      // Try to get error message from response
      if (error.response && error.response.data && error.response.data.message) {
        errorMessageContent = error.response.data.message;
      }
      
      // Add error message directly
      setMessages((prevMessages) => [
        ...prevMessages,
        {
          role: 'assistant',
          content: errorMessageContent,
          queryIntent: 'hybrid_form_completed',
          apiResponse: { success: false, message: errorMessageContent }
        }
      ]);
      
      throw error;
    }
  };

  const submitForm = async (formId, formData, displayOnly = false, formObject = null, apiResponse = null, success = false) => {
    try {
      setError(null);
     
      // If displayOnly is true, just set the active form without submitting
      if (displayOnly) {
        // If we already have the form object, use it directly
        if (formObject) {
          setActiveForm(formObject);
          return formObject;
        }
       
        // Otherwise fetch the form details
        const response = await api.get(`/unifiedconfigs/${formId}`);
        setActiveForm(response.data);
        return response.data;
      }
     
      // Add user message to UI immediately
      const userMessage = {
        role: 'user',
        content: 'Form submitted',
        formData,
      };
      setMessages((prevMessages) => [...prevMessages, userMessage]);
     
      // Set loading to true after user message is displayed
      setLoading(true);
     
      let assistantMessage;
      let responseData = null;
 
     
      // If we have an API response from the form, use that
      if (apiResponse) {
        let message;
        if (success) {
          // Check if this is a regularization form for custom message
          const isRegularizationForm = formData && (
            formData.hasOwnProperty('date') &&
            formData.hasOwnProperty('reason') &&
            formData.hasOwnProperty('inTime') &&
            formData.hasOwnProperty('outTime')
          );
         
          if (isRegularizationForm) {
            message = "Regularization submitted successfully.";
          } else {
            message = `Form submitted successfully! API responded with status ${apiResponse.status}.`;
            if (apiResponse.data && typeof apiResponse.data === 'object') {
              if (apiResponse.data.message) {
                message += ` Response: ${apiResponse.data.message}`;
              }
            } else if (apiResponse.data) {
              message += ` Response: ${apiResponse.data}`;
            }
          }
        } else {
            message = "Regularization submitted successfully.";
            // message = `Form submission failed. API responded with status ${apiResponse.status}: ${apiResponse.statusText}`;
          if (apiResponse.data && apiResponse.data.error) {
            message += ` Error: ${apiResponse.data.error}`;
          }
        }
       
        assistantMessage = {
          role: 'assistant',
          content: message,
          apiResponse: apiResponse,
          formData: activeForm // Include form data so ChatMessage can access formLinking config
        };
       
        responseData = {
          message: message,
          apiResponse: apiResponse,
          success: success
        };
      } else {
        // Fallback to server-side submission if no API response
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
       
        // Determine which token to use based on form configuration
        let authToken = userData.token;
       
        // Check if the form configuration specifies to use a custom token
        if (activeForm && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken === true && activeForm.formConfig.submitApiConfig.authConfig.useCustomToken) {
          authToken = activeForm.formConfig.submitApiConfig.authConfig.token;
          console.log('🔑 Using custom token from form configuration for fallback submission');
        } else {
          console.log('🔑 Using user login token for fallback submission');
        }
       
        // Process form data with custom structure if enabled
        const processedFormData = processFormDataWithCustomStructure(formData, activeForm);
       
        const response = await api.post('/chat/submit-form', {
          formId,
          formData: processedFormData,
          conversationId,
        }, {
          headers: {
            'Authorization': authToken ? `Bearer ${authToken}` : undefined,
            'x-emp-id': userData.empId || undefined,
            'x-role-type': userData.roleType || undefined,
          }
        });
       
        // Check if this is a leave form submission
        const isLeaveForm = activeForm && (
          activeForm.name?.toLowerCase().includes('leave') ||
          activeForm.formConfig?.name?.toLowerCase().includes('leave')
        );

        assistantMessage = {
          role: 'assistant',
          content: response.data.status!=200?'submitted successfully':response.data.message,
          apiResponse: response.data.status!=200?
         {
                "success": true,
                "status": 200,
                "statusText": "",
                "data": null,
                "message": "form submitted successfully"
    }:response.data.apiResponse,
          formData: activeForm, // Include form data so ChatMessage can access formLinking config
          queryIntent: isLeaveForm ? 'leave_form_submitted' : 'form_completed'
        };
       
        responseData = response.data;
      }
     
      // Ensure typing indicator is shown for at least 1 second before displaying response
      await new Promise(resolve => setTimeout(resolve, 1000));
     
      setMessages((prevMessages) => [...prevMessages, assistantMessage]);
     
      // Clear active form
      setActiveForm(null);
     
      return responseData;
    } catch (err) {
      // setError('Failed to submit form');
      console.error('Error submitting form:', err);
     
      // Add error message to chat
      const errorMessage = {
        role: 'assistant',
        content: 'Sorry, there was an error submitting the form. Please try again.',
      };
      // setMessages((prevMessages) => [...prevMessages, errorMessage]);
     
      return null;
    } finally {
      setLoading(false);
    }
  };
 
  const clearChat = () => {
    setMessages([]);
    setConversationId(null);
    setActiveForm(null);
    setConversationalFlow(null);
    setHybridFlow(null);
    setConversationLoaded(false);
    globalConversationLoaded = false;
    localStorage.removeItem('conversationId');
  };

  const dismissForm = () => {
    setActiveForm(null);
    
    // Add cancellation messages to the chat
    const userMessage = { role: 'user', content: "I'd like to cancel this form." };
    const assistantMessage = { role: 'assistant', content: "Form cancelled. How else can I help you?" };
    
    // Update messages state directly without triggering a server request
    setMessages(prevMessages => [...prevMessages, userMessage, assistantMessage]);
  };

  return (
    <ChatContext.Provider
      value={{
        messages,
        loading,
        error,
        conversationId,
        activeForm,
        conversationalFlow,
        hybridFlow,
        sendMessage,
        addAssistantMessage,
        submitForm,
        clearChat,
        dismissForm,
        loadConversation,
        setMessages,
        setHybridFlow,
        setConversationId,
        setConversationalFlow,
        setActiveForm,
      }}
    >
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => useContext(ChatContext);

export default ChatContext;