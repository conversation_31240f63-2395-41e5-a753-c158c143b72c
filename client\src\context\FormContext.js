import React, { createContext, useContext, useState, useEffect } from 'react';
import api from '../utils/api';

const FormContext = createContext();

export const FormProvider = ({ children }) => {
  const [forms, setForms] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [currentForm, setCurrentForm] = useState(null);

  // Load forms on initial render
  useEffect(() => {
    fetchForms();
  }, []);

  const fetchForms = async () => {
    try {
      setLoading(true);
      console.log('🔄 Fetching forms from:', api.defaults.baseURL + '/unifiedconfigs');
      const response = await api.get('/unifiedconfigs');
      console.log('📋 Forms response:', response.data);
      console.log('📋 Forms count:', response.data?.length || 0);
      console.log('📋 Form titles:', response.data?.map(f => f.formTitle || f.name || f._id) || []);
      setForms(response.data);
      return response.data;
    } catch (err) {
      setError('Failed to fetch forms');
      console.error('Error fetching forms:', err);
      console.error('Error details:', {
        message: err.message,
        status: err.response?.status,
        statusText: err.response?.statusText,
        url: err.config?.url,
        baseURL: err.config?.baseURL,
        fullURL: err.config?.baseURL + err.config?.url,
        data: err.response?.data
      });
      return [];
    } finally {
      setLoading(false);
    }
  };
  
  const getForms = async () => {
    // If forms are already loaded, return them
    if (forms.length > 0) {
      return forms;
    }
    // Otherwise fetch them
    return await fetchForms();
  };

  const getFormById = async (id) => {
    try {
      setLoading(true);
      const response = await api.get(`/unifiedconfigs/${id}`);
      setCurrentForm(response.data);
      return response.data;
    } catch (err) {
      setError('Failed to fetch form');
      console.error('Error fetching form:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const getFormByName = async (name) => {
    try {
      setLoading(true);
      console.log(`🔍 Searching for form by name: "${name}"`);
      console.log(`🌐 API call: ${api.defaults.baseURL}/unifiedconfigs/name/${name}`);
      const response = await api.get(`/unifiedconfigs/name/${name}`);
      console.log(`✅ Found form by name "${name}":`, response.data);
      setCurrentForm(response.data);
      return response.data;
    } catch (err) {
      setError('Failed to fetch form');
      console.error(`❌ Error fetching form "${name}":`, err);
      console.error('Error details:', {
        status: err.response?.status,
        statusText: err.response?.statusText,
        url: err.config?.url,
        data: err.response?.data
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  const createForm = async (formData) => {
    try {
      setLoading(true);
      const response = await api.post('/unifiedconfigs', formData);
      setForms([...forms, response.data]);
      setCurrentForm(response.data);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to create form');
      console.error('Error creating form:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateForm = async (id, formData) => {
    try {
      setLoading(true);
      const response = await api.put(`/unifiedconfigs/${id}`, formData);
      setForms(forms.map(form => (form._id === id ? response.data : form)));
      setCurrentForm(response.data);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update form');
      console.error('Error updating form:', err);
      return null;
    } finally {
      setLoading(false);
    }
  };

  const deleteForm = async (id) => {
    try {
      setLoading(true);
      await api.delete(`/unifiedconfigs/${id}`);
      setForms(forms.filter(form => form._id !== id));
      if (currentForm && currentForm._id === id) {
        setCurrentForm(null);
      }
      return true;
    } catch (err) {
      setError('Failed to delete form');
      console.error('Error deleting form:', err);
      return false;
    } finally {
      setLoading(false);
    }
  };

  const searchForms = async (query) => {
    try {
      setLoading(true);
      const response = await api.get(`/unifiedconfigs/search?query=${query}`);
      return response.data;
    } catch (err) {
      setError('Failed to search forms');
      console.error('Error searching forms:', err);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const clearCurrentForm = () => {
    setCurrentForm(null);
  };

  const clearError = () => {
    setError(null);
  };

  return (
    <FormContext.Provider
      value={{
        forms,
        loading,
        error,
        currentForm,
        fetchForms,
        getForms,
        getFormById,
        getFormByName,
        createForm,
        updateForm,
        deleteForm,
        searchForms,
        clearCurrentForm,
        clearError,
      }}
    >
      {children}
    </FormContext.Provider>
  );
};

export const useForm = () => useContext(FormContext);

export default FormContext;