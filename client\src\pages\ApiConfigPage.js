import React, { useState, useEffect } from 'react';
import { useApiConfig } from '../context/ApiConfigContext';
import ApiConfigForm from '../components/ApiConfigForm';

const ApiConfigPage = () => {
  const {
    apiConfigs,
    loading,
    error,
    currentApiConfig,
    fetchApiConfigs,
    getApiConfigById,
    deleteApiConfig,
    clearCurrentApiConfig,
    clearError,
  } = useApiConfig();
  
  const [showConfigForm, setShowConfigForm] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredConfigs, setFilteredConfigs] = useState([]);
  const [testResults, setTestResults] = useState({});

  // Filter API configs when search term or configs change
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredConfigs(apiConfigs);
    } else {
      const filtered = apiConfigs.filter(
        (config) =>
          config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          config.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          config.endpoint.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredConfigs(filtered);
    }
  }, [searchTerm, apiConfigs]);
  
  const handleCreateConfig = () => {
    clearCurrentApiConfig();
    setShowConfigForm(true);
  };
  
  const handleEditConfig = async (id) => {
    await getApiConfigById(id);
    setShowConfigForm(true);
  };
  
  const handleDeleteConfig = async (id) => {
    if (window.confirm('Are you sure you want to delete this API configuration?')) {
      await deleteApiConfig(id);
    }
  };
  
  const handleConfigSave = () => {
    setShowConfigForm(false);
    fetchApiConfigs();
  };
  
  const handleConfigCancel = () => {
    setShowConfigForm(false);
    clearCurrentApiConfig();
  };

  const getMethodColor = (method) => {
    switch (method?.toUpperCase()) {
      case 'GET': return 'bg-green-100 text-green-700';
      case 'POST': return 'bg-blue-100 text-blue-700';
      case 'PUT': return 'bg-yellow-100 text-yellow-700';
      case 'DELETE': return 'bg-red-100 text-red-700';
      case 'PATCH': return 'bg-purple-100 text-purple-700';
      default: return 'bg-gray-100 text-gray-700';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">API Configuration</h1>
          <p className="text-gray-600 mt-1">Manage your standalone API endpoints</p>
        </div>
        <button
          onClick={handleCreateConfig}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Add New API Config
        </button>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <span>{error}</span>
          <button
            className="float-right"
            onClick={clearError}
          >
            &times;
          </button>
        </div>
      )}
      
      {/* API Config Form */}
      {showConfigForm ? (
        <ApiConfigForm
          initialConfig={currentApiConfig}
          onSave={handleConfigSave}
          onCancel={handleConfigCancel}
        />
      ) : (
        <>
          {/* Search Bar */}
          <div className="mb-6">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search API configurations..."
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          {/* API Configs List */}
          {loading ? (
            <div className="text-center py-10">
              <svg className="animate-spin h-10 w-10 text-blue-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="mt-3 text-gray-600">Loading API configurations...</p>
            </div>
          ) : filteredConfigs.length > 0 ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {filteredConfigs.map((config) => (
                <div
                  key={config._id}
                  className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow"
                >
                  <div className="p-5">
                    <div className="flex justify-between items-start mb-3">
                      <h3 className="text-xl font-semibold text-gray-800">
                        {config.name}
                      </h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getMethodColor(config.method)}`}>
                        {config.method.toUpperCase()}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3 text-sm">
                      {config.description || 'No description provided'}
                    </p>
                    
                    <div className="mb-3">
                      <p className="text-sm font-medium text-gray-700 mb-1">Endpoint:</p>
                      <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded font-mono break-all">
                        {config.endpoint}
                      </p>
                    </div>
                    
                    {config.headers && Object.keys(config.headers).length > 0 && (
                      <div className="mb-3">
                        <p className="text-sm font-medium text-gray-700 mb-1">Headers:</p>
                        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                          {Object.entries(config.headers).map(([key, value]) => (
                            <div key={key} className="font-mono">
                              <span className="font-semibold">{key}:</span> {value}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {/* Test Result */}
                    {testResults[config._id] && (
                      <div className={`mb-3 p-2 rounded text-sm ${
                        testResults[config._id].success 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-red-100 text-red-700'
                      }`}>
                        <strong>Test Result:</strong> {testResults[config._id].message}
                        {testResults[config._id].responseTime && (
                          <span className="ml-2">({testResults[config._id].responseTime}ms)</span>
                        )}
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>Created {new Date(config.createdAt).toLocaleDateString()}</span>
                      <span>Updated {new Date(config.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 px-5 py-3 border-t border-gray-200 flex justify-end space-x-2">
                    <button
                      onClick={() => handleEditConfig(config._id)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteConfig(config._id)}
                      className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              <p className="text-gray-600">
                {searchTerm
                  ? 'No API configurations match your search criteria'
                  : 'No API configurations available. Create your first API config!'}
              </p>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="mt-3 text-blue-500 hover:text-blue-700"
                >
                  Clear search
                </button>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default ApiConfigPage;