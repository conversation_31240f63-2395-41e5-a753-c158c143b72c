import React, { useState, useEffect } from 'react';
import { useForm } from '../context/FormContext';
import FormBuilder from '../components/FormBuilder';

const FormsPage = () => {
  const {
    forms,
    loading,
    error,
    currentForm,
    fetchForms,
    getFormById,
    deleteForm,
    clearCurrentForm,
    clearError,
  } = useForm();
  
  const [showFormBuilder, setShowFormBuilder] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredForms, setFilteredForms] = useState([]);
  
  // Filter forms when search term or forms change
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredForms(forms);
    } else {
      const filtered = forms.filter(
        (form) =>
          form.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          form.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredForms(filtered);
    }
  }, [searchTerm, forms]);
  
  const handleCreateForm = () => {
    clearCurrentForm();
    setShowFormBuilder(true);
  };
  
  const handleEditForm = async (id) => {
    await getFormById(id);
    setShowFormBuilder(true);
  };
  
  const handleDeleteForm = async (id) => {
    if (window.confirm('Are you sure you want to delete this form?')) {
      await deleteForm(id);
    }
  };
  
  const handleFormSave = () => {
    setShowFormBuilder(false);
    fetchForms();
  };
  
  const handleFormCancel = () => {
    setShowFormBuilder(false);
    clearCurrentForm();
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-800">Form Management</h1>
        <button
          onClick={handleCreateForm}
          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Create New Form
        </button>
      </div>
      
      {/* Error Message */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <span>{error}</span>
          <button
            className="float-right"
            onClick={clearError}
          >
            &times;
          </button>
        </div>
      )}
      
      {/* Form Builder */}
      {showFormBuilder ? (
        <FormBuilder
          initialForm={currentForm}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
        />
      ) : (
        <>
          {/* Search Bar */}
          <div className="mb-6">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search forms..."
              className="w-full p-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          {/* Forms List */}
          {loading ? (
            <div className="text-center py-10">
              <svg className="animate-spin h-10 w-10 text-blue-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p className="mt-3 text-gray-600">Loading forms...</p>
            </div>
          ) : filteredForms.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredForms.map((form) => (
                <div
                  key={form._id}
                  className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-200 hover:shadow-lg transition-shadow"
                >
                  <div className="p-5">
                    <h3 className="text-xl font-semibold text-gray-800 mb-2">
                      {form.name}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-2">
                      {form.description || 'No description provided'}
                    </p>
                    <div className="flex justify-between items-center text-sm text-gray-500">
                      <span>{form.fields.length} fields</span>
                      <span>{new Date(form.updatedAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="bg-gray-50 px-5 py-3 border-t border-gray-200 flex justify-end space-x-2">
                    <button
                      onClick={() => handleEditForm(form._id)}
                      className="px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => handleDeleteForm(form._id)}
                      className="px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-red-500"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-10">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <p className="text-gray-600">
                {searchTerm
                  ? 'No forms match your search criteria'
                  : 'No forms available. Create your first form!'}
              </p>
              {searchTerm && (
                <button
                  onClick={() => setSearchTerm('')}
                  className="mt-3 text-blue-500 hover:text-blue-700"
                >
                  Clear search
                </button>
              )}
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default FormsPage;