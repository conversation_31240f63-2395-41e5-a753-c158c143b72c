import React, { useState, useEffect } from 'react';
import UnifiedConfigBuilder from '../components/UnifiedConfigBuilder';
import api from '../utils/api';

const UnifiedConfigPage = () => {
  const [configs, setConfigs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showBuilder, setShowBuilder] = useState(false);
  const [editingConfig, setEditingConfig] = useState(null);
  const [testingConfig, setTestingConfig] = useState(null);
  const [testResult, setTestResult] = useState(null);
  const [filterType, setFilterType] = useState('all');
  const [stats, setStats] = useState(null);

  useEffect(() => {
    fetchConfigs();
    fetchStats();
  }, [filterType]);

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      const params = filterType !== 'all' ? { type: filterType } : {};
      const response = await api.get('/unified-configs', { params });
      
      // Handle both old array format and new object format
      const configsData = response.data.data || response.data;
      setConfigs(Array.isArray(configsData) ? configsData : []);
      
      console.log('Fetched configs:', {
        responseType: typeof response.data,
        isArray: Array.isArray(response.data),
        hasDataProperty: !!response.data.data,
        configsCount: Array.isArray(configsData) ? configsData.length : 0
      });
    } catch (err) {
      setError('Failed to fetch configurations');
      console.error('Error fetching configs:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await api.get('/unified-configs/stats/overview');
      setStats(response.data);
    } catch (err) {
      console.error('Error fetching stats:', err);
    }
  };

  const handleSave = async (configData) => {
    console.log('UnifiedConfigPage: handleSave called with:', configData);
    try {
      if (editingConfig) {
        console.log('UnifiedConfigPage: Updating existing config:', editingConfig._id);
        const response = await api.put(`/unified-configs/${editingConfig._id}`, configData);
        console.log('UnifiedConfigPage: Update response:', response.data);
      } else {
        console.log('UnifiedConfigPage: Creating new config');
        const response = await api.post('/unified-configs', configData);
        console.log('UnifiedConfigPage: Create response:', response.data);
      }
      
      console.log('UnifiedConfigPage: Save successful, updating UI');
      setShowBuilder(false);
      setEditingConfig(null);
      fetchConfigs();
      fetchStats();
    } catch (err) {
      console.error('UnifiedConfigPage: Error saving config:', err);
      console.error('UnifiedConfigPage: Error details:', err.response?.data);
      throw err;
    }
  };

  const handleEdit = (config) => {
    setEditingConfig(config);
    setShowBuilder(true);
  };

  const handleDelete = async (configId) => {
    const config = configs.find(c => c._id === configId);
    if (window.confirm(`Are you sure you want to delete "${config?.name}"? This action cannot be undone.`)) {
      try {
        await api.delete(`/unified-configs/${configId}`);
        fetchConfigs();
        fetchStats();
      } catch (err) {
        console.error('Error deleting config:', err);
        alert('Failed to delete configuration');
      }
    }
  };

  const handleTest = async (config) => {
    if (config.type !== 'api') {
      alert('Only API configurations can be tested');
      return;
    }

    try {
      setTestingConfig(config._id);
      setTestResult(null);
      
      const response = await api.post(`/unified-configs/${config._id}/test`, {
        testData: {} // You can add test data here
      });
      
      setTestResult(response.data);
    } catch (err) {
      console.error('Error testing config:', err);
      setTestResult({
        success: false,
        error: err.response?.data?.error || err.message
      });
    } finally {
      setTestingConfig(null);
    }
  };

  const toggleStatus = async (config) => {
    try {
      await api.put(`/unified-configs/${config._id}`, {
        isActive: !config.isActive
      });
      fetchConfigs();
      fetchStats();
    } catch (err) {
      console.error('Error updating status:', err);
      alert('Failed to update status');
    }
  };

  const handleCreateNew = (type) => {
    setEditingConfig(null);
    setShowBuilder(true);
    // You can pass the default type to the builder if needed
  };

  if (showBuilder) {
    return (
      <div className="min-h-screen bg-gray-50 py-6">
        <div className="max-w-5xl mx-auto px-4">
          <UnifiedConfigBuilder
            initialConfig={editingConfig}
            onSave={handleSave}
            onCancel={() => {
              setShowBuilder(false);
              setEditingConfig(null);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-6">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Unified Configuration Manager</h1>
              <p className="text-gray-600 mt-2">
                Manage both API endpoints and Forms in one place with dynamic matching
              </p>
            </div>
            <div className="flex space-x-2">
              {/* <button
                onClick={() => handleCreateNew('api')}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                New API
              </button> */}
              {/* <button
                onClick={() => handleCreateNew('form')}
                className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                New Form
              </button> */}
              <button
                onClick={() => setShowBuilder(true)}
                className="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg flex items-center"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
                New Config
              </button>
            </div>
          </div>
        </div>

        {/* Stats Dashboard */}
        {stats && (
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-purple-600">{stats.total.configs}</div>
              <div className="text-gray-600">Total Configs</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-blue-600">{stats.total.apis}</div>
              <div className="text-gray-600">APIs</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-green-600">{stats.total.forms}</div>
              <div className="text-gray-600">Forms</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-emerald-600">{stats.total.active}</div>
              <div className="text-gray-600">Active</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow">
              <div className="text-2xl font-bold text-red-600">{stats.total.inactive}</div>
              <div className="text-gray-600">Inactive</div>
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="mb-6">
          <div className="flex space-x-2">
            <button
              onClick={() => setFilterType('all')}
              className={`px-4 py-2 rounded-lg ${
                filterType === 'all' 
                  ? 'bg-purple-500 text-white' 
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              All ({stats?.total.configs || 0})
            </button>
            <button
              onClick={() => setFilterType('api')}
              className={`px-4 py-2 rounded-lg ${
                filterType === 'api' 
                  ? 'bg-blue-500 text-white' 
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              APIs ({stats?.total.apis || 0})
            </button>
            <button
              onClick={() => setFilterType('form')}
              className={`px-4 py-2 rounded-lg ${
                filterType === 'form' 
                  ? 'bg-green-500 text-white' 
                  : 'bg-white text-gray-700 hover:bg-gray-50'
              }`}
            >
              Forms ({stats?.total.forms || 0})
            </button>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Test Result */}
        {testResult && (
          <div className={`border px-4 py-3 rounded mb-4 ${
            testResult.success 
              ? 'bg-green-50 border-green-200 text-green-700' 
              : 'bg-red-50 border-red-200 text-red-700'
          }`}>
            <div className="font-medium">
              {testResult.success ? '✅ Test Successful' : '❌ Test Failed'}
            </div>
            <div className="text-sm mt-1">
              {testResult.success 
                ? `Status: ${testResult.status} | Response Time: ${testResult.responseTime}ms`
                : `Error: ${testResult.error}`
              }
            </div>
            {testResult.formattedResponse && (
              <div className="mt-2 p-2 bg-gray-100 rounded text-sm">
                <pre className="whitespace-pre-wrap">{testResult.formattedResponse}</pre>
              </div>
            )}
          </div>
        )}

        {/* Configurations List */}
        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          </div>
        ) : configs.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-gray-500 text-lg">
              {filterType === 'all' ? 'No configurations found' : `No ${filterType} configurations found`}
            </div>
            <p className="text-gray-400 mt-2">Create your first configuration to get started</p>
          </div>
        ) : (
          <div className="grid gap-4">
            {configs.map((config) => (
              <div key={config._id} className="bg-white rounded-lg shadow border border-gray-200">
                <div className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <h3 className="text-lg font-semibold text-gray-900">{config.name}</h3>
                        
                        {/* Type Badge */}
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          config.type === 'api' 
                            ? 'bg-blue-100 text-blue-800' 
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {config.type.toUpperCase()}
                        </span>
                        
                        {/* Status Badge */}
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          config.isActive 
                            ? 'bg-emerald-100 text-emerald-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {config.isActive ? 'Active' : 'Inactive'}
                        </span>
                        
                        {/* Priority Badge */}
                        {config.priority > 0 && (
                          <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800">
                            Priority: {config.priority}
                          </span>
                        )}

                        {/* Method Badge for APIs */}
                        {config.type === 'api' && config.apiConfig?.method && (
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            config.apiConfig.method === 'GET' ? 'bg-blue-100 text-blue-800' :
                            config.apiConfig.method === 'POST' ? 'bg-green-100 text-green-800' :
                            config.apiConfig.method === 'PUT' ? 'bg-yellow-100 text-yellow-800' :
                            config.apiConfig.method === 'DELETE' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {config.apiConfig.method}
                          </span>
                        )}
                      </div>
                      
                      <div className="mt-2 text-gray-600">
                        {config.description || 'No description provided'}
                      </div>
                      
                      {/* Endpoint/Fields Info */}
                      <div className="mt-2 text-sm text-gray-500 font-mono">
                        {config.type === 'api' ? (
                          config.apiConfig?.endpoint || 'No endpoint configured'
                        ) : (
                          `${config.formConfig?.fields?.length || 0} fields configured`
                        )}
                      </div>
                      
                      {/* Keywords/Trigger Phrases */}
                      <div className="mt-3 flex flex-wrap gap-2">
                        {config.triggerPhrases?.map((phrase, index) => (
                          <span key={index} className="px-2 py-1 bg-purple-100 text-purple-800 text-xs rounded">
                            "{phrase}"
                          </span>
                        ))}
                        {config.keywords?.map((keyword, index) => (
                          <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded">
                            {keyword}
                          </span>
                        ))}
                      </div>
                      
                      {/* Last Test Result for APIs */}
                      {config.type === 'api' && config.apiConfig?.lastTestResult && (
                        <div className="mt-3 text-xs text-gray-500">
                          Last tested: {new Date(config.apiConfig.lastTestedAt).toLocaleString()} - 
                          {config.apiConfig.lastTestResult.success ? (
                            <span className="text-green-600 ml-1">✅ Success</span>
                          ) : (
                            <span className="text-red-600 ml-1">❌ Failed</span>
                          )}
                        </div>
                      )}
                      
                      {/* Data Injection Indicator */}
                      {config.type === 'api' && config.apiConfig?.dataInjection?.injectUserData && (
                        <div className="mt-2 flex items-center text-xs text-blue-600">
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                          </svg>
                          Auto-injects user data
                        </div>
                      )}

                      {/* Category & Tags */}
                      <div className="mt-2 flex items-center space-x-4 text-xs text-gray-500">
                        <span>Category: {config.category || 'general'}</span>
                        {config.tags && config.tags.length > 0 && (
                          <span>Tags: {config.tags.join(', ')}</span>
                        )}
                      </div>
                    </div>
                    
                    {/* Action Buttons */}
                    <div className="flex items-center space-x-2 ml-4">
                      {config.type === 'api' && (
                        <button
                          onClick={() => handleTest(config)}
                          disabled={testingConfig === config._id}
                          className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
                        >
                          {testingConfig === config._id ? 'Testing...' : 'Test'}
                        </button>
                      )}
                      
                      <button
                        onClick={() => toggleStatus(config)}
                        className={`px-3 py-1 text-sm rounded ${
                          config.isActive
                            ? 'bg-red-100 text-red-700 hover:bg-red-200'
                            : 'bg-green-100 text-green-700 hover:bg-green-200'
                        }`}
                      >
                        {config.isActive ? 'Disable' : 'Enable'}
                      </button>
                      
                      <button
                        onClick={() => handleEdit(config)}
                        className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
                      >
                        Edit
                      </button>
                      
                      <button
                        onClick={() => handleDelete(config._id)}
                        className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedConfigPage;