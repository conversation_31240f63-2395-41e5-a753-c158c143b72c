/**
 * Utility functions for matching user queries to forms
 */

/**
 * Calculates the similarity score between a query and a form
 * @param {string} query - The user's query
 * @param {object} form - The form object to match against
 * @returns {number} A similarity score (0-1)
 */
export const calculateSimilarity = (query, form) => {
  if (!query || !form) return 0;
  
  // Convert query to lowercase for case-insensitive matching
  const normalizedQuery = query.toLowerCase();
  
  // Create a set of keywords from the form
  const formKeywords = new Set([
    ...(form.name ? form.name.toLowerCase().split(/\s+/) : []),
    ...(form.description ? form.description.toLowerCase().split(/\s+/) : []),
    // Add field names and labels
    ...(form.fields ? form.fields.flatMap(field => [
      field.name.toLowerCase(),
      ...field.label.toLowerCase().split(/\s+/)
    ]) : [])
  ]);
  
  // Count how many words from the query appear in the form keywords
  const queryWords = normalizedQuery.split(/\s+/);
  const matchingWords = queryWords.filter(word => 
    formKeywords.has(word) || 
    Array.from(formKeywords).some(keyword => keyword.includes(word) || word.includes(keyword))
  );
  
  // Calculate similarity score (0-1)
  return matchingWords.length / queryWords.length;
};

/**
 * Finds the best matching form for a given query
 * @param {string} query - The user's query
 * @param {Array} forms - Array of available forms
 * @param {number} threshold - Minimum similarity score to consider a match (0-1)
 * @returns {object|null} The best matching form or null if no match found
 */
export const findBestMatchingForm = (query, forms, threshold = 0.3) => {
  if (!query || !forms || forms.length === 0) return null;
  
  // Calculate similarity scores for each form
  const scoredForms = forms.map(form => ({
    form,
    score: calculateSimilarity(query, form)
  }));
  
  // Sort by score (descending)
  scoredForms.sort((a, b) => b.score - a.score);
  
  // Return the best match if it meets the threshold
  return scoredForms[0].score >= threshold ? scoredForms[0].form : null;
};

/**
 * Extracts potential field values from a user query
 * @param {string} query - The user's query
 * @param {object} form - The form to extract values for
 * @returns {object} An object with field names as keys and extracted values as values
 */
export const extractFieldValues = (query, form) => {
  if (!query || !form || !form.fields) return {};
  
  const extractedValues = {};
  
  // For each field, try to find a value in the query
  form.fields.forEach(field => {
    // Create patterns to match field values
    // Example: "<NAME_EMAIL>" or "email: <EMAIL>"
    const patterns = [
      new RegExp(`${field.label}\\s+(?:is|are|:|=)\\s+([\\w@.]+)`, 'i'),
      new RegExp(`${field.name}\\s+(?:is|are|:|=)\\s+([\\w@.]+)`, 'i'),
      // For multi-word values (in quotes)
      new RegExp(`${field.label}\\s+(?:is|are|:|=)\\s+"([^"]+)"`, 'i'),
      new RegExp(`${field.name}\\s+(?:is|are|:|=)\\s+"([^"]+)"`, 'i')
    ];
    
    // Try each pattern
    for (const pattern of patterns) {
      const match = query.match(pattern);
      if (match && match[1]) {
        extractedValues[field.name] = match[1];
        break;
      }
    }
  });
  
  return extractedValues;
};