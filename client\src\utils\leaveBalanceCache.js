/**
 * Simple cache utility to prevent duplicate leave balance API calls
 */

class LeaveBalanceCache {
  constructor() {
    this.cache = new Map();
    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  }

  // Generate cache key based on user and query
  generateKey(userId, query) {
    const normalizedQuery = query?.toLowerCase().trim() || 'leave_balance';
    return `${userId}_${normalizedQuery}`;
  }

  // Check if we have cached data for this query
  has(userId, query) {
    const key = this.generateKey(userId, query);
    const cached = this.cache.get(key);
    
    if (!cached) return false;
    
    // Check if cache is still valid
    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_DURATION) {
      this.cache.delete(key);
      return false;
    }
    
    return true;
  }

  // Get cached data
  get(userId, query) {
    const key = this.generateKey(userId, query);
    const cached = this.cache.get(key);
    
    if (!cached) return null;
    
    // Check if cache is still valid
    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  // Set cached data
  set(userId, query, data) {
    const key = this.generateKey(userId, query);
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // Clear cache for a specific user
  clearUser(userId) {
    for (const [key] of this.cache) {
      if (key.startsWith(`${userId}_`)) {
        this.cache.delete(key);
      }
    }
  }

  // Clear all cache
  clear() {
    this.cache.clear();
  }

  // Clean expired entries
  cleanup() {
    const now = Date.now();
    for (const [key, value] of this.cache) {
      if (now - value.timestamp > this.CACHE_DURATION) {
        this.cache.delete(key);
      }
    }
  }
}

// Create a singleton instance
const leaveBalanceCache = new LeaveBalanceCache();

// Cleanup expired entries every minute
setInterval(() => {
  leaveBalanceCache.cleanup();
}, 60 * 1000);

export default leaveBalanceCache;
