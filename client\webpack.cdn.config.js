const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

module.exports = {
  mode: 'production',
  entry: './src/cdn/index.js',
  output: {
    path: path.resolve(__dirname, 'cdn/dist'),
    filename: 'chatbot-widget.js',
    library: 'BotNexusChatbot',
    libraryTarget: 'umd',
    globalObject: 'this',
    clean: true
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react']
          }
        }
      },
      {
        test: /\.css$/,
        use: [
          MiniCssExtractPlugin.loader,
          'css-loader',
          'postcss-loader'
        ]
      }
    ]
  },
  plugins: [
    new MiniCssExtractPlugin({
      filename: 'chatbot-widget.css'
    })
  ],
  resolve: {
    extensions: ['.js', '.jsx']
  },
  externals: {
    // Don't bundle React if it's available globally
    // react: 'React',
    // 'react-dom': 'ReactDOM'
  }
};