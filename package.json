{"name": "ai-form-builder", "version": "1.0.0", "description": "Dynamic form builder chatbot application", "main": "index.js", "scripts": {"start": "concurrently \"npm run server\" \"npm run client\"", "server": "cd server && npm run dev", "client": "cd client && npm start", "install-all": "npm install && cd client && npm install && cd ../server && npm install && cd ../cdn-widget && npm install", "build-widget": "node build-widget.js", "widget-dev": "cd cdn-widget && npm run dev", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["form-builder", "chatbot", "ai", "react", "node", "mongodb"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "concurrently": "^9.1.2", "node-fetch": "^3.3.2", "pdf-parse": "^1.1.1", "run": "^1.5.0"}}