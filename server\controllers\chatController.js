const axios = require('axios');

const Document = require('../models/Document');
const UnifiedConfig = require('../models/UnifiedConfig');
const Conversation = require('../models/Conversation');
const formMatcher = require('../utils/formMatcher');
const unifiedDynamicMatchingService = require('../services/unifiedDynamicMatchingService');
const conversationalFormService = require('../services/conversationalFormService');
const hybridFormService = require('../services/hybridFormService');

// Function to fetch leave balance from the API
const fetchLeaveBalance = async (userData, returnStructuredData = false) => {
  try {
    // Check if user data is available
    if (!userData || !userData.empId || !userData.token) {
      if (returnStructuredData) {
        return {
          success: false,
          message: "You need to be logged in to check your leave balance.",
          data: null
        };
      }
      return "You need to be logged in to check your leave balance.";
    }

    // Get current year
    const currentYear = new Date().getFullYear();

    // Call the leave balance API
    const response = await axios.get(
      `https://dev.budgie.co.in/budgie/v3/api/employee/leave/${userData.empId}/${currentYear}`,
      {
        headers: {
          'Authorization': `Bearer ${userData.token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    // Process the response
    if (response.data && response.data.status === true) {
      const leaveData = response.data.data;

      if (returnStructuredData) {
        // Return structured data for frontend processing
        if (leaveData && Array.isArray(leaveData) && leaveData.length > 0) {
          // Transform the data to a consistent format
          const transformedData = leaveData.map(leave => ({
            leaveTypeName: leave.leaveType || leave.type || 'Leave',
            balance: leave.balance || leave.availableLeaves || leave.remainingLeaves || 0
          }));

          return {
            success: true,
            data: transformedData,
            formattedResponse: transformedData.map((leave, index) =>
              `Record ${index + 1}:\n  leaveTypeName: ${leave.leaveTypeName}\n  balance: ${leave.balance}`
            ).join('\n\n')
          };
        } else {
          return {
            success: true,
            data: [],
            formattedResponse: "No leave balance information available for this year."
          };
        }
      } else {
        // Return formatted message (original behavior)
        let formattedMessage = "";

        if (leaveData) {
          if (Array.isArray(leaveData)) {
            if (leaveData.length === 0) {
              formattedMessage += "You don't have any leave records for this year.";
            } else {
              leaveData.forEach(leave => {
                const leaveType = leave.leaveType || leave.type || 'Leave';
                const balance = leave.balance || leave.availableLeaves || leave.remainingLeaves || 0;
                formattedMessage += `• ${leaveType}: ${balance} days remaining\n`;
              });
            }
          } else if (typeof leaveData === 'object') {
            // Handle case where data is a single object with multiple leave types
            if (Object.keys(leaveData).length === 0) {
              formattedMessage += "You don't have any leave records for this year.";
            } else if (leaveData.leaveDetails && Array.isArray(leaveData.leaveDetails)) {
              // If there's a nested array of leave details
              leaveData.leaveDetails.forEach(leave => {
                const leaveType = leave.leaveType || leave.type || 'Leave';
                const balance = leave.balance || leave.availableLeaves || leave.remainingLeaves || 0;
                formattedMessage += `• ${leaveType}: ${balance} days remaining\n`;
              });
            } else {
              // Try to extract leave information from the object directly
              const totalBalance = leaveData.totalBalance || leaveData.balance || leaveData.availableLeaves || 0;
              if (totalBalance > 0) {
                formattedMessage += `• Total leave balance: ${totalBalance} days\n`;
              }

              // Check for specific leave types in the object
              const leaveTypes = ['casual', 'sick', 'earned', 'compensatory', 'maternity', 'paternity'];
              let hasSpecificTypes = false;

              leaveTypes.forEach(type => {
                if (leaveData[type] !== undefined && leaveData[type] > 0) {
                  hasSpecificTypes = true;
                  formattedMessage += `• ${type.charAt(0).toUpperCase() + type.slice(1)} leave: ${leaveData[type]} days\n`;
                }
              });

              if (!hasSpecificTypes && !totalBalance) {
                formattedMessage += "No specific leave balance information available.";
              }
            }
          } else {
            formattedMessage += "No specific leave balance information available.";
          }
        } else {
          formattedMessage += "No leave balance information available for this year.";
        }

        return formattedMessage.trim();
      }
    } else {
      // Handle error or no data case
      const errorMessage = response.data?.message || "No information available";
      if (returnStructuredData) {
        return {
          success: false,
          message: `I couldn't retrieve your leave balance at the moment. ${errorMessage}`,
          data: null
        };
      }
      return `I couldn't retrieve your leave balance at the moment. ${errorMessage}`;
    }
  } catch (error) {
    console.error('Error fetching leave balance:', error);
    const errorMsg = "There was an error retrieving your leave balance. Please try again later.";
    if (returnStructuredData) {
      return {
        success: false,
        message: errorMsg,
        data: null
      };
    }
    return errorMsg;
  }
};



// @desc    Process chat message and detect form requests or analyze documents
// @route   POST /api/chat
// @access  Public
const processMessage = async (req, res) => {
  try {
    console.log('📥 Received chat request:', { message: req.body.message, conversationId: req.body.conversationId });
    let { message, conversationId } = req.body;

    // Validate message
    if (!message || typeof message !== 'string' || message.trim() === '') {
      return res.status(400).json({ message: 'Message is required and cannot be empty' });
    }

    // Find or create conversation
    let conversation;
    if (conversationId) {
      conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        return res.status(404).json({ message: 'Conversation not found' });
      }
    } else {
      conversation = await Conversation.create({
        title: message.substring(0, 30) + '...',
        messages: [],
      });
    }

    // Add user message to conversation (handle special form linking marker)
    let userMessageContent = message;
    if (message.startsWith('__START_FORM_DIRECT__')) {
      // For direct form linking, don't add the user message as it would show the special marker
      // The assistant response will be added later without a corresponding user message
      userMessageContent = null;
    }
    
    if (userMessageContent) {
      conversation.messages.push({
        role: 'user',
        content: userMessageContent,
      });
    }

    // Save conversation with user message
    await conversation.save();

    // Get user data from headers or request body for dynamic API injection
    const userData = {
      empId: req.headers['x-emp-id'] || req.body.empId,
      token: req.headers.authorization?.replace('Bearer ', ''),
      roleType: req.headers['x-role-type'] || req.body.roleType
    };

    // Store user data in conversation metadata for conversational forms
    if (!conversation.metadata) {
      conversation.metadata = {};
    }
    conversation.metadata.empId = userData.empId;
    conversation.metadata.token = userData.token;
    conversation.metadata.roleType = userData.roleType;
    
    // Save conversation with updated metadata
    await conversation.save();

    console.log('👤 User data available:', !!userData.empId, !!userData.token, !!userData.roleType);
    
    // Initialize variables for tracking form cancellation
    let formCancelled = false;

    // Check if user wants to cancel an active form flow
    if (message.toLowerCase().trim() === 'cancel' || message.toLowerCase().trim() === 'stop') {
      if (conversationalFormService.hasActiveFormFlow(conversation)) {
        const cancelResult = await conversationalFormService.cancelFormFlow(conversation);
        
        // Add assistant response
        conversation.messages.push({
          role: 'assistant',
          content: cancelResult.message,
          queryIntent: 'form_cancelled'
        });
        
        await conversation.save();
        
        return res.json({
          message: cancelResult.message,
          conversationId: conversation._id,
          queryIntent: 'form_cancelled'
        });
      } else if (hybridFormService.hasActiveHybridFlow(conversation)) {
        const cancelResult = await hybridFormService.cancelHybridFlow(conversation);
        
        // Add assistant response
        conversation.messages.push({
          role: 'assistant',
          content: cancelResult.message,
          queryIntent: 'form_cancelled'
        });
        
        await conversation.save();
        
        return res.json({
          message: cancelResult.message,
          conversationId: conversation._id,
          queryIntent: 'form_cancelled'
        });
      }
    }

    // Check if there's an active hybrid form flow FIRST (before conversational form flow)
    const hybridFlowCheck = {
      hasActiveFormFlow: !!conversation.activeFormFlow,
      formId: conversation.activeFormFlow?.formId,
      flowType: conversation.activeFormFlow?.flowType,
      isComplete: conversation.activeFormFlow?.isComplete,
      hasActiveHybridFlow: hybridFormService.hasActiveHybridFlow(conversation)
    };
    console.log('🔍 Checking hybrid flow:', hybridFlowCheck);
    
    if (hybridFormService.hasActiveHybridFlow(conversation)) {
      console.log('🔄 Processing hybrid form response');
      
      const hybridResult = await hybridFormService.processHybridResponse(conversation, message);
      
      // Check if form was cancelled due to validation error and we should process as new query
      if (hybridResult.shouldProcessAsNewQuery) {
        console.log('🔄 Hybrid form cancelled due to validation error, processing as new query:', hybridResult.originalMessage);
        
        // Update the last user message in conversation to match what we're processing
        if (conversation.messages.length > 0 && conversation.messages[conversation.messages.length - 1].role === 'user') {
          conversation.messages[conversation.messages.length - 1].content = hybridResult.originalMessage;
          await conversation.save();
        }
        
        // Process the original message through the normal flow
        message = hybridResult.originalMessage;
        formCancelled = true;
        // Continue with the rest of the processing logic below
      } else {
        // Add assistant response
        conversation.messages.push({
          role: 'assistant',
          content: hybridResult.message,
          formData: hybridResult.isHybridFormComplete ? hybridResult.formData : null,
          queryIntent: hybridResult.isHybridFormComplete ? 'hybrid_form_completed' : 
                      (hybridResult.transitionToForm ? 'hybrid_form_transition' : 'hybrid_form_conversation'),
          apiResponse: hybridResult.apiResponse || null
        });
        
        await conversation.save();
        
        const responseData = {
          message: hybridResult.message,
          conversationId: conversation._id,
          queryIntent: hybridResult.isHybridFormComplete ? (hybridResult.isGetRequest ? 'hybrid_form_data_retrieved' : 'hybrid_form_completed') : 
                      (hybridResult.transitionToForm ? 'hybrid_form_transition' : 'hybrid_form_conversation'),
          formData: hybridResult.isHybridFormComplete ? hybridResult.formData : null,
          formId: hybridResult.formId || null,
          needsSubmission: hybridResult.isHybridFormComplete ? hybridResult.needsSubmission : false,
          formConfig: hybridResult.isHybridFormComplete ? hybridResult.formConfig : null,
          apiResponse: hybridResult.apiResponse || null,
          isGetRequest: hybridResult.isGetRequest || false,
          // Hybrid flow specific properties
          isHybridFlow: true,
          isConversationalPhase: hybridResult.isConversationalPhase || false,
          transitionToForm: hybridResult.transitionToForm || false,
          formFields: hybridResult.formFields || null,
          collectedData: hybridResult.collectedData || null,
          conversationalData: hybridResult.conversationalData || null,
          hybridFlow: hybridResult.isHybridFormComplete ? null : {
            isActive: true,
            isConversationalPhase: hybridResult.isConversationalPhase || false,
            currentStep: hybridResult.currentStep,
            totalConversationalSteps: hybridResult.totalConversationalSteps,
            totalFormSteps: hybridResult.totalFormSteps,
            fieldName: hybridResult.fieldName,
            fieldType: hybridResult.fieldType,
            options: hybridResult.options,
            isValidationError: hybridResult.isValidationError || false
          }
        };
        
        // Log hybrid flow response for debugging
        console.log('🔄 Hybrid form response:', {
          isHybridFlow: responseData.isHybridFlow,
          isConversationalPhase: responseData.isConversationalPhase,
          fieldName: responseData.hybridFlow?.fieldName,
          fieldType: responseData.hybridFlow?.fieldType,
          hasOptions: !!(responseData.hybridFlow?.options && responseData.hybridFlow?.options.length > 0)
        });
        
        return res.json(responseData);
      }
    }

    // Check if there's an active conversational form flow (after hybrid form check)
    const conversationalFlowCheck = {
      hasActiveFormFlow: !!conversation.activeFormFlow,
      formId: conversation.activeFormFlow?.formId,
      flowType: conversation.activeFormFlow?.flowType,
      isComplete: conversation.activeFormFlow?.isComplete,
      hasActiveConversationalFlow: conversationalFormService.hasActiveFormFlow(conversation)
    };
    console.log('🔍 Checking conversational flow:', conversationalFlowCheck);
    
    if (conversationalFormService.hasActiveFormFlow(conversation)) {
      console.log('🎯 Processing conversational form response');
      
      const formResult = await conversationalFormService.processFormResponse(conversation, message);
      
      console.log('🔍 Form result:', {
        success: formResult.success,
        shouldProcessAsNewQuery: formResult.shouldProcessAsNewQuery,
        isValidationError: formResult.isValidationError,
        originalMessage: formResult.originalMessage
      });
      
      // Check if form was cancelled due to validation error and we should process as new query
      if (formResult.shouldProcessAsNewQuery) {
        console.log('🔄 Form cancelled due to validation error, processing as new query:', formResult.originalMessage);
        
        // Update the last user message in conversation to match what we're processing
        if (conversation.messages.length > 0 && conversation.messages[conversation.messages.length - 1].role === 'user') {
          conversation.messages[conversation.messages.length - 1].content = formResult.originalMessage;
          await conversation.save();
        }
        
        // Process the original message through the normal flow
        message = formResult.originalMessage;
        formCancelled = true;
        console.log('🔄 Continuing to normal flow with message:', message);
        console.log('🔄 About to continue to normal processing...');
        // Continue with the rest of the processing logic below
      } else {
        // Add assistant response
        conversation.messages.push({
          role: 'assistant',
          content: formResult.message,
          formData: formResult.isFormComplete ? formResult.formData : null,
          queryIntent: formResult.isFormComplete ? 'form_completed' : 'form_conversation',
          apiResponse: formResult.apiResponse || null
        });
        
        await conversation.save();
        
        return res.json({
          message: formResult.message,
          conversationId: conversation._id,
          queryIntent: formResult.isFormComplete ? (formResult.isGetRequest ? 'form_data_retrieved' : 'form_completed') : 'form_conversation',
          formData: formResult.isFormComplete ? formResult.formData : null,
          formId: formResult.isFormComplete ? formResult.formId : null,
          needsSubmission: formResult.isFormComplete ? formResult.needsSubmission : false,
          formConfig: formResult.isFormComplete ? formResult.formConfig : null,
          apiResponse: formResult.apiResponse || null,
          isGetRequest: formResult.isGetRequest || false,
          conversationalFlow: !formResult.isFormComplete ? {
            isActive: true,
            currentStep: formResult.currentStep,
            totalSteps: formResult.totalSteps,
            fieldName: formResult.fieldName,
            fieldType: formResult.fieldType,
            options: formResult.options,
            isValidationError: formResult.isValidationError || false
          } : null
        });
      }
    }

    console.log('🎯 Processing message with dynamic matching:', message);
    console.log('🎯 Starting normal flow processing...');

    // Check for direct form start marker (form linking without form name display)
    if (message.startsWith('__START_FORM_DIRECT__')) {
      const formName = message.replace('__START_FORM_DIRECT__', '');
      console.log('🔗 Direct form linking detected for:', formName);
      
      // Find the form by exact name match for direct linking
      const directMatchResults = await unifiedDynamicMatchingService.findMatches(formName);
      
      if (directMatchResults.bestMatch && directMatchResults.matchType === 'form') {
        console.log(`✅ Direct form matched: ${directMatchResults.bestMatch.name}`);
        
        const formData = directMatchResults.bestMatch;
        
        // Check if hybrid flow is enabled for this form
        if (formData.formConfig?.hybridFlow?.enabled) {
          console.log(`🔄 Starting hybrid form flow for linked form: ${formData.name}`);
          
          const hybridResult = await hybridFormService.startHybridFlow(conversation, formData);
          
          // Add assistant response WITHOUT adding a user message for the form name
          // This prevents the form name from being displayed in the chat
          conversation.messages.push({
            role: 'assistant',
            content: hybridResult.message,
            queryIntent: hybridResult.transitionToForm ? 'hybrid_form_transition' : 'hybrid_form_conversation',
            formData: null // Don't show full form data in conversational mode
          });
          
          await conversation.save();
          
          return res.json({
            message: hybridResult.message,
            conversationId: conversation._id,
            queryIntent: hybridResult.transitionToForm ? 'hybrid_form_transition' : 'hybrid_form_conversation',
            hybridFlow: {
              isActive: true,
              formId: formData._id,
              formName: formData.name,
              currentStep: hybridResult.currentStep,
              totalConversationalSteps: hybridResult.totalConversationalSteps,
              isConversationalPhase: !hybridResult.transitionToForm,
              transitionToForm: hybridResult.transitionToForm,
              conversationalFields: hybridResult.conversationalFields,
              options: hybridResult.options,
              fieldType: hybridResult.fieldType,
              fieldName: hybridResult.fieldName,
              collectedData: hybridResult.collectedData
            },
            isHybridFlow: true,
            isConversationalPhase: !hybridResult.transitionToForm,
            transitionToForm: hybridResult.transitionToForm,
            formFields: hybridResult.formFields,
            collectedData: hybridResult.collectedData,
            conversationalData: hybridResult.conversationalData,
            options: hybridResult.options,
            fieldType: hybridResult.fieldType,
            fieldName: hybridResult.fieldName,
            currentStep: hybridResult.currentStep,
            totalSteps: hybridResult.totalConversationalSteps,
            formConfig: formData.formConfig
          });
        } else {
          // For traditional forms, also start directly without displaying form name
          console.log(`📝 Starting traditional form for linked form: ${formData.name}`);
          
          const formResult = await conversationalFormService.startFormFlow(conversation, formData);
          
          // Add assistant response without form name
          conversation.messages.push({
            role: 'assistant',
            content: formResult.message,
            queryIntent: formResult.isFormComplete ? 'form_completed' : 'form_conversation',
            formData: formResult.isFormComplete ? formData : null
          });
          
          await conversation.save();
          
          return res.json({
            message: formResult.message,
            conversationId: conversation._id,
            queryIntent: formResult.isFormComplete ? 'form_completed' : 'form_conversation',
            conversationalFlow: {
              isActive: !formResult.isFormComplete,
              formId: formData._id,
              formName: formData.name,
              currentStep: formResult.currentStep,
              totalSteps: formResult.totalSteps,
              options: formResult.options,
              fieldType: formResult.fieldType,
              fieldName: formResult.fieldName
            },
            isFormFlow: !formResult.isFormComplete,
            options: formResult.options,
            fieldType: formResult.fieldType,
            fieldName: formResult.fieldName,
            currentStep: formResult.currentStep,
            totalSteps: formResult.totalSteps,
            formConfig: formData.formConfig,
            formData: formResult.isFormComplete ? formData : null
          });
        }
      } else {
        console.log('❌ No form found for direct linking:', formName);
        // Fall back to normal processing if direct form not found
      }
    }

    // Use unified dynamic matching service to find forms and APIs
    console.log('🔍 About to call unifiedDynamicMatchingService.findMatches with:', message);
    const matchResults = await unifiedDynamicMatchingService.findMatches(message);
    console.log('🔍 MatchResults received:', matchResults ? 'Yes' : 'No');
    
    let queryIntent = 'information';
    let responseMessage = '';
    let formData = null;
    let documentContext = null;
    let apiResponse = null;
    
    console.log('🔍 Dynamic match results:', {
      bestMatch: matchResults.bestMatch?.name,
      matchType: matchResults.matchType,
      confidence: matchResults.confidence,
      totalForms: matchResults.forms.length,
      totalApis: matchResults.apis.length
    });
    


    // Process matches based on confidence and type
    if (matchResults.bestMatch && matchResults.confidence >= 0.4) {
      // High confidence match found
      if (matchResults.matchType === 'form') {
        console.log(`✅ Form matched: ${matchResults.bestMatch.name} (confidence: ${matchResults.confidence})`);
        queryIntent = 'form';
        formData = matchResults.bestMatch;
        
        // Check if hybrid flow is enabled for this form
        if (matchResults.bestMatch.formConfig?.hybridFlow?.enabled) {
          console.log(`🔄 Starting hybrid form flow: ${matchResults.bestMatch.name}`);
          
          const hybridResult = await hybridFormService.startHybridFlow(conversation, matchResults.bestMatch);
          
          // Add assistant response
          conversation.messages.push({
            role: 'assistant',
            content: hybridResult.message,
            queryIntent: hybridResult.transitionToForm ? 'hybrid_form_transition' : 'hybrid_form_conversation',
            formData: null // Don't show full form data in conversational mode
          });
          
          await conversation.save();
          
          const startResponseData = {
            message: hybridResult.message,
            conversationId: conversation._id,
            queryIntent: hybridResult.transitionToForm ? 'hybrid_form_transition' : 'hybrid_form_conversation',
            isHybridFlow: true,
            isConversationalPhase: hybridResult.isConversationalPhase || false,
            transitionToForm: hybridResult.transitionToForm || false,
            formFields: hybridResult.formFields || null,
            collectedData: hybridResult.collectedData || null,
            formConfig: hybridResult.formConfig || null,
            hybridFlow: hybridResult.transitionToForm ? null : {
              isActive: true,
              isConversationalPhase: hybridResult.isConversationalPhase || false,
              currentStep: hybridResult.currentStep,
              totalConversationalSteps: hybridResult.totalConversationalSteps,
              totalFormSteps: hybridResult.totalFormSteps,
              fieldName: hybridResult.fieldName,
              fieldType: hybridResult.fieldType,
              options: hybridResult.options
            },
            matchResults: {
              confidence: matchResults.confidence,
              matchType: matchResults.matchType,
              totalMatches: (matchResults.forms?.length || 0) + (matchResults.apis?.length || 0)
            }
          };
          
          // Log hybrid form start for debugging
          console.log('🔄 Starting hybrid form:', {
            formName: matchResults.bestMatch.name,
            isHybridFlow: startResponseData.isHybridFlow,
            isConversationalPhase: startResponseData.isConversationalPhase,
            fieldName: startResponseData.hybridFlow?.fieldName,
            fieldType: startResponseData.hybridFlow?.fieldType,
            hasOptions: !!(startResponseData.hybridFlow?.options && startResponseData.hybridFlow?.options.length > 0)
          });
          
          return res.json(startResponseData);
        } else if (matchResults.bestMatch.formConfig?.conversationalFlow?.enabled) {
          console.log(`🎯 Starting conversational form flow: ${matchResults.bestMatch.name}`);
          
          const flowResult = await conversationalFormService.startFormFlow(conversation, matchResults.bestMatch);
          
          // Add assistant response
          conversation.messages.push({
            role: 'assistant',
            content: flowResult.message,
            queryIntent: 'form_conversation',
            formData: null // Don't show full form data in conversational mode
          });
          
          await conversation.save();
          
          return res.json({
            message: flowResult.message,
            conversationId: conversation._id,
            queryIntent: 'form_conversation',
            conversationalFlow: {
              isActive: true,
              currentStep: flowResult.currentStep,
              totalSteps: flowResult.totalSteps,
              fieldName: flowResult.fieldName,
              fieldType: flowResult.fieldType,
              options: flowResult.options
            },
            matchResults: {
              confidence: matchResults.confidence,
              matchType: matchResults.matchType,
              totalMatches: (matchResults.forms?.length || 0) + (matchResults.apis?.length || 0)
            }
          });
        } else {
          // Traditional form display or GET request handling
          const isGetRequest = matchResults.bestMatch.formConfig?.submitApiConfig?.method === 'GET';
          
          if (isGetRequest) {
            console.log(`🔍 GET request detected for form: ${matchResults.bestMatch.name}`);
            console.log('📋 Executing GET request immediately...');
            
            try {
              // Execute the GET request immediately for non-conversational forms
              const getResult = await unifiedDynamicMatchingService.submitDynamicForm(
                matchResults.bestMatch, 
                {}, // Empty form data for GET request
                userData
              );
              
              apiResponse = getResult;
              
              if (getResult.success && getResult.formattedResponse) {
                responseMessage = getResult.formattedResponse;
                queryIntent = 'form_data_retrieved';
                
                // Include form data for form linking
                formData = matchResults.bestMatch;
                console.log('🔗 Form linking config:', formData.formConfig?.formLinking);
              } else if (!getResult.success) {
                responseMessage = `${getResult.error || 'Failed to retrieve data. Please try again.'}`;
                queryIntent = 'form_error';
              }
              
              console.log('✅ GET request completed successfully');
            } catch (error) {
              console.error('❌ Error executing GET request:', error);
              responseMessage = `❌ **Error retrieving data**\n\n${error.message}`;
              queryIntent = 'form_error';
              apiResponse = {
                success: false,
                error: error.message
              };
            }
          } else {
            // Traditional form display for POST/PUT/PATCH
            responseMessage = matchResults.bestMatch.prompt || 
              `I found the "${matchResults.bestMatch.name}" form. Please fill out the required information:`;
            
            // Special handling for Leave Application form - fetch leave balance for frontend
            if (matchResults.bestMatch.name === 'Leave Application') {
              console.log('🍃 Leave Application form detected - fetching leave balance...');
              try {
                const leaveBalance = await fetchLeaveBalance(userData);
                // Store leave balance in apiResponse for frontend to use
                apiResponse = {
                  leaveBalance: leaveBalance,
                  success: true
                };
              } catch (error) {
                console.error('❌ Error fetching leave balance:', error);
                apiResponse = {
                  leaveBalance: "Could not fetch leave balance at this time.",
                  success: false
                };
              }
            }
          }
        }
        
      } else if (matchResults.matchType === 'api') {
        console.log(`🚀 Executing dynamic API: ${matchResults.bestMatch.name} (confidence: ${matchResults.confidence})`);
        queryIntent = 'api';

        // Special handling for Leave Balance API
        if (matchResults.bestMatch.name === 'Leave Balance API' ||
            matchResults.bestMatch.name.toLowerCase().includes('leave balance')) {
          console.log('🍃 Leave Balance API detected - fetching structured data...');

          const leaveBalanceResult = await fetchLeaveBalance(userData, true);

          if (leaveBalanceResult.success && leaveBalanceResult.data && leaveBalanceResult.data.length > 0) {
            responseMessage = `Here's your leave balance information:\n\n${leaveBalanceResult.formattedResponse}`;
            apiResponse = {
              success: true,
              data: leaveBalanceResult.data,
              formattedResponse: leaveBalanceResult.formattedResponse
            };
          } else {
            responseMessage = leaveBalanceResult.message || "No leave balance information available.";
            apiResponse = {
              success: false,
              message: leaveBalanceResult.message,
              data: null
            };
          }
          queryIntent = 'leave_balance_query';
        } else {
          // Handle other API calls using the unified service
          const apiResult = await unifiedDynamicMatchingService.executeDynamicApi(
            matchResults.bestMatch,
            userData
          );

          apiResponse = apiResult;

          // Format response using template or default formatter
          responseMessage = unifiedDynamicMatchingService.formatApiResponse(
            matchResults.bestMatch,
            apiResult
          );
        }

        console.log(`✅ API executed: ${matchResults.bestMatch.name} (success: ${apiResponse.success})`);
      }
      
    } else {
      // Check if this is a leave balance query that didn't match the API configuration
      const isLeaveBalanceQuery = message.toLowerCase().includes('leave balance') ||
                                  message.toLowerCase().includes('check my leave') ||
                                  message.toLowerCase().includes('show my leave') ||
                                  (message.toLowerCase().includes('leave') && message.toLowerCase().includes('balance'));

      if (isLeaveBalanceQuery) {
        console.log('🍃 Direct leave balance query detected - fetching data...');

        try {
          const leaveBalanceResult = await fetchLeaveBalance(userData, true);

          if (leaveBalanceResult.success && leaveBalanceResult.data && leaveBalanceResult.data.length > 0) {
            responseMessage = `Here's your leave balance information:\n\n${leaveBalanceResult.formattedResponse}`;
            apiResponse = {
              success: true,
              data: leaveBalanceResult.data,
              formattedResponse: leaveBalanceResult.formattedResponse
            };
            queryIntent = 'leave_balance_query';
          } else {
            responseMessage = leaveBalanceResult.message || "No leave balance information available.";
            apiResponse = {
              success: false,
              message: leaveBalanceResult.message,
              data: null
            };
            queryIntent = 'leave_balance_error';
          }
        } catch (error) {
          console.error('❌ Error fetching leave balance:', error);
          responseMessage = "There was an error retrieving your leave balance. Please try again later.";
          queryIntent = 'leave_balance_error';
        }
      } else {
        // No good match found, use document analysis
        console.log(`🔍 No meaningful match found (confidence: ${matchResults.confidence || 0}), using document analysis`);
        documentContext = await analyzeDocumentsForQuery(message);
        responseMessage = await processWithOllama(message, conversation.messages, documentContext);
        queryIntent = 'information';
      }
    }

    // Add assistant response to conversation
    conversation.messages.push({
      role: 'assistant',
      content: responseMessage,
      formData: formData,
      queryIntent: queryIntent,
      documentContext: documentContext ? { 
        relevantDocs: documentContext.relevantDocuments.length,
        hasContext: documentContext.context.length > 0 
      } : null,
      apiResponse: apiResponse, // Store API response if available
    });

    // Save conversation with assistant response
    await conversation.save();

    // Return the response
    console.log('🎯 About to send response:', {
      responseMessage: responseMessage?.substring(0, 100) + '...',
      queryIntent,
      hasFormData: !!formData,
      hasApiResponse: !!apiResponse,
      formCancelled: formCancelled
    });
    res.json({
      message: responseMessage,
      formData,
      conversationId: conversation._id,
      queryIntent: queryIntent,
      documentAnalysis: documentContext ? {
        relevantDocuments: documentContext.relevantDocuments.length,
        hasRelevantContent: documentContext.context.length > 0
      } : null,
      apiResponse: apiResponse, // Include API response if available
      formCancelled: formCancelled, // Flag to indicate form was cancelled
      matchResults: {
        confidence: matchResults?.confidence || 0,
        matchType: matchResults?.matchType,
        totalMatches: (matchResults?.forms?.length || 0) + (matchResults?.apis?.length || 0)
      }
    });
  } catch (error) {
    console.error('❌ Error processing message:', error);
    console.error('Error stack:', error.stack);
    console.error('Error occurred while processing message:', req.body.message);
    console.error('ConversationId:', req.body.conversationId);
    res.status(500).json({ 
      message: 'Sorry, there was an error processing your request. Please try again.',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
};

const pdfParse = require('pdf-parse');

// @desc    Analyze documents for relevance to user query (basic version)
const analyzeDocumentsForQuery = async (query) => {
  try {
    console.log('Analyzing documents for query:', query);
    
    // Get all documents from database (broadened search criteria)
    const documents = await Document.find({});
    
    console.log(`Total documents found: ${documents.length}`);
    
    if (documents.length === 0) {
      console.log('No documents found in database');
      return {
        relevantDocuments: [],
        context: '',
        summary: 'No documents available for analysis.'
      };
    }

    const relevantDocuments = [];
    const documentContents = [];

    // Process each document
    for (const doc of documents) {
      try {
        console.log(`Processing document: ${doc.name}, Type: ${doc.fileType}`);
        
        let textContent = '';
        
        // Try to extract text content from various file types
        if (doc.file) {
          if (doc.fileType.includes('text') || doc.fileType.includes('application/json')) {
            textContent = doc.file.toString('utf8');
          } else if (doc.fileType.includes('application/pdf')) {
            // Parse PDF content
            try {
              const pdfData = await pdfParse(doc.file);
              textContent = pdfData.text;
              console.log(`Extracted ${textContent.length} characters from PDF`);
            } catch (pdfError) {
              console.error(`Error parsing PDF ${doc.name}:`, pdfError);
            }
          } else {
            console.log(`Unsupported file type: ${doc.fileType} for document: ${doc.name}`);
            continue;
          }
        } else {
          console.log(`No file content found for document: ${doc.name}`);
          continue;
        }

        if (textContent.length === 0) {
          console.log(`No text content extracted from document: ${doc.name}`);
          continue;
        }

        // Check if document content is relevant to the query
        const relevanceScore = calculateRelevanceScore(query, textContent, doc.name, doc.description);
        
        console.log(`Relevance score for ${doc.name}: ${relevanceScore}`);

        if (relevanceScore > 0.1) { // Threshold for relevance
          relevantDocuments.push({
            id: doc._id,
            name: doc.name,
            description: doc.description,
            fileType: doc.fileType,
            relevanceScore: relevanceScore,
            content: textContent.substring(0, 5000) // Increased from 2000
          });
          
          // Increase the content length limit
          documentContents.push({
            name: doc.name,
            content: textContent.substring(0, 5000) // Increased from 1500
          });
        }
      } catch (docError) {
        console.error(`Error processing document ${doc.name}:`, docError);
      }
    }

    // Sort by relevance score
    relevantDocuments.sort((a, b) => b.relevanceScore - a.relevanceScore);
    
    // Create context from most relevant documents (limit to top 3)
    const topRelevantDocs = relevantDocuments.slice(0, 3);
    let context = '';
    
    if (topRelevantDocs.length > 0) {
      context = topRelevantDocs.map(doc => 
        `Document: "${doc.name}"\nContent: ${doc.content}\n---\n`
      ).join('');
    }

    console.log(`Found ${relevantDocuments.length} relevant documents for query`);
    console.log('📄 Document context being returned:', {
      relevantDocsCount: topRelevantDocs.length,
      contextLength: context.length,
      contextPreview: context.substring(0, 200) + '...'
    });
    
    return {
      relevantDocuments: topRelevantDocs,
      context: context,
      summary: relevantDocuments.length > 0 
        ? `Found ${relevantDocuments.length} relevant document(s) to help answer your question.`
        : 'No directly relevant documents found, but I can still help with general information.'
    };

  } catch (error) {
    console.error('Error analyzing documents:', error);
    return {
      relevantDocuments: [],
      context: '',
      summary: 'Error occurred while analyzing documents.'
    };
  }
};

// Helper function to calculate relevance score
const calculateRelevanceScore = (query, content, fileName, description) => {
  const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2);
  const contentLower = content.toLowerCase();
  const fileNameLower = fileName.toLowerCase();
  const descriptionLower = (description || '').toLowerCase();
  
  let score = 0;
  let totalWords = queryWords.length;
  
  if (totalWords === 0) return 0;
  
  queryWords.forEach(word => {
    // Higher weight for matches in file name and description
    if (fileNameLower.includes(word)) {
      score += 0.3;
    }
    if (descriptionLower.includes(word)) {
      score += 0.2;
    }
    
    // Count occurrences in content
    const regex = new RegExp(word, 'gi');
    const matches = content.match(regex);
    if (matches) {
      // Logarithmic scoring to prevent single word from dominating
      score += Math.min(0.5, matches.length * 0.1);
    }
  });
  
  return score / totalWords;
};

// @desc    Submit form data and process API request
// @route   POST /api/chat/submit-form
// @access  Public
const submitForm = async (req, res) => {
  try {
    const { formId, formData, conversationId, userContext, isHybridForm } = req.body;

    // Find the form configuration
    const form = await UnifiedConfig.findById(formId);
    if (!form || form.type !== 'form') {
      return res.status(404).json({ message: 'Form configuration not found' });
    }

    // Find the conversation
    const conversation = await Conversation.findById(conversationId);
    if (!conversation) {
      return res.status(404).json({ message: 'Conversation not found' });
    }

    // Get user data for submission
    const userData = {
      empId: req.headers['x-emp-id'] || userContext?.empId,
      token: req.headers.authorization?.replace('Bearer ', '') || userContext?.token,
      roleType: req.headers['x-role-type'] || userContext?.roleType
    };

    console.log('📝 Form submission details:');
    console.log('- Form name:', form.name);
    console.log('- Form data:', formData);
    console.log('- Is hybrid form:', isHybridForm);
    console.log('- User data:', { empId: !!userData.empId, token: !!userData.token, roleType: !!userData.roleType });

    let submitResult;

    // Check if this is a hybrid form completion
    if (isHybridForm && hybridFormService.hasActiveHybridFlow(conversation)) {
      console.log('🔄 Handling hybrid form submission');
      submitResult = await hybridFormService.handleFormSubmission(conversation, form, formData);
    } else {
      // Regular form submission
      submitResult = await unifiedDynamicMatchingService.submitDynamicForm(form, formData, userData, formId, conversationId);
    }

    // Add form submission message to conversation
    conversation.messages.push({
      role: 'user',
      content: isHybridForm ? 'Hybrid form submitted' : 'Form submitted',
      formData: formData,
      formConfig: form.name,
    });

    // Handle hybrid form submission response
    if (isHybridForm && submitResult.isConversationalPhase) {
      // Transitioning to post-form conversation
      conversation.messages.push({
        role: 'assistant',
        content: submitResult.message,
        queryIntent: 'hybrid_form_conversation',
        isHybridFlow: true,
        isConversationalPhase: true,
        fieldType: submitResult.fieldType,
        fieldName: submitResult.fieldName,
        options: submitResult.options,
        phase: submitResult.phase
      });

      await conversation.save();

      return res.json({
        message: submitResult.message,
        conversationId: conversation._id,
        queryIntent: 'hybrid_form_conversation',
        isHybridFlow: true,
        isConversationalPhase: true,
        fieldType: submitResult.fieldType,
        fieldName: submitResult.fieldName,
        options: submitResult.options,
        currentStep: submitResult.currentStep,
        totalConversationalSteps: submitResult.totalConversationalSteps,
        totalFormSteps: submitResult.totalFormSteps,
        collectedData: submitResult.collectedData,
        phase: submitResult.phase,
        hybridFlow: {
          isActive: true,
          isConversationalPhase: true,
          currentStep: submitResult.currentStep,
          totalConversationalSteps: submitResult.totalConversationalSteps,
          totalFormSteps: submitResult.totalFormSteps,
          fieldName: submitResult.fieldName,
          fieldType: submitResult.fieldType,
          options: submitResult.options,
          phase: submitResult.phase
        }
      });
    } else {
      // Regular form submission or hybrid form completion
      conversation.messages.push({
        role: 'assistant',
        content: submitResult.success ? submitResult.message : `Failed to submit form: ${submitResult.error}`,
        apiResponse: submitResult,
        queryIntent: isHybridForm ? 'hybrid_form_completed' : 'form_completed',
      });

      await conversation.save();

      return res.json({
        message: submitResult.message,
        success: submitResult.success,
        apiResponse: submitResult,
        conversationId: conversation._id,
        status: submitResult.status,
        isHybridForm: isHybridForm,
        queryIntent: isHybridForm ? 'hybrid_form_completed' : 'form_completed'
      });
    }
  } catch (error) {
    console.error('Error submitting form:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Get conversation history
// @route   GET /api/chat/conversations/:id
// @access  Public
const getConversation = async (req, res) => {
  try {
    const conversation = await Conversation.findById(req.params.id);
    if (!conversation) {
      return res.status(404).json({ message: 'Conversation not found' });
    }
    res.json(conversation);
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Get all conversations
// @route   GET /api/chat/conversations
// @access  Public
const getConversations = async (req, res) => {
  try {
    const conversations = await Conversation.find({})
      .select('title createdAt updatedAt')
      .sort({ updatedAt: -1 });
    res.json(conversations);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// Enhanced helper function to process message with Ollama and document context
const processWithOllama = async (message, conversationHistory, documentContext) => {
  try {
    console.log('🤖 Calling Ollama API with URL:', process.env.OLLAMA_API_URL);
    
    // Check if Ollama URL is configured
    if (!process.env.OLLAMA_API_URL) {
      console.error('❌ OLLAMA_API_URL is not configured');
      return 'I apologize, but the AI service is not properly configured. Please contact your administrator.';
    }
    // Format conversation history for Ollama
    const messages = conversationHistory.map(msg => ({
      role: msg.role,
      content: msg.content,
    }));

    // Enhanced system message 
    let systemMessage = `You are a helpful assistant that helps users with forms and answers their questions using available documents and information.

When users ask questions:
1. If they're asking for information, first check if there are relevant documents available that can help answer their question. Use the document content to provide accurate, specific answers based on the available information.

2. If they're requesting to create, fill out, or submit a form, acknowledge their request and explain that you'll help them with the form process.

3. Always prioritize information from the provided documents when available, and clearly indicate when your response is based on document content vs. general knowledge.

The system will automatically detect whether the query is informational or form-related and handle the form display separately.`;

    // Add document context if available
    console.log('🔍 Document context check:', {
      hasDocumentContext: !!documentContext,
      hasContext: documentContext?.context?.length > 0,
      contextLength: documentContext?.context?.length || 0,
      relevantDocsCount: documentContext?.relevantDocuments?.length || 0
    });
    
    if (documentContext && documentContext.context.length > 0) {
      console.log('✅ Using document context for response');
      
      // Add document context as a separate message instead of in system message
      messages.push({
        role: 'user',
        content: `Here are the relevant documents for reference:\n\n${documentContext.context}\n\nPlease use this information to answer my question.`
      });
      
      systemMessage += `\n\nIMPORTANT: The user will provide you with relevant documents. You MUST use the information from those documents to answer their questions. DO NOT provide general knowledge if the documents contain relevant information. Always start your response by mentioning that you're using information from the provided documents.`;
    } else {
      console.log('❌ No document context available - using general knowledge');
      systemMessage += `\n\nNo relevant documents were found for this query. Provide helpful general information based on your knowledge.`;
    }

    messages.unshift({
      role: 'system',
      content: systemMessage,
    });

    console.log('📤 System message length:', systemMessage.length);
    console.log('📤 System message (first 500 chars):', systemMessage.substring(0, 500) + '...');
    if (documentContext && documentContext.context.length > 0) {
      console.log('📄 Document content preview:', documentContext.context.substring(0, 300) + '...');
    }

    // Call Ollama API with the correct endpoint
    const response = await axios.post(`${process.env.OLLAMA_API_URL}/chat`, {
      model: 'llama3.2',
      messages: messages,
      stream: false,
    });

    // The response format is different in the new API
    return response.data.message.content;
  } catch (error) {
    console.error('❌ Error calling Ollama:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      response: error.response?.data,
      status: error.response?.status
    });
    
    if (error.code === 'ECONNREFUSED') {
      return 'I apologize, but the AI service is currently unavailable. Please make sure Ollama is running on your system.';
    }
    
    return 'I apologize, but I encountered an error processing your request. Please try again.';
  }
};

// Helper function to detect form requests in messages
const detectFormRequest = async (message) => {
  // Get all form names from the database
  const forms = await UnifiedConfig.find({ type: 'form', isActive: true }).select('name description');
  
  if (forms.length === 0) {
    return null;
  }
  
  // Check if any form name is mentioned in the message
  const normalizedMessage = message.toLowerCase();
  
  // First, try exact form name matches
  for (const form of forms) {
    const formName = form.name.toLowerCase();
    // Check for exact form name mentions
    if (normalizedMessage.includes(formName)) {
      console.log(`Exact form name match found: "${form.name}"`);
      return form.name;
    }
  }
  
  // Second, try to match individual words from form names
  // This helps with cases like "Show my leave balance" for a form named "Leave Balance"
  for (const form of forms) {
    const formNameWords = form.name.toLowerCase().split(/\s+/);
    
    // For multi-word form names, check if all significant words are present
    if (formNameWords.length > 1) {
      // Filter out common words that might not be mentioned
      const significantWords = formNameWords.filter(word => 
        word.length > 3 && !['form', 'the', 'and', 'for', 'with', 'this', 'that', 'from', 'your'].includes(word)
      );
      
      // If all significant words are present in the message, it's likely referring to this form
      const allSignificantWordsPresent = significantWords.every(word => 
        normalizedMessage.includes(word)
      );
      
      if (allSignificantWordsPresent && significantWords.length > 0) {
        console.log(`All significant words match for form: "${form.name}"`);
        return form.name;
      }
    }
    
    // For single-word form names or if the above check fails
    // Check if any significant word from the form name appears in the message
    const significantFormWords = formNameWords.filter(word => 
      word.length > 3 && !['form', 'the', 'and', 'for', 'with', 'this', 'that', 'from', 'your'].includes(word)
    );
    
    // Special case for "Leave Balance" form and "Show my leave balance" query
    if (form.name.toLowerCase().includes('leave') && normalizedMessage.includes('leave')) {
      console.log(`Special case match for leave-related form: "${form.name}"`);
      return form.name;
    }
    
    // Special case for "balance" in the query
    if (form.name.toLowerCase().includes('balance') && normalizedMessage.includes('balance')) {
      console.log(`Special case match for balance-related form: "${form.name}"`);
      return form.name;
    }
    
    // Check if any significant word from the form name appears in the message
    const nameMatch = significantFormWords.some(word => 
      normalizedMessage.includes(word)
    );
    
    if (nameMatch) {
      console.log(`Significant word match for form: "${form.name}"`);
      return form.name;
    }
  }
  
  // If still no match, check form descriptions
  for (const form of forms) {
    if (form.description) {
      const formDescWords = form.description.toLowerCase().split(/\s+/);
      
      // Check if any significant word from the description appears in the message
      const significantDescWords = formDescWords.filter(word => 
        word.length > 3 && !['form', 'the', 'and', 'for', 'with', 'this', 'that', 'from', 'your'].includes(word)
      );
      
      const descMatch = significantDescWords.some(word => 
        normalizedMessage.includes(word)
      );
      
      if (descMatch) {
        console.log(`Description word match for form: "${form.name}"`);
        return form.name;
      }
    }
  }
  
  // Check for specific keywords that might indicate a form type
  const formTypeKeywords = {
    'leave': ['leave', 'vacation', 'time off', 'day off', 'absence', 'apply leave', 'leave apply', 'leave application', 'take leave', 'request leave', 'leave request'],
    'balance': ['balance', 'remaining', 'available'],
    'request': ['request', 'apply', 'application'],
    'feedback': ['feedback', 'review', 'opinion', 'survey'],
    'contact': ['contact', 'reach out', 'message', 'inquiry'],
    'registration': ['register', 'sign up', 'join', 'enroll']
  };
  
  // Check if any form matches these keywords
  for (const form of forms) {
    const formNameLower = form.name.toLowerCase();
    
    for (const [formType, keywords] of Object.entries(formTypeKeywords)) {
      // If the form name contains this form type
      if (formNameLower.includes(formType)) {
        // Check if any of the related keywords are in the message
        const keywordMatch = keywords.some(keyword => normalizedMessage.includes(keyword));
        if (keywordMatch) {
          console.log(`Form type keyword match for "${form.name}" using keyword category "${formType}"`);
          return form.name;
        }
      }
    }
  }
  
  // If no specific match is found, but the query is clearly form-related,
  // try to find the most relevant form based on the message content
  const formKeywords = [
    'create', 'submit', 'fill', 'complete', 'make', 'add', 'generate', 
    'show', 'view', 'see', 'check', 'balance', 'request', 'form',
    'leave', 'vacation', 'time off', 'application'
  ];
  
  const containsActionKeyword = formKeywords.some(keyword => 
    normalizedMessage.includes(keyword)
  );
  
  if (containsActionKeyword) {
    // Find the form with the highest similarity score
    let bestMatch = null;
    let highestScore = 0;
    
    for (const form of forms) {
      const score = formMatcher.calculateSimilarity(message, form);
      if (score > highestScore) {
        highestScore = score;
        bestMatch = form.name;
      }
    }
    
    // Only return the best match if the score is above a threshold
    if (highestScore > 0.2) {
      console.log(`Similarity match for form: "${bestMatch}" with score ${highestScore}`);
      return bestMatch;
    }
    
    // If no good match is found but the user clearly wants a form,
    // return the first form as a fallback
    if (normalizedMessage.includes('form') || 
        normalizedMessage.includes('balance') || 
        normalizedMessage.includes('request') ||
        normalizedMessage.includes('leave')) {
      console.log(`Fallback to first form: "${forms[0].name}"`);
      return forms[0].name;
    }
  }

  console.log('No form match found');
  return null;
};

// Helper function to process API requests based on form configuration
const processApiRequest = async (apiConfig, formData, userContext = null) => {
  try {
    const { method, endpoint, headers, authType, authDetails } = apiConfig;
    
    // Prepare headers - create a clean object instead of spreading the Map
    const requestHeaders = {};
    
    // If headers is a Map (from Mongoose), convert it properly
    if (headers && typeof headers.forEach === 'function') {
      // This handles both Map and Mongoose's DocumentArray
      headers.forEach((value, key) => {
        // Only add if it's a valid header (not internal Mongoose properties)
        if (typeof key === 'string' && !key.startsWith('$') && !key.startsWith('_')) {
          requestHeaders[key] = value;
        }
      });
    } else if (headers && typeof headers === 'object') {
      // If it's a regular object, filter out any internal properties
      Object.keys(headers).forEach(key => {
        if (!key.startsWith('$') && !key.startsWith('_')) {
          requestHeaders[key] = headers[key];
        }
      });
    }
    
    // Add Content-Type header if not present
    if (!requestHeaders['Content-Type'] && !requestHeaders['content-type']) {
      requestHeaders['Content-Type'] = 'application/json';
    }
    
    // Add authorization if needed
    if (authType === 'basic') {
      const { username, password } = authDetails;
      const auth = Buffer.from(`${username}:${password}`).toString('base64');
      requestHeaders['Authorization'] = `Basic ${auth}`;
    } else if (authType === 'bearer') {
      requestHeaders['Authorization'] = `Bearer ${authDetails.token}`;
    } else if (authType === 'apiKey') {
      const { key, value, in: location } = authDetails;
      if (location === 'header' && key) {
        requestHeaders[key] = value;
      }
      // Handle query params for apiKey if needed
    }

    // Add token from userContext if available (for leave forms)
    if (userContext && userContext.token) {
      requestHeaders['Authorization'] = `Bearer ${userContext.token}`;
    }

    console.log('Request headers:', requestHeaders);
    console.log('Form data being sent:', formData);

    // Make the API request
    const response = await axios({
      method: method.toLowerCase(),
      url: endpoint,
      headers: requestHeaders,
      data: method !== 'GET' ? formData : undefined,
      params: method === 'GET' ? formData : undefined,
    });

    return {
      status: response.status,
      data: response.data,
    };
  } catch (error) {
    console.error('Error making API request:', error);
    
    // Log more detailed error information
    if (error.response) {
      console.error('Response error data:', error.response.data);
      console.error('Response error status:', error.response.status);
      console.error('Response error headers:', error.response.headers);
    } else if (error.request) {
      console.error('Request error:', error.request);
    } else {
      console.error('Error message:', error.message);
    }
    
    // Return a more detailed error object
    return {
      error: true,
      status: error.response?.status || 500,
      message: error.message,
      data: error.response?.data || {},
      details: error.stack
    };
  }
};

module.exports = {
  processMessage,
  submitForm,
  getConversation,
  getConversations,
};