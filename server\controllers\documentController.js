const Document = require('../models/Document');

// @desc    Upload multiple documents
// @route   POST /api/documents/upload
// @access  Public
const uploadDocuments = async (req, res) => {
  try {
    console.log('Upload request received');
    console.log('Files:', req.files ? req.files.length : 'No files');
    console.log('Body:', req.body);
    
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ message: 'No files uploaded' });
    }

    const uploadedDocs = [];
    const failedUploads = [];

    // Process each uploaded file
    for (const file of req.files) {
      try {
        console.log(`Processing file: ${file.originalname}, size: ${file.size}, type: ${file.mimetype}`);
        
        const newDocument = new Document({
          name: file.originalname,
          description: req.body.description || '',
          fileType: file.mimetype,
          fileSize: file.size,
          file: file.buffer,
          uploadedBy: req.body.uploadedBy || 'Anonymous',
          tags: req.body.tags ? req.body.tags.split(',').map(tag => tag.trim()) : [],
        });

        const savedDocument = await newDocument.save();
        console.log(`Document saved with ID: ${savedDocument._id}`);
        
        uploadedDocs.push({
          id: savedDocument._id,
          name: savedDocument.name,
          fileType: savedDocument.fileType,
          fileSize: savedDocument.fileSize,
          createdAt: savedDocument.createdAt,
        });
      } catch (error) {
        console.error(`Error uploading file ${file.originalname}:`, error);
        failedUploads.push({
          name: file.originalname,
          error: error.message,
        });
      }
    }

    if (uploadedDocs.length === 0) {
      return res.status(500).json({
        message: 'All uploads failed',
        failedUploads,
      });
    }

    res.status(201).json({
      message: `Successfully uploaded ${uploadedDocs.length} document(s)${failedUploads.length > 0 ? `, ${failedUploads.length} failed` : ''}`,
      uploadedDocuments: uploadedDocs,
      failedUploads: failedUploads.length > 0 ? failedUploads : undefined,
    });
  } catch (error) {
    console.error('Error in document upload:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Get all documents (metadata only, not the file content)
// @route   GET /api/documents
// @access  Public
const getDocuments = async (req, res) => {
  console.log('GET /api/documents endpoint called');
  try {
    console.log('Querying documents from MongoDB...');
    const documents = await Document.find({})
      .select('-file') // Exclude the file data to reduce response size
      .sort({ createdAt: -1 });
    
    console.log(`Found ${documents.length} documents`);
    res.json(documents);
  } catch (error) {
    console.error('Error fetching documents:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Get document by ID
// @route   GET /api/documents/:id
// @access  Public
const getDocumentById = async (req, res) => {
  try {
    const document = await Document.findById(req.params.id).select('-file');
    
    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }
    
    res.json(document);
  } catch (error) {
    console.error('Error fetching document by ID:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Download document file
// @route   GET /api/documents/:id/download
// @access  Public
const downloadDocument = async (req, res) => {
  try {
    const document = await Document.findById(req.params.id);
    
    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }
    
    // Set appropriate headers for file download
    res.set({
      'Content-Type': document.fileType,
      'Content-Disposition': `attachment; filename="${document.name}"`,
      'Content-Length': document.fileSize,
    });
    
    // Send the file buffer
    res.send(document.file);
  } catch (error) {
    console.error('Error downloading document:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Delete document
// @route   DELETE /api/documents/:id
// @access  Public
const deleteDocument = async (req, res) => {
  try {
    const document = await Document.findByIdAndDelete(req.params.id);
    
    if (!document) {
      return res.status(404).json({ message: 'Document not found' });
    }
    
    res.json({ message: 'Document deleted successfully' });
  } catch (error) {
    console.error('Error deleting document:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

// @desc    Search documents
// @route   GET /api/documents/search
// @access  Public
const searchDocuments = async (req, res) => {
  try {
    const { query } = req.query;
    
    if (!query) {
      return res.status(400).json({ message: 'Search query is required' });
    }
    
    const documents = await Document.find({
      $or: [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $regex: query, $options: 'i' } },
      ],
    }).select('-file');
    
    res.json(documents);
  } catch (error) {
    console.error('Error searching documents:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
};

module.exports = {
  uploadDocuments,
  getDocuments,
  getDocumentById,
  downloadDocument,
  deleteDocument,
  searchDocuments,
};