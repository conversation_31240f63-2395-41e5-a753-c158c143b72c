const express = require('express');
const dotenv = require('dotenv');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');
const connectDB = require('./config/db');

// Load environment variables
dotenv.config();

// Connect to MongoDB
connectDB();

// Initialize Express
const app = express();

// Middleware
app.use(cors({
  origin: true, // Allow all origins for CDN widget embedding
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'x-emp-id', 'x-role-type'],
  credentials: false // Set to false for CDN usage
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Logging in development mode
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Routes
console.log('Registering auth routes...');
app.use('/api/auth', require('./routes/authRoutes'));
console.log('Registering chat routes...');
app.use('/api/chat', require('./routes/chatRoutes'));
console.log('Registering document routes...');
try {
  const documentRoutes = require('./routes/documentRoutes');
  app.use('/api/documents', documentRoutes);
  console.log('Document routes registered successfully');
} catch (error) {
  console.error('Error registering document routes:', error);
}
console.log('Registering unified configuration routes...');
app.use('/api/configs', require('./routes/unifiedConfigRoutes'));
app.use('/api/unified-configs', require('./routes/unifiedConfigRoutes'));

// Register API Configuration routes
console.log('Registering API configuration routes...');
app.use('/api/api-configs', require('./routes/apiConfigRoutes'));

// Legacy route compatibility
console.log('Setting up legacy route compatibility...');
app.use('/api/unifiedconfigs', require('./routes/unifiedConfigRoutes'));
app.use('/api/api-configs', require('./routes/unifiedConfigRoutes'));

// Serve CDN files
console.log('Setting up CDN file serving...');
app.use('/cdn', express.static(path.join(__dirname, '../client/cdn/dist')));

// Serve chatbot widget directly
app.get('/chatbot-widget.js', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/cdn/dist/chatbot-widget.js'));
});

app.get('/chatbot-widget.css', (req, res) => {
  res.sendFile(path.join(__dirname, '../client/cdn/dist/chatbot-widget.css'));
});

// Basic route
app.get('/', (req, res) => {
  res.send('API is running...');
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({
    message: 'Server Error',
    stack: process.env.NODE_ENV === 'production' ? null : err.stack,
  });
});

// Start server
const PORT = process.env.PORT || 8517;
app.listen(PORT, () => {
  console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
});