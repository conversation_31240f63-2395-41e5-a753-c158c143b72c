const mongoose = require('mongoose');

const ApiConfigSchema = new mongoose.Schema({
  // Basic Configuration
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  description: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    required: true,
    enum: ['api', 'form'],
    default: 'api',
    index: true
  },
  
  // Triggering Configuration
  keywords: [{
    type: String,
    trim: true,
    index: true
  }],
  triggerPhrases: [{
    type: String,
    trim: true,
    index: true
  }],
  prompt: {
    type: String,
    default: ''
  },
  
  // Status and Priority
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  priority: {
    type: Number,
    default: 0,
    index: true
  },
  category: {
    type: String,
    default: 'general',
    index: true
  },
  tags: [{
    type: String,
    trim: true
  }],
  
  // API Configuration (for type: 'api')
  apiConfig: {
    endpoint: {
      type: String,
      required: function() { return this.type === 'api'; }
    },
    method: {
      type: String,
      enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      default: 'GET'
    },
    headers: {
      type: Map,
      of: String,
      default: new Map()
    },
    queryParams: {
      type: Map,
      of: String,
      default: new Map()
    },
    bodyTemplate: {
      type: String,
      default: ''
    },
    
    // Authentication
    authType: {
      type: String,
      enum: ['none', 'bearer', 'basic', 'apikey', 'custom'],
      default: 'none'
    },
    authConfig: {
      token: String,
      username: String,
      password: String,
      apiKey: String,
      apiKeyHeader: String,
      customHeaders: {
        type: Map,
        of: String,
        default: new Map()
      }
    },
    
    // Response Configuration
    responseTemplate: {
      type: String,
      default: ''
    },
    responseMapping: {
      type: Map,
      of: String,
      default: new Map()
    },
    
    // Request Configuration
    timeout: {
      type: Number,
      default: 30000,
      min: 1000,
      max: 300000
    },
    retryConfig: {
      enabled: {
        type: Boolean,
        default: true
      },
      maxRetries: {
        type: Number,
        default: 3,
        min: 0,
        max: 10
      },
      retryDelay: {
        type: Number,
        default: 1000,
        min: 100,
        max: 10000
      }
    },
    
    // Data Injection
    dataInjection: {
      injectUserData: {
        type: Boolean,
        default: false
      },
      userDataFields: [{
        type: String,
        enum: ['empId', 'token', 'roleType', 'userId', 'email', 'department']
      }],
      requiredFields: [{
        name: String,
        type: {
          type: String,
          enum: ['string', 'number', 'boolean', 'date', 'array', 'object']
        },
        required: Boolean,
        defaultValue: mongoose.Schema.Types.Mixed
      }]
    }
  },
  
  // Form Configuration (for type: 'form')
  formConfig: {
    fields: [{
      name: {
        type: String,
        required: true
      },
      label: {
        type: String,
        required: true
      },
      type: {
        type: String,
        enum: ['text', 'email', 'password', 'number', 'date', 'select', 'multiselect', 'textarea', 'checkbox', 'radio', 'file'],
        required: true
      },
      required: {
        type: Boolean,
        default: false
      },
      placeholder: String,
      defaultValue: mongoose.Schema.Types.Mixed,
      options: [{
        label: String,
        value: mongoose.Schema.Types.Mixed
      }],
      validation: {
        minLength: Number,
        maxLength: Number,
        min: Number,
        max: Number,
        pattern: String,
        customValidation: String
      },
      readonly: {
        type: Boolean,
        default: false
      },
      hidden: {
        type: Boolean,
        default: false
      }
    }],
    
    // Form Submit Configuration
    submitApiConfig: {
      endpoint: {
        type: String,
        required: function() { return this.type === 'form'; }
      },
      method: {
        type: String,
        enum: ['POST', 'PUT', 'PATCH'],
        default: 'POST'
      },
      headers: {
        type: Map,
        of: String,
        default: new Map()
      },
      authType: {
        type: String,
        enum: ['none', 'bearer', 'basic', 'apikey'],
        default: 'bearer'
      },
      authConfig: {
        token: String,
        username: String,
        password: String,
        apiKey: String,
        apiKeyHeader: String
      },
      dataMapping: {
        type: Map,
        of: String,
        default: new Map()
      },
      successMessage: {
        type: String,
        default: 'Form submitted successfully!'
      },
      errorMessage: {
        type: String,
        default: 'Failed to submit form. Please try again.'
      },
      redirectUrl: String
    },
    
    prefillData: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      default: new Map()
    }
  },
  
  // Metadata
  createdBy: {
    type: String,
    default: 'system'
  },
  updatedBy: {
    type: String,
    default: 'system'
  },
  
  // Usage Statistics
  stats: {
    totalTriggers: {
      type: Number,
      default: 0
    },
    successfulTriggers: {
      type: Number,
      default: 0
    },
    failedTriggers: {
      type: Number,
      default: 0
    },
    lastTriggered: Date,
    averageResponseTime: Number
  }
}, {
  timestamps: true,
  collection: 'apiConfigs' // This ensures the collection name is 'apiConfigs'
});

// Indexes for better performance
ApiConfigSchema.index({ type: 1, isActive: 1 });
ApiConfigSchema.index({ keywords: 1 });
ApiConfigSchema.index({ triggerPhrases: 1 });
ApiConfigSchema.index({ category: 1, isActive: 1 });
ApiConfigSchema.index({ priority: -1, createdAt: -1 });

// Text search index
ApiConfigSchema.index({
  name: 'text',
  description: 'text',
  keywords: 'text',
  triggerPhrases: 'text',
  prompt: 'text'
});

// Validation middleware
ApiConfigSchema.pre('save', function(next) {
  // Update timestamp
  this.updatedAt = new Date();
  
  // Validate based on type
  if (this.type === 'api') {
    if (!this.apiConfig || !this.apiConfig.endpoint) {
      return next(new Error('API configuration requires endpoint'));
    }
    
    // Validate URL format
    try {
      new URL(this.apiConfig.endpoint);
    } catch (error) {
      return next(new Error('API endpoint must be a valid URL'));
    }
  } else if (this.type === 'form') {
    if (!this.formConfig) {
      return next(new Error('Form configuration is required'));
    }
    
    // For GET requests, fields are optional since they're used for data retrieval
    const isGetRequest = this.formConfig.submitApiConfig?.method === 'GET';
    if (!this.formConfig.fields || (this.formConfig.fields.length === 0 && !isGetRequest)) {
      return next(new Error('Form configuration requires at least one field (except for GET requests)'));
    }
    
    if (!this.formConfig.submitApiConfig || !this.formConfig.submitApiConfig.endpoint) {
      return next(new Error('Form configuration requires submit endpoint'));
    }
    
    // Validate submit URL format
    try {
      new URL(this.formConfig.submitApiConfig.endpoint);
    } catch (error) {
      return next(new Error('Form submit endpoint must be a valid URL'));
    }
  }
  
  // Ensure at least one trigger method
  if ((!this.keywords || this.keywords.length === 0) && 
      (!this.triggerPhrases || this.triggerPhrases.length === 0)) {
    return next(new Error('Configuration must have at least one keyword or trigger phrase'));
  }
  
  next();
});

// Static methods for querying
ApiConfigSchema.statics.findActiveApis = function() {
  return this.find({ type: 'api', isActive: true }).sort({ priority: -1, createdAt: -1 });
};

ApiConfigSchema.statics.findActiveForms = function() {
  return this.find({ type: 'form', isActive: true }).sort({ priority: -1, createdAt: -1 });
};

ApiConfigSchema.statics.findActiveConfigs = function() {
  return this.find({ isActive: true }).sort({ type: 1, priority: -1, createdAt: -1 });
};

ApiConfigSchema.statics.searchByKeywords = function(keywords) {
  const keywordArray = Array.isArray(keywords) ? keywords : [keywords];
  return this.find({
    isActive: true,
    keywords: { $in: keywordArray }
  }).sort({ priority: -1, createdAt: -1 });
};

ApiConfigSchema.statics.searchByTriggerPhrases = function(phrase) {
  return this.find({
    isActive: true,
    triggerPhrases: { $regex: new RegExp(phrase, 'i') }
  }).sort({ priority: -1, createdAt: -1 });
};

ApiConfigSchema.statics.findMatchingConfigs = function(userInput) {
  const searchTerms = userInput.toLowerCase().split(' ');
  
  return this.find({
    isActive: true,
    $or: [
      { keywords: { $in: searchTerms } },
      { triggerPhrases: { $regex: new RegExp(userInput, 'i') } },
      { $text: { $search: userInput } }
    ]
  }).sort({ priority: -1, score: { $meta: 'textScore' } });
};

ApiConfigSchema.statics.incrementTriggerStats = function(configId, success = true) {
  const updateFields = {
    'stats.totalTriggers': 1,
    'stats.lastTriggered': new Date()
  };
  
  if (success) {
    updateFields['stats.successfulTriggers'] = 1;
  } else {
    updateFields['stats.failedTriggers'] = 1;
  }
  
  return this.findByIdAndUpdate(
    configId,
    { $inc: updateFields },
    { new: true }
  );
};

module.exports = mongoose.model('ApiConfig', ApiConfigSchema);