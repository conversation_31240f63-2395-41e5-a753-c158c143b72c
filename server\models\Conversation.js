const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema(
  {
    role: {
      type: String,
      required: true,
      enum: ['user', 'assistant', 'system'],
    },
    content: {
      type: String,
      required: true,
    },
    formData: {
      type: Object,
      default: null,
    },
    apiResponse: {
      type: Object,
      default: null,
    },
  },
  {
    timestamps: true,
  }
);

const conversationSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      default: 'New Conversation',
    },
    messages: [messageSchema],
    // Conversational form state
    activeFormFlow: {
      formId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'UnifiedConfig',
        default: null
      },
      flowType: {
        type: String,
        enum: ['conversational', 'hybrid'],
        default: null
      },
      currentFieldIndex: {
        type: Number,
        default: 0
      },
      collectedData: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
      },
      conversationalFields: {
        type: [Object],
        default: []
      },
      formFields: {
        type: [Object],
        default: []
      },
      isConversationalPhase: {
        type: Boolean,
        default: false
      },
      isComplete: {
        type: Boolean,
        default: false
      },
      startedAt: {
        type: Date,
        default: null
      },
      phase: {
        type: String,
        enum: ['pre-form', 'form', 'post-form'],
        default: null
      }
    }
  },
  {
    timestamps: true,
  }
);

const Conversation = mongoose.model('Conversation', conversationSchema);

module.exports = Conversation;