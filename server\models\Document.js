const mongoose = require('mongoose');

const documentSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
    },
    description: {
      type: String,
      default: '',
    },
    fileType: {
      type: String,
      required: true,
    },
    fileSize: {
      type: Number,
      required: true,
    },
    file: {
      type: Buffer,
      required: true,
    },
    uploadedBy: {
      type: String,
      default: 'Anonymous',
    },
    tags: {
      type: [String],
      default: [],
    },
  },
  {
    timestamps: true,
  }
);

// Create indexes for better search performance
documentSchema.index({ name: 'text', description: 'text', tags: 'text' });

const Document = mongoose.model('Document', documentSchema);

module.exports = Document;