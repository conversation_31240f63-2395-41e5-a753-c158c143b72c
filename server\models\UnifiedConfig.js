const mongoose = require('mongoose');

const UnifiedConfigSchema = new mongoose.Schema({
  // Common fields for both API and Form configurations
  name: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  type: {
    type: String,
    required: true,
    enum: ['api', 'form'],
    index: true
  },
  description: {
    type: String,
    default: ''
  },
  prompt: {
    type: String,
    default: '',
    text: true // Enable text search
  },
  keywords: [{
    type: String,
    text: true // Enable text search
  }],
  triggerPhrases: [{
    type: String,
    text: true // Enable text search
  }],
  isActive: {
    type: Boolean,
    default: true,
    index: true
  },
  
  // API-specific fields (only used when type === 'api')
  apiConfig: {
    endpoint: {
      type: String,
      default: ''
    },
    method: {
      type: String,
      enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      default: 'GET'
    },
    headers: {
      type: Map,
      of: String,
      default: new Map()
    },
    authType: {
      type: String,
      enum: ['none', 'bearer', 'apikey', 'basic'],
      default: 'none'
    },
    authConfig: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      default: new Map()
    },
    responseTemplate: {
      type: String,
      default: ''
    },
    timeout: {
      type: Number,
      default: 30000,
      min: 1000,
      max: 300000
    },
    retryCount: {
      type: Number,
      default: 3,
      min: 0,
      max: 10
    },
    dataInjection: {
      injectUserData: {
        type: Boolean,
        default: true
      },
      requiredFields: [{
        type: String
      }],
      autoFields: {
        empId: {
          type: Boolean,
          default: false
        },
        token: {
          type: Boolean,
          default: false
        },
        roleType: {
          type: Boolean,
          default: false
        }
      }
    },
    // Custom payload structure configuration
    customPayload: {
      enabled: {
        type: Boolean,
        default: false
      },
      structure: {
        type: mongoose.Schema.Types.Mixed,
        default: {}
      },
      mergeStrategy: {
        type: String,
        enum: ['replace', 'merge', 'append'],
        default: 'replace'
      },
      transformations: [{
        field: String,
        operation: {
          type: String,
          enum: ['format', 'calculate', 'transform', 'condition']
        },
        value: mongoose.Schema.Types.Mixed
      }]
    },
    lastTestedAt: {
      type: Date
    },
    lastTestResult: {
      success: Boolean,
      statusCode: Number,
      responseTime: Number,
      message: String,
      error: String
    }
  },
  
  // Form-specific fields (only used when type === 'form')
  formConfig: {
    // Conversational flow configuration
    conversationalFlow: {
      enabled: {
        type: Boolean,
        default: false
      },
      completionMessage: {
        type: String,
        default: 'Thank you! Your form has been submitted successfully.'
      }
    },
    // Hybrid flow configuration
    hybridFlow: {
      enabled: {
        type: Boolean,
        default: false
      },
      completionMessage: {
        type: String,
        default: 'Please complete the remaining form fields below.'
      }
    },
    fields: [{
      name: {
        type: String,
        required: function() { return this.parent().type === 'form'; }
      },
      label: {
        type: String,
        required: function() { return this.parent().type === 'form'; }
      },
      type: {
        type: String,
        enum: ['text', 'email', 'password', 'number', 'date', 'time', 'datetime-local', 'textarea', 'select', 'radio', 'checkbox', 'file'],
        required: function() { return this.parent().type === 'form'; }
      },
      required: {
        type: Boolean,
        default: false
      },
      readonly: {
        type: Boolean,
        default: false
      },
      placeholder: {
        type: String,
        default: ''
      },
      defaultValue: {
        type: String,
        default: ''
      },
      options: [{
        type: String
      }],
      validation: {
        min: Number,
        max: Number,
        minLength: Number,
        maxLength: Number,
        pattern: String,
        custom: String
      },
      // Conversational flow specific properties
      conversationalPrompt: {
        type: String,
        default: ''
      },
      skipInConversation: {
        type: Boolean,
        default: false
      },
      // Hybrid flow specific properties
      displayMode: {
        type: String,
        enum: ['form', 'conversational', 'auto'],
        default: 'auto' // 'form' = always show as form field, 'conversational' = always ask via chat, 'auto' = decide based on flow type
      }
    }],
    submitApiConfig: {
      endpoint: {
        type: String,
        default: ''
      },
      method: {
        type: String,
        enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
        default: 'POST'
      },
      headers: {
        type: Map,
        of: String,
        default: new Map()
      },
      authType: {
        type: String,
        enum: ['none', 'bearer', 'apikey', 'basic'],
        default: 'bearer'
      },
      authConfig: {
        type: Map,
        of: mongoose.Schema.Types.Mixed,
        default: new Map()
      },
      dataMapping: {
        type: Map,
        of: String,
        default: new Map()
      },
      // Custom payload structure configuration for forms
      customPayload: {
        enabled: {
          type: Boolean,
          default: false
        },
        structure: {
          type: mongoose.Schema.Types.Mixed,
          default: {}
        },
        mergeStrategy: {
          type: String,
          enum: ['replace', 'merge', 'append'],
          default: 'replace'
        },
        transformations: [{
          field: String,
          operation: {
            type: String,
            enum: ['format', 'calculate', 'transform', 'condition']
          },
          value: mongoose.Schema.Types.Mixed
        }]
      },
      // Access control configuration for role-based authorization
      accessControl: {
        enabled: {
          type: Boolean,
          default: false
        },
        allowedRoles: [{
          type: String
        }]
      },
      successMessage: {
        type: String,
        default: 'Form submitted successfully!'
      },
      errorMessage: {
        type: String,
        default: 'Failed to submit form. Please try again.'
      },
      // For GET requests - template to format the response data
      responseTemplate: {
        type: String,
        default: ''
      }
    },
    prefillData: {
      type: Map,
      of: mongoose.Schema.Types.Mixed,
      default: new Map()
    },
    // Form-to-form linking configuration
    formLinking: {
      enabled: {
        type: Boolean,
        default: false
      },
      // Configuration for when this form displays data with Apply buttons
      recordActions: [{
        buttonText: {
          type: String,
          default: 'Apply'
        },
        targetFormId: {
          type: mongoose.Schema.Types.ObjectId,
          ref: 'UnifiedConfig'
        },
        targetFormName: {
          type: String
        },
        // Field mapping from record data to target form
        fieldMapping: {
          type: Map,
          of: String,
          default: new Map()
        },
        // Conditions for when to show the button
        conditions: [{
          field: String,
          operator: {
            type: String,
            enum: ['equals', 'not_equals', 'contains', 'not_contains', 'exists', 'not_exists']
          },
          value: mongoose.Schema.Types.Mixed
        }],
        // Custom styling for the button
        buttonStyle: {
          type: String,
          default: 'primary'
        },
        // Auto-trigger configuration
        autoTrigger: {
          enabled: {
            type: Boolean,
            default: false
          },
          delaySeconds: {
            type: Number,
            default: 2
          }
        },
        // Auto-submit on click configuration (when auto-trigger is disabled)
        autoSubmitOnClick: {
          type: Boolean,
          default: false
        }
      }]
    }
  },
  
  // Metadata
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  createdBy: {
    type: String,
    default: 'system'
  },
  tags: [{
    type: String,
    index: true
  }],
  category: {
    type: String,
    default: 'general',
    index: true
  },
  priority: {
    type: Number,
    default: 0,
    index: true
  }
}, {
  timestamps: true,
  collection: 'unifiedconfigs',
  toJSON: {
    transform: function(doc, ret) {
      // Convert Map fields to regular objects for proper JSON serialization
      if (ret.apiConfig) {
        if (ret.apiConfig.headers instanceof Map) {
          ret.apiConfig.headers = Object.fromEntries(ret.apiConfig.headers);
        }
        if (ret.apiConfig.authConfig instanceof Map) {
          ret.apiConfig.authConfig = Object.fromEntries(ret.apiConfig.authConfig);
        }
      }
      
      if (ret.formConfig) {
        if (ret.formConfig.submitApiConfig) {
          if (ret.formConfig.submitApiConfig.headers instanceof Map) {
            ret.formConfig.submitApiConfig.headers = Object.fromEntries(ret.formConfig.submitApiConfig.headers);
          }
          if (ret.formConfig.submitApiConfig.authConfig instanceof Map) {
            ret.formConfig.submitApiConfig.authConfig = Object.fromEntries(ret.formConfig.submitApiConfig.authConfig);
          }
          if (ret.formConfig.submitApiConfig.dataMapping instanceof Map) {
            ret.formConfig.submitApiConfig.dataMapping = Object.fromEntries(ret.formConfig.submitApiConfig.dataMapping);
          }
        }
        if (ret.formConfig.prefillData instanceof Map) {
          ret.formConfig.prefillData = Object.fromEntries(ret.formConfig.prefillData);
        }
        
        // Handle form linking field mapping
        if (ret.formConfig.formLinking?.recordActions) {
          ret.formConfig.formLinking.recordActions = ret.formConfig.formLinking.recordActions.map(action => ({
            ...action,
            fieldMapping: action.fieldMapping instanceof Map ? Object.fromEntries(action.fieldMapping) : action.fieldMapping
          }));
        }
      }
      
      return ret;
    }
  },
  toObject: {
    transform: function(doc, ret) {
      // Convert Map fields to regular objects for proper object serialization
      if (ret.apiConfig) {
        if (ret.apiConfig.headers instanceof Map) {
          ret.apiConfig.headers = Object.fromEntries(ret.apiConfig.headers);
        }
        if (ret.apiConfig.authConfig instanceof Map) {
          ret.apiConfig.authConfig = Object.fromEntries(ret.apiConfig.authConfig);
        }
      }
      
      if (ret.formConfig) {
        if (ret.formConfig.submitApiConfig) {
          if (ret.formConfig.submitApiConfig.headers instanceof Map) {
            ret.formConfig.submitApiConfig.headers = Object.fromEntries(ret.formConfig.submitApiConfig.headers);
          }
          if (ret.formConfig.submitApiConfig.authConfig instanceof Map) {
            ret.formConfig.submitApiConfig.authConfig = Object.fromEntries(ret.formConfig.submitApiConfig.authConfig);
          }
          if (ret.formConfig.submitApiConfig.dataMapping instanceof Map) {
            ret.formConfig.submitApiConfig.dataMapping = Object.fromEntries(ret.formConfig.submitApiConfig.dataMapping);
          }
        }
        if (ret.formConfig.prefillData instanceof Map) {
          ret.formConfig.prefillData = Object.fromEntries(ret.formConfig.prefillData);
        }
        
        // Handle form linking field mapping
        if (ret.formConfig.formLinking?.recordActions) {
          ret.formConfig.formLinking.recordActions = ret.formConfig.formLinking.recordActions.map(action => ({
            ...action,
            fieldMapping: action.fieldMapping instanceof Map ? Object.fromEntries(action.fieldMapping) : action.fieldMapping
          }));
        }
      }
      
      return ret;
    }
  }
});

// Create text index for search functionality
UnifiedConfigSchema.index({
  name: 'text',
  description: 'text',
  prompt: 'text',
  keywords: 'text',
  triggerPhrases: 'text'
}, {
  weights: {
    name: 10,
    triggerPhrases: 8,
    keywords: 6,
    prompt: 4,
    description: 2
  }
});

// Compound indexes for efficient queries
UnifiedConfigSchema.index({ type: 1, isActive: 1 });
UnifiedConfigSchema.index({ type: 1, category: 1 });
UnifiedConfigSchema.index({ type: 1, priority: -1 });

// Virtual for getting safe JSON (without sensitive data)
UnifiedConfigSchema.methods.toSafeJSON = function() {
  const obj = this.toObject();
  
  // Remove sensitive authentication data
  if (obj.apiConfig && obj.apiConfig.authConfig) {
    // Convert Map to object if needed
    const authConfig = obj.apiConfig.authConfig instanceof Map ? 
      Object.fromEntries(obj.apiConfig.authConfig) : obj.apiConfig.authConfig;
    
    const safeAuthConfig = {};
    Object.keys(authConfig).forEach(key => {
      if (key.toLowerCase().includes('token') || key.toLowerCase().includes('key') || key.toLowerCase().includes('secret')) {
        safeAuthConfig[key] = '***HIDDEN***';
      } else {
        safeAuthConfig[key] = authConfig[key];
      }
    });
    obj.apiConfig.authConfig = safeAuthConfig;
  }
  
  if (obj.formConfig && obj.formConfig.submitApiConfig && obj.formConfig.submitApiConfig.authConfig) {
    // Convert Map to object if needed
    const authConfig = obj.formConfig.submitApiConfig.authConfig instanceof Map ? 
      Object.fromEntries(obj.formConfig.submitApiConfig.authConfig) : obj.formConfig.submitApiConfig.authConfig;
    
    const safeAuthConfig = {};
    Object.keys(authConfig).forEach(key => {
      if (key.toLowerCase().includes('token') || key.toLowerCase().includes('key') || key.toLowerCase().includes('secret')) {
        safeAuthConfig[key] = '***HIDDEN***';
      } else {
        safeAuthConfig[key] = authConfig[key];
      }
    });
    obj.formConfig.submitApiConfig.authConfig = safeAuthConfig;
  }
  
  return obj;
};

// Pre-save middleware to update timestamps and validate
UnifiedConfigSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  
  // Validate based on type
  if (this.type === 'api') {
    if (!this.apiConfig || !this.apiConfig.endpoint) {
      return next(new Error('API configuration requires endpoint'));
    }
  } else if (this.type === 'form') {
    if (!this.formConfig) {
      return next(new Error('Form configuration is required'));
    }
    
    // For GET requests, fields are optional since they're used for data retrieval
    const isGetRequest = this.formConfig.submitApiConfig?.method === 'GET';
    if (!this.formConfig.fields || (this.formConfig.fields.length === 0 && !isGetRequest)) {
      return next(new Error('Form configuration requires at least one field (except for GET requests)'));
    }
  }
  
  next();
});

// Static methods for common queries
UnifiedConfigSchema.statics.findActiveApis = function() {
  return this.find({ type: 'api', isActive: true }).sort({ priority: -1, createdAt: -1 });
};

UnifiedConfigSchema.statics.findActiveForms = function() {
  return this.find({ type: 'form', isActive: true }).sort({ priority: -1, createdAt: -1 });
};

UnifiedConfigSchema.statics.findActiveConfigs = function() {
  return this.find({ isActive: true }).sort({ type: 1, priority: -1, createdAt: -1 });
};

UnifiedConfigSchema.statics.searchConfigs = function(query, type = null) {
  const searchQuery = {
    $text: { $search: query },
    isActive: true
  };
  
  if (type) {
    searchQuery.type = type;
  }
  
  return this.find(searchQuery, { score: { $meta: 'textScore' } })
    .sort({ score: { $meta: 'textScore' }, priority: -1 });
};

UnifiedConfigSchema.statics.findByCategory = function(category, type = null) {
  const query = { category, isActive: true };
  if (type) query.type = type;
  
  return this.find(query).sort({ priority: -1, createdAt: -1 });
};

module.exports = mongoose.model('UnifiedConfig', UnifiedConfigSchema);
