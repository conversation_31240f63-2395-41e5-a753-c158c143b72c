{"name": "server", "version": "1.0.0", "description": "Backend for AI Form Builder", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1", "setup-auto-trigger": "node scripts/setupAutoTriggerDemo.js"}, "keywords": ["api", "mongodb", "express", "form-builder", "chatbot"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "form-data": "^4.0.3", "moment": "^2.30.1", "mongoose": "^8.15.2", "morgan": "^1.10.0", "multer": "^2.0.1", "nodemon": "^3.1.10", "ollama": "^0.5.16"}}