const express = require('express');
const router = express.Router();
const ApiConfig = require('../models/ApiConfig');
const axios = require('axios');

// @desc    Get all API configurations
// @route   GET /api/api-configs
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { type, category, active } = req.query;
    
    let query = {};
    
    if (type) query.type = type;
    if (category) query.category = category;
    if (active !== undefined) query.isActive = active === 'true';
    
    const configs = await ApiConfig.find(query)
      .sort({ priority: -1, createdAt: -1 })
      .lean();
    
    res.json(configs);
  } catch (error) {
    console.error('Error fetching API configurations:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Get API configuration by ID
// @route   GET /api/api-configs/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const config = await ApiConfig.findById(req.params.id);
    
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    res.json(config);
  } catch (error) {
    console.error('Error fetching API configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Create new API configuration
// @route   POST /api/api-configs
// @access  Public
router.post('/', async (req, res) => {
  try {
    console.log('Creating new API configuration:', req.body);
    
    const configData = req.body;
    
    // Validate required fields
    if (!configData.name || !configData.type) {
      return res.status(400).json({ 
        message: 'Name and type are required' 
      });
    }
    
    if (!['api', 'form'].includes(configData.type)) {
      return res.status(400).json({ 
        message: 'Type must be "api" or "form"' 
      });
    }
    
    // Check if name already exists
    const existingConfig = await ApiConfig.findOne({ name: configData.name });
    if (existingConfig) {
      return res.status(400).json({ 
        message: 'Configuration with this name already exists' 
      });
    }
    
    // Create new configuration
    const config = new ApiConfig(configData);
    await config.save();
    
    console.log('API configuration created successfully:', config._id);
    res.status(201).json(config);
  } catch (error) {
    console.error('Error creating API configuration:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error', 
        errors: validationErrors 
      });
    }
    
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Update API configuration
// @route   PUT /api/api-configs/:id
// @access  Public
router.put('/:id', async (req, res) => {
  try {
    console.log('Updating API configuration:', req.params.id);
    
    const configData = req.body;
    
    // Find existing configuration
    const existingConfig = await ApiConfig.findById(req.params.id);
    if (!existingConfig) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    // Check if name is being changed and if new name already exists
    if (configData.name && configData.name !== existingConfig.name) {
      const nameExists = await ApiConfig.findOne({ 
        name: configData.name, 
        _id: { $ne: req.params.id } 
      });
      if (nameExists) {
        return res.status(400).json({ 
          message: 'Configuration with this name already exists' 
        });
      }
    }
    
    // Update configuration
    const updatedConfig = await ApiConfig.findByIdAndUpdate(
      req.params.id,
      { ...configData, updatedBy: 'system', updatedAt: new Date() },
      { new: true, runValidators: true }
    );
    
    console.log('API configuration updated successfully:', updatedConfig._id);
    res.json(updatedConfig);
  } catch (error) {
    console.error('Error updating API configuration:', error);
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return res.status(400).json({ 
        message: 'Validation error', 
        errors: validationErrors 
      });
    }
    
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Delete API configuration
// @route   DELETE /api/api-configs/:id
// @access  Public
router.delete('/:id', async (req, res) => {
  try {
    console.log('Deleting API configuration:', req.params.id);
    
    const config = await ApiConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    await ApiConfig.findByIdAndDelete(req.params.id);
    
    console.log('API configuration deleted successfully:', req.params.id);
    res.json({ message: 'Configuration deleted successfully' });
  } catch (error) {
    console.error('Error deleting API configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Search API configurations
// @route   GET /api/api-configs/search/:query
// @access  Public
router.get('/search/:query', async (req, res) => {
  try {
    const { query } = req.params;
    const { type } = req.query;
    
    console.log('Searching API configurations:', query);
    
    const configs = await ApiConfig.findMatchingConfigs(query);
    
    // Filter by type if specified
    const filteredConfigs = type 
      ? configs.filter(config => config.type === type)
      : configs;
    
    res.json(filteredConfigs);
  } catch (error) {
    console.error('Error searching API configurations:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Test API configuration
// @route   POST /api/api-configs/:id/test
// @access  Public
router.post('/:id/test', async (req, res) => {
  try {
    console.log('Testing API configuration:', req.params.id);
    
    const config = await ApiConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    if (config.type !== 'api') {
      return res.status(400).json({ message: 'Only API configurations can be tested' });
    }
    
    const testData = req.body.testData || {};
    const result = await executeApiConfig(config, testData);
    
    res.json({
      success: result.success,
      data: result.data,
      error: result.error,
      responseTime: result.responseTime,
      statusCode: result.statusCode
    });
  } catch (error) {
    console.error('Error testing API configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Execute API configuration (trigger dynamically)
// @route   POST /api/api-configs/:id/execute
// @access  Public
router.post('/:id/execute', async (req, res) => {
  try {
    console.log('Executing API configuration:', req.params.id);
    
    const config = await ApiConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    if (!config.isActive) {
      return res.status(400).json({ message: 'Configuration is not active' });
    }
    
    const userData = req.body.userData || {};
    const inputData = req.body.inputData || {};
    
    let result;
    if (config.type === 'api') {
      result = await executeApiConfig(config, { ...userData, ...inputData });
    } else if (config.type === 'form') {
      result = await executeFormSubmit(config, inputData);
    }
    
    // Update statistics
    await ApiConfig.incrementTriggerStats(config._id, result.success);
    
    res.json({
      success: result.success,
      data: result.data,
      error: result.error,
      responseTime: result.responseTime,
      message: result.message
    });
  } catch (error) {
    console.error('Error executing API configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Process user input and execute matching configurations
// @route   POST /api/api-configs/process
// @access  Public
router.post('/process', async (req, res) => {
  try {
    const { userInput, userData, options } = req.body;
    
    if (!userInput) {
      return res.status(400).json({ message: 'User input is required' });
    }
    
    console.log('Processing user input:', userInput);
    
    const ApiTriggerService = require('../services/apiTriggerService');
    const result = await ApiTriggerService.processUserInput(userInput, userData || {}, options || {});
    
    res.json(result);
  } catch (error) {
    console.error('Error processing user input:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Find matching configurations for user input
// @route   POST /api/api-configs/match
// @access  Public
router.post('/match', async (req, res) => {
  try {
    const { userInput, type } = req.body;
    
    if (!userInput) {
      return res.status(400).json({ message: 'User input is required' });
    }
    
    console.log('Finding matching configurations for:', userInput);
    
    let configs = await ApiConfig.findMatchingConfigs(userInput);
    
    // Filter by type if specified
    if (type) {
      configs = configs.filter(config => config.type === type);
    }
    
    res.json({
      matches: configs.length,
      configurations: configs
    });
  } catch (error) {
    console.error('Error finding matching configurations:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Get API configuration statistics
// @route   GET /api/api-configs/stats/overview
// @access  Public
router.get('/stats/overview', async (req, res) => {
  try {
    const [totalConfigs, totalApis, totalForms, activeConfigs, inactiveConfigs] = await Promise.all([
      ApiConfig.countDocuments(),
      ApiConfig.countDocuments({ type: 'api' }),
      ApiConfig.countDocuments({ type: 'form' }),
      ApiConfig.countDocuments({ isActive: true }),
      ApiConfig.countDocuments({ isActive: false })
    ]);
    
    const categories = await ApiConfig.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    const recentlyUsed = await ApiConfig.find({ 'stats.lastTriggered': { $exists: true } })
      .sort({ 'stats.lastTriggered': -1 })
      .limit(5)
      .select('name type stats.lastTriggered stats.totalTriggers');
    
    res.json({
      totalConfigs,
      totalApis,
      totalForms,
      activeConfigs,
      inactiveConfigs,
      categories: categories.map(cat => ({ name: cat._id, count: cat.count })),
      recentlyUsed
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Helper function to execute API configuration
async function executeApiConfig(config, data = {}) {
  const startTime = Date.now();
  
  try {
    const { apiConfig } = config;
    
    // Prepare headers
    const headers = { 'Content-Type': 'application/json' };
    
    // Add configured headers
    if (apiConfig.headers) {
      for (const [key, value] of apiConfig.headers) {
        headers[key] = value;
      }
    }
    
    // Add authentication
    if (apiConfig.authType && apiConfig.authType !== 'none') {
      switch (apiConfig.authType) {
        case 'bearer':
          if (apiConfig.authConfig?.token) {
            headers['Authorization'] = `Bearer ${apiConfig.authConfig.token}`;
          }
          break;
        case 'basic':
          if (apiConfig.authConfig?.username && apiConfig.authConfig?.password) {
            const auth = Buffer.from(`${apiConfig.authConfig.username}:${apiConfig.authConfig.password}`).toString('base64');
            headers['Authorization'] = `Basic ${auth}`;
          }
          break;
        case 'apikey':
          if (apiConfig.authConfig?.apiKey && apiConfig.authConfig?.apiKeyHeader) {
            headers[apiConfig.authConfig.apiKeyHeader] = apiConfig.authConfig.apiKey;
          }
          break;
        case 'custom':
          if (apiConfig.authConfig?.customHeaders) {
            for (const [key, value] of apiConfig.authConfig.customHeaders) {
              headers[key] = value;
            }
          }
          break;
      }
    }
    
    // Prepare URL with query parameters
    let url = apiConfig.endpoint;
    if (apiConfig.queryParams && apiConfig.queryParams.size > 0) {
      const urlObj = new URL(url);
      for (const [key, value] of apiConfig.queryParams) {
        urlObj.searchParams.append(key, value);
      }
      url = urlObj.toString();
    }
    
    // Prepare request body
    let requestBody = null;
    if (['POST', 'PUT', 'PATCH'].includes(apiConfig.method)) {
      if (apiConfig.bodyTemplate) {
        // Replace placeholders in body template
        requestBody = replaceTemplateVariables(apiConfig.bodyTemplate, data);
        try {
          requestBody = JSON.parse(requestBody);
        } catch (e) {
          // If not valid JSON, send as string
        }
      } else {
        requestBody = data;
      }
    }
    
    // Make API request
    const response = await axios({
      method: apiConfig.method.toLowerCase(),
      url: url,
      headers: headers,
      data: requestBody,
      timeout: apiConfig.timeout || 30000,
      validateStatus: () => true // Don't throw on HTTP error status
    });
    
    const responseTime = Date.now() - startTime;
    const isSuccess = response.status >= 200 && response.status < 300;
    
    let processedData = response.data;
    
    // Apply response template if configured
    if (isSuccess && apiConfig.responseTemplate) {
      processedData = replaceTemplateVariables(apiConfig.responseTemplate, response.data);
    }
    
    return {
      success: isSuccess,
      data: processedData,
      statusCode: response.status,
      responseTime: responseTime,
      error: isSuccess ? null : `HTTP ${response.status}: ${response.statusText}`
    };
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    console.error('API execution error:', error.message);
    
    return {
      success: false,
      data: null,
      statusCode: error.response?.status || 0,
      responseTime: responseTime,
      error: error.message
    };
  }
}

// Helper function to execute form submit
async function executeFormSubmit(config, formData) {
  const startTime = Date.now();
  
  try {
    const { formConfig } = config;
    const { submitApiConfig } = formConfig;
    
    // Prepare headers
    const headers = { 'Content-Type': 'application/json' };
    
    // Add configured headers
    if (submitApiConfig.headers) {
      for (const [key, value] of submitApiConfig.headers) {
        headers[key] = value;
      }
    }
    
    // Add authentication
    if (submitApiConfig.authType && submitApiConfig.authType !== 'none') {
      switch (submitApiConfig.authType) {
        case 'bearer':
          if (submitApiConfig.authConfig?.token) {
            headers['Authorization'] = `Bearer ${submitApiConfig.authConfig.token}`;
          }
          break;
        case 'basic':
          if (submitApiConfig.authConfig?.username && submitApiConfig.authConfig?.password) {
            const auth = Buffer.from(`${submitApiConfig.authConfig.username}:${submitApiConfig.authConfig.password}`).toString('base64');
            headers['Authorization'] = `Basic ${auth}`;
          }
          break;
        case 'apikey':
          if (submitApiConfig.authConfig?.apiKey && submitApiConfig.authConfig?.apiKeyHeader) {
            headers[submitApiConfig.authConfig.apiKeyHeader] = submitApiConfig.authConfig.apiKey;
          }
          break;
      }
    }
    
    // Apply data mapping
    let mappedData = formData;
    if (submitApiConfig.dataMapping && submitApiConfig.dataMapping.size > 0) {
      mappedData = {};
      for (const [formField, apiField] of submitApiConfig.dataMapping) {
        if (formData[formField] !== undefined) {
          mappedData[apiField] = formData[formField];
        }
      }
    }
    
    // Make API request
    const response = await axios({
      method: submitApiConfig.method.toLowerCase(),
      url: submitApiConfig.endpoint,
      headers: headers,
      data: mappedData,
      timeout: 30000,
      validateStatus: () => true
    });
    
    const responseTime = Date.now() - startTime;
    const isSuccess = response.status >= 200 && response.status < 300;
    
    return {
      success: isSuccess,
      data: response.data,
      responseTime: responseTime,
      message: isSuccess ? submitApiConfig.successMessage : submitApiConfig.errorMessage,
      error: isSuccess ? null : `HTTP ${response.status}: ${response.statusText}`
    };
    
  } catch (error) {
    const responseTime = Date.now() - startTime;
    
    return {
      success: false,
      data: null,
      responseTime: responseTime,
      message: config.formConfig.submitApiConfig.errorMessage,
      error: error.message
    };
  }
}

// Helper function to replace template variables
function replaceTemplateVariables(template, data) {
  let result = template;
  
  // Replace {{variable}} patterns
  const matches = template.match(/\{\{([^}]+)\}\}/g);
  if (matches) {
    matches.forEach(match => {
      const variable = match.replace(/\{\{|\}\}/g, '').trim();
      const value = getNestedProperty(data, variable);
      result = result.replace(match, value !== undefined ? value : '');
    });
  }
  
  return result;
}

// Helper function to get nested property from object
function getNestedProperty(obj, path) {
  return path.split('.').reduce((current, prop) => {
    return current && current[prop] !== undefined ? current[prop] : undefined;
  }, obj);
}

module.exports = router;