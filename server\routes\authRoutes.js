const express = require('express');
const axios = require('axios');
const ApiService = require('../services/apiService');
const router = express.Router();

// @desc    Login user by proxying to external API using dynamic configuration
// @route   POST /api/auth/login
// @access  Public
const loginUser = async (req, res) => {
  try {
    console.log('Attempting to log in...');
    const { empId, password } = req.body;

    // Validate input
    if (!empId || !password) {
      return res.status(400).json({
        success: false,
        message: 'Employee ID and password are required'
      });
    }

    console.log(`Login attempt for empId: ${empId}`);

    // Make request using dynamic API configuration
    // The API config should be stored in MongoDB with name 'login-api'
    const response = await ApiService.makeApiCall('Login', {
      empId,
      password
    });

    console.log('External API response status:', response.status);
    console.log('External API response data:', JSON.stringify(response.data, null, 2));

    // Check if login was successful
    if (response.data && response.data.status === true && response.data.data && response.data.data.token) {
      // Extract user details from the nested response structure
      const responseData = response.data.data;
      const userDetails = responseData.userDetails || {};
      
      // Create a user object with login info
      const userData = {
        empId: userDetails.empId || empId,
        firstName: userDetails.firstName || empId,
        lastName: userDetails.lastName || '',
        email: userDetails.officialEmail || '',
        designation: userDetails.designation || '',
        roleType: userDetails.roleType || userDetails.role || 'employee', // Include roleType
        token: responseData.token,
        isLoggedIn: true,
        loginTime: new Date().toISOString()
      };

      // Return success response
      res.json({
        success: true,
        message: 'Login successful',
        data: userData
      });
    } else {
      // Login failed
      console.log('Login failed - invalid response structure');
      res.status(401).json({
        success: false,
        message: 'Invalid employee ID or password'
      });
    }

  } catch (error) {
    console.error('Login error:', error);

    // Handle different types of errors
    if (error.message.includes('not found or inactive')) {
      // API configuration not found
      return res.status(500).json({
        success: false,
        message: 'Login service configuration not found. Please contact administrator.'
      });
    }

    if (error.response) {
      // External API returned an error response
      const status = error.response.status;
      const errorData = error.response.data;
      
      console.error('External API error status:', status);
      console.error('External API error data:', errorData);

      if (status === 401) {
        return res.status(401).json({
          success: false,
          message: 'Invalid employee ID or password'
        });
      } else if (status === 400) {
        return res.status(400).json({
          success: false,
          message: errorData.message || 'Bad request'
        });
      } else {
        return res.status(500).json({
          success: false,
          message: 'Login service temporarily unavailable'
        });
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message);
      res.status(503).json({
        success: false,
        message: 'Unable to connect to login service'
      });
    } else {
      // Other error
      console.error('Unexpected error:', error.message);
      res.status(500).json({
        success: false,
        message: 'An unexpected error occurred'
      });
    }
  }
};

// @desc    Get supervisor and reviewer information using dynamic API configuration
// @route   GET /api/auth/supervisor-reviewer-info
// @access  Private
const getSupervisorReviewerInfo = async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authorization token is required'
      });
    }

    console.log('🔄 Fetching supervisor and reviewer information using dynamic config...');

    // Make request using dynamic API configuration
    // The API config should be stored in MongoDB with name 'supervisor-reviewer-info'
    const response = await ApiService.makeApiCall('supervisor-reviewer-info', null, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });

    console.log('✅ Supervisor and reviewer info fetched from dynamic config');
    console.log('Response status:', response.status);
    console.log('Response data:', JSON.stringify(response.data, null, 2));

    // Return the response data directly
    res.json({
      success: true,
      message: 'Supervisor and reviewer information fetched successfully',
      data: response.data
    });

  } catch (error) {
    console.error('❌ Error fetching supervisor and reviewer information:', error);

    // Handle different types of errors
    if (error.message.includes('not found or inactive')) {
      // API configuration not found
      return res.status(500).json({
        success: false,
        message: 'Supervisor/reviewer service configuration not found. Please contact administrator.'
      });
    }

    if (error.response) {
      // External API returned an error response
      const status = error.response.status;
      const errorData = error.response.data;
      
      console.error('External API error status:', status);
      console.error('External API error data:', errorData);

      if (status === 401) {
        return res.status(401).json({
          success: false,
          message: 'Unauthorized - Invalid or expired token'
        });
      } else if (status === 403) {
        return res.status(403).json({
          success: false,
          message: 'Access forbidden - Insufficient permissions'
        });
      } else {
        return res.status(status).json({
          success: false,
          message: errorData.message || 'External service error'
        });
      }
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message);
      res.status(503).json({
        success: false,
        message: 'Unable to connect to supervisor/reviewer service'
      });
    } else {
      // Other error
      console.error('Unexpected error:', error.message);
      res.status(500).json({
        success: false,
        message: 'An unexpected error occurred'
      });
    }
  }
};

// @desc    Get regularization data using dynamic API configuration with placeholder replacement
// @route   GET /api/auth/regularization-quick-add
// @access  Private
const getRegularizationQuickAdd = async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authorization token is required'
      });
    }

    console.log('🔧 Starting getRegularizationQuickAdd with dynamic config and placeholder replacement...');
    
    // Get current month and year for placeholder replacement
    const now = new Date();
    const currentMonth = String(now.getMonth() + 1).padStart(2, '0');
    const currentYear = now.getFullYear();
    const monthYear = `${currentMonth}-${currentYear}`;
    
    console.log('📅 Date info for placeholder replacement:', { 
      currentMonth, 
      currentYear, 
      monthYear,
      currentDate: now.toISOString().split('T')[0]
    });

    // Define placeholders for replacement
    const placeholders = {
      '<month>': currentMonth,
      '<year>': currentYear.toString(),
      '<monthYear>': monthYear
    };

    console.log('🔄 Placeholders for replacement:', placeholders);

    // Make request using dynamic API configuration with placeholder replacement
    const response = await ApiService.makeApiCall('regularization-quick-add', null, {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      placeholders: placeholders
    });

    console.log('✅ Regularization data fetched from dynamic config with placeholders');
    console.log('Response status:', response.status);
    console.log('Response data keys:', Object.keys(response.data || {}));

    // Process the response data
    let message;
    if (response.data && response.data.data && response.data.data.attendanceInfo) {
      if (response.data.data.attendanceInfo.length === 0) {
        message = `📋 **Attendance Regularization**\n\nNo attendance records found for ${response.data.data.monthYear || monthYear} that require regularization.\n\nℹ️ **Possible reasons:**\n• No attendance data for this month\n• All attendance is already regularized\n• You may need to check a different month\n• API might be returning empty data\n\n🔍 **Debug Info:** Check server logs for detailed API response.`;
        console.log('🔍 No records found - showing empty message');
      } else {
        message = `📋 **Attendance Regularization**\n\nFound ${response.data.data.attendanceInfo.length} attendance record${response.data.data.attendanceInfo.length !== 1 ? 's' : ''} for ${response.data.data.monthYear || monthYear}. Click "Apply" next to any date to submit a regularization request.`;
      }
    } else {
      message = `📋 **Attendance Regularization**\n\nUnexpected response format from API. Please check the configuration.`;
    }

    // Return the processed response
    res.json({
      success: true,
      message: 'Regularization data fetched successfully',
      data: {
        message: message,
        apiResponse: response.data,
        attendanceInfo: response.data?.data?.attendanceInfo || [],
        isRegularizationData: true
      }
    });

  } catch (error) {
    console.error('❌ Error fetching regularization data:', error);

    // Handle different types of errors
    if (error.message.includes('not found or inactive')) {
      return res.status(500).json({
        success: false,
        message: 'Regularization service configuration not found. Please contact administrator.',
        data: {
          message: `❌ **Error fetching regularization data**\n\nConfiguration error: ${error.message}`,
          apiResponse: null
        }
      });
    }

    if (error.response) {
      const status = error.response.status;
      const errorData = error.response.data;
      
      console.error('External API error status:', status);
      console.error('External API error data:', errorData);

      return res.status(status).json({
        success: false,
        message: 'External API error',
        data: {
          message: `❌ **Error fetching regularization data**\n\n${errorData.message || `API Error: ${status}`}`,
          apiResponse: errorData
        }
      });
    } else if (error.request) {
      console.error('Network error:', error.message);
      res.status(503).json({
        success: false,
        message: 'Unable to connect to regularization service',
        data: {
          message: `❌ **Error fetching regularization data**\n\nNetwork error: ${error.message}`,
          apiResponse: null
        }
      });
    } else {
      console.error('Unexpected error:', error.message);
      res.status(500).json({
        success: false,
        message: 'An unexpected error occurred',
        data: {
          message: `❌ **Error fetching regularization data**\n\nUnexpected error: ${error.message}`,
          apiResponse: null
        }
      });
    }
  }
};

router.post('/login', loginUser);
router.get('/supervisor-reviewer-info', getSupervisorReviewerInfo);
router.get('/regularization-quick-add', getRegularizationQuickAdd);

module.exports = router;