const express = require('express');
const router = express.Router();
const {
  processMessage,
  submitForm,
  getConversation,
  getConversations,
} = require('../controllers/chatController');
const ApiService = require('../services/apiService');

// @desc    Get regularization data using dynamic API configuration
// @route   GET /api/chat/regularization-data
// @access  Private
const getRegularizationData = async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      return res.status(401).json({
        success: false,
        message: 'Authorization token is required'
      });
    }

    console.log('🔧 Starting getRegularizationData with dynamic config...');
    
    // Get current month and year
    const now = new Date();
    const currentMonth = String(now.getMonth() + 1).padStart(2, '0');
    const currentYear = now.getFullYear();
    const monthYear = `${currentMonth}-${currentYear}`;
    
    console.log('📅 Date info:', { 
      currentMonth, 
      currentYear, 
      monthYear,
      currentDate: now.toISOString().split('T')[0]
    });

    // Make request using dynamic API configuration
    // The API config should be stored in MongoDB with name 'regularization-quick-add'
    const response = await ApiService.makeApiCall('regularization-quick-add', null, {
      headers: {
        'Authorization': `Bearer ${token}`
      },
      params: {
        monthYear: monthYear
      }
    });

    console.log('✅ Regularization data fetched from dynamic config');
    console.log('Response status:', response.status);
    console.log('Response data keys:', Object.keys(response.data || {}));

    // Process the response data
    let message;
    if (response.data && response.data.data && response.data.data.attendanceInfo) {
      if (response.data.data.attendanceInfo.length === 0) {
        message = `📋 **Attendance Regularization**\n\nNo attendance records found for ${response.data.data.monthYear || monthYear} that require regularization.\n\nℹ️ **Possible reasons:**\n• No attendance data for this month\n• All attendance is already regularized\n• You may need to check a different month\n• API might be returning empty data\n\n🔍 **Debug Info:** Check server logs for detailed API response.`;
        console.log('🔍 No records found - showing empty message');
      } else {
        message = `📋 **Attendance Regularization**\n\nFound ${response.data.data.attendanceInfo.length} attendance record${response.data.data.attendanceInfo.length !== 1 ? 's' : ''} for ${response.data.data.monthYear || monthYear}. Click "Apply" next to any date to submit a regularization request.`;
      }
    } else {
      message = `📋 **Attendance Regularization**\n\nUnexpected response format from API. Please check the configuration.`;
    }

    // Return the processed response
    res.json({
      success: true,
      message: 'Regularization data fetched successfully',
      data: {
        message: message,
        apiResponse: response.data,
        attendanceInfo: response.data?.data?.attendanceInfo || [],
        isRegularizationData: true
      }
    });

  } catch (error) {
    console.error('❌ Error fetching regularization data:', error);

    // Handle different types of errors
    if (error.message.includes('not found or inactive')) {
      // API configuration not found
      return res.status(500).json({
        success: false,
        message: 'Regularization service configuration not found. Please contact administrator.',
        data: {
          message: `❌ **Error fetching regularization data**\n\nConfiguration error: ${error.message}`,
          apiResponse: null
        }
      });
    }

    if (error.response) {
      // External API returned an error response
      const status = error.response.status;
      const errorData = error.response.data;
      
      console.error('External API error status:', status);
      console.error('External API error data:', errorData);

      return res.status(status).json({
        success: false,
        message: 'External API error',
        data: {
          message: `❌ **Error fetching regularization data**\n\n${errorData.message || `API Error: ${status}`}`,
          apiResponse: errorData
        }
      });
    } else if (error.request) {
      // Network error
      console.error('Network error:', error.message);
      res.status(503).json({
        success: false,
        message: 'Unable to connect to regularization service',
        data: {
          message: `❌ **Error fetching regularization data**\n\nNetwork error: ${error.message}`,
          apiResponse: null
        }
      });
    } else {
      // Other error
      console.error('Unexpected error:', error.message);
      res.status(500).json({
        success: false,
        message: 'An unexpected error occurred',
        data: {
          message: `❌ **Error fetching regularization data**\n\nUnexpected error: ${error.message}`,
          apiResponse: null
        }
      });
    }
  }
};

// Chat routes
router.route('/').post(processMessage);
router.route('/submit-form').post(submitForm);
router.route('/conversations').get(getConversations);
router.route('/conversations/:id').get(getConversation);
router.route('/regularization-data').get(getRegularizationData);

module.exports = router;