const express = require('express');
const router = express.Router();
const multer = require('multer');
const {
  uploadDocuments,
  getDocuments,
  getDocumentById,
  downloadDocument,
  deleteDocument,
  searchDocuments,
} = require('../controllers/documentController');

// Configure multer for memory storage (files will be stored in memory as Buffer objects)
const storage = multer.memoryStorage();
const upload = multer({ 
  storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept all file types
    cb(null, true);
  }
});

// Upload multiple documents
router.post('/upload', upload.array('documents', 10), uploadDocuments);

// Debug route
router.get('/debug', (req, res) => {
  res.json({ message: 'Document routes are working' });
});

// Get all documents (metadata only)
router.get('/', getDocuments);

// Search documents
router.get('/search', searchDocuments);

// Get document by ID
router.get('/:id', getDocumentById);

// Download document
router.get('/:id/download', downloadDocument);

// Delete document
router.delete('/:id', deleteDocument);

module.exports = router;