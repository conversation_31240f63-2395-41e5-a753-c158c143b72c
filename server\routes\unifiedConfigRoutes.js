const express = require('express');
const router = express.Router();
const UnifiedConfig = require('../models/UnifiedConfig');
const unifiedDynamicMatchingService = require('../services/unifiedDynamicMatchingService');
const formLinkingService = require('../services/formLinkingService');
const payloadBuilderService = require('../services/payloadBuilderService');

// @desc    Get all configurations (both APIs and Forms)
// @route   GET /api/unified-configs
// @access  Public
router.get('/', async (req, res) => {
  try {
    const { type, category, active, includeMetadata = 'false', limit, skip, forEdit = 'false' } = req.query;
    
    let query = {};
    if (type) query.type = type;
    if (category) query.category = category;
    if (active !== undefined) query.isActive = active === 'true';
    
    let queryBuilder = UnifiedConfig.find(query)
      .sort({ type: 1, priority: -1, createdAt: -1 });
    
    // Add pagination if provided
    if (skip) queryBuilder = queryBuilder.skip(parseInt(skip));
    if (limit) queryBuilder = queryBuilder.limit(parseInt(limit));
    
    const configs = await queryBuilder;
    
    // Get total count for pagination
    const totalCount = await UnifiedConfig.countDocuments(query);
    
    // Add metadata if requested
    const response = configs.map(config => {
      // Always return full config with actual tokens (including authconfig.token, authconfig.usecustomtoken, etc.)
      // This ensures the client gets complete configuration objects
      const configData = config.toObject();
      
      if (includeMetadata === 'true') {
        configData.metadata = {
          configType: config.type,
          isActive: config.isActive,
          lastUpdated: config.updatedAt,
          fieldsCount: config.type === 'form' ? config.formConfig?.fields?.length || 0 : 0,
          hasValidation: config.type === 'form' ? config.formConfig?.fields?.some(field => field.required) || false : false,
          hasSubmitApi: config.type === 'form' ? !!config.formConfig?.submitApiConfig?.endpoint : false,
          hasApiEndpoint: config.type === 'api' ? !!config.apiConfig?.endpoint : false
        };
      }
      
      return configData;
    });
    
    // For backward compatibility, return array directly if no special parameters
    if (includeMetadata === 'false' && !limit && !skip) {
      res.json(response);
    } else {
      // Return enhanced response with metadata
      res.json({
        count: response.length,
        totalCount,
        filters: { type, category, active },
        pagination: { 
          limit: limit ? parseInt(limit) : null, 
          skip: skip ? parseInt(skip) : null 
        },
        data: response
      });
    }
  } catch (error) {
    console.error('Error fetching configurations:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Get configuration by name
// @route   GET /api/unified-configs/name/:name
// @access  Public
router.get('/name/:name', async (req, res) => {
  try {
    const { name } = req.params;
    const { type, active } = req.query;
    
    // Build query object
    const query = { name };
    
    // Add type filter if specified
    if (type) {
      query.type = type;
    }
    
    // Add active filter if specified
    if (active !== undefined) {
      query.isActive = active === 'true';
    }
    
    const config = await UnifiedConfig.findOne(query);
    
    if (!config) {
      return res.status(404).json({ 
        message: `Configuration '${name}' not found`,
        query: query
      });
    }
    
    // Validate form configuration if it's a form type
    if (config.type === 'form') {
      if (!config.formConfig || !config.formConfig.fields || config.formConfig.fields.length === 0) {
        return res.status(400).json({ 
          message: 'Invalid form configuration: no fields defined',
          configName: name
        });
      }
      
      // Validate submit API configuration
      if (!config.formConfig.submitApiConfig || !config.formConfig.submitApiConfig.endpoint) {
        return res.status(400).json({ 
          message: 'Invalid form configuration: submit API endpoint not configured',
          configName: name
        });
      }
    }
    
    // Validate API configuration if it's an API type
    if (config.type === 'api') {
      if (!config.apiConfig || !config.apiConfig.endpoint) {
        return res.status(400).json({ 
          message: 'Invalid API configuration: endpoint not configured',
          configName: name
        });
      }
    }
    
    // Return the configuration with additional metadata
    const response = {
      ...config.toObject(),
      metadata: {
        configType: config.type,
        isActive: config.isActive,
        lastUpdated: config.updatedAt,
        fieldsCount: config.type === 'form' ? config.formConfig?.fields?.length || 0 : 0,
        hasValidation: config.type === 'form' ? config.formConfig?.fields?.some(field => field.required) || false : false
      }
    };
    
    res.json(response);
  } catch (error) {
    console.error('Error fetching configuration by name:', error);
    res.status(500).json({ 
      message: 'Server error while fetching configuration', 
      error: error.message,
      configName: req.params.name
    });
  }
});

// @desc    Get forms available for linking
// @route   GET /api/unifiedconfigs/forms-for-linking
// @access  Public
router.get('/forms-for-linking', async (req, res) => {
  try {
    const forms = await UnifiedConfig.find({ 
      type: 'form', 
      isActive: true 
    }).select('_id name category description').sort({ name: 1 });
    
    res.json({
      success: true,
      forms: forms
    });
  } catch (error) {
    console.error('Error fetching forms for linking:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// @desc    Get configurations by type (api or form)
// @route   GET /api/unified-configs/type/:type
// @access  Public
router.get('/type/:type', async (req, res) => {
  try {
    const { type } = req.params;
    const { active, category, includeMetadata = 'false' } = req.query;
    
    if (!['api', 'form'].includes(type)) {
      return res.status(400).json({ message: 'Invalid type. Must be "api" or "form"' });
    }
    
    const query = { type };
    if (active !== undefined) query.isActive = active === 'true';
    if (category) query.category = category;
    
    const configs = await UnifiedConfig.find(query)
      .sort({ priority: -1, createdAt: -1 });
    
    // Add metadata if requested
    const response = configs.map(config => {
      const configData = config.toObject();
      
      if (includeMetadata === 'true') {
        configData.metadata = {
          configType: config.type,
          isActive: config.isActive,
          lastUpdated: config.updatedAt,
          fieldsCount: config.type === 'form' ? config.formConfig?.fields?.length || 0 : 0,
          hasValidation: config.type === 'form' ? config.formConfig?.fields?.some(field => field.required) || false : false,
          hasSubmitApi: config.type === 'form' ? !!config.formConfig?.submitApiConfig?.endpoint : false,
          hasApiEndpoint: config.type === 'api' ? !!config.apiConfig?.endpoint : false
        };
      }
      
      return configData;
    });
    
    // For backward compatibility, return array directly if no metadata requested
    if (includeMetadata === 'false') {
      res.json(response);
    } else {
      // Return enhanced response with metadata
      res.json({
        count: response.length,
        type: type,
        filters: { active, category },
        data: response
      });
    }
  } catch (error) {
    console.error('Error fetching configurations by type:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Get single configuration
// @route   GET /api/unified-configs/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const { includeMetadata = 'false', forEdit = 'false' } = req.query;
    
    const config = await UnifiedConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    // Validate configuration based on type
    if (config.type === 'form') {
      if (!config.formConfig || !config.formConfig.fields || config.formConfig.fields.length === 0) {
        console.warn(`Form configuration '${config.name}' has no fields defined`);
      }
      if (!config.formConfig?.submitApiConfig?.endpoint) {
        console.warn(`Form configuration '${config.name}' has no submit API endpoint`);
      }
    }
    
    if (config.type === 'api') {
      if (!config.apiConfig?.endpoint) {
        console.warn(`API configuration '${config.name}' has no endpoint defined`);
      }
    }
    
    // Always return full config with actual tokens (including authconfig.token, authconfig.usecustomtoken, etc.)
    const response = config.toObject();
    
    // Add metadata if requested
    if (includeMetadata === 'true') {
      response.metadata = {
        configType: config.type,
        isActive: config.isActive,
        lastUpdated: config.updatedAt,
        fieldsCount: config.type === 'form' ? config.formConfig?.fields?.length || 0 : 0,
        hasValidation: config.type === 'form' ? config.formConfig?.fields?.some(field => field.required) || false : false,
        hasSubmitApi: config.type === 'form' ? !!config.formConfig?.submitApiConfig?.endpoint : false,
        hasApiEndpoint: config.type === 'api' ? !!config.apiConfig?.endpoint : false,
        triggerPhrasesCount: config.triggerPhrases ? config.triggerPhrases.length : 0,
        keywordsCount: config.keywords ? config.keywords.length : 0,
        tagsCount: config.tags ? config.tags.length : 0
      };
    }
    
    res.json(response);
  } catch (error) {
    console.error('Error fetching configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Create new configuration (API or Form)
// @route   POST /api/unified-configs
// @access  Public
router.post('/', async (req, res) => {
  try {
    const configData = req.body;
    console.log('Creating new config with data:', JSON.stringify(configData, null, 2));
    
    // Validate required fields
    if (!configData.name || !configData.type) {
      return res.status(400).json({ 
        message: 'Name and type are required' 
      });
    }
    
    if (!['api', 'form'].includes(configData.type)) {
      return res.status(400).json({ 
        message: 'Type must be "api" or "form"' 
      });
    }
    
    // Check if name already exists
    const existingConfig = await UnifiedConfig.findOne({ name: configData.name });
    if (existingConfig) {
      return res.status(400).json({ 
        message: 'Configuration with this name already exists' 
      });
    }
    
    // Create appropriate configuration structure
    let processedData = {
      name: configData.name,
      type: configData.type,
      description: configData.description || '',
      prompt: configData.prompt || '',
      keywords: configData.keywords || [],
      triggerPhrases: configData.triggerPhrases || [],
      isActive: configData.isActive !== undefined ? configData.isActive : true,
      priority: configData.priority || 0,
      category: configData.category || 'general',
      tags: configData.tags || []
    };
    
    if (configData.type === 'api') {
      processedData.apiConfig = {
        endpoint: configData.endpoint || configData.apiConfig?.endpoint || '',
        method: configData.method || configData.apiConfig?.method || 'GET',
        headers: new Map(Object.entries(configData.headers || configData.apiConfig?.headers || {})),
        authType: configData.authType || configData.apiConfig?.authType || 'none',
        authConfig: new Map(Object.entries(configData.authConfig || configData.apiConfig?.authConfig || {})),
        responseTemplate: configData.responseTemplate || configData.apiConfig?.responseTemplate || '',
        timeout: configData.timeout || configData.apiConfig?.timeout || 30000,
        retryCount: configData.retryCount || configData.apiConfig?.retryCount || 3,
        dataInjection: {
          injectUserData: configData.dataInjection?.injectUserData !== false,
          requiredFields: configData.dataInjection?.requiredFields || [],
          autoFields: {
            empId: configData.dataInjection?.autoFields?.empId || false,
            token: configData.dataInjection?.autoFields?.token || false,
            roleType: configData.dataInjection?.autoFields?.roleType || false
          }
        },
        customPayload: {
          enabled: configData.customPayload?.enabled || false,
          structure: configData.customPayload?.structure || {},
          mergeStrategy: configData.customPayload?.mergeStrategy || 'replace',
          transformations: configData.customPayload?.transformations || []
        }
      };
    } else if (configData.type === 'form') {
      console.log('Processing form config:', {
        conversationalFlow: configData.formConfig?.conversationalFlow,
        hybridFlow: configData.formConfig?.hybridFlow,
        fieldsCount: configData.formConfig?.fields?.length || 0
      });
      
      processedData.formConfig = {
        conversationalFlow: {
          enabled: configData.formConfig?.conversationalFlow?.enabled || false,
          completionMessage: configData.formConfig?.conversationalFlow?.completionMessage || 'Thank you! Your form has been submitted successfully.'
        },
        hybridFlow: {
          enabled: configData.formConfig?.hybridFlow?.enabled || false,
          completionMessage: configData.formConfig?.hybridFlow?.completionMessage || 'Please complete the remaining form fields below.'
        },
        fields: configData.formConfig?.fields || [],
        submitApiConfig: {
          endpoint: configData.apiConfig?.endpoint || configData.formConfig?.submitApiConfig?.endpoint || '',
          method: configData.apiConfig?.method || configData.formConfig?.submitApiConfig?.method || 'POST',
          headers: new Map(Object.entries(configData.apiConfig?.headers || configData.formConfig?.submitApiConfig?.headers || {})),
          authType: configData.apiConfig?.authType || configData.formConfig?.submitApiConfig?.authType || 'bearer',
          authConfig: new Map(Object.entries(configData.apiConfig?.authDetails || configData.formConfig?.submitApiConfig?.authConfig || {})),
          dataMapping: new Map(Object.entries(configData.formConfig?.submitApiConfig?.dataMapping || {})),
          customPayload: {
            enabled: configData.formConfig?.submitApiConfig?.customPayload?.enabled || false,
            structure: configData.formConfig?.submitApiConfig?.customPayload?.structure || {},
            mergeStrategy: configData.formConfig?.submitApiConfig?.customPayload?.mergeStrategy || 'replace',
            transformations: configData.formConfig?.submitApiConfig?.customPayload?.transformations || []
          },
          accessControl: {
            enabled: configData.formConfig?.submitApiConfig?.accessControl?.enabled || false,
            allowedRoles: configData.formConfig?.submitApiConfig?.accessControl?.allowedRoles || []
          },
          successMessage: configData.formConfig?.submitApiConfig?.successMessage || 'Form submitted successfully!',
          errorMessage: configData.formConfig?.submitApiConfig?.errorMessage || 'Failed to submit form. Please try again.'
        },
        prefillData: new Map(Object.entries(configData.formConfig?.prefillData || {})),
        formLinking: {
          enabled: configData.formConfig?.formLinking?.enabled || false,
          recordActions: (configData.formConfig?.formLinking?.recordActions || []).map(action => ({
            ...action,
            fieldMapping: action.fieldMapping ? new Map(Object.entries(action.fieldMapping)) : new Map(),
            // Ensure autoSubmitOnClick is properly saved
            autoSubmitOnClick: action.autoSubmitOnClick || false,
            // Ensure autoTrigger is properly saved
            autoTrigger: action.autoTrigger || { enabled: false, delaySeconds: 2 }
          }))
        }
      };
    }
    
    const config = new UnifiedConfig(processedData);
    await config.save();
    
    console.log(`✅ Created new ${configData.type} configuration: ${config.name}`);
    res.status(201).json(config.toObject());
  } catch (error) {
    console.error('Error creating configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Update configuration
// @route   PUT /api/unified-configs/:id
// @access  Public
router.put('/:id', async (req, res) => {
  try {
    const config = await UnifiedConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    const updateData = req.body;
    console.log('Updating config with data:', JSON.stringify(updateData, null, 2));
    
    // Check if new name conflicts with existing (if name is being changed)
    if (updateData.name && updateData.name !== config.name) {
      const existingConfig = await UnifiedConfig.findOne({ name: updateData.name });
      if (existingConfig) {
        return res.status(400).json({ 
          message: 'Configuration with this name already exists' 
        });
      }
    }
    
    // Update basic fields
    if (updateData.name) config.name = updateData.name;
    if (updateData.description !== undefined) config.description = updateData.description;
    if (updateData.prompt !== undefined) config.prompt = updateData.prompt;
    if (updateData.keywords !== undefined) config.keywords = updateData.keywords;
    if (updateData.triggerPhrases !== undefined) config.triggerPhrases = updateData.triggerPhrases;
    if (updateData.isActive !== undefined) config.isActive = updateData.isActive;
    if (updateData.priority !== undefined) config.priority = updateData.priority;
    if (updateData.category !== undefined) config.category = updateData.category;
    if (updateData.tags !== undefined) config.tags = updateData.tags;
    
    // Update type-specific configurations
    if (config.type === 'api' && updateData.apiConfig) {
      const apiUpdate = updateData.apiConfig;
      if (!config.apiConfig) config.apiConfig = {};
      
      if (apiUpdate.endpoint !== undefined) config.apiConfig.endpoint = apiUpdate.endpoint;
      if (apiUpdate.method !== undefined) config.apiConfig.method = apiUpdate.method;
      if (apiUpdate.headers !== undefined) config.apiConfig.headers = new Map(Object.entries(apiUpdate.headers));
      if (apiUpdate.authType !== undefined) config.apiConfig.authType = apiUpdate.authType;
      if (apiUpdate.authConfig !== undefined) config.apiConfig.authConfig = new Map(Object.entries(apiUpdate.authConfig));
      if (apiUpdate.responseTemplate !== undefined) config.apiConfig.responseTemplate = apiUpdate.responseTemplate;
      if (apiUpdate.timeout !== undefined) config.apiConfig.timeout = apiUpdate.timeout;
      if (apiUpdate.retryCount !== undefined) config.apiConfig.retryCount = apiUpdate.retryCount;
      if (apiUpdate.dataInjection !== undefined) config.apiConfig.dataInjection = apiUpdate.dataInjection;
      if (apiUpdate.customPayload !== undefined) config.apiConfig.customPayload = apiUpdate.customPayload;
    } else if (config.type === 'form' && updateData.formConfig) {
      const formUpdate = updateData.formConfig;
      if (!config.formConfig) config.formConfig = {};
      
      console.log('Updating form config with:', {
        conversationalFlow: formUpdate.conversationalFlow,
        hybridFlow: formUpdate.hybridFlow,
        fieldsCount: formUpdate.fields?.length || 0
      });
      
      if (formUpdate.conversationalFlow !== undefined) {
        config.formConfig.conversationalFlow = {
          enabled: formUpdate.conversationalFlow.enabled || false,
          completionMessage: formUpdate.conversationalFlow.completionMessage || 'Thank you! Your form has been submitted successfully.'
        };
      }
      if (formUpdate.hybridFlow !== undefined) {
        config.formConfig.hybridFlow = {
          enabled: formUpdate.hybridFlow.enabled || false,
          completionMessage: formUpdate.hybridFlow.completionMessage || 'Please complete the remaining form fields below.'
        };
      }
      if (formUpdate.fields !== undefined) config.formConfig.fields = formUpdate.fields;
      if (formUpdate.submitApiConfig !== undefined) {
        config.formConfig.submitApiConfig = {
          ...config.formConfig.submitApiConfig,
          ...formUpdate.submitApiConfig,
          headers: formUpdate.submitApiConfig.headers ? new Map(Object.entries(formUpdate.submitApiConfig.headers)) : config.formConfig.submitApiConfig.headers,
          authConfig: formUpdate.submitApiConfig.authConfig ? new Map(Object.entries(formUpdate.submitApiConfig.authConfig)) : config.formConfig.submitApiConfig.authConfig,
          dataMapping: formUpdate.submitApiConfig.dataMapping ? new Map(Object.entries(formUpdate.submitApiConfig.dataMapping)) : config.formConfig.submitApiConfig.dataMapping,
          customPayload: formUpdate.submitApiConfig.customPayload ? formUpdate.submitApiConfig.customPayload : config.formConfig.submitApiConfig.customPayload,
          accessControl: formUpdate.submitApiConfig.accessControl ? formUpdate.submitApiConfig.accessControl : config.formConfig.submitApiConfig.accessControl
        };
      }
      if (formUpdate.prefillData !== undefined) config.formConfig.prefillData = new Map(Object.entries(formUpdate.prefillData));
      if (formUpdate.formLinking !== undefined) {
        config.formConfig.formLinking = {
          enabled: formUpdate.formLinking.enabled || false,
          recordActions: (formUpdate.formLinking.recordActions || []).map(action => ({
            ...action,
            fieldMapping: action.fieldMapping ? new Map(Object.entries(action.fieldMapping)) : new Map(),
            // Ensure autoSubmitOnClick is properly saved
            autoSubmitOnClick: action.autoSubmitOnClick || false,
            // Ensure autoTrigger is properly saved
            autoTrigger: action.autoTrigger || { enabled: false, delaySeconds: 2 }
          }))
        };
      }
    }
    
    // Handle legacy format updates
    if (updateData.endpoint || updateData.method || updateData.headers) {
      if (config.type === 'api') {
        if (updateData.endpoint) config.apiConfig.endpoint = updateData.endpoint;
        if (updateData.method) config.apiConfig.method = updateData.method;
        if (updateData.headers) config.apiConfig.headers = new Map(Object.entries(updateData.headers));
      }
    }
    
    if (updateData.fields && config.type === 'form') {
      config.formConfig.fields = updateData.fields;
    }
    
    await config.save();
    
    console.log(`✅ Updated ${config.type} configuration: ${config.name}`);
    res.json(config.toObject());
  } catch (error) {
    console.error('Error updating configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Delete configuration
// @route   DELETE /api/unified-configs/:id
// @access  Public
router.delete('/:id', async (req, res) => {
  try {
    const config = await UnifiedConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    await UnifiedConfig.findByIdAndDelete(req.params.id);
    
    console.log(`🗑️ Deleted ${config.type} configuration: ${config.name}`);
    res.json({ message: 'Configuration deleted successfully' });
  } catch (error) {
    console.error('Error deleting configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Test API configuration
// @route   POST /api/unified-configs/:id/test
// @access  Public
router.post('/:id/test', async (req, res) => {
  try {
    const config = await UnifiedConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    if (config.type !== 'api') {
      return res.status(400).json({ message: 'Only API configurations can be tested' });
    }
    
    const testData = req.body.testData || {};
    const userData = {
      empId: req.headers['x-emp-id'],
      token: req.headers.authorization?.replace('Bearer ', ''),
      roleType: req.headers['x-role-type']
    };
    
    console.log(`🧪 Testing API configuration: ${config.name}`);
    
    const startTime = Date.now();
    const result = await unifiedDynamicMatchingService.executeDynamicApi(config, userData, testData);
    const responseTime = Date.now() - startTime;
    
    // Update last test result
    config.apiConfig.lastTestedAt = new Date();
    config.apiConfig.lastTestResult = {
      success: result.success,
      statusCode: result.status,
      responseTime: responseTime,
      message: result.success ? 'Test successful' : 'Test failed',
      error: result.error || null
    };
    await config.save();
    
    console.log(`🧪 Test result for ${config.name}:`, {
      success: result.success,
      status: result.status,
      responseTime: responseTime
    });
    
    res.json({
      success: result.success,
      status: result.status,
      statusText: result.statusText,
      responseTime: responseTime,
      data: result.data,
      error: result.error,
      formattedResponse: unifiedDynamicMatchingService.formatApiResponse(config, result)
    });
  } catch (error) {
    console.error('Error testing API configuration:', error);
    res.status(500).json({ 
      success: false,
      message: 'Test failed', 
      error: error.message 
    });
  }
});

// @desc    Search and match configurations based on query
// @route   POST /api/unified-configs/match
// @access  Public
router.post('/match', async (req, res) => {
  try {
    const { query } = req.body;
    
    if (!query) {
      return res.status(400).json({ message: 'Query is required' });
    }
    
    console.log(`🔍 Matching configurations for query: "${query}"`);
    
    const matchResults = await unifiedDynamicMatchingService.findMatches(query);
    
    res.json({
      query: query,
      totalForms: matchResults.forms.length,
      totalApis: matchResults.apis.length,
      totalConfigs: matchResults.totalConfigs,
      bestMatch: matchResults.bestMatch,
      confidence: matchResults.confidence,
      matchType: matchResults.matchType,
      forms: matchResults.forms.slice(0, 5), // Limit results
      apis: matchResults.apis.slice(0, 5),
      allMatches: matchResults.allMatches.slice(0, 10)
    });
  } catch (error) {
    console.error('Error matching configurations:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Execute API by ID with dynamic data
// @route   POST /api/unified-configs/:id/execute
// @access  Public
router.post('/:id/execute', async (req, res) => {
  try {
    const config = await UnifiedConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    if (config.type !== 'api' || !config.isActive) {
      return res.status(400).json({ message: 'Configuration is not an active API' });
    }
    
    const requestData = req.body;
    const userData = {
      empId: req.headers['x-emp-id'],
      token: req.headers.authorization?.replace('Bearer ', ''),
      roleType: req.headers['x-role-type']
    };
    
    console.log(`⚡ Executing API: ${config.name}`);
    
    const result = await unifiedDynamicMatchingService.executeDynamicApi(config, userData, requestData);
    const formattedResponse = unifiedDynamicMatchingService.formatApiResponse(config, result);
    
    res.json({
      configId: config._id,
      apiName: config.name,
      success: result.success,
      status: result.status,
      statusText: result.statusText,
      data: result.data,
      error: result.error,
      formattedResponse: formattedResponse
    });
  } catch (error) {
    console.error('Error executing API:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Submit form by ID
// @route   POST /api/unified-configs/:id/submit
// @access  Public
router.post('/:id/submit', async (req, res) => {
  try {
    const config = await UnifiedConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }
    
    if (config.type !== 'form' || !config.isActive) {
      return res.status(400).json({ message: 'Configuration is not an active form' });
    }
    
    const formData = req.body.formData || req.body;
    const userData = {
      empId: req.headers['x-emp-id'],
      token: req.headers.authorization?.replace('Bearer ', ''),
      roleType: req.headers['x-role-type']
    };
    
    console.log(`📝 Submitting form: ${config.name}`);
    console.log('📝 Form data received:', formData);
    console.log('📝 User data:', { empId: userData.empId, roleType: userData.roleType, hasToken: !!userData.token });
    
    const result = await unifiedDynamicMatchingService.submitDynamicForm(config, formData, userData);
    
    console.log('📝 Form submission result:', {
      success: result.success,
      status: result.status,
      statusText: result.statusText,
      message: result.message,
      error: result.error
    });
    
    res.json({
      configId: config._id,
      formName: config.name,
      success: result.success,
      status: result.status,
      statusText: result.statusText,
      data: result.data,
      error: result.error,
      message: result.message
    });
  } catch (error) {
    console.error('Error submitting form:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Preview payload structure for a configuration
// @route   POST /api/unified-configs/:id/preview-payload
// @access  Public
router.post('/:id/preview-payload', async (req, res) => {
  try {
    const config = await UnifiedConfig.findById(req.params.id);
    if (!config) {
      return res.status(404).json({ message: 'Configuration not found' });
    }

    const { sampleData = {}, userData = {} } = req.body;

    let payload = {};
    let previewType = '';

    if (config.type === 'api') {
      payload = payloadBuilderService.buildApiPayload(config.apiConfig, userData, sampleData);
      previewType = 'API';
    } else if (config.type === 'form') {
      payload = payloadBuilderService.buildFormPayload(config, sampleData, userData);
      previewType = 'Form';
    }

    res.json({
      success: true,
      configId: config._id,
      configName: config.name,
      configType: config.type,
      previewType: previewType,
      payload: payload,
      sampleData: sampleData,
      userData: userData
    });
  } catch (error) {
    console.error('Error previewing payload:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// @desc    Validate payload structure
// @route   POST /api/unified-configs/validate-payload
// @access  Public
router.post('/validate-payload', async (req, res) => {
  try {
    const { structure } = req.body;
    
    if (!structure) {
      return res.status(400).json({ 
        valid: false, 
        error: 'Payload structure is required' 
      });
    }

    const validation = payloadBuilderService.validatePayloadStructure(structure);
    
    res.json({
      valid: validation.valid,
      error: validation.error || null,
      structure: structure
    });
  } catch (error) {
    console.error('Error validating payload structure:', error);
    res.status(500).json({ 
      valid: false, 
      error: 'Server error during validation' 
    });
  }
});

// @desc    Get available placeholders for payload building
// @route   GET /api/unified-configs/payload-placeholders
// @access  Public
router.get('/payload-placeholders', async (req, res) => {
  try {
    const placeholders = payloadBuilderService.getAvailablePlaceholders();
    
    res.json({
      success: true,
      placeholders: placeholders
    });
  } catch (error) {
    console.error('Error fetching payload placeholders:', error);
    res.status(500).json({ 
      success: false, 
      message: 'Server error', 
      error: error.message 
    });
  }
});

// @desc    Get configuration statistics
// @route   GET /api/unified-configs/stats/overview
// @access  Public
router.get('/stats/overview', async (req, res) => {
  try {
    const [totalConfigs, totalForms, totalApis, activeConfigs, inactiveConfigs] = await Promise.all([
      UnifiedConfig.countDocuments(),
      UnifiedConfig.countDocuments({ type: 'form' }),
      UnifiedConfig.countDocuments({ type: 'api' }),
      UnifiedConfig.countDocuments({ isActive: true }),
      UnifiedConfig.countDocuments({ isActive: false })
    ]);
    
    const categories = await UnifiedConfig.aggregate([
      { $group: { _id: '$category', count: { $sum: 1 } } },
      { $sort: { count: -1 } }
    ]);
    
    res.json({
      total: {
        configs: totalConfigs,
        forms: totalForms,
        apis: totalApis,
        active: activeConfigs,
        inactive: inactiveConfigs
      },
      categories: categories.map(cat => ({
        name: cat._id,
        count: cat.count
      }))
    });
  } catch (error) {
    console.error('Error fetching statistics:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Process form linking request
// @route   POST /api/unified-configs/:id/form-link
// @access  Public
router.post('/:id/form-link', async (req, res) => {
  try {
    const { recordData, parentData = {}, actionIndex = 0, buttonText } = req.body;
    
    if (!recordData) {
      return res.status(400).json({ message: 'Record data is required' });
    }
    
    const result = await formLinkingService.processFormLinking(
      req.params.id,
      recordData,
      parentData,
      actionIndex,
      buttonText
    );
    
    if (result.success) {
      res.json({
        success: true,
        targetForm: result.targetForm,
        prefillData: result.prefillData,
        buttonText: result.buttonText,
        buttonStyle: result.buttonStyle,
        autoTrigger: result.autoTrigger,
        autoSubmitOnClick: result.autoSubmitOnClick
      });
    } else {
      res.status(400).json({
        success: false,
        message: result.error
      });
    }
  } catch (error) {
    console.error('Error processing form linking:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Check auto-trigger for form and record
// @route   POST /api/unified-configs/:id/check-auto-trigger
// @access  Public
router.post('/:id/check-auto-trigger', async (req, res) => {
  try {
    const { recordData } = req.body;
    
    if (!recordData) {
      return res.status(400).json({ message: 'Record data is required' });
    }
    
    const formLinkingConfig = await formLinkingService.getFormLinkingConfig(req.params.id);
    
    if (!formLinkingConfig) {
      return res.json({
        success: true,
        shouldTrigger: false
      });
    }
    
    const autoTriggerInfo = formLinkingService.shouldAutoTrigger(recordData, formLinkingConfig);
    
    res.json({
      success: true,
      ...autoTriggerInfo
    });
  } catch (error) {
    console.error('Error checking auto-trigger:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Get available forms for linking
// @route   GET /api/unified-configs/forms-for-linking
// @access  Public
router.get('/forms-for-linking', async (req, res) => {
  try {
    const forms = await formLinkingService.getAvailableFormsForLinking();
    
    res.json({
      success: true,
      forms: forms
    });
  } catch (error) {
    console.error('Error fetching forms for linking:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Get form linking configuration
// @route   GET /api/unified-configs/:id/form-linking-config
// @access  Public
router.get('/:id/form-linking-config', async (req, res) => {
  try {
    const config = await formLinkingService.getFormLinkingConfig(req.params.id);
    
    if (!config) {
      return res.status(404).json({ message: 'Form linking configuration not found' });
    }
    
    res.json({
      success: true,
      config: config
    });
  } catch (error) {
    console.error('Error fetching form linking configuration:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Enable auto-trigger for form linking
// @route   POST /api/unifiedconfigs/:id/enable-auto-trigger
// @access  Public
router.post('/:id/enable-auto-trigger', async (req, res) => {
  try {
    const { delaySeconds = 3, actionIndex = 0 } = req.body;
    
    const form = await UnifiedConfig.findById(req.params.id);
    
    if (!form) {
      return res.status(404).json({ message: 'Form not found' });
    }
    
    if (!form.formConfig?.formLinking?.enabled) {
      return res.status(400).json({ message: 'Form linking is not enabled for this form' });
    }
    
    if (!form.formConfig.formLinking.recordActions || form.formConfig.formLinking.recordActions.length === 0) {
      return res.status(400).json({ message: 'No record actions configured for form linking' });
    }
    
    if (actionIndex >= form.formConfig.formLinking.recordActions.length) {
      return res.status(400).json({ message: 'Invalid action index' });
    }
    
    // Enable auto-trigger for the specified action
    form.formConfig.formLinking.recordActions[actionIndex].autoTrigger = {
      enabled: true,
      delaySeconds: delaySeconds
    };
    
    await form.save();
    
    res.json({
      success: true,
      message: `Auto-trigger enabled for action ${actionIndex} with ${delaySeconds} seconds delay`,
      data: form.formConfig.formLinking.recordActions[actionIndex]
    });
    
  } catch (error) {
    console.error('Error enabling auto-trigger:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// @desc    Disable auto-trigger for form linking
// @route   POST /api/unifiedconfigs/:id/disable-auto-trigger
// @access  Public
router.post('/:id/disable-auto-trigger', async (req, res) => {
  try {
    const { actionIndex = 0 } = req.body;
    
    const form = await UnifiedConfig.findById(req.params.id);
    
    if (!form) {
      return res.status(404).json({ message: 'Form not found' });
    }
    
    if (!form.formConfig?.formLinking?.recordActions || form.formConfig.formLinking.recordActions.length === 0) {
      return res.status(400).json({ message: 'No record actions configured for form linking' });
    }
    
    if (actionIndex >= form.formConfig.formLinking.recordActions.length) {
      return res.status(400).json({ message: 'Invalid action index' });
    }
    
    // Disable auto-trigger for the specified action
    form.formConfig.formLinking.recordActions[actionIndex].autoTrigger = {
      enabled: false,
      delaySeconds: 2
    };
    
    await form.save();
    
    res.json({
      success: true,
      message: `Auto-trigger disabled for action ${actionIndex}`,
      data: form.formConfig.formLinking.recordActions[actionIndex]
    });
    
  } catch (error) {
    console.error('Error disabling auto-trigger:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

module.exports = router;
