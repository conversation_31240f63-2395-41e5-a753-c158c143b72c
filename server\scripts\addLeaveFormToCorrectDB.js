const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB using the actual connection string from .env
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/form_builder';
    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB Connected to:', mongoUri);
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function addLeaveFormToCorrectDB() {
  try {
    await connectDB();
    
    // Check if Leave Application form already exists
    const existingForm = await UnifiedConfig.findOne({name: 'Leave Application'});
    if (existingForm) {
      console.log('✅ Leave Application form already exists, updating it...');
      
      // Update the existing form
      existingForm.formConfig.fields = [
        {
          name: 'leaveType',
          label: 'Leave Type',
          type: 'select',
          required: true,
          options: ['sick leave', 'casual leave', 'Privilege Leave']
        },
        {
          name: 'fromDate',
          label: 'From Date',
          type: 'date',
          required: true
        },
        {
          name: 'fromSession',
          label: 'From Session',
          type: 'select',
          required: true,
          options: ['Session 1', 'Session 2']
        },
        {
          name: 'toDate',
          label: 'To Date',
          type: 'date',
          required: true
        },
        {
          name: 'toSession',
          label: 'To Session',
          type: 'select',
          required: true,
          options: ['Session 1', 'Session 2']
        },
        {
          name: 'reason',
          label: 'Reason',
          type: 'textarea',
          required: true,
          placeholder: 'Please provide reason for your leave application'
        }
      ];
      existingForm.triggerPhrases = ['leave apply', 'apply leave', 'apply for leave', 'leave application', 'request leave', 'submit leave'];
      existingForm.priority = 100;
      existingForm.isActive = true;
      
      await existingForm.save();
      console.log('✅ Updated existing Leave Application form');
    } else {
      // Create new Leave Application form
      const leaveForm = new UnifiedConfig({
        name: 'Leave Application',
        type: 'form',
        description: 'Apply for various types of leave',
        prompt: 'Use this form when you want to apply for leave, vacation, time off, or any type of absence from work.',
        keywords: ['leave', 'vacation', 'time off', 'absence', 'pto', 'holiday', 'sick leave', 'personal leave'],
        triggerPhrases: ['leave apply', 'apply leave', 'apply for leave', 'leave application', 'request leave', 'submit leave'],
        isActive: true,
        priority: 100,
        category: 'general',
        createdBy: 'system',
        formConfig: {
          fields: [
            {
              name: 'leaveType',
              label: 'Leave Type',
              type: 'select',
              required: true,
              options: ['sick leave', 'casual leave', 'Privilege Leave']
            },
            {
              name: 'fromDate',
              label: 'From Date',
              type: 'date',
              required: true
            },
            {
              name: 'fromSession',
              label: 'From Session',
              type: 'select',
              required: true,
              options: ['Session 1', 'Session 2']
            },
            {
              name: 'toDate',
              label: 'To Date',
              type: 'date',
              required: true
            },
            {
              name: 'toSession',
              label: 'To Session',
              type: 'select',
              required: true,
              options: ['Session 1', 'Session 2']
            },
            {
              name: 'reason',
              label: 'Reason',
              type: 'textarea',
              required: true,
              placeholder: 'Please provide reason for your leave application'
            }
          ],
          submitApiConfig: {
            endpoint: 'https://dev.budgie.co.in/budgie/v3/api/leave/apply',
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            authType: 'bearer',
            authConfig: {},
            dataMapping: {},
            successMessage: 'Leave application submitted successfully!',
            errorMessage: 'Failed to submit leave application. Please try again.',
            responseTemplate: ''
          }
        }
      });
      
      await leaveForm.save();
      console.log('✅ Created new Leave Application form');
    }
    
    // List all forms to verify
    const forms = await UnifiedConfig.find({type: 'form'}, 'name isActive priority').sort({priority: -1});
    console.log('\n📋 All forms in database:');
    forms.forEach((form, index) => {
      console.log(`${index + 1}. Name: ${form.name}, Active: ${form.isActive}, Priority: ${form.priority}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

addLeaveFormToCorrectDB();