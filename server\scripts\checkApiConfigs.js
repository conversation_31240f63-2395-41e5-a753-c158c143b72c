const mongoose = require('mongoose');
const ApiConfig = require('../models/ApiConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function checkApiConfigs() {
  try {
    await connectDB();
    
    // Check for forms in ApiConfig collection
    const forms = await ApiConfig.find({type: 'form'}, 'name isActive priority type');
    console.log('Forms in ApiConfig collection:');
    forms.forEach((form, index) => {
      console.log(`${index + 1}. Name: ${form.name}, Active: ${form.isActive}, Priority: ${form.priority}, ID: ${form._id}`);
    });
    
    // Delete the old Leave Apply form from ApiConfig
    const deleteResult = await ApiConfig.deleteMany({name: 'Leave Apply'});
    console.log('\n✅ Deleted Leave Apply forms from ApiConfig:', deleteResult);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkApiConfigs();