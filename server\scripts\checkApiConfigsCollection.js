const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function checkApiConfigsCollection() {
  try {
    await connectDB();
    
    // Check apiConfigs collection directly
    const docs = await mongoose.connection.db.collection('apiConfigs').find({
      $or: [
        {name: /Leave Apply/i},
        {_id: new mongoose.Types.ObjectId('68665cf3c9aa38ef23705497')}
      ]
    }).toArray();
    
    console.log('🔍 Found documents in apiConfigs collection:');
    docs.forEach((doc, index) => {
      console.log(`${index + 1}. Name: ${doc.name}, ID: ${doc._id}, Active: ${doc.isActive}, Type: ${doc.type}`);
    });
    
    // Delete the old Leave Apply form
    if (docs.length > 0) {
      const deleteResult = await mongoose.connection.db.collection('apiConfigs').deleteMany({
        $or: [
          {name: /Leave Apply/i},
          {_id: new mongoose.Types.ObjectId('68665cf3c9aa38ef23705497')}
        ]
      });
      console.log('\n✅ Deleted old Leave Apply forms:', deleteResult);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkApiConfigsCollection();