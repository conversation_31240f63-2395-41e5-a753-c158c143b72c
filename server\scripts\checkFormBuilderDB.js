const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB using the actual connection string from .env
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/form_builder';
    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB Connected to:', mongoUri);
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function checkFormBuilderDB() {
  try {
    await connectDB();
    
    const targetId = '68665cf3c9aa38ef23705497';
    
    // List all collections in form_builder database
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📋 Collections in form_builder database:');
    collections.forEach((collection, index) => {
      console.log(`${index + 1}. ${collection.name}`);
    });
    
    // Search for the specific form
    for (const collection of collections) {
      try {
        console.log(`\n🔍 Checking collection: ${collection.name}`);
        
        // Try to find by ObjectId
        let docs = [];
        try {
          docs = await mongoose.connection.db.collection(collection.name).find({
            _id: new mongoose.Types.ObjectId(targetId)
          }).toArray();
        } catch (error) {
          // If ObjectId fails, try as string
          docs = await mongoose.connection.db.collection(collection.name).find({
            _id: targetId
          }).toArray();
        }
        
        if (docs.length > 0) {
          console.log(`✅ FOUND in ${collection.name}:`, docs.length, 'documents');
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. Name: ${doc.name}, Type: ${doc.type}, Active: ${doc.isActive}`);
            console.log(`   ID: ${doc._id}`);
            console.log(`   Priority: ${doc.priority}`);
            console.log(`   Created: ${doc.createdAt}`);
          });
          
          // Delete the old form
          const deleteResult = await mongoose.connection.db.collection(collection.name).deleteOne({
            _id: new mongoose.Types.ObjectId(targetId)
          });
          console.log(`🗑️ Deleted old form:`, deleteResult);
        }
      } catch (error) {
        console.log(`⚠️  Error checking ${collection.name}:`, error.message);
      }
    }
    
    // Also search for "Leave Apply" by name
    console.log('\n🔍 Searching for "Leave Apply" by name...');
    for (const collection of collections) {
      try {
        const docs = await mongoose.connection.db.collection(collection.name).find({
          name: /Leave Apply/i
        }).toArray();
        
        if (docs.length > 0) {
          console.log(`✅ FOUND "Leave Apply" in ${collection.name}:`, docs.length, 'documents');
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. Name: ${doc.name}, ID: ${doc._id}, Active: ${doc.isActive}`);
          });
          
          // Delete all "Leave Apply" forms
          const deleteResult = await mongoose.connection.db.collection(collection.name).deleteMany({
            name: /Leave Apply/i
          });
          console.log(`🗑️ Deleted "Leave Apply" forms:`, deleteResult);
        }
      } catch (error) {
        // Skip collections that might have different structure
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkFormBuilderDB();