const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB using the actual connection string from .env
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGO_URI || 'mongodb://localhost:27017/form_builder';
    await mongoose.connect(mongoUri);
    console.log('✅ MongoDB Connected to:', mongoUri);
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function checkFormFields() {
  try {
    await connectDB();
    
    // Get the Leave Application form
    const form = await UnifiedConfig.findOne({name: 'Leave Application'});
    
    if (form) {
      console.log('📋 Leave Application Form Fields:');
      console.log('Form ID:', form._id);
      console.log('Form Name:', form.name);
      console.log('Form Active:', form.isActive);
      console.log('Form Priority:', form.priority);
      
      if (form.formConfig && form.formConfig.fields) {
        console.log('\n🔧 Form Fields:');
        form.formConfig.fields.forEach((field, index) => {
          console.log(`${index + 1}. ${field.name} (${field.label})`);
          console.log(`   Type: ${field.type}`);
          console.log(`   Required: ${field.required}`);
          if (field.options) {
            console.log(`   Options: ${JSON.stringify(field.options)}`);
          }
          if (field.placeholder) {
            console.log(`   Placeholder: ${field.placeholder}`);
          }
          console.log('');
        });
      } else {
        console.log('❌ No form fields found');
      }
      
      // Check submit API config
      if (form.formConfig && form.formConfig.submitApiConfig) {
        console.log('🚀 Submit API Configuration:');
        console.log('Endpoint:', form.formConfig.submitApiConfig.endpoint);
        console.log('Method:', form.formConfig.submitApiConfig.method);
        console.log('Auth Type:', form.formConfig.submitApiConfig.authType);
      }
    } else {
      console.log('❌ Leave Application form not found');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkFormFields();