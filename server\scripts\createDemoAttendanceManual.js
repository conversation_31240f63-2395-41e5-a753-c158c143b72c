const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');

async function createDemoAttendance() {
  try {
    // Connect to MongoDB
    await mongoose.connect('mongodb://localhost:27017/ai_form_assistant');
    console.log('MongoDB Connected');

    // Create Regularization form first
    const regularizationForm = new UnifiedConfig({
      name: 'Regularization',
      type: 'form',
      description: 'Employee regularization form for attendance correction',
      prompt: 'Please fill out the regularization form to correct your attendance.',
      keywords: ['regularization', 'attendance', 'correction'],
      triggerPhrases: ['regularization', 'attendance correction'],
      isActive: true,
      priority: 1,
      category: 'hr',
      tags: ['hr', 'attendance', 'regularization'],
      formConfig: {
        conversationalFlow: {
          enabled: false,
          completionMessage: 'Thank you! Your regularization request has been submitted successfully.'
        },
        fields: [
          {
            name: 'date',
            type: 'date',
            label: 'Date',
            required: true,
            placeholder: 'Select date'
          },
          {
            name: 'inTime',
            type: 'time',
            label: 'In Time',
            required: true,
            placeholder: 'Enter in time'
          },
          {
            name: 'outTime',
            type: 'time',
            label: 'Out Time',
            required: false,
            placeholder: 'Enter out time'
          },
          {
            name: 'reason',
            type: 'textarea',
            label: 'Reason',
            required: true,
            placeholder: 'Enter reason for regularization'
          },
          {
            name: 'appliedTo',
            type: 'text',
            label: 'Applied To',
            required: false,
            placeholder: 'Supervisor name'
          }
        ],
        submitApiConfig: {
          endpoint: 'https://api.example.com/demo/regularization',
          method: 'POST',
          headers: new Map([
            ['Content-Type', 'application/json'],
            ['Authorization', 'Bearer demo-token']
          ]),
          authType: 'bearer',
          authConfig: new Map([
            ['token', 'demo-token'],
            ['useCustomToken', 'true']
          ]),
          dataMapping: new Map(),
          customPayload: {
            enabled: false,
            structure: {},
            mergeStrategy: 'replace',
            transformations: []
          },
          successMessage: 'Regularization request submitted successfully!',
          errorMessage: 'Failed to submit regularization request. Please try again.'
        },
        prefillData: new Map(),
        formLinking: {
          enabled: false,
          recordActions: []
        }
      }
    });

    await regularizationForm.save();
    console.log('✅ Regularization form created');

    // Create Demo Attendance form with form linking
    const demoAttendanceForm = new UnifiedConfig({
      name: 'Demo Attendance',
      type: 'form',
      description: 'Demo attendance form that shows attendance records with apply buttons',
      prompt: 'I can show you your attendance data. This is a demo form that displays attendance records with apply buttons.',
      keywords: ['attendance', 'demo', 'attendance data', 'demo attendance'],
      triggerPhrases: ['demo attendance', 'show attendance', 'attendance demo'],
      isActive: true,
      priority: 2,
      category: 'demo',
      tags: ['demo', 'attendance', 'testing'],
      formConfig: {
        conversationalFlow: {
          enabled: false,
          completionMessage: 'Thank you! Your attendance data has been retrieved.'
        },
        fields: [],
        submitApiConfig: {
          endpoint: 'https://api.example.com/demo/attendance',
          method: 'GET',
          headers: new Map([
            ['Content-Type', 'application/json'],
            ['Authorization', 'Bearer demo-token']
          ]),
          authType: 'bearer',
          authConfig: new Map([
            ['token', 'demo-token'],
            ['useCustomToken', 'true']
          ]),
          dataMapping: new Map(),
          customPayload: {
            enabled: false,
            structure: {},
            mergeStrategy: 'replace',
            transformations: []
          },
          successMessage: 'Attendance data retrieved successfully!',
          errorMessage: 'Failed to retrieve attendance data. Please try again.'
        },
        prefillData: new Map(),
        formLinking: {
          enabled: true,
          recordActions: [
            {
              label: 'Apply for Regularization',
              targetForm: regularizationForm._id,
              fieldMapping: new Map([
                ['attendanceDate', 'date'],
                ['actualInTime', 'inTime'],
                ['actualOutTime', 'outTime']
              ])
            }
          ]
        }
      }
    });

    await demoAttendanceForm.save();
    console.log('✅ Demo Attendance form created with form linking');

    console.log('\n🎉 Setup completed successfully!');
    console.log('You can now:');
    console.log('1. Ask "demo attendance" in the chat');
    console.log('2. The system will display attendance records');
    console.log('3. Click "Apply for Regularization" buttons on records');
    console.log('4. The regularization form will open with pre-filled data');
    console.log('\nForm IDs:');
    console.log('- Demo Attendance:', demoAttendanceForm._id);
    console.log('- Regularization:', regularizationForm._id);

  } catch (error) {
    console.error('❌ Error setting up demo attendance:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Database connection closed');
  }
}

createDemoAttendance();