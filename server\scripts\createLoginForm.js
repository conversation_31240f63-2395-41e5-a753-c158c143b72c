const mongoose = require('mongoose');
const Form = require('../models/Form');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/ai-form-builder')
  .then(() => console.log('MongoDB Connected'))
  .catch(err => console.error('MongoDB Connection Error:', err));

// Create login form
const loginForm = {
  name: 'Login Form',
  description: 'Form for user authentication',
  fields: [
    {
      name: 'username',
      label: 'Employee ID',
      type: 'text',
      placeholder: 'Enter your employee ID',
      required: true,
      options: [],
      validation: {
        minLength: 3
      }
    },
    {
      name: 'password',
      label: 'Password',
      type: 'password',
      placeholder: 'Enter your password',
      required: true,
      options: [],
      validation: {
        minLength: 6
      }
    }
  ],
  apiConfig: {
    method: 'POST',
    endpoint: '/api/auth/login',
    headers: {
      'Content-Type': 'application/json'
    },
    authType: 'none',
    authDetails: {},
    requestBody: null,
    requestBodyType: 'json'
  }
};

// Save the form to the database
Form.create(loginForm)
  .then(form => {
    console.log('Login form created successfully:');
    console.log(form);
    mongoose.disconnect();
  })
  .catch(err => {
    console.error('Error creating login form:', err);
    mongoose.disconnect();
  });