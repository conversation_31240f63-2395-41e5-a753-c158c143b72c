const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function deleteOldForm() {
  try {
    await connectDB();
    
    // Find and delete the old form by ID
    const oldFormId = '68665cf3c9aa38ef23705497';
    const result = await UnifiedConfig.deleteOne({_id: oldFormId});
    console.log('✅ Deleted old form:', result);
    
    // Also delete any forms with name "Leave Apply"
    const result2 = await UnifiedConfig.deleteMany({name: 'Leave Apply'});
    console.log('✅ Deleted forms with name "Leave Apply":', result2);
    
    // List remaining forms
    const forms = await UnifiedConfig.find({type: 'form'}, 'name isActive priority').sort({priority: -1});
    console.log('\n📋 Remaining forms:');
    forms.forEach(form => {
      console.log(`- ${form.name}: active=${form.isActive}, priority=${form.priority}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

deleteOldForm();