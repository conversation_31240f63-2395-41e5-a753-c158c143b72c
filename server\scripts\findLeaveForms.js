const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function findLeaveForms() {
  try {
    await connectDB();
    
    const forms = await UnifiedConfig.find({
      type: 'form',
      $or: [
        {name: /leave/i},
        {prompt: /leave/i},
        {keywords: /leave/i}
      ]
    }, 'name isActive priority prompt keywords triggerPhrases');
    
    console.log('All leave-related forms:');
    forms.forEach((form, index) => {
      console.log(`${index + 1}. Name: ${form.name}`);
      console.log(`   Active: ${form.isActive}`);
      console.log(`   Priority: ${form.priority}`);
      console.log(`   Prompt: ${form.prompt}`);
      console.log(`   Keywords: ${form.keywords}`);
      console.log(`   Trigger Phrases: ${form.triggerPhrases}`);
      console.log('---');
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

findLeaveForms();