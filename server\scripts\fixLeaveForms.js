const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function fixLeaveForms() {
  try {
    await connectDB();
    
    // Disable the old 'Leave Apply' form
    await UnifiedConfig.updateOne({name: 'Leave Apply'}, {isActive: false});
    console.log('✅ Disabled old Leave Apply form');
    
    // Make sure Leave Application form is active and has higher priority
    await UnifiedConfig.updateOne({name: 'Leave Application'}, {isActive: true, priority: 10});
    console.log('✅ Activated Leave Application form with higher priority');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error fixing forms:', error);
    process.exit(1);
  }
}

fixLeaveForms();