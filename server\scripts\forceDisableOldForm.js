const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function forceDisableOldForm() {
  try {
    await connectDB();
    
    // Force disable the old 'Leave Apply' form
    const result = await UnifiedConfig.updateOne(
      {name: 'Leave Apply'}, 
      {$set: {isActive: false, priority: -1}}
    );
    console.log('✅ Force disabled old Leave Apply form:', result);
    
    // Ensure Leave Application form is properly configured
    const result2 = await UnifiedConfig.updateOne(
      {name: 'Leave Application'}, 
      {$set: {isActive: true, priority: 100}}
    );
    console.log('✅ Ensured Leave Application form is active:', result2);
    
    // List all forms to verify
    const forms = await UnifiedConfig.find({type: 'form'}, 'name isActive priority').sort({priority: -1});
    console.log('\n📋 All forms status:');
    forms.forEach(form => {
      console.log(`- ${form.name}: active=${form.isActive}, priority=${form.priority}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

forceDisableOldForm();