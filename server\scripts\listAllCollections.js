const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function listAllCollections() {
  try {
    await connectDB();
    
    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📋 All collections in database:');
    collections.forEach((collection, index) => {
      console.log(`${index + 1}. ${collection.name}`);
    });
    
    // Check each collection for forms with "Leave Apply"
    for (const collection of collections) {
      try {
        const docs = await mongoose.connection.db.collection(collection.name).find({
          $or: [
            {name: /Leave Apply/i},
            {_id: mongoose.Types.ObjectId('68665cf3c9aa38ef23705497')}
          ]
        }).toArray();
        
        if (docs.length > 0) {
          console.log(`\n🔍 Found matching documents in collection "${collection.name}":`);
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. Name: ${doc.name}, ID: ${doc._id}, Active: ${doc.isActive}`);
          });
        }
      } catch (error) {
        // Skip collections that might have different structure
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

listAllCollections();