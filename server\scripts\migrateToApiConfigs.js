const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

// Import models
const UnifiedConfig = require('../models/UnifiedConfig');
const ApiConfig = require('../models/ApiConfig');

async function migrateToApiConfigs() {
  try {
    console.log('🚀 Starting migration to apiConfigs collection...\n');
    
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ Connected to MongoDB');
    
    // Get all existing unified configurations
    const existingConfigs = await UnifiedConfig.find({});
    console.log(`📊 Found ${existingConfigs.length} existing configurations to migrate`);
    
    if (existingConfigs.length === 0) {
      console.log('ℹ️ No existing configurations found. Creating sample data...');
      await createSampleConfigurations();
      return;
    }
    
    let migrated = 0;
    let skipped = 0;
    let errors = 0;
    
    for (const config of existingConfigs) {
      try {
        // Check if already exists in apiConfigs
        const existing = await ApiConfig.findOne({ name: config.name });
        if (existing) {
          console.log(`⏭️ Skipping "${config.name}" - already exists in apiConfigs`);
          skipped++;
          continue;
        }
        
        // Transform the data structure
        const apiConfigData = {
          name: config.name,
          description: config.description || '',
          type: config.type || 'api',
          keywords: config.keywords || [],
          triggerPhrases: config.triggerPhrases || [],
          prompt: config.prompt || '',
          isActive: config.isActive !== undefined ? config.isActive : true,
          priority: config.priority || 0,
          category: config.category || 'general',
          tags: config.tags || [],
          createdBy: config.createdBy || 'system',
          updatedBy: config.updatedBy || 'system'
        };
        
        // Handle API configuration
        if (config.type === 'api' && config.apiConfig) {
          apiConfigData.apiConfig = {
            endpoint: config.apiConfig.endpoint || '',
            method: config.apiConfig.method || 'GET',
            headers: convertToMap(config.apiConfig.headers),
            queryParams: new Map(),
            bodyTemplate: '',
            authType: config.apiConfig.authType || 'none',
            authConfig: config.apiConfig.authConfig || {},
            responseTemplate: config.apiConfig.responseTemplate || '',
            responseMapping: new Map(),
            timeout: config.apiConfig.timeout || 30000,
            retryConfig: {
              enabled: true,
              maxRetries: config.apiConfig.retryCount || 3,
              retryDelay: 1000
            },
            dataInjection: {
              injectUserData: config.apiConfig.dataInjection?.injectUserData || false,
              userDataFields: [],
              requiredFields: config.apiConfig.dataInjection?.requiredFields || []
            }
          };
          
          // Handle auto fields migration
          if (config.apiConfig.dataInjection?.autoFields) {
            const autoFields = config.apiConfig.dataInjection.autoFields;
            const userDataFields = [];
            if (autoFields.empId) userDataFields.push('empId');
            if (autoFields.token) userDataFields.push('token');
            if (autoFields.roleType) userDataFields.push('roleType');
            apiConfigData.apiConfig.dataInjection.userDataFields = userDataFields;
          }
        }
        
        // Handle Form configuration
        if (config.type === 'form' && config.formConfig) {
          apiConfigData.formConfig = {
            fields: config.formConfig.fields || [],
            submitApiConfig: {
              endpoint: config.formConfig.submitApiConfig?.endpoint || '',
              method: config.formConfig.submitApiConfig?.method || 'POST',
              headers: convertToMap(config.formConfig.submitApiConfig?.headers),
              authType: config.formConfig.submitApiConfig?.authType || 'bearer',
              authConfig: config.formConfig.submitApiConfig?.authConfig || {},
              dataMapping: convertToMap(config.formConfig.submitApiConfig?.dataMapping),
              successMessage: config.formConfig.submitApiConfig?.successMessage || 'Form submitted successfully!',
              errorMessage: config.formConfig.submitApiConfig?.errorMessage || 'Failed to submit form. Please try again.',
              redirectUrl: config.formConfig.submitApiConfig?.redirectUrl
            },
            prefillData: convertToMap(config.formConfig.prefillData)
          };
        }
        
        // Add stats structure
        apiConfigData.stats = {
          totalTriggers: 0,
          successfulTriggers: 0,
          failedTriggers: 0,
          lastTriggered: null,
          averageResponseTime: null
        };
        
        // Create new ApiConfig document
        const newApiConfig = new ApiConfig(apiConfigData);
        await newApiConfig.save();
        
        console.log(`✅ Migrated "${config.name}" (${config.type})`);
        migrated++;
        
      } catch (error) {
        console.error(`❌ Error migrating "${config.name}":`, error.message);
        errors++;
      }
    }
    
    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Migrated: ${migrated} configurations`);
    console.log(`   ⏭️ Skipped: ${skipped} configurations`);
    console.log(`   ❌ Errors: ${errors} configurations`);
    
    // Verify migration
    const apiConfigCount = await ApiConfig.countDocuments();
    console.log(`   📈 Total in apiConfigs collection: ${apiConfigCount}`);
    
    console.log('\n✅ Migration completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('1. Verify data in apiConfigs collection');
    console.log('2. Test API endpoints with new collection');
    console.log('3. Update frontend to use /api/api-configs endpoints');
    console.log('4. Once verified, you can remove old unifiedconfigs collection');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n🔒 Database connection closed');
  }
}

async function createSampleConfigurations() {
  console.log('🌱 Creating sample API configurations...\n');
  
  const sampleConfigs = [
    {
      name: 'Weather API',
      description: 'Get current weather information',
      type: 'api',
      keywords: ['weather', 'temperature', 'forecast', 'climate'],
      triggerPhrases: ['get weather', 'weather info', 'temperature today'],
      priority: 5,
      category: 'weather',
      tags: ['api', 'weather', 'external'],
      isActive: true,
      apiConfig: {
        endpoint: 'https://httpbin.org/json',
        method: 'GET',
        headers: new Map([['User-Agent', 'WeatherBot/1.0']]),
        authType: 'none',
        authConfig: {},
        responseTemplate: '{"weather": "{{slideshow.title}}", "temp": "25°C"}',
        timeout: 30000,
        retryConfig: {
          enabled: true,
          maxRetries: 3,
          retryDelay: 1000
        },
        dataInjection: {
          injectUserData: false,
          userDataFields: ['userId'],
          requiredFields: []
        }
      }
    },
    {
      name: 'User Profile API',
      description: 'Fetch user profile information',
      type: 'api',
      keywords: ['user', 'profile', 'account', 'info'],
      triggerPhrases: ['get user info', 'user profile', 'my account'],
      priority: 3,
      category: 'user',
      tags: ['api', 'user', 'profile'],
      isActive: true,
      apiConfig: {
        endpoint: 'https://httpbin.org/get',
        method: 'GET',
        headers: new Map([['Authorization', 'Bearer {{token}}']]),
        authType: 'bearer',
        authConfig: {
          token: 'sample-token'
        },
        responseTemplate: '{"user": "{{headers.User-Agent}}", "id": "{{args.id}}"}',
        timeout: 15000,
        retryConfig: {
          enabled: true,
          maxRetries: 2,
          retryDelay: 500
        },
        dataInjection: {
          injectUserData: true,
          userDataFields: ['userId', 'empId', 'token'],
          requiredFields: []
        }
      }
    },
    {
      name: 'Contact Form',
      description: 'Submit contact information',
      type: 'form',
      keywords: ['contact', 'form', 'message', 'submit'],
      triggerPhrases: ['contact us', 'send message', 'get in touch'],
      priority: 2,
      category: 'forms',
      tags: ['form', 'contact', 'submit'],
      isActive: true,
      formConfig: {
        fields: [
          {
            name: 'name',
            label: 'Full Name',
            type: 'text',
            required: true,
            placeholder: 'Enter your full name',
            validation: {
              minLength: 2,
              maxLength: 50
            }
          },
          {
            name: 'email',
            label: 'Email Address',
            type: 'email',
            required: true,
            placeholder: 'Enter your email address'
          },
          {
            name: 'message',
            label: 'Message',
            type: 'textarea',
            required: true,
            placeholder: 'Enter your message',
            validation: {
              minLength: 10,
              maxLength: 1000
            }
          }
        ],
        submitApiConfig: {
          endpoint: 'https://httpbin.org/post',
          method: 'POST',
          headers: new Map([['Content-Type', 'application/json']]),
          authType: 'none',
          authConfig: {},
          dataMapping: new Map([
            ['name', 'customer_name'],
            ['email', 'customer_email'],
            ['message', 'customer_message']
          ]),
          successMessage: 'Thank you for contacting us! We will get back to you soon.',
          errorMessage: 'Failed to send your message. Please try again later.'
        },
        prefillData: new Map()
      }
    }
  ];
  
  let created = 0;
  for (const configData of sampleConfigs) {
    try {
      const config = new ApiConfig(configData);
      await config.save();
      console.log(`✅ Created sample config: "${configData.name}"`);
      created++;
    } catch (error) {
      console.error(`❌ Error creating sample config "${configData.name}":`, error.message);
    }
  }
  
  console.log(`\n🎉 Created ${created} sample configurations in apiConfigs collection`);
}

// Helper function to convert objects to Maps
function convertToMap(obj) {
  if (!obj) return new Map();
  if (obj instanceof Map) return obj;
  if (typeof obj === 'object') {
    return new Map(Object.entries(obj));
  }
  return new Map();
}

// Run migration if called directly
if (require.main === module) {
  migrateToApiConfigs();
}

module.exports = { migrateToApiConfigs, createSampleConfigurations };