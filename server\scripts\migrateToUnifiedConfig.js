const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Temporary schemas for migration - to read old data if it exists
const FormSchema = new mongoose.Schema({}, { collection: 'forms', strict: false });
const ApiConfigSchema = new mongoose.Schema({}, { collection: 'apiconfigs', strict: false });
const OldForm = mongoose.model('OldForm', FormSchema);
const OldApiConfig = mongoose.model('OldApiConfig', ApiConfigSchema);

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

// Convert Map objects to plain objects for migration
const convertMapToObject = (mapOrObj) => {
  if (!mapOrObj) return {};
  if (mapOrObj instanceof Map) {
    return Object.fromEntries(mapOrObj);
  }
  if (typeof mapOrObj === 'object' && mapOrObj.constructor === Object) {
    return mapOrObj;
  }
  if (mapOrObj.toObject && typeof mapOrObj.toObject === 'function') {
    return mapOrObj.toObject();
  }
  return mapOrObj;
};

async function migrateToUnifiedConfig() {
  try {
    await connectDB();
    
    console.log('🔄 Starting migration to unified configuration system...\n');
    
    // Get existing forms and API configs
    const [existingForms, existingApiConfigs, existingUnifiedConfigs] = await Promise.all([
      OldForm.find({}).catch(() => []), // May not exist
      OldApiConfig.find({}).catch(() => []), // May not exist
      UnifiedConfig.find({})
    ]);
    
    console.log(`📊 Current data:`);
    console.log(`   - Forms: ${existingForms.length}`);
    console.log(`   - API Configs: ${existingApiConfigs.length}`);
    console.log(`   - Unified Configs: ${existingUnifiedConfigs.length}\n`);
    
    let migratedForms = 0;
    let migratedApis = 0;
    let skippedForms = 0;
    let skippedApis = 0;
    let errors = [];
    
    // Migrate Forms to UnifiedConfig
    console.log('📝 Migrating Forms to Unified Configuration...');
    
    for (const form of existingForms) {
      try {
        // Check if already exists in unified config
        const existing = await UnifiedConfig.findOne({ 
          name: form.name, 
          type: 'form' 
        });
        
        if (existing) {
          console.log(`⚠️  Form "${form.name}" already exists in unified config, skipping...`);
          skippedForms++;
          continue;
        }
        
        // Convert Map objects to plain objects for migration
        const convertMapToObject = (mapOrObj) => {
          if (!mapOrObj) return {};
          if (mapOrObj instanceof Map) {
            return Object.fromEntries(mapOrObj);
          }
          return mapOrObj;
        };

        const unifiedConfig = new UnifiedConfig({
          type: 'form',
          name: form.name,
          description: form.description || '',
          prompt: form.prompt || '',
          keywords: form.keywords || [],
          triggerPhrases: [], // Forms didn't have trigger phrases originally
          priority: 0,
          category: 'general',
          tags: [],
          isActive: true,
          
          formConfig: {
            fields: form.fields || [],
            submitApiConfig: {
              endpoint: form.apiConfig?.endpoint || '',
              method: form.apiConfig?.method || 'POST',
              headers: convertMapToObject(form.apiConfig?.headers),
              authType: form.apiConfig?.authType || 'bearer',
              authConfig: convertMapToObject(form.apiConfig?.authDetails),
              dataMapping: {},
              successMessage: 'Form submitted successfully!',
              errorMessage: 'Failed to submit form. Please try again.'
            },
            prefillData: {}
          }
        });
        
        await unifiedConfig.save();
        console.log(`✅ Migrated form: ${form.name}`);
        migratedForms++;
        
      } catch (error) {
        console.error(`❌ Error migrating form "${form.name}":`, error.message);
        errors.push(`Form ${form.name}: ${error.message}`);
      }
    }
    
    // Migrate API Configs to UnifiedConfig
    console.log('\n📡 Migrating API Configurations to Unified Configuration...');
    
    for (const apiConfig of existingApiConfigs) {
      try {
        // Check if already exists in unified config
        const existing = await UnifiedConfig.findOne({ 
          name: apiConfig.name, 
          type: 'api' 
        });
        
        if (existing) {
          console.log(`⚠️  API "${apiConfig.name}" already exists in unified config, skipping...`);
          skippedApis++;
          continue;
        }
        
        // Convert Map objects to plain objects for migration
        const convertMapToObject = (mapOrObj) => {
          if (!mapOrObj) return {};
          if (mapOrObj instanceof Map) {
            return Object.fromEntries(mapOrObj);
          }
          return mapOrObj;
        };

        const unifiedConfig = new UnifiedConfig({
          type: 'api',
          name: apiConfig.name,
          description: apiConfig.description || '',
          prompt: apiConfig.prompt || '',
          keywords: apiConfig.keywords || [],
          triggerPhrases: apiConfig.triggerPhrases || [],
          priority: 0,
          category: 'general',
          tags: [],
          isActive: apiConfig.isActive !== false,
          
          apiConfig: {
            endpoint: apiConfig.endpoint || '',
            method: apiConfig.method || 'GET',
            headers: convertMapToObject(apiConfig.headers),
            authType: apiConfig.authType || 'none',
            authConfig: convertMapToObject(apiConfig.authConfig),
            responseTemplate: apiConfig.responseTemplate || '',
            timeout: apiConfig.timeout || 30000,
            retryCount: apiConfig.retryCount || 3,
            dataInjection: {
              injectUserData: apiConfig.dataInjection?.injectUserData !== false,
              requiredFields: apiConfig.dataInjection?.requiredFields || [],
              autoFields: {
                empId: apiConfig.dataInjection?.autoFields?.empId || false,
                token: apiConfig.dataInjection?.autoFields?.token || false,
                roleType: apiConfig.dataInjection?.autoFields?.roleType || false
              }
            },
            lastTestedAt: apiConfig.lastTestedAt,
            lastTestResult: apiConfig.lastTestResult
          }
        });
        
        await unifiedConfig.save();
        console.log(`✅ Migrated API: ${apiConfig.name}`);
        migratedApis++;
        
      } catch (error) {
        console.error(`❌ Error migrating API "${apiConfig.name}":`, error.message);
        errors.push(`API ${apiConfig.name}: ${error.message}`);
      }
    }
    
    // Migration Summary
    console.log('\n🎉 Migration completed!');
    console.log('\n📊 Migration Summary:');
    console.log(`   ✅ Forms migrated: ${migratedForms}`);
    console.log(`   ✅ APIs migrated: ${migratedApis}`);
    console.log(`   ⚠️  Forms skipped: ${skippedForms}`);
    console.log(`   ⚠️  APIs skipped: ${skippedApis}`);
    console.log(`   ❌ Errors: ${errors.length}`);
    
    if (errors.length > 0) {
      console.log('\n❌ Migration Errors:');
      errors.forEach(error => console.log(`   - ${error}`));
    }
    
    // Final counts
    const finalCounts = await Promise.all([
      UnifiedConfig.countDocuments(),
      UnifiedConfig.countDocuments({ type: 'form' }),
      UnifiedConfig.countDocuments({ type: 'api' }),
      UnifiedConfig.countDocuments({ isActive: true })
    ]);
    
    console.log('\n📊 Final Unified Configuration Counts:');
    console.log(`   - Total configurations: ${finalCounts[0]}`);
    console.log(`   - Form configurations: ${finalCounts[1]}`);
    console.log(`   - API configurations: ${finalCounts[2]}`);
    console.log(`   - Active configurations: ${finalCounts[3]}`);
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Test the unified configuration system');
    console.log('2. Update any remaining hardcoded references');
    console.log('3. Consider backing up the old collections before removing them');
    console.log('4. Use the Unified Config page at /unified-configs to manage configurations');
    
    // Ask about cleanup
    console.log('\n💡 Note: Original Form and ApiConfig collections are preserved for safety.');
    console.log('   You can manually remove them after confirming the migration works correctly.');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);

if (args.includes('--help')) {
  console.log(`
🔄 Unified Configuration Migration Script

This script migrates existing Form and ApiConfig documents to the new unified UnifiedConfig collection.

Usage:
  node scripts/migrateToUnifiedConfig.js [options]

Options:
  --help      Show this help message
  --dry-run   Show what would be migrated without making changes (not implemented yet)

Examples:
  node scripts/migrateToUnifiedConfig.js
  
The script will:
1. Connect to MongoDB
2. Find all existing Forms and ApiConfigs
3. Convert them to the new unified UnifiedConfig format
4. Save them to the UnifiedConfigs collection
5. Preserve original collections for safety

Note: Existing configurations with the same name will be skipped to prevent duplicates.
`);
  process.exit(0);
}

// Run migration
migrateToUnifiedConfig().catch(console.error);
