const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function searchAllCollections() {
  try {
    await connectDB();
    
    const targetId = '68665cf3c9aa38ef23705497';
    
    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📋 Searching all collections for ID:', targetId);
    
    for (const collection of collections) {
      try {
        console.log(`\n🔍 Checking collection: ${collection.name}`);
        
        // Try to find by ObjectId
        let docs = [];
        try {
          docs = await mongoose.connection.db.collection(collection.name).find({
            _id: new mongoose.Types.ObjectId(targetId)
          }).toArray();
        } catch (error) {
          // If ObjectId fails, try as string
          docs = await mongoose.connection.db.collection(collection.name).find({
            _id: targetId
          }).toArray();
        }
        
        if (docs.length > 0) {
          console.log(`✅ FOUND in ${collection.name}:`, docs.length, 'documents');
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. Name: ${doc.name}, Type: ${doc.type}, Active: ${doc.isActive}`);
            console.log(`   ID: ${doc._id}`);
            console.log(`   Created: ${doc.createdAt}`);
          });
        } else {
          console.log(`❌ Not found in ${collection.name}`);
        }
      } catch (error) {
        console.log(`⚠️  Error checking ${collection.name}:`, error.message);
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

searchAllCollections();