const mongoose = require('mongoose');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function searchByName() {
  try {
    await connectDB();
    
    // List all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    console.log('📋 Searching all collections for "Leave Apply"');
    
    for (const collection of collections) {
      try {
        console.log(`\n🔍 Checking collection: ${collection.name}`);
        
        const docs = await mongoose.connection.db.collection(collection.name).find({
          name: /Leave Apply/i
        }).toArray();
        
        if (docs.length > 0) {
          console.log(`✅ FOUND in ${collection.name}:`, docs.length, 'documents');
          docs.forEach((doc, index) => {
            console.log(`${index + 1}. Name: ${doc.name}, Type: ${doc.type}, Active: ${doc.isActive}`);
            console.log(`   ID: ${doc._id}`);
            console.log(`   Priority: ${doc.priority}`);
            console.log(`   Created: ${doc.createdAt}`);
            
            // Show first few fields if it's a form
            if (doc.formConfig && doc.formConfig.fields) {
              console.log(`   Fields: ${doc.formConfig.fields.length} fields`);
              doc.formConfig.fields.slice(0, 3).forEach((field, i) => {
                console.log(`     ${i + 1}. ${field.name} (${field.type})`);
              });
            }
          });
        } else {
          console.log(`❌ Not found in ${collection.name}`);
        }
      } catch (error) {
        console.log(`⚠️  Error checking ${collection.name}:`, error.message);
      }
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

searchByName();