// const mongoose = require('mongoose');
// const UnifiedConfig = require('../models/UnifiedConfig');
// require('dotenv').config();

// const connectDB = async () => {
//   try {
//     await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
//     console.log('MongoDB Connected');
//   } catch (error) {
//     console.error('Database connection error:', error);
//     process.exit(1);
//   }
// };

// const setupDemoAttendanceWithFormLinking = async () => {
//   try {
//     console.log('🚀 Setting up demo attendance form with form linking...');
    
//     // First, find or create the regularization form
//     let regularizationForm = await UnifiedConfig.findOne({
//       name: 'Regularization',
//       type: 'form'
//     });

//     if (!regularizationForm) {
//       console.log('📝 Creating Regularization form...');
//       regularizationForm = await UnifiedConfig.create({
//         name: 'Regularization',
//         type: 'form',
//         description: 'Attendance Regularization Form',
//         prompt: 'Please fill out the attendance regularization form with the required details.',
//         keywords: ['regularization', 'attendance', 'regularize', 'punch', 'in', 'out', 'time'],
//         triggerPhrases: ['regularization', 'regularize attendance', 'attendance regularization'],
//         priority: 5,
//         category: 'attendance',
//         tags: ['attendance', 'regularization', 'HR'],
//         isActive: true,
//         formConfig: {
//           fields: [
//             {
//               name: 'date',
//               label: 'Date',
//               type: 'date',
//               required: true,
//               placeholder: 'Select the date'
//             },
//             {
//               name: 'reason',
//               label: 'Reason for Regularization',
//               type: 'textarea',
//               required: true,
//               placeholder: 'Please explain why you need to regularize attendance'
//             },
//             {
//               name: 'inTime',
//               label: 'In Time',
//               type: 'text',
//               required: true,
//               placeholder: 'HH:MM (e.g., 09:30)'
//             },
//             {
//               name: 'outTime',
//               label: 'Out Time',
//               type: 'text',
//               required: true,
//               placeholder: 'HH:MM (e.g., 18:30)'
//             },
//             {
//               name: 'remark',
//               label: 'Additional Remarks',
//               type: 'textarea',
//               required: false,
//               placeholder: 'Any additional information'
//             }
//           ],
//           submitApiConfig: {
//             endpoint: 'https://api.example.com/attendance/regularize',
//             method: 'POST',
//             headers: new Map([
//               ['Content-Type', 'application/json'],
//               ['Authorization', 'Bearer {token}']
//             ]),
//             authType: 'bearer',
//             authConfig: new Map([
//               ['useCustomToken', false]
//             ]),
//             successMessage: 'Regularization request submitted successfully!',
//             errorMessage: 'Failed to submit regularization request. Please try again.'
//           }
//         }
//       });
//       console.log('✅ Regularization form created successfully');
//     } else {
//       console.log('✅ Regularization form already exists');
//     }

//     // Now create/update the Demo Attendance form with form linking
//     const attendanceFormData = {
//       name: 'Demo Attendance',
//       type: 'form',
//       description: 'Demo Attendance Data Retrieval',
//       prompt: 'I can show you your attendance data. This is a demo form that displays attendance records with apply buttons.',
//       keywords: ['attendance', 'demo', 'attendance data', 'demo attendance'],
//       triggerPhrases: ['demo attendance', 'show attendance', 'attendance demo'],
//       priority: 8,
//       category: 'demo',
//       tags: ['demo', 'attendance', 'testing'],
//       isActive: true,
//       formConfig: {
//         fields: [], // No fields needed for GET request
//         submitApiConfig: {
//           endpoint: 'https://api.example.com/attendance/demo',
//           method: 'GET',
//           headers: new Map([
//             ['Content-Type', 'application/json'],
//             ['Authorization', 'Bearer {token}']
//           ]),
//           authType: 'bearer',
//           authConfig: new Map([
//             ['useCustomToken', false]
//           ]),
//           successMessage: 'Attendance data retrieved successfully!',
//           errorMessage: 'Failed to retrieve attendance data. Please try again.',
//           responseTemplate: `📅 **Demo Attendance Data**

// **Month/Year:** {data.monthYear}
// **Employee ID:** {data.empId}

// **Attendance Records:**
// {data.attendanceInfo.length} record(s) found

// _Use the Apply buttons below to regularize attendance for specific dates._`
//         },
//         // Configure form linking
//         formLinking: {
//           enabled: true,
//           recordActions: [
//             {
//               buttonText: 'Apply for Regularization',
//               targetFormId: regularizationForm._id,
//               targetFormName: 'Regularization',
//               fieldMapping: {
//                 'date': 'attendanceDate',
//                 'inTime': 'actualInTime',
//                 'outTime': 'actualOutTime'
//               },
//               conditions: [
//                 {
//                   field: 'attendanceData',
//                   operator: 'contains',
//                   value: 'A'
//                 }
//               ],
//               buttonStyle: 'primary'
//             }
//           ]
//         }
//       }
//     };

//     // Check if Demo Attendance form already exists
//     let demoAttendanceForm = await UnifiedConfig.findOne({
//       name: 'Demo Attendance',
//       type: 'form'
//     });

//     if (demoAttendanceForm) {
//       console.log('📝 Updating existing Demo Attendance form...');
//       Object.assign(demoAttendanceForm, attendanceFormData);
//       await demoAttendanceForm.save();
//       console.log('✅ Demo Attendance form updated successfully');
//     } else {
//       console.log('📝 Creating new Demo Attendance form...');
//       demoAttendanceForm = await UnifiedConfig.create(attendanceFormData);
//       console.log('✅ Demo Attendance form created successfully');
//     }

//     console.log('\n🎉 Setup completed successfully!');
//     console.log('\nYou can now:');
//     console.log('1. Ask "demo attendance" in the chat');
//     console.log('2. The system will display attendance records');
//     console.log('3. Click "Apply for Regularization" buttons on records');
//     console.log('4. The regularization form will open with pre-filled data');
    
//     console.log('\nForm IDs:');
//     console.log(`- Demo Attendance: ${demoAttendanceForm._id}`);
//     console.log(`- Regularization: ${regularizationForm._id}`);

//   } catch (error) {
//     console.error('❌ Error setting up demo attendance with form linking:', error);
//     throw error;
//   }
// };

// const main = async () => {
//   await connectDB();
//   await setupDemoAttendanceWithFormLinking();
//   await mongoose.connection.close();
//   console.log('\n🔌 Database connection closed');
// };

// // Run the script
// if (require.main === module) {
//   main().catch(console.error);
// }

// module.exports = { setupDemoAttendanceWithFormLinking };