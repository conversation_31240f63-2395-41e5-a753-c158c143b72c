const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

// Sample dynamic configurations
const sampleApiConfigs = [
  {
    name: 'Leave Balance API',
    description: 'Fetches employee leave balance information',
    endpoint: 'https://dev.budgie.co.in/budgie/v3/api/employee/leave/{empId}/{year}',
    method: 'GET',
    keywords: ['leave', 'balance', 'vacation', 'pto', 'time off'],
    prompt: 'This API provides leave balance information for employees',
    triggerPhrases: ['check my leave balance', 'how many leaves do I have', 'show leave status', 'leave balance'],
    responseTemplate: '🏖️ **Your Leave Balance**\n\nTotal Available: {data.totalBalance} days\nUsed: {data.usedLeaves} days\nRemaining: {data.remainingLeaves} days',
    headers: {
      'Content-Type': 'application/json'
    },
    authType: 'bearer',
    authConfig: {
      bearerToken: ''
    },
    dataInjection: {
      injectUserData: true,
      requiredFields: ['empId', 'year'],
      autoFields: {
        empId: true,
        token: true,
        roleType: false
      }
    },
    timeout: 30000,
    retryCount: 3,
    isActive: true
  },
  {
    name: 'Regularization Data API',
    description: 'Fetches attendance regularization data for employees',
    endpoint: 'https://dev.budgie.co.in/budgie/v3/api/attendance-regularization/quick-add',
    method: 'GET',
    keywords: ['regularization', 'attendance', 'punch', 'correction'],
    prompt: 'This API provides attendance regularization data and options',
    triggerPhrases: ['regularization apply', 'attendance correction', 'missed punch', 'regularize attendance'],
    responseTemplate: '📋 **Regularization Information**\n\n{data.message}\n\nPlease review the attendance data and apply for regularization if needed.',
    headers: {
      'Content-Type': 'application/json'
    },
    authType: 'bearer',
    authConfig: {
      bearerToken: ''
    },
    dataInjection: {
      injectUserData: true,
      requiredFields: [],
      autoFields: {
        empId: true,
        token: true,
        roleType: false
      }
    },
    timeout: 30000,
    retryCount: 3,
    isActive: true
  }
];

const sampleForms = [
  {
    name: 'Leave Application',
    description: 'Apply for various types of leave',
    prompt: 'Use this form when you want to apply for leave, vacation, time off, or any type of absence from work.',
    keywords: ['leave', 'vacation', 'time off', 'absence', 'pto', 'holiday', 'sick leave', 'personal leave'],
    triggerPhrases: ['leave apply', 'apply leave', 'apply for leave', 'leave application', 'request leave', 'submit leave'],
    fields: [
      {
        name: 'leaveType',
        label: 'Leave Type',
        type: 'select',
        required: true,
        options: ['sick leave', 'casual leave', 'Privilege Leave']
      },
      {
        name: 'fromDate',
        label: 'From Date',
        type: 'date',
        required: true
      },
      {
        name: 'fromSession',
        label: 'From Session',
        type: 'select',
        required: true,
        options: ['Session 1', 'Session 2']
      },
      {
        name: 'toDate',
        label: 'To Date',
        type: 'date',
        required: true
      },
      {
        name: 'toSession',
        label: 'To Session',
        type: 'select',
        required: true,
        options: ['Session 1', 'Session 2']
      },
      {
        name: 'reason',
        label: 'Reason',
        type: 'textarea',
        required: true,
        placeholder: 'Please provide reason for your leave application'
      }
    ],
    apiConfig: {
      method: 'POST',
      endpoint: 'https://dev.budgie.co.in/budgie/v3/api/leave/apply',
      headers: {
        'Content-Type': 'application/json'
      },
      authType: 'bearer',
      authDetails: {}
    }
  },
  {
    name: 'Attendance Regularization',
    description: 'Regularize attendance for missed punches or incorrect timings',
    prompt: 'Use this form to regularize your attendance, correct punch timings, or report missed check-ins/check-outs.',
    keywords: ['regularization', 'attendance', 'punch', 'check-in', 'check-out', 'timing correction', 'missed punch'],
    fields: [
      {
        name: 'date',
        label: 'Date',
        type: 'date',
        required: true
      },
      {
        name: 'inTime',
        label: 'In Time',
        type: 'text',
        required: true,
        placeholder: 'HH:MM (e.g., 09:30)'
      },
      {
        name: 'outTime',
        label: 'Out Time',
        type: 'text',
        required: true,
        placeholder: 'HH:MM (e.g., 18:30)'
      },
      {
        name: 'reason',
        label: 'Reason for Regularization',
        type: 'textarea',
        required: true,
        placeholder: 'Please explain why regularization is needed'
      }
    ],
    apiConfig: {
      method: 'POST',
      endpoint: 'https://dev.budgie.co.in/budgie/v3/api/attendance-regularization/apply',
      headers: {
        'Content-Type': 'application/json'
      },
      authType: 'bearer',
      authDetails: {}
    }
  }
];

async function setupConfigs() {
  try {
    await connectDB();
    
    console.log('🚀 Setting up dynamic configurations...\n');
    
    // Create API configurations
    console.log('📡 Creating API configurations...');
    for (const configData of sampleApiConfigs) {
      try {
        const existing = await UnifiedConfig.findOne({ name: configData.name, type: 'api' });
        if (existing) {
          console.log(`⚠️  API config "${configData.name}" already exists, skipping...`);
          continue;
        }
        
        // Convert to UnifiedConfig structure
        const apiData = {
          name: configData.name,
          type: 'api',
          description: configData.description || '',
          prompt: configData.prompt || '',
          keywords: configData.keywords || [],
          triggerPhrases: configData.triggerPhrases || [],
          isActive: configData.isActive !== undefined ? configData.isActive : true,
          priority: configData.priority || 0,
          category: configData.category || 'general',
          tags: configData.tags || [],
          apiConfig: {
            endpoint: configData.endpoint,
            method: configData.method || 'GET',
            headers: new Map(Object.entries(configData.headers || {})),
            authType: configData.authType || 'none',
            authConfig: new Map(Object.entries(configData.authConfig || {})),
            responseTemplate: configData.responseTemplate || '',
            timeout: configData.timeout || 30000,
            retryCount: configData.retryCount || 3,
            dataInjection: {
              injectUserData: configData.dataInjection?.injectUserData !== false,
              requiredFields: configData.dataInjection?.requiredFields || [],
              autoFields: {
                empId: configData.dataInjection?.autoFields?.empId || false,
                token: configData.dataInjection?.autoFields?.token || false,
                roleType: configData.dataInjection?.autoFields?.roleType || false
              }
            }
          }
        };

        const config = new UnifiedConfig(apiData);
        await config.save();
        console.log(`✅ Created API config: ${config.name}`);
      } catch (error) {
        console.error(`❌ Error creating API config "${configData.name}":`, error.message);
      }
    }
    
    // Create form configurations
    console.log('\n📝 Creating form configurations...');
    for (const formData of sampleForms) {
      try {
        const existing = await UnifiedConfig.findOne({ name: formData.name, type: 'form' });
        if (existing) {
          console.log(`⚠️  Form "${formData.name}" already exists, updating with new fields...`);
          // Update existing form with new fields
          existing.prompt = formData.prompt;
          existing.keywords = formData.keywords;
          await existing.save();
          console.log(`✅ Updated form: ${existing.name}`);
          continue;
        }
        
        // Convert to UnifiedConfig structure for forms
        const dynamicFormData = {
          name: formData.name,
          type: 'form',
          description: formData.description || '',
          prompt: formData.prompt || '',
          keywords: formData.keywords || [],
          triggerPhrases: formData.triggerPhrases || [],
          isActive: formData.isActive !== undefined ? formData.isActive : true,
          priority: formData.priority || 0,
          category: formData.category || 'general',
          tags: formData.tags || [],
          formConfig: {
            fields: formData.fields || [],
            submitApiConfig: {
              endpoint: formData.apiConfig?.endpoint || '',
              method: formData.apiConfig?.method || 'POST',
              headers: new Map(Object.entries(formData.apiConfig?.headers || {})),
              authType: formData.apiConfig?.authType || 'bearer',
              authConfig: new Map(Object.entries(formData.apiConfig?.authConfig || {})),
              dataMapping: new Map(Object.entries(formData.apiConfig?.dataMapping || {})),
              successMessage: formData.apiConfig?.successMessage || 'Form submitted successfully!',
              errorMessage: formData.apiConfig?.errorMessage || 'Failed to submit form. Please try again.'
            },
            prefillData: new Map(Object.entries(formData.prefillData || {}))
          }
        };

        const form = new UnifiedConfig(dynamicFormData);
        await form.save();
        console.log(`✅ Created form: ${form.name}`);
      } catch (error) {
        console.error(`❌ Error creating form "${formData.name}":`, error.message);
      }
    }
    
    console.log('\n🎉 Dynamic configuration setup completed!');
    console.log('\n📊 Summary:');
    
    const totalApis = await UnifiedConfig.countDocuments({ type: 'api' });
    const totalForms = await UnifiedConfig.countDocuments({ type: 'form' });
    const activeApis = await UnifiedConfig.countDocuments({ type: 'api', isActive: true });
    
    console.log(`- Total API configurations: ${totalApis}`);
    console.log(`- Active API configurations: ${activeApis}`);
    console.log(`- Total form configurations: ${totalForms}`);
    
    console.log('\n🔍 Next steps:');
    console.log('1. Start your server: npm run dev');
    console.log('2. Test queries like: "check my leave balance", "apply for leave", "regularization"');
    console.log('3. Configure API tokens in the API configurations');
    console.log('4. Add more dynamic configurations as needed');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
if (args.includes('--reset')) {
  console.log('⚠️  Resetting all configurations...');
  UnifiedConfig.deleteMany({}).then(() => {
    setupConfigs();
  });
} else {
  setupConfigs();
}
