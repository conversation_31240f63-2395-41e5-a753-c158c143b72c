const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function testFindActiveConfigs() {
  try {
    await connectDB();
    
    // Test the findActiveConfigs method
    const configs = await UnifiedConfig.findActiveConfigs();
    console.log('📋 Active configurations found:', configs.length);
    
    configs.forEach((config, index) => {
      console.log(`${index + 1}. Name: ${config.name}, Type: ${config.type}, Active: ${config.isActive}, Priority: ${config.priority}`);
    });
    
    // Test specifically for forms
    const forms = await UnifiedConfig.find({ type: 'form', isActive: true });
    console.log('\n📝 Active forms found:', forms.length);
    
    forms.forEach((form, index) => {
      console.log(`${index + 1}. Name: ${form.name}, Active: ${form.isActive}, Priority: ${form.priority}`);
      console.log(`   Keywords: ${form.keywords}`);
      console.log(`   Trigger Phrases: ${form.triggerPhrases}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

testFindActiveConfigs();