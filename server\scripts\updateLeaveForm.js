const mongoose = require('mongoose');
const UnifiedConfig = require('../models/UnifiedConfig');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/botnexus');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB Connection Error:', error);
    process.exit(1);
  }
};

async function updateLeaveForm() {
  try {
    await connectDB();
    
    const form = await UnifiedConfig.findOne({type: 'form', name: 'Leave Application'});
    if (form) {
      form.formConfig.fields = [
        {
          name: 'leaveType',
          label: 'Leave Type',
          type: 'select',
          required: true,
          options: ['sick leave', 'casual leave', 'Privilege Leave']
        },
        {
          name: 'fromDate',
          label: 'From Date',
          type: 'date',
          required: true
        },
        {
          name: 'fromSession',
          label: 'From Session',
          type: 'select',
          required: true,
          options: ['Session 1', 'Session 2']
        },
        {
          name: 'toDate',
          label: 'To Date',
          type: 'date',
          required: true
        },
        {
          name: 'toSession',
          label: 'To Session',
          type: 'select',
          required: true,
          options: ['Session 1', 'Session 2']
        },
        {
          name: 'reason',
          label: 'Reason',
          type: 'textarea',
          required: true,
          placeholder: 'Please provide reason for your leave application'
        }
      ];
      form.triggerPhrases = ['leave apply', 'apply leave', 'apply for leave', 'leave application', 'request leave', 'submit leave'];
      await form.save();
      console.log('✅ Updated Leave Application form with new fields');
    } else {
      console.log('❌ Leave Application form not found');
    }
    process.exit(0);
  } catch (error) {
    console.error('❌ Error updating form:', error);
    process.exit(1);
  }
}

updateLeaveForm();