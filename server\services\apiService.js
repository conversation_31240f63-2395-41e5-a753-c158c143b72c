const axios = require('axios');
const UnifiedConfig = require('../models/UnifiedConfig');

class ApiService {
  /**
   * Get API configuration by name (supports both API and Form types)
   * @param {string} configName - Name of the API configuration
   * @returns {Object} API configuration object
   * @throws {Error} If configuration not found or inactive
   */
  static async getApiConfig(configName) {
    // First try to find an API type configuration
    let config = await UnifiedConfig.findOne({ 
      name: configName, 
      type: 'api',
      isActive: true 
    });

    // If not found, try to find a form type configuration
    if (!config) {
      config = await UnifiedConfig.findOne({ 
        name: configName, 
        type: 'form',
        isActive: true 
      });
    }

    if (!config) {
      throw new Error(`API configuration '${configName}' not found or inactive`);
    }

    return config;
  }

  /**
   * Build request configuration from UnifiedConfig structure
   * @param {Object} unifiedConfig - UnifiedConfig object (can be API or Form type)
   * @returns {Object} Request configuration for axios
   */
  static buildRequestConfigFromDynamic(unifiedConfig) {
    let apiConfigSection;
    
    // Determine which config section to use based on type
    if (unifiedConfig.type === 'api') {
      apiConfigSection = unifiedConfig.apiConfig;
    } else if (unifiedConfig.type === 'form') {
      apiConfigSection = unifiedConfig.formConfig.submitApiConfig;
    } else {
      throw new Error(`Unsupported configuration type: ${unifiedConfig.type}`);
    }

    if (!apiConfigSection) {
      throw new Error(`No API configuration found for '${unifiedConfig.name}' (type: ${unifiedConfig.type})`);
    }

    const config = {
      method: apiConfigSection.method.toLowerCase(),
      url: apiConfigSection.endpoint,
      timeout: apiConfigSection.timeout || 30000,
      headers: {}
    };

    // Convert Map headers to object (handle both Map and plain object formats)
    if (apiConfigSection.headers) {
      if (apiConfigSection.headers instanceof Map) {
        config.headers = Object.fromEntries(apiConfigSection.headers);
      } else if (typeof apiConfigSection.headers === 'object') {
        config.headers = { ...apiConfigSection.headers };
      }
    }

    // Add authentication based on type
    let authConfig = {};
    if (apiConfigSection.authConfig) {
      if (apiConfigSection.authConfig instanceof Map) {
        authConfig = Object.fromEntries(apiConfigSection.authConfig);
      } else if (typeof apiConfigSection.authConfig === 'object') {
        authConfig = { ...apiConfigSection.authConfig };
      }
    }
    
    switch (apiConfigSection.authType) {
      case 'apikey':
        if (authConfig.apiKey) {
          config.headers['X-API-Key'] = authConfig.apiKey;
        }
        break;
      case 'bearer':
        if (authConfig.bearerToken) {
          config.headers['Authorization'] = `Bearer ${authConfig.bearerToken}`;
        }
        break;
      case 'basic':
        if (authConfig.username && authConfig.password) {
          const credentials = Buffer.from(`${authConfig.username}:${authConfig.password}`).toString('base64');
          config.headers['Authorization'] = `Basic ${credentials}`;
        }
        break;
    }

    return config;
  }

  /**
   * Make a dynamic API call using configuration from database
   * @param {string} configName - Name of the API configuration
   * @param {Object} data - Request data/body
   * @param {Object} options - Additional options
   * @param {Object} options.params - Query parameters
   * @param {Object} options.headers - Additional headers to merge
   * @param {number} options.timeout - Override timeout
   * @returns {Object} API response
   */
  static async makeApiCall(configName, data = null, options = {}) {
    try {
      console.log(`Making API call to: ${configName}`);
      // Get API configuration from database
      const apiConfig = await this.getApiConfig(configName);
      
      // Validate the configuration
      this.validateConfig(apiConfig);
      
      // Build request configuration from UnifiedConfig structure
      const requestConfig = this.buildRequestConfigFromDynamic(apiConfig);
      
      // Handle placeholder replacement in the URL
      if (options.placeholders) {
        console.log('🔄 Replacing placeholders in URL:', requestConfig.url);
        console.log('📝 Available placeholders:', options.placeholders);
        
        let updatedUrl = requestConfig.url;
        
        // Replace each placeholder in the URL
        Object.entries(options.placeholders).forEach(([placeholder, value]) => {
          const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
          updatedUrl = updatedUrl.replace(regex, value);
          console.log(`   • Replaced ${placeholder} with ${value}`);
        });
        
        requestConfig.url = updatedUrl;
        console.log('✅ Updated URL after placeholder replacement:', requestConfig.url);
      }
      
      // Get the appropriate config section for method checking
      const apiConfigSection = apiConfig.type === 'api' ? apiConfig.apiConfig : apiConfig.formConfig.submitApiConfig;
      
      // Add request data if provided
      if (data && ['POST', 'PUT', 'PATCH'].includes(apiConfigSection.method.toUpperCase())) {
        requestConfig.data = data;
      }
      
      // Add query parameters if provided
      if (options.params) {
        requestConfig.params = options.params;
      }
      
      // Merge additional headers
      if (options.headers) {
        requestConfig.headers = {
          ...requestConfig.headers,
          ...options.headers
        };
      }
      
      // Override timeout if provided
      if (options.timeout) {
        requestConfig.timeout = options.timeout;
      }

      console.log(`Making API call to: ${apiConfig.name}`);
      console.log(`Request config:`, {
        ...requestConfig,
        headers: this.sanitizeHeaders(requestConfig.headers)
      });

      // Check if this is a demo endpoint and handle it specially
      if (requestConfig.url.includes('api.example.com') || requestConfig.url.includes('/demo')) {
        console.log('🎭 Demo endpoint detected, using mock response');
        const mockResponse = this.getMockResponse(apiConfig.name, requestConfig);
        return mockResponse;
      }

      // Make the API call with retry logic
      const retryCount = apiConfigSection.retryCount || 3;
      const response = await this.makeRequestWithRetry(requestConfig, retryCount);
      
      // Update last test result
      await this.updateTestResult(apiConfig._id, apiConfig.type, {
        success: true,
        statusCode: response.status,
        responseTime: response.responseTime,
        message: 'Success',
        error: null
      });

      return response;

    } catch (error) {
      console.error(`API call failed for config '${configName}':`, error.message);
      
      // Try to update test result if we have the config
      try {
        let config = await UnifiedConfig.findOne({ name: configName, type: 'api' });
        if (!config) {
          config = await UnifiedConfig.findOne({ name: configName, type: 'form' });
        }
        if (config) {
          await this.updateTestResult(config._id, config.type, {
            success: false,
            statusCode: error.response?.status || null,
            responseTime: null,
            message: 'Error',
            error: error.message
          });
        }
      } catch (updateError) {
        console.error('Failed to update test result:', updateError.message);
      }

      throw error;
    }
  }

  /**
   * Make request with retry logic
   * @param {Object} requestConfig - Request configuration
   * @param {number} retryCount - Number of retries
   * @returns {Object} Response object
   */
  static async makeRequestWithRetry(requestConfig, retryCount = 3) {
    let lastError;
    const startTime = Date.now();

    for (let attempt = 0; attempt <= retryCount; attempt++) {
      try {
        const response = await axios(requestConfig);
        response.responseTime = Date.now() - startTime;
        return response;
      } catch (error) {
        lastError = error;
        
        // Don't retry on certain status codes
        if (error.response && [400, 401, 403, 404, 422].includes(error.response.status)) {
          break;
        }

        // Don't retry on last attempt
        if (attempt === retryCount) {
          break;
        }

        // Wait before retry (exponential backoff)
        const waitTime = Math.pow(2, attempt) * 1000;
        console.log(`Request failed, retrying in ${waitTime}ms... (attempt ${attempt + 1}/${retryCount + 1})`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }

    throw lastError;
  }

  /**
   * Update test result for API or Form configuration
   * @param {string} configId - Configuration ID
   * @param {string} configType - Configuration type ('api' or 'form')
   * @param {Object} testResult - Test result object
   */
  static async updateTestResult(configId, configType, testResult) {
    try {
      let updatePath;
      if (configType === 'api') {
        updatePath = {
          'apiConfig.lastTestedAt': new Date(),
          'apiConfig.lastTestResult': testResult
        };
      } else if (configType === 'form') {
        updatePath = {
          'formConfig.submitApiConfig.lastTestedAt': new Date(),
          'formConfig.submitApiConfig.lastTestResult': testResult
        };
      } else {
        console.error('Unknown config type for test result update:', configType);
        return;
      }

      await UnifiedConfig.findByIdAndUpdate(configId, updatePath);
    } catch (error) {
      console.error('Failed to update test result:', error.message);
    }
  }

  /**
   * Sanitize headers for logging (hide sensitive data)
   * @param {Object} headers - Headers object
   * @returns {Object} Sanitized headers
   */
  static sanitizeHeaders(headers) {
    const sensitiveKeys = ['authorization', 'x-api-key', 'bearer', 'token'];
    const sanitized = { ...headers };
    
    Object.keys(sanitized).forEach(key => {
      if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
        sanitized[key] = '***hidden***';
      }
    });
    
    return sanitized;
  }

  /**
   * Test API configuration
   * @param {string} configName - Name of the API configuration
   * @param {Object} testData - Optional test data
   * @returns {Object} Test result
   */
  static async testApiConfig(configName, testData = null) {
    try {
      const response = await this.makeApiCall(configName, testData);
      return {
        success: true,
        statusCode: response.status,
        responseTime: response.responseTime,
        message: 'Test successful',
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        statusCode: error.response?.status || null,
        responseTime: null,
        message: 'Test failed',
        error: error.message
      };
    }
  }

  /**
   * Get all active API and Form configurations
   * @returns {Array} Array of API and Form configurations
   */
  static async getActiveConfigs() {
    return await UnifiedConfig.find({ 
      $or: [{ type: 'api' }, { type: 'form' }], 
      isActive: true 
    }).sort({ name: 1 });
  }

  /**
   * Validate API configuration before making call
   * @param {Object} unifiedConfig - UnifiedConfig object (can be API or Form type)
   * @throws {Error} If configuration is invalid
   */
  static validateConfig(unifiedConfig) {
    let apiConfigSection;
    
    if (unifiedConfig.type === 'api') {
      apiConfigSection = unifiedConfig.apiConfig;
    } else if (unifiedConfig.type === 'form') {
      apiConfigSection = unifiedConfig.formConfig?.submitApiConfig;
    } else {
      throw new Error(`Unsupported configuration type: ${unifiedConfig.type}`);
    }

    if (!apiConfigSection) {
      throw new Error(`No API configuration found for '${unifiedConfig.name}' (type: ${unifiedConfig.type})`);
    }

    if (!apiConfigSection.endpoint) {
      throw new Error('API endpoint is required');
    }

    if (!apiConfigSection.method) {
      throw new Error('HTTP method is required');
    }

    try {
      new URL(apiConfigSection.endpoint);
    } catch {
      throw new Error('Invalid endpoint URL');
    }
  }

  /**
   * Get mock response for demo endpoints
   * @param {string} configName - Name of the API configuration
   * @param {Object} requestConfig - Request configuration
   * @returns {Object} Mock response object
   */
  static getMockResponse(configName, requestConfig) {
    const mockResponses = {
      'Demo Attendance': {
        data: {
          monthYear: "07-2025",
          attendanceInfo: [
            {
              inTime: "09:00",
              attendanceDate: "2025-07-02",
              outTime: "18:00",
              actualOutTime: "19:03:00",
              actualInTime: "10:03:00",
              attendanceData: "A:P"
            },
            {
              inTime: "09:00",
              attendanceDate: "2025-07-07",
              outTime: "18:00",
              actualOutTime: "00:00",
              actualInTime: "00:00",
              attendanceData: "A"
            },
            {
              inTime: "09:00",
              attendanceDate: "2025-07-10",
              outTime: "18:00",
              actualOutTime: "18:15:00",
              actualInTime: "08:45:00",
              attendanceData: "P"
            },
            {
              inTime: "09:00",
              attendanceDate: "2025-07-15",
              outTime: "18:00",
              actualOutTime: "00:00",
              actualInTime: "00:00",
              attendanceData: "A"
            }
          ],
          empId: "NTS0218"
        },
        status: 200
      },
      'Regularization': {
        data: {
          success: true,
          message: "Regularization request submitted successfully",
          requestId: "REG-" + Date.now()
        },
        status: 200
      }
    };

    // Return mock response if available, otherwise return a default success response
    const mockResponse = mockResponses[configName] || {
      data: { success: true, message: "Mock response for " + configName },
      status: 200
    };

    // Add response time
    mockResponse.responseTime = Math.random() * 500 + 100; // Random response time between 100-600ms

    console.log(`🎭 Returning mock response for ${configName}:`, mockResponse);
    return mockResponse;
  }
}

module.exports = ApiService;
