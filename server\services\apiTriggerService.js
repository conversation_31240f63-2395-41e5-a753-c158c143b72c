const ApiConfig = require('../models/ApiConfig');
const axios = require('axios');

class ApiTriggerService {
  /**
   * Find and execute matching API configurations based on user input
   * @param {string} userInput - The user's input text
   * @param {Object} userData - User context data
   * @param {Object} options - Additional options
   * @returns {Promise<Object>} - Execution results
   */
  static async processUserInput(userInput, userData = {}, options = {}) {
    try {
      console.log('Processing user input:', userInput);
      
      // Find matching configurations
      const matchingConfigs = await ApiConfig.findMatchingConfigs(userInput);
      
      if (matchingConfigs.length === 0) {
        return {
          success: false,
          message: 'No matching configurations found',
          matches: 0,
          results: []
        };
      }
      
      console.log(`Found ${matchingConfigs.length} matching configurations`);
      
      // Execute the highest priority configuration (or all if specified)
      const executeAll = options.executeAll || false;
      const configsToExecute = executeAll ? matchingConfigs : [matchingConfigs[0]];
      
      const results = [];
      
      for (const config of configsToExecute) {
        try {
          console.log(`Executing configuration: ${config.name}`);
          
          let result;
          if (config.type === 'api') {
            result = await this.executeApiConfig(config, userData);
          } else if (config.type === 'form') {
            // For forms, we typically return the form structure rather than executing
            result = await this.getFormStructure(config);
          }
          
          result.configId = config._id;
          result.configName = config.name;
          result.configType = config.type;
          
          results.push(result);
          
          // Update statistics
          await ApiConfig.incrementTriggerStats(config._id, result.success);
          
          // If not executing all and we have a successful result, break
          if (!executeAll && result.success) {
            break;
          }
          
        } catch (error) {
          console.error(`Error executing config ${config.name}:`, error);
          results.push({
            configId: config._id,
            configName: config.name,
            configType: config.type,
            success: false,
            error: error.message
          });
        }
      }
      
      return {
        success: results.some(r => r.success),
        message: `Executed ${results.length} configuration(s)`,
        matches: matchingConfigs.length,
        results: results
      };
      
    } catch (error) {
      console.error('Error processing user input:', error);
      return {
        success: false,
        message: 'Failed to process user input',
        error: error.message,
        matches: 0,
        results: []
      };
    }
  }
  
  /**
   * Execute a specific API configuration
   * @param {Object} config - API configuration object
   * @param {Object} data - Data to pass to the API
   * @returns {Promise<Object>} - Execution result
   */
  static async executeApiConfig(config, data = {}) {
    const startTime = Date.now();
    
    try {
      const { apiConfig } = config;
      
      // Prepare headers
      const headers = { 'Content-Type': 'application/json' };
      
      // Add configured headers
      if (apiConfig.headers) {
        const headersMap = apiConfig.headers instanceof Map ? apiConfig.headers : new Map(Object.entries(apiConfig.headers));
        for (const [key, value] of headersMap) {
          if (key && value) {
            headers[key] = this.replaceTemplateVariables(value, data);
          }
        }
      }
      
      // Add authentication
      await this.addAuthentication(headers, apiConfig, data);
      
      // Prepare URL with query parameters
      let url = apiConfig.endpoint;
      if (apiConfig.queryParams) {
        const queryMap = apiConfig.queryParams instanceof Map ? apiConfig.queryParams : new Map(Object.entries(apiConfig.queryParams));
        if (queryMap.size > 0) {
          const urlObj = new URL(url);
          for (const [key, value] of queryMap) {
            if (key && value) {
              urlObj.searchParams.append(key, this.replaceTemplateVariables(value, data));
            }
          }
          url = urlObj.toString();
        }
      }
      
      // Prepare request body
      let requestBody = null;
      if (['POST', 'PUT', 'PATCH'].includes(apiConfig.method)) {
        if (apiConfig.bodyTemplate) {
          // Replace placeholders in body template
          requestBody = this.replaceTemplateVariables(apiConfig.bodyTemplate, data);
          try {
            requestBody = JSON.parse(requestBody);
          } catch (e) {
            // If not valid JSON, send as string
          }
        } else if (apiConfig.dataInjection?.injectUserData) {
          // Inject user data based on configuration
          requestBody = this.injectUserData(data, apiConfig.dataInjection);
        } else {
          requestBody = data;
        }
      }
      
      console.log(`Making ${apiConfig.method} request to: ${url}`);
      
      // Make API request with retry logic
      const response = await this.makeRequestWithRetry({
        method: apiConfig.method.toLowerCase(),
        url: url,
        headers: headers,
        data: requestBody,
        timeout: apiConfig.timeout || 30000
      }, apiConfig.retryConfig);
      
      const responseTime = Date.now() - startTime;
      const isSuccess = response.status >= 200 && response.status < 300;
      
      let processedData = response.data;
      
      // Apply response template if configured
      if (isSuccess && apiConfig.responseTemplate) {
        processedData = this.replaceTemplateVariables(apiConfig.responseTemplate, response.data);
        try {
          processedData = JSON.parse(processedData);
        } catch (e) {
          // Keep as string if not valid JSON
        }
      }
      
      // Apply response mapping if configured
      if (isSuccess && apiConfig.responseMapping) {
        processedData = this.applyResponseMapping(response.data, apiConfig.responseMapping);
      }
      
      return {
        success: isSuccess,
        data: processedData,
        statusCode: response.status,
        responseTime: responseTime,
        error: isSuccess ? null : `HTTP ${response.status}: ${response.statusText}`
      };
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      console.error('API execution error:', error.message);
      
      return {
        success: false,
        data: null,
        statusCode: error.response?.status || 0,
        responseTime: responseTime,
        error: error.message
      };
    }
  }
  
  /**
   * Get form structure for display
   * @param {Object} config - Form configuration object
   * @returns {Promise<Object>} - Form structure
   */
  static async getFormStructure(config) {
    try {
      const { formConfig } = config;
      
      return {
        success: true,
        data: {
          formId: config._id,
          name: config.name,
          description: config.description,
          fields: formConfig.fields,
          submitConfig: {
            endpoint: formConfig.submitApiConfig.endpoint,
            method: formConfig.submitApiConfig.method,
            successMessage: formConfig.submitApiConfig.successMessage,
            errorMessage: formConfig.submitApiConfig.errorMessage
          },
          prefillData: formConfig.prefillData || {}
        },
        type: 'form',
        message: 'Form structure retrieved successfully'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        type: 'form'
      };
    }
  }
  
  /**
   * Submit form data
   * @param {string} configId - Configuration ID
   * @param {Object} formData - Form data to submit
   * @returns {Promise<Object>} - Submission result
   */
  static async submitForm(configId, formData) {
    const startTime = Date.now();
    
    try {
      const config = await ApiConfig.findById(configId);
      if (!config) {
        throw new Error('Configuration not found');
      }
      
      if (config.type !== 'form') {
        throw new Error('Configuration is not a form');
      }
      
      const { formConfig } = config;
      const { submitApiConfig } = formConfig;
      
      // Validate form data against field configuration
      const validationResult = this.validateFormData(formData, formConfig.fields);
      if (!validationResult.valid) {
        return {
          success: false,
          error: 'Form validation failed',
          validationErrors: validationResult.errors,
          responseTime: Date.now() - startTime
        };
      }
      
      // Prepare headers
      const headers = { 'Content-Type': 'application/json' };
      
      // Add configured headers
      if (submitApiConfig.headers) {
        const headersMap = submitApiConfig.headers instanceof Map ? submitApiConfig.headers : new Map(Object.entries(submitApiConfig.headers));
        for (const [key, value] of headersMap) {
          if (key && value) {
            headers[key] = value;
          }
        }
      }
      
      // Add authentication
      await this.addAuthentication(headers, submitApiConfig);
      
      // Apply data mapping
      let mappedData = formData;
      if (submitApiConfig.dataMapping) {
        mappedData = {};
        const mappingMap = submitApiConfig.dataMapping instanceof Map ? submitApiConfig.dataMapping : new Map(Object.entries(submitApiConfig.dataMapping));
        for (const [formField, apiField] of mappingMap) {
          if (formData[formField] !== undefined) {
            mappedData[apiField] = formData[formField];
          }
        }
      }
      
      // Make API request
      const response = await axios({
        method: submitApiConfig.method.toLowerCase(),
        url: submitApiConfig.endpoint,
        headers: headers,
        data: mappedData,
        timeout: 30000,
        validateStatus: () => true
      });
      
      const responseTime = Date.now() - startTime;
      const isSuccess = response.status >= 200 && response.status < 300;
      
      // Update statistics
      await ApiConfig.incrementTriggerStats(config._id, isSuccess);
      
      return {
        success: isSuccess,
        data: response.data,
        responseTime: responseTime,
        message: isSuccess ? submitApiConfig.successMessage : submitApiConfig.errorMessage,
        error: isSuccess ? null : `HTTP ${response.status}: ${response.statusText}`,
        redirectUrl: isSuccess ? submitApiConfig.redirectUrl : null
      };
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        success: false,
        data: null,
        responseTime: responseTime,
        message: 'Failed to submit form',
        error: error.message
      };
    }
  }
  
  // Helper methods
  
  static async addAuthentication(headers, config, data = {}) {
    if (config.authType && config.authType !== 'none' && config.authConfig) {
      switch (config.authType) {
        case 'bearer':
          if (config.authConfig.token) {
            headers['Authorization'] = `Bearer ${this.replaceTemplateVariables(config.authConfig.token, data)}`;
          }
          break;
        case 'basic':
          if (config.authConfig.username && config.authConfig.password) {
            const auth = Buffer.from(`${config.authConfig.username}:${config.authConfig.password}`).toString('base64');
            headers['Authorization'] = `Basic ${auth}`;
          }
          break;
        case 'apikey':
          if (config.authConfig.apiKey && config.authConfig.apiKeyHeader) {
            headers[config.authConfig.apiKeyHeader] = this.replaceTemplateVariables(config.authConfig.apiKey, data);
          }
          break;
        case 'custom':
          if (config.authConfig.customHeaders) {
            const customMap = config.authConfig.customHeaders instanceof Map ? config.authConfig.customHeaders : new Map(Object.entries(config.authConfig.customHeaders));
            for (const [key, value] of customMap) {
              if (key && value) {
                headers[key] = this.replaceTemplateVariables(value, data);
              }
            }
          }
          break;
      }
    }
  }
  
  static async makeRequestWithRetry(requestConfig, retryConfig) {
    const maxRetries = retryConfig?.maxRetries || 3;
    const retryDelay = retryConfig?.retryDelay || 1000;
    const retryEnabled = retryConfig?.enabled !== false;
    
    let lastError;
    
    for (let attempt = 0; attempt <= (retryEnabled ? maxRetries : 0); attempt++) {
      try {
        const response = await axios({
          ...requestConfig,
          validateStatus: () => true
        });
        
        // If successful or client error (4xx), don't retry
        if (response.status < 500) {
          return response;
        }
        
        // Server error (5xx) - retry if not last attempt
        if (attempt < maxRetries && retryEnabled) {
          console.log(`Request failed with ${response.status}, retrying in ${retryDelay}ms... (attempt ${attempt + 1}/${maxRetries})`);
          await this.delay(retryDelay);
          continue;
        }
        
        return response;
        
      } catch (error) {
        lastError = error;
        
        // Network or timeout error - retry if not last attempt
        if (attempt < maxRetries && retryEnabled) {
          console.log(`Request failed with error: ${error.message}, retrying in ${retryDelay}ms... (attempt ${attempt + 1}/${maxRetries})`);
          await this.delay(retryDelay);
          continue;
        }
        
        throw error;
      }
    }
    
    throw lastError;
  }
  
  static replaceTemplateVariables(template, data) {
    if (typeof template !== 'string') return template;
    
    let result = template;
    
    // Replace {{variable}} patterns
    const matches = template.match(/\{\{([^}]+)\}\}/g);
    if (matches) {
      matches.forEach(match => {
        const variable = match.replace(/\{\{|\}\}/g, '').trim();
        const value = this.getNestedProperty(data, variable);
        result = result.replace(match, value !== undefined ? String(value) : '');
      });
    }
    
    return result;
  }
  
  static getNestedProperty(obj, path) {
    return path.split('.').reduce((current, prop) => {
      return current && current[prop] !== undefined ? current[prop] : undefined;
    }, obj);
  }
  
  static injectUserData(data, injectionConfig) {
    const result = { ...data };
    
    if (injectionConfig.userDataFields && injectionConfig.userDataFields.length > 0) {
      injectionConfig.userDataFields.forEach(field => {
        if (data[field] !== undefined) {
          result[field] = data[field];
        }
      });
    }
    
    if (injectionConfig.requiredFields && injectionConfig.requiredFields.length > 0) {
      injectionConfig.requiredFields.forEach(fieldConfig => {
        if (result[fieldConfig.name] === undefined && fieldConfig.defaultValue !== undefined) {
          result[fieldConfig.name] = fieldConfig.defaultValue;
        }
      });
    }
    
    return result;
  }
  
  static applyResponseMapping(responseData, mapping) {
    const mappingMap = mapping instanceof Map ? mapping : new Map(Object.entries(mapping));
    const mapped = {};
    
    for (const [sourcePath, targetPath] of mappingMap) {
      const value = this.getNestedProperty(responseData, sourcePath);
      if (value !== undefined) {
        this.setNestedProperty(mapped, targetPath, value);
      }
    }
    
    return mapped;
  }
  
  static setNestedProperty(obj, path, value) {
    const parts = path.split('.');
    let current = obj;
    
    for (let i = 0; i < parts.length - 1; i++) {
      if (!(parts[i] in current)) {
        current[parts[i]] = {};
      }
      current = current[parts[i]];
    }
    
    current[parts[parts.length - 1]] = value;
  }
  
  static validateFormData(formData, fields) {
    const errors = [];
    
    fields.forEach(field => {
      const value = formData[field.name];
      
      // Check required fields
      if (field.required && (value === undefined || value === null || value === '')) {
        errors.push(`${field.label || field.name} is required`);
        return;
      }
      
      // Skip validation if field is not provided and not required
      if (value === undefined || value === null || value === '') {
        return;
      }
      
      // Type validation
      switch (field.type) {
        case 'email':
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            errors.push(`${field.label || field.name} must be a valid email address`);
          }
          break;
        case 'number':
          if (isNaN(value)) {
            errors.push(`${field.label || field.name} must be a number`);
          }
          break;
        case 'date':
          if (isNaN(Date.parse(value))) {
            errors.push(`${field.label || field.name} must be a valid date`);
          }
          break;
      }
      
      // Validation rules
      if (field.validation) {
        const { minLength, maxLength, min, max, pattern } = field.validation;
        
        if (minLength && value.length < minLength) {
          errors.push(`${field.label || field.name} must be at least ${minLength} characters long`);
        }
        
        if (maxLength && value.length > maxLength) {
          errors.push(`${field.label || field.name} must be no more than ${maxLength} characters long`);
        }
        
        if (min && parseFloat(value) < min) {
          errors.push(`${field.label || field.name} must be at least ${min}`);
        }
        
        if (max && parseFloat(value) > max) {
          errors.push(`${field.label || field.name} must be no more than ${max}`);
        }
        
        if (pattern && !new RegExp(pattern).test(value)) {
          errors.push(`${field.label || field.name} format is invalid`);
        }
      }
    });
    
    return {
      valid: errors.length === 0,
      errors: errors
    };
  }
  
  static delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = ApiTriggerService;