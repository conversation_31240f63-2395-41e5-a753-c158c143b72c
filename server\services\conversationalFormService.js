const UnifiedConfig = require('../models/UnifiedConfig');
const Conversation = require('../models/Conversation');
const unifiedDynamicMatchingService = require('./unifiedDynamicMatchingService');

class ConversationalFormService {
  
  /**
   * Start a conversational form flow
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @returns {Object} - Response with the first question
   */
  async startFormFlow(conversation, form) {
    try {
      // Initialize the form flow state
      conversation.activeFormFlow = {
        formId: form._id,
        currentFieldIndex: 0,
        collectedData: new Map(),
        isComplete: false,
        startedAt: new Date()
      };

      // Check if this is a GET request with no fields
      const isGetRequest = form.formConfig.submitApiConfig?.method === 'GET';
      const hasFields = this.getFormFieldCount(form) > 0;
      
      if (isGetRequest && !hasFields) {
        // For GET requests with no fields, complete immediately
        return await this.completeFormFlow(conversation, form);
      }
      
      // Get the first question
      const firstQuestion = this.getNextQuestion(form, 0, new Map());
      
      await conversation.save();
      
      return {
        success: true,
        message: firstQuestion.message,
        fieldName: firstQuestion.fieldName,
        fieldType: firstQuestion.fieldType,
        options: firstQuestion.options,
        isFormFlow: true,
        currentStep: 1,
        totalSteps: this.getFormFieldCount(form)
      };
    } catch (error) {
      console.error('Error starting form flow:', error);
      return {
        success: false,
        message: 'Sorry, I encountered an error starting the form. Please try again.'
      };
    }
  }

  /**
   * Process user response in conversational form flow
   * @param {Object} conversation - The conversation object
   * @param {string} userResponse - User's response
   * @returns {Object} - Response with next question or completion
   */
  async processFormResponse(conversation, userResponse) {
    try {
      const formFlow = conversation.activeFormFlow;
      
      if (!formFlow || formFlow.isComplete) {
        return {
          success: false,
          message: 'No active form flow found.'
        };
      }

      // Ensure collectedData is a Map - convert from plain object if needed
      this.ensureCollectedDataIsMap(formFlow);

      // Get the form configuration
      const form = await UnifiedConfig.findById(formFlow.formId);
      if (!form || !form.formConfig.conversationalFlow.enabled) {
        return {
          success: false,
          message: 'Form configuration not found or conversational flow is disabled.'
        };
      }

      // Get current field
      const fields = this.getConversationalFields(form);
      const currentField = fields[formFlow.currentFieldIndex];
      
      if (!currentField) {
        return {
          success: false,
          message: 'Invalid field index.'
        };
      }

      // Validate the user response
      const validationResult = this.validateFieldResponse(currentField, userResponse);
      console.log('🔍 Validation result:', {
        isValid: validationResult.isValid,
        fieldName: currentField.name,
        fieldType: currentField.type,
        fieldOptions: currentField.options,
        userResponse: userResponse,
        validationMessage: validationResult.message
      });
      
      if (!validationResult.isValid) {
        console.log('❌ Validation failed, cancelling form and processing as new query:', userResponse);
        
        // Close the current form flow
        await this.cancelFormFlow(conversation);
        
        // Return a special response indicating form was closed and new query should be processed
        return {
          success: false,
          isValidationError: true,
          shouldProcessAsNewQuery: true,
          originalMessage: userResponse,
          validationMessage: validationResult.message
        };
      }

      // Store the validated response
      formFlow.collectedData.set(currentField.name, validationResult.value);
      
      // Move to next field
      formFlow.currentFieldIndex++;
      
      // Check if we've completed all fields
      if (formFlow.currentFieldIndex >= fields.length) {
        return await this.completeFormFlow(conversation, form);
      }

      // Get next question
      const nextQuestion = this.getNextQuestion(form, formFlow.currentFieldIndex, formFlow.collectedData);
      
      // Convert Map back to plain object for database storage
      this.convertMapToObjectForStorage(formFlow);
      
      await conversation.save();
      
      return {
        success: true,
        message: nextQuestion.message,
        fieldName: nextQuestion.fieldName,
        fieldType: nextQuestion.fieldType,
        options: nextQuestion.options,
        isFormFlow: true,
        currentStep: formFlow.currentFieldIndex + 1,
        totalSteps: fields.length,
        collectedData: formFlow.collectedData
      };
      
    } catch (error) {
      console.error('Error processing form response:', error);
      return {
        success: false,
        message: 'Sorry, I encountered an error processing your response. Please try again.'
      };
    }
  }

  /**
   * Complete the form flow and submit the data
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @returns {Object} - Completion response
   */
  async completeFormFlow(conversation, form) {
    try {
      const formFlow = conversation.activeFormFlow;
      
      // Ensure collectedData is a Map - convert from plain object if needed
      this.ensureCollectedDataIsMap(formFlow);
      
      // Convert Map to regular object for submission
      const formData = Object.fromEntries(formFlow.collectedData);
      
      console.log('🎯 Completing conversational form with data:', formData);
      console.log('🎯 Raw collected data Map:', formFlow.collectedData);
      console.log('🎯 Form fields configuration:', this.getConversationalFields(form).map(f => ({ name: f.name, type: f.type })));
      console.log('🎯 Form ID:', form._id);
      console.log('🎯 Conversation ID:', conversation._id);
      console.log('🎯 User metadata:', {
        empId: conversation.metadata?.empId,
        hasToken: !!conversation.metadata?.token,
        roleType: conversation.metadata?.roleType
      });
      
      // Mark flow as complete
      formFlow.isComplete = true;
      
      // Convert Map back to plain object for database storage
      this.convertMapToObjectForStorage(formFlow);
      
      // Get user data from conversation metadata
      const userData = {
        empId: conversation.metadata?.empId,
        token: conversation.metadata?.token,
        roleType: conversation.metadata?.roleType
      };
      
      // Check if this is a GET request - if so, execute immediately
      const isGetRequest = form.formConfig.submitApiConfig?.method === 'GET';
      let apiResponse = null;
      let completionMessage = form.formConfig.conversationalFlow?.completionMessage || 'Thank you! Your form has been submitted successfully.';
      
      if (isGetRequest) {
        console.log(`🔍 GET request detected for form: ${form.name}`);
        console.log('📋 Executing GET request immediately...');
        
        try {
          // Execute the GET request immediately
          apiResponse = await unifiedDynamicMatchingService.submitDynamicForm(
            form, 
            formData, 
            userData, 
            form._id, 
            conversation._id
          );
          
          if (apiResponse.success && apiResponse.formattedResponse) {
            completionMessage = apiResponse.formattedResponse;
          } else if (!apiResponse.success) {
            completionMessage = `❌ **Error retrieving data**\n\n${apiResponse.error || 'Failed to retrieve data. Please try again.'}`;
          }
          
          console.log('✅ GET request completed successfully');
        } catch (error) {
          console.error('❌ Error executing GET request:', error);
          completionMessage = `❌ **Error retrieving data**\n\n${error.message}`;
          apiResponse = {
            success: false,
            error: error.message
          };
        }
      } else {
        console.log(`📋 POST/PUT/PATCH request for form: ${form.name}`);
        console.log('📋 Form data will be sent to client for submission');
        completionMessage = 'Processing your form submission...';
      }
      
      await conversation.save();
      
      return {
        success: true,
        message: completionMessage,
        isFormComplete: true,
        formData: formData,
        formId: form._id,
        needsSubmission: !isGetRequest, // GET requests don't need client submission
        formConfig: form,
        apiResponse: apiResponse,
        totalSteps: this.getFormFieldCount(form),
        isGetRequest: isGetRequest
      };
      
    } catch (error) {
      console.error('Error completing form flow:', error);
      return {
        success: false,
        message: 'Sorry, I encountered an error completing your form. Please try again.'
      };
    }
  }

  /**
   * Get the next question for the user
   * @param {Object} form - The form configuration
   * @param {number} fieldIndex - Current field index
   * @param {Map} collectedData - Already collected data
   * @returns {Object} - Question object
   */
  getNextQuestion(form, fieldIndex, collectedData) {
    const fields = this.getConversationalFields(form);
    const field = fields[fieldIndex];
    
    if (!field) {
      return {
        message: 'No more questions.',
        fieldName: null,
        fieldType: null,
        options: null
      };
    }

    // Use custom conversational prompt if available
    let message = field.conversationalPrompt || this.generateDefaultPrompt(field);
    
    // Don't add options text to message - the client will display them as buttons
    // The options will be sent separately in the response
    
    return {
      message: message,
      fieldName: field.name,
      fieldType: field.type,
      options: field.options || null
    };
  }

  /**
   * Generate default prompt for a field
   * @param {Object} field - Field configuration
   * @returns {string} - Generated prompt
   */
  generateDefaultPrompt(field) {
    const fieldLabel = field.label || field.name;
    const requiredText = field.required ? ' (required)' : '';
    
    switch (field.type) {
      case 'email':
        return `Please provide your ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'date':
        return `Please enter the ${fieldLabel.toLowerCase()} (YYYY-MM-DD format)${requiredText}:`;
      case 'time':
        return `Please enter the ${fieldLabel.toLowerCase()} (HH:MM format)${requiredText}:`;
      case 'number':
        return `Please enter a number for ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'textarea':
        return `Please provide details for ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'select':
      case 'radio':
        return `Please select an option for ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'checkbox':
        return `Please confirm ${fieldLabel.toLowerCase()} (yes/no)${requiredText}:`;
      default:
        return `Please provide ${fieldLabel.toLowerCase()}${requiredText}:`;
    }
  }

  /**
   * Validate user response for a field
   * @param {Object} field - Field configuration
   * @param {string} response - User response
   * @returns {Object} - Validation result
   */
  validateFieldResponse(field, response) {
    const trimmedResponse = response.trim();
    
    // Check required fields
    if (field.required && !trimmedResponse) {
      return {
        isValid: false,
        message: `${field.label || field.name} is required. Please provide a value.`
      };
    }
    
    // If field is not required and response is empty, allow it
    if (!trimmedResponse && !field.required) {
      return {
        isValid: true,
        value: ''
      };
    }
    
    // Type-specific validation
    switch (field.type) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide a valid email address.'
          };
        }
        break;
        
      case 'number':
        if (isNaN(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide a valid number.'
          };
        }
        break;
        
      case 'date':
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide date in YYYY-MM-DD format (e.g., 2024-01-15).'
          };
        }
        break;
        
      case 'time':
        const timeRegex = /^\d{2}:\d{2}$/;
        if (!timeRegex.test(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide time in HH:MM format (e.g., 14:30).'
          };
        }
        break;
        
      case 'select':
      case 'radio':
        if (field.options && field.options.length > 0) {
          // Check if response is a number (option index)
          const optionIndex = parseInt(trimmedResponse) - 1;
          if (optionIndex >= 0 && optionIndex < field.options.length) {
            return {
              isValid: true,
              value: field.options[optionIndex]
            };
          }
          
          // Check if response matches an option directly
          const matchingOption = field.options.find(opt => 
            opt.toLowerCase() === trimmedResponse.toLowerCase()
          );
          
          if (matchingOption) {
            return {
              isValid: true,
              value: matchingOption
            };
          }
          
          return {
            isValid: false,
            message: `Please select a valid option by number (1-${field.options.length}) or by name.`
          };
        }
        break;
        
      case 'checkbox':
        // Handle checkbox fields with multiple options (checklist)
        if (field.options && field.options.length > 0) {
          // Parse comma-separated or multiple selections
          const selections = trimmedResponse.split(',').map(s => s.trim());
          const validSelections = [];
          
          for (const selection of selections) {
            // Check if selection is a number (option index)
            const optionIndex = parseInt(selection) - 1;
            if (optionIndex >= 0 && optionIndex < field.options.length) {
              validSelections.push(field.options[optionIndex]);
            } else {
              // Check if selection matches an option directly
              const matchingOption = field.options.find(opt => 
                opt.toLowerCase() === selection.toLowerCase()
              );
              if (matchingOption) {
                validSelections.push(matchingOption);
              }
            }
          }
          
          if (validSelections.length === 0) {
            return {
              isValid: false,
              message: `Please select valid options. You can select multiple options by separating them with commas (e.g., "1,3" or "option1,option2"). Available options: ${field.options.map((opt, idx) => `${idx + 1}. ${opt}`).join(', ')}`
            };
          }
          
          return {
            isValid: true,
            value: validSelections
          };
        }
    }
    
    // If we get here, validation passed
    return {
      isValid: true,
      value: field.type === 'number' ? parseFloat(trimmedResponse) : trimmedResponse
    };
  }

  /**
   * Get fields that should be included in conversational flow
   * @param {Object} form - Form configuration
   * @returns {Array} - Array of fields
   */
  getConversationalFields(form) {
    if (!form.formConfig || !form.formConfig.fields) {
      return [];
    }
    
    return form.formConfig.fields.filter(field => !field.skipInConversation);
  }

  /**
   * Get total count of fields in conversational flow
   * @param {Object} form - Form configuration
   * @returns {number} - Field count
   */
  getFormFieldCount(form) {
    return this.getConversationalFields(form).length;
  }

  /**
   * Ensure collectedData is a Map - convert from plain object if needed
   * @param {Object} formFlow - Form flow object
   */
  ensureCollectedDataIsMap(formFlow) {
    if (!(formFlow.collectedData instanceof Map)) {
      formFlow.collectedData = new Map(Object.entries(formFlow.collectedData || {}));
    }
  }

  /**
   * Convert Map back to plain object for database storage
   * @param {Object} formFlow - Form flow object
   */
  convertMapToObjectForStorage(formFlow) {
    if (formFlow.collectedData instanceof Map) {
      formFlow.collectedData = Object.fromEntries(formFlow.collectedData);
    }
  }



  /**
   * Check if a conversation has an active form flow
   * @param {Object} conversation - Conversation object
   * @returns {boolean} - True if has active flow
   */
  hasActiveFormFlow(conversation) {
    return conversation.activeFormFlow && 
           conversation.activeFormFlow.formId && 
           conversation.activeFormFlow.flowType !== 'hybrid' &&
           !conversation.activeFormFlow.isComplete;
  }

  /**
   * Cancel active form flow
   * @param {Object} conversation - Conversation object
   * @returns {Object} - Cancellation response
   */
  async cancelFormFlow(conversation) {
    try {
      if (conversation.activeFormFlow) {
        conversation.activeFormFlow.isComplete = true;
        conversation.activeFormFlow.formId = null;
        await conversation.save();
      }
      
      return {
        success: true,
        message: 'Form cancelled. How else can I help you?'
      };
    } catch (error) {
      console.error('Error cancelling form flow:', error);
      return {
        success: false,
        message: 'Error cancelling form.'
      };
    }
  }
}

module.exports = new ConversationalFormService();