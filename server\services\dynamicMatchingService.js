const UnifiedConfig = require('../models/UnifiedConfig');
const axios = require('axios');

class DynamicMatchingService {
  
  /**
   * Find matching forms and APIs for a given query using unified UnifiedConfig collection
   * @param {string} query - User query
   * @returns {Object} - Match results with forms, apis, and best match
   */
  async findMatches(query) {
    try {
      console.log('🔍 Finding matches for query:', query);
      
      // Get all active configurations
      const allConfigs = await UnifiedConfig.findActiveConfigs();
      
      console.log(`📊 Available configurations: ${allConfigs.length} total (${allConfigs.filter(c => c.type === 'form').length} forms, ${allConfigs.filter(c => c.type === 'api').length} APIs)`);
      
      // Calculate confidence scores for all configurations
      const matches = allConfigs.map(config => ({
        ...config.toObject(),
        confidence: this.calculateMatchConfidence(query, config)
      }));
      
      // Filter and sort by confidence
      const validMatches = matches
        .filter(match => match.confidence > 0)
        .sort((a, b) => {
          // Sort by confidence first, then by priority, then by creation date
          if (b.confidence !== a.confidence) return b.confidence - a.confidence;
          if (b.priority !== a.priority) return (b.priority || 0) - (a.priority || 0);
          return new Date(b.createdAt) - new Date(a.createdAt);
        });
      
      // Separate forms and APIs
      const formMatches = validMatches.filter(m => m.type === 'form');
      const apiMatches = validMatches.filter(m => m.type === 'api');
      
      console.log('🎯 Top matches:', validMatches.slice(0, 3).map(m => ({ 
        name: m.name, 
        type: m.type, 
        confidence: m.confidence.toFixed(3),
        priority: m.priority || 0
      })));
      
      return {
        forms: formMatches,
        apis: apiMatches,
        allMatches: validMatches,
        bestMatch: validMatches[0] || null,
        confidence: validMatches[0]?.confidence || 0,
        matchType: validMatches[0]?.type || null,
        totalConfigs: allConfigs.length
      };
      
    } catch (error) {
      console.error('❌ Error finding matches:', error);
      return {
        forms: [],
        apis: [],
        allMatches: [],
        bestMatch: null,
        confidence: 0,
        matchType: null,
        totalConfigs: 0
      };
    }
  }
  
  /**
   * Find matching forms based on prompt, keywords, and name
   */
  async findMatchingForms(userQuery) {
    try {
      const query = userQuery.toLowerCase();
      
      // First try exact text search
      const textSearchForms = await Form.find(
        { 
          $text: { $search: userQuery },
          'fields.0': { $exists: true } // Must have at least one field
        },
        { score: { $meta: 'textScore' } }
      ).sort({ score: { $meta: 'textScore' } }).limit(5);
      
      // Then try keyword/phrase matching
      const keywordForms = await Form.find({
        $or: [
          { keywords: { $in: [new RegExp(query, 'i')] } },
          { prompt: new RegExp(query, 'i') },
          { name: new RegExp(query, 'i') },
          { description: new RegExp(query, 'i') }
        ],
        'fields.0': { $exists: true }
      }).limit(5);
      
      // Combine and score results
      const allForms = [...textSearchForms, ...keywordForms];
      const uniqueForms = this.removeDuplicates(allForms, '_id');
      
      return uniqueForms.map(form => ({
        ...form.toObject(),
        type: 'form',
        confidence: this.calculateFormConfidence(form, userQuery)
      }));
    } catch (error) {
      console.error('❌ Error finding matching forms:', error);
      return [];
    }
  }
  
  /**
   * Find matching APIs based on keywords, prompt, and trigger phrases
   */
  async findMatchingApis(userQuery) {
    try {
      const query = userQuery.toLowerCase();
      
      // Text search
      const textSearchApis = await UnifiedConfig.find(
        { 
          $text: { $search: userQuery },
          type: 'api',
          isActive: true
        },
        { score: { $meta: 'textScore' } }
      ).sort({ score: { $meta: 'textScore' } }).limit(5);
      
      // Keyword/phrase matching
      const keywordApis = await UnifiedConfig.find({
        $and: [
          { type: 'api' },
          { isActive: true },
          {
            $or: [
              { keywords: { $in: [new RegExp(query, 'i')] } },
              { triggerPhrases: { $in: [new RegExp(query, 'i')] } },
              { prompt: new RegExp(query, 'i') },
              { name: new RegExp(query, 'i') },
              { description: new RegExp(query, 'i') }
            ]
          }
        ]
      }).limit(5);
      
      // Combine and score results
      const allApis = [...textSearchApis, ...keywordApis];
      const uniqueApis = this.removeDuplicates(allApis, '_id');
      
      return uniqueApis.map(api => ({
        ...api.toObject(),
        type: 'api',
        confidence: this.calculateApiConfidence(api, userQuery)
      }));
    } catch (error) {
      console.error('❌ Error finding matching APIs:', error);
      return [];
    }
  }
  
  /**
   * Calculate confidence score for form matches
   */
  calculateFormConfidence(form, userQuery) {
    let confidence = 0;
    const query = userQuery.toLowerCase();
    
    // Name match (highest weight)
    if (form.name.toLowerCase().includes(query)) {
      confidence += 0.4;
    }
    
    // Keywords match
    if (form.keywords && form.keywords.some(keyword => 
      query.includes(keyword.toLowerCase()) || keyword.toLowerCase().includes(query)
    )) {
      confidence += 0.3;
    }
    
    // Prompt match
    if (form.prompt && form.prompt.toLowerCase().includes(query)) {
      confidence += 0.2;
    }
    
    // Description match
    if (form.description && form.description.toLowerCase().includes(query)) {
      confidence += 0.1;
    }
    
    return Math.min(confidence, 1.0);
  }
  
  /**
   * Calculate confidence score for API matches
   */
  calculateApiConfidence(api, userQuery) {
    let confidence = 0;
    const query = userQuery.toLowerCase();
    
    // Trigger phrases match (highest weight)
    if (api.triggerPhrases && api.triggerPhrases.some(phrase => 
      query.includes(phrase.toLowerCase()) || phrase.toLowerCase().includes(query)
    )) {
      confidence += 0.4;
    }
    
    // Name match
    if (api.name.toLowerCase().includes(query)) {
      confidence += 0.3;
    }
    
    // Keywords match
    if (api.keywords && api.keywords.some(keyword => 
      query.includes(keyword.toLowerCase()) || keyword.toLowerCase().includes(query)
    )) {
      confidence += 0.2;
    }
    
    // Prompt match
    if (api.prompt && api.prompt.toLowerCase().includes(query)) {
      confidence += 0.1;
    }
    
    return Math.min(confidence, 1.0);
  }
  
  /**
   * Determine the best match from all results
   */
  determineBestMatch(matches) {
    if (!matches || matches.length === 0) return null;
    
    // Sort by confidence score
    const sortedMatches = matches.sort((a, b) => b.confidence - a.confidence);
    
    // Return best match if confidence is above threshold
    const bestMatch = sortedMatches[0];
    if (bestMatch.confidence >= 0.3) {
      return bestMatch;
    }
    
    return null;
  }
  
  /**
   * Execute dynamic API call with user data injection
   */
  async executeDynamicApi(apiConfig, userData = null, formData = null) {
    try {
      console.log('🚀 Executing dynamic API:', apiConfig.name);
      
      // Build base request config
      const requestConfig = apiConfig.buildRequestConfig();
      
      // Inject user data if configured
      let dataToSend = { ...formData };
      
      if (apiConfig.dataInjection?.injectUserData && userData) {
        // Auto-inject configured fields
        if (apiConfig.dataInjection.autoFields?.empId && userData.empId) {
          dataToSend.empId = userData.empId;
        }
        if (apiConfig.dataInjection.autoFields?.roleType && userData.roleType) {
          dataToSend.type = userData.roleType;
        }
        
        // Auto-inject token in headers if configured
        if (apiConfig.dataInjection.autoFields?.token && userData.token) {
          requestConfig.headers['Authorization'] = `Bearer ${userData.token}`;
        }
      }
      
      // Add data to request
      if (requestConfig.method === 'get') {
        requestConfig.params = dataToSend;
      } else {
        requestConfig.data = dataToSend;
      }
      
      console.log('📤 API Request config:', {
        method: requestConfig.method,
        url: requestConfig.url,
        hasData: !!requestConfig.data,
        hasParams: !!requestConfig.params,
        headers: Object.keys(requestConfig.headers)
      });
      
      // Execute request
      const response = await axios(requestConfig);
      
      console.log('✅ API Response:', {
        status: response.status,
        statusText: response.statusText,
        dataSize: JSON.stringify(response.data).length
      });
      
      return {
        success: true,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        responseTime: Date.now()
      };
      
    } catch (error) {
      console.error('❌ Dynamic API execution error:', error.message);
      
      return {
        success: false,
        status: error.response?.status || 500,
        statusText: error.response?.statusText || 'Internal Server Error',
        error: error.message,
        data: error.response?.data || null
      };
    }
  }
  
  /**
   * Remove duplicate objects from array based on a key
   */
  removeDuplicates(array, key) {
    const seen = new Set();
    return array.filter(item => {
      const keyValue = item[key]?.toString();
      if (seen.has(keyValue)) {
        return false;
      }
      seen.add(keyValue);
      return true;
    });
  }
  
  /**
   * Format API response using template
   */
  formatApiResponse(apiConfig, apiResponse) {
    try {
      if (!apiConfig.responseTemplate) {
        return this.defaultResponseFormatter(apiResponse);
      }
      
      // Simple template replacement (can be enhanced with a proper template engine)
      let formatted = apiConfig.responseTemplate;
      
      // Replace common placeholders
      formatted = formatted.replace(/\{status\}/g, apiResponse.status);
      formatted = formatted.replace(/\{statusText\}/g, apiResponse.statusText);
      
      // If response has data, try to replace data placeholders
      if (apiResponse.data && typeof apiResponse.data === 'object') {
        Object.keys(apiResponse.data).forEach(key => {
          formatted = formatted.replace(
            new RegExp(`\\{data\\.${key}\\}`, 'g'), 
            apiResponse.data[key]
          );
        });
      }
      
      return formatted;
    } catch (error) {
      console.error('❌ Error formatting response:', error);
      return this.defaultResponseFormatter(apiResponse);
    }
  }
  
  /**
   * Default response formatter
   */
  defaultResponseFormatter(apiResponse) {
    if (apiResponse.success) {
      return `✅ **Request Successful**\n\nStatus: ${apiResponse.status} ${apiResponse.statusText}\n\nResponse: ${JSON.stringify(apiResponse.data, null, 2)}`;
    } else {
      return `❌ **Request Failed**\n\nStatus: ${apiResponse.status} ${apiResponse.statusText}\n\nError: ${apiResponse.error}`;
    }
  }
}

module.exports = new DynamicMatchingService();
