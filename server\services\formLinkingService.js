const UnifiedConfig = require('../models/UnifiedConfig');

class FormLinkingService {
  
  /**
   * Get form linking configuration for a specific form
   * @param {string} formId - The form ID
   * @returns {Object} - Form linking configuration
   */
  async getFormLinkingConfig(formId) {
    try {
      const form = await UnifiedConfig.findById(formId);
      if (!form || form.type !== 'form') {
        return null;
      }
      
      return form.formConfig?.formLinking || null;
    } catch (error) {
      console.error('Error getting form linking config:', error);
      return null;
    }
  }
  
  /**
   * Check if a record should show apply buttons based on conditions
   * @param {Object} record - The data record
   * @param {Array} conditions - Array of conditions to check
   * @returns {Boolean} - Whether to show the apply button
   */
  shouldShowApplyButton(record, conditions = []) {
    if (!conditions || conditions.length === 0) {
      return true; // Show by default if no conditions
    }
    
    return conditions.every(condition => {
      const fieldValue = record[condition.field];
      const conditionValue = condition.value;
      
      switch (condition.operator) {
        case 'equals':
          return fieldValue === conditionValue;
        case 'not_equals':
          return fieldValue !== conditionValue;
        case 'contains':
          return fieldValue && fieldValue.toString().includes(conditionValue);
        case 'not_contains':
          return !fieldValue || !fieldValue.toString().includes(conditionValue);
        case 'exists':
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        case 'not_exists':
          return fieldValue === undefined || fieldValue === null || fieldValue === '';
        default:
          return true;
      }
    });
  }
  
  /**
   * Map record data to target form fields
   * @param {Object} record - The source record data
   * @param {Object} parentData - The parent data object (for parent-level fields)
   * @param {Object} fieldMapping - Field mapping configuration
   * @param {string} buttonText - The button text to map (if needed)
   * @returns {Object} - Mapped data for target form
   */
  mapRecordToForm(record, parentData = {}, fieldMapping = {}, buttonText = null, isAutoSubmit = false) {
    const mappedData = {};
    
    // Convert Map to object if necessary
    const mapping = fieldMapping instanceof Map ? Object.fromEntries(fieldMapping) : fieldMapping;
    
    console.log('🔍 Field mapping debug:', {
      mapping,
      buttonText,
      isAutoSubmit,
      record: Object.keys(record),
      parentData: Object.keys(parentData)
    });
    
    Object.entries(mapping).forEach(([targetField, sourceField]) => {
      console.log(`🔍 Mapping ${sourceField} -> ${targetField}`);
      
      // Check if this field should be mapped to the button text
      if (sourceField === 'buttonText' && buttonText !== null) {
        console.log(`✅ Mapping button text "${buttonText}" to field "${targetField}"`);
        mappedData[targetField] = buttonText;
      }
      // First check if the field exists in the record
      else if (record[sourceField] !== undefined) {
        console.log(`✅ Mapping record field "${sourceField}": ${record[sourceField]} -> "${targetField}"`);
        mappedData[targetField] = record[sourceField];
      }
      // Then check if the field exists in the parent data
      else if (parentData[sourceField] !== undefined) {
        console.log(`✅ Mapping parent field "${sourceField}": ${parentData[sourceField]} -> "${targetField}"`);
        mappedData[targetField] = parentData[sourceField];
      }
      else {
        console.log(`⚠️ No mapping found for "${sourceField}" -> "${targetField}"`);
      }
    });

    // Also add button text mapping for fields that explicitly want it
    if (buttonText !== null) {
      // Check if any field is specifically mapped to get button text
      Object.entries(mapping).forEach(([targetField, sourceField]) => {
        if (sourceField === 'buttonText') {
          console.log(`🎯 Special buttonText mapping: "${buttonText}" -> "${targetField}"`);
          mappedData[targetField] = buttonText;
        }
      });
      
      // Also check if there's a direct 'type' field that should get button text
      if (!mappedData.type && mapping.type === 'buttonText') {
        console.log(`🎯 Direct type field mapping: "${buttonText}" -> "type"`);
        mappedData.type = buttonText;
      }
    }
    
    // For auto-submit, only include explicitly mapped fields to avoid sending wrong data structure
    if (!isAutoSubmit) {
      // Also include direct field matches (same field names) - only for normal form display
      Object.keys(record).forEach(key => {
        if (!mappedData[key]) {
          mappedData[key] = record[key];
        }
      });
      
      // Include parent data fields if they don't conflict - only for normal form display
      Object.keys(parentData).forEach(key => {
        if (!mappedData[key] && !record[key]) {
          mappedData[key] = parentData[key];
        }
      });
    } else {
      console.log('🚀 Auto-submit mode: Only including explicitly mapped fields');
    }
    
    return mappedData;
  }
  
  /**
   * Get target form configuration for form linking
   * @param {string} targetFormId - Target form ID
   * @param {string} targetFormName - Target form name (fallback)
   * @returns {Object} - Target form configuration
   */
  async getTargetForm(targetFormId, targetFormName) {
    try {
      let targetForm = null;
      
      // First try by ID
      if (targetFormId) {
        targetForm = await UnifiedConfig.findById(targetFormId);
      }
      
      // Fallback to name search
      if (!targetForm && targetFormName) {
        targetForm = await UnifiedConfig.findOne({ 
          name: targetFormName, 
          type: 'form', 
          isActive: true 
        });
      }
      
      return targetForm;
    } catch (error) {
      console.error('Error getting target form:', error);
      return null;
    }
  }
  
  /**
   * Check if auto-trigger should be activated for a record
   * @param {Object} record - The data record
   * @param {Object} formLinkingConfig - Form linking configuration
   * @returns {Object} - Auto-trigger information
   */
  shouldAutoTrigger(record, formLinkingConfig) {
    if (!formLinkingConfig?.enabled || !formLinkingConfig.recordActions?.length) {
      return { shouldTrigger: false };
    }
    
    // Find the first action with auto-trigger enabled
    const autoTriggerAction = formLinkingConfig.recordActions.find(action => 
      action.autoTrigger?.enabled && this.shouldShowApplyButton(record, action.conditions)
    );
    
    if (!autoTriggerAction) {
      return { shouldTrigger: false };
    }
    
    return {
      shouldTrigger: true,
      actionIndex: formLinkingConfig.recordActions.indexOf(autoTriggerAction),
      delaySeconds: autoTriggerAction.autoTrigger.delaySeconds || 0,
      targetFormId: autoTriggerAction.targetFormId,
      targetFormName: autoTriggerAction.targetFormName
    };
  }

  /**
   * Process form linking request
   * @param {string} sourceFormId - Source form ID
   * @param {Object} recordData - Record data from source form
   * @param {Object} parentData - Parent data object (for parent-level fields)
   * @param {string} actionIndex - Index of the action button clicked
   * @returns {Object} - Result with target form and prefilled data
   */
  async processFormLinking(sourceFormId, recordData, parentData = {}, actionIndex = 0, buttonText = null) {
    try {
      console.log('🔗 Processing form linking request:', {
        sourceFormId,
        recordData,
        parentData,
        actionIndex,
        buttonText
      });
      
      // Get source form configuration
      const sourceForm = await UnifiedConfig.findById(sourceFormId);
      if (!sourceForm || sourceForm.type !== 'form') {
        throw new Error('Source form not found');
      }
      
      const formLinking = sourceForm.formConfig?.formLinking;
      if (!formLinking?.enabled || !formLinking.recordActions?.length) {
        throw new Error('Form linking not configured for this form');
      }
      
      // Get the specific action configuration
      const action = formLinking.recordActions[actionIndex];
      if (!action) {
        throw new Error('Action configuration not found');
      }
      
      // Check conditions
      if (!this.shouldShowApplyButton(recordData, action.conditions)) {
        throw new Error('Conditions not met for this action');
      }
      
      // Get target form
      const targetForm = await this.getTargetForm(action.targetFormId, action.targetFormName);
      if (!targetForm) {
        throw new Error('Target form not found');
      }
      
      // Map record data to target form
      // Pass isAutoSubmit flag to only include mapped fields when auto-submit is enabled
      const isAutoSubmit = action.autoSubmitOnClick || false;
      const mappedData = this.mapRecordToForm(recordData, parentData, action.fieldMapping, buttonText, isAutoSubmit);
      
      console.log('✅ Form linking processed successfully:', {
        targetFormName: targetForm.name,
        mappedFieldsCount: Object.keys(mappedData).length,
        mappedFields: Object.keys(mappedData),
        mappedData: mappedData,
        buttonTextUsed: buttonText
      });
      
      return {
        success: true,
        targetForm: targetForm,
        prefillData: mappedData,
        buttonText: action.buttonText || 'Apply',
        buttonStyle: action.buttonStyle || 'primary',
        autoTrigger: action.autoTrigger || { enabled: false, delaySeconds: 0 },
        autoSubmitOnClick: action.autoSubmitOnClick || false
      };
      
    } catch (error) {
      console.error('❌ Error processing form linking:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }
  
  /**
   * Get all available forms for form linking configuration
   * @returns {Array} - Array of available forms
   */
  async getAvailableFormsForLinking() {
    try {
      const forms = await UnifiedConfig.find({ 
        type: 'form', 
        isActive: true 
      }).select('_id name description category').sort({ name: 1 });
      
      return forms;
    } catch (error) {
      console.error('Error getting available forms:', error);
      return [];
    }
  }
}

module.exports = new FormLinkingService();