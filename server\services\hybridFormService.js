const UnifiedConfig = require('../models/UnifiedConfig');
const Conversation = require('../models/Conversation');
const unifiedDynamicMatchingService = require('./unifiedDynamicMatchingService');

class HybridFormService {
  
  /**
   * Start a hybrid form flow
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @returns {Object} - Response with the first question or form display
   */
  async startHybridFlow(conversation, form) {
    try {
      // Initialize the hybrid flow state
      conversation.activeFormFlow = {
        formId: form._id,
        flowType: 'hybrid',
        currentFieldIndex: 0,
        collectedData: {},
        conversationalFields: this.getPreFormConversationalFields(form),
        formFields: this.getFormFields(form),
        isConversationalPhase: true,
        isComplete: false,
        startedAt: new Date(),
        phase: 'pre-form'
      };
      
      console.log('🚀 HYBRID FLOW INITIALIZED:', {
        formId: form._id,
        flowType: conversation.activeFormFlow.flowType,
        isConversationalPhase: conversation.activeFormFlow.isConversationalPhase
      });

      // Check if there are any pre-form conversational fields
      const preFormFields = this.getPreFormConversationalFields(form);
      const postFormFields = this.getPostFormConversationalFields(form);
      
      if (preFormFields.length === 0) {
        // No pre-form conversational fields, skip to form phase
        return await this.transitionToFormPhase(conversation, form);
      }
      
      // Start with first pre-form conversational question
      const firstQuestion = this.getNextConversationalQuestion(form, 0, {}, 'pre-form');
      
      await conversation.save();
      
      console.log('🚀 HYBRID FLOW SAVED:', {
        formId: conversation.activeFormFlow.formId,
        flowType: conversation.activeFormFlow.flowType,
        isConversationalPhase: conversation.activeFormFlow.isConversationalPhase
      });
      
      return {
        success: true,
        message: firstQuestion.message,
        fieldName: firstQuestion.fieldName,
        fieldType: firstQuestion.fieldType,
        options: firstQuestion.options,
        isHybridFlow: true,
        isConversationalPhase: true,
        currentStep: 1,
        totalConversationalSteps: preFormFields.length + postFormFields.length,
        totalFormSteps: this.getFormFields(form).length,
        phase: 'pre-form'
      };
    } catch (error) {
      console.error('Error starting hybrid flow:', error);
      return {
        success: false,
        message: 'Sorry, I encountered an error starting the hybrid form. Please try again.'
      };
    }
  }

  /**
   * Process user response in hybrid form flow
   * @param {Object} conversation - The conversation object
   * @param {string} userResponse - User's response
   * @returns {Object} - Response with next question or form transition
   */
  async processHybridResponse(conversation, userResponse) {
    try {
      const formFlow = conversation.activeFormFlow;
      
      if (!formFlow || formFlow.isComplete) {
        return {
          success: false,
          message: 'No active hybrid form flow found.'
        };
      }

      // Get the form configuration
      console.log('🔍 Looking for form with ID:', formFlow.formId);
      const form = await UnifiedConfig.findById(formFlow.formId);
      console.log('🔍 Form found:', !!form);
      console.log('🔍 Hybrid flow enabled:', form?.formConfig?.hybridFlow?.enabled);
      
      if (!form || !form.formConfig.hybridFlow.enabled) {
        console.log('❌ Form not found or hybrid flow disabled');
        return {
          success: false,
          message: 'Form configuration not found or hybrid flow is disabled.'
        };
      }

      console.log('🔍 Hybrid form flow state:', {
        isConversationalPhase: formFlow.isConversationalPhase,
        currentStep: formFlow.currentStep,
        totalConversationalSteps: formFlow.totalConversationalSteps,
        totalFormSteps: formFlow.totalFormSteps,
        userResponse: userResponse
      });

      if (formFlow.isConversationalPhase) {
        return await this.processConversationalResponse(conversation, form, userResponse);
      } else {
        // User is in form phase but sent a message - check if it's irrelevant
        console.log('⚠️ User sent message during form phase:', userResponse);
        
        // Check if the message is actually form submission (JSON format)
        if (userResponse.trim().startsWith('{') && userResponse.trim().endsWith('}')) {
          // This is likely a form submission, let client handle it
          return {
            success: false,
            message: 'Form phase should be handled by client.'
          };
        }
        
        // Otherwise, treat as irrelevant message and cancel form
        console.log('🚫 Irrelevant message during form phase, cancelling form');
        return {
          success: false,
          message: 'Form cancelled due to irrelevant input.',
          shouldProcessAsNewQuery: true,
          originalMessage: userResponse
        };
      }
      
    } catch (error) {
      console.error('Error processing hybrid response:', error);
      return {
        success: false,
        message: 'Sorry, I encountered an error processing your response. Please try again.'
      };
    }
  }

  /**
   * Process conversational response
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @param {string} userResponse - User's response
   * @returns {Object} - Response with next question or transition
   */
  async processConversationalResponse(conversation, form, userResponse) {
    const formFlow = conversation.activeFormFlow;
    const phase = formFlow.phase || 'pre-form';
    
    // Get the appropriate fields based on phase
    const conversationalFields = phase === 'pre-form' 
      ? this.getPreFormConversationalFields(form) 
      : this.getPostFormConversationalFields(form);
    
    const currentField = conversationalFields[formFlow.currentFieldIndex];
    
    if (!currentField) {
      return {
        success: false,
        message: 'Invalid field index in conversational phase.'
      };
    }

    // Validate the user response
    const validationResult = this.validateFieldResponse(currentField, userResponse);
    if (!validationResult.isValid) {
      // Close the current hybrid form flow
      await this.cancelHybridFlow(conversation);
      
      // Return a special response indicating form was closed and new query should be processed
      return {
        success: false,
        isValidationError: true,
        shouldProcessAsNewQuery: true,
        originalMessage: userResponse,
        validationMessage: validationResult.message
      };
    }

    // Store the validated response
    formFlow.collectedData[currentField.name] = validationResult.value;
    
    // Move to next conversational field
    formFlow.currentFieldIndex++;
    
    // Check if we've completed current phase's conversational fields
    if (formFlow.currentFieldIndex >= conversationalFields.length) {
      if (phase === 'pre-form') {
        // Transition to form phase
        return await this.transitionToFormPhase(conversation, form);
      } else {
        // Complete the hybrid flow (post-form conversational fields done)
        return await this.completeHybridFlow(conversation, form);
      }
    }

    // Get next conversational question in current phase
    const nextQuestion = this.getNextConversationalQuestion(form, formFlow.currentFieldIndex, formFlow.collectedData, phase);
    
    await conversation.save();
    
    const preFormFields = this.getPreFormConversationalFields(form);
    const postFormFields = this.getPostFormConversationalFields(form);
    const totalConversationalSteps = preFormFields.length + postFormFields.length;
    
    let currentStepNumber;
    if (phase === 'pre-form') {
      currentStepNumber = formFlow.currentFieldIndex + 1;
    } else {
      currentStepNumber = preFormFields.length + formFlow.currentFieldIndex + 1;
    }
    
    return {
      success: true,
      message: nextQuestion.message,
      fieldName: nextQuestion.fieldName,
      fieldType: nextQuestion.fieldType,
      options: nextQuestion.options,
      isHybridFlow: true,
      isConversationalPhase: true,
      currentStep: currentStepNumber,
      totalConversationalSteps: totalConversationalSteps,
      totalFormSteps: this.getFormFields(form).length,
      collectedData: formFlow.collectedData,
      phase: phase
    };
  }

  /**
   * Transition from conversational phase to form phase
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @returns {Object} - Transition response
   */
  async transitionToFormPhase(conversation, form) {
    const formFlow = conversation.activeFormFlow;
    
    // Mark as transitioning to form phase
    formFlow.isConversationalPhase = false;
    formFlow.currentFieldIndex = 0; // Reset for form phase
    formFlow.phase = 'form'; // Set phase to form
    
    const formFields = this.getFormFields(form);
    const collectedData = formFlow.collectedData;
    
    // Check if there are any form fields to show
    if (formFields.length === 0) {
      // No form fields, check if there are post-form conversational fields
      const postFormFields = this.getPostFormConversationalFields(form);
      if (postFormFields.length > 0) {
        return await this.transitionToPostFormConversation(conversation, form);
      } else {
        // No form fields and no post-form conversation, complete the flow
        return await this.completeHybridFlow(conversation, form);
      }
    }
    
    const completionMessage = form.formConfig.hybridFlow?.completionMessage || 'Please complete the remaining form fields below.';
    
    await conversation.save();
    
    const preFormFields = this.getPreFormConversationalFields(form);
    const postFormFields = this.getPostFormConversationalFields(form);
    
    return {
      success: true,
      message: completionMessage,
      isHybridFlow: true,
      isConversationalPhase: false,
      transitionToForm: true,
      formId: form._id,
      formFields: formFields,
      collectedData: collectedData,
      formConfig: form,
      totalConversationalSteps: preFormFields.length + postFormFields.length,
      totalFormSteps: formFields.length
    };
  }

  /**
   * Transition from form phase to post-form conversational phase
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @returns {Object} - Transition response
   */
  async transitionToPostFormConversation(conversation, form) {
    const formFlow = conversation.activeFormFlow;
    
    // Mark as transitioning to post-form conversational phase
    formFlow.isConversationalPhase = true;
    formFlow.currentFieldIndex = 0; // Reset for post-form conversation
    formFlow.phase = 'post-form'; // Set phase to post-form
    
    const postFormFields = this.getPostFormConversationalFields(form);
    
    if (postFormFields.length === 0) {
      // No post-form conversational fields, complete the flow
      return await this.completeHybridFlow(conversation, form);
    }
    
    // Get first post-form conversational question
    const firstQuestion = this.getNextConversationalQuestion(form, 0, formFlow.collectedData, 'post-form');
    
    await conversation.save();
    
    const preFormFields = this.getPreFormConversationalFields(form);
    const totalConversationalSteps = preFormFields.length + postFormFields.length;
    const currentStepNumber = preFormFields.length + 1;
    
    return {
      success: true,
      message: firstQuestion.message,
      fieldName: firstQuestion.fieldName,
      fieldType: firstQuestion.fieldType,
      options: firstQuestion.options,
      isHybridFlow: true,
      isConversationalPhase: true,
      currentStep: currentStepNumber,
      totalConversationalSteps: totalConversationalSteps,
      totalFormSteps: this.getFormFields(form).length,
      collectedData: formFlow.collectedData,
      phase: 'post-form'
    };
  }

  /**
   * Handle form submission in hybrid flow
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @param {Object} formData - Form data from client
   * @returns {Object} - Response (either post-form conversation or completion)
   */
  async handleFormSubmission(conversation, form, formData = {}) {
    const formFlow = conversation.activeFormFlow;
    
    // Merge form data with existing collected data
    Object.entries(formData).forEach(([key, value]) => {
      formFlow.collectedData[key] = value;
    });
    
    // Check if there are post-form conversational fields
    const postFormFields = this.getPostFormConversationalFields(form);
    
    if (postFormFields.length > 0) {
      // Transition to post-form conversation
      return await this.transitionToPostFormConversation(conversation, form);
    } else {
      // No post-form conversation, complete the flow
      return await this.completeHybridFlow(conversation, form, formData);
    }
  }

  /**
   * Complete hybrid form with form data
   * @param {Object} conversation - The conversation object
   * @param {Object} form - The form configuration
   * @param {Object} formData - Form data from client
   * @returns {Object} - Completion response
   */
  async completeHybridFlow(conversation, form, formData = {}) {
    try {
      const formFlow = conversation.activeFormFlow;
      
      // Merge conversational data with form data
      const conversationalData = formFlow.collectedData;
      const finalData = { ...conversationalData, ...formData };
      
      console.log('🔄 Completing hybrid form with data:', finalData);
      console.log('🔄 Conversational data:', conversationalData);
      console.log('🔄 Form data:', formData);
      console.log('🔄 Form ID:', form._id);
      console.log('🔄 Conversation ID:', conversation._id);
      
      // Mark flow as complete
      formFlow.isComplete = true;
      
      // Get user data from conversation metadata
      const userData = {
        empId: conversation.metadata?.empId,
        token: conversation.metadata?.token,
        roleType: conversation.metadata?.roleType
      };
      
      // Check if this is a GET request - if so, execute immediately
      const isGetRequest = form.formConfig.submitApiConfig?.method === 'GET';
      let apiResponse = null;
      let completionMessage = 'Thank you! Your hybrid form has been submitted successfully.';
      
      if (isGetRequest) {
        console.log(`🔍 GET request detected for hybrid form: ${form.name}`);
        console.log('📋 Executing GET request immediately...');
        
        try {
          // Execute the GET request immediately
          apiResponse = await unifiedDynamicMatchingService.submitDynamicForm(
            form, 
            finalData, 
            userData, 
            form._id, 
            conversation._id
          );
          
          if (apiResponse.success && apiResponse.formattedResponse) {
            completionMessage = apiResponse.formattedResponse;
          } else if (!apiResponse.success) {
            completionMessage = `❌ **Error retrieving data**\n\n${apiResponse.error || 'Failed to retrieve data. Please try again.'}`;
          }
          
          console.log('✅ GET request completed successfully');
        } catch (error) {
          console.error('❌ Error executing GET request:', error);
          completionMessage = `❌ **Error retrieving data**\n\n${error.message}`;
          apiResponse = {
            success: false,
            error: error.message
          };
        }
      } else {
        console.log(`📋 POST/PUT/PATCH request for hybrid form: ${form.name}`);
        console.log('📋 Form data will be sent to client for submission');
        
        // For hybrid forms, we should try to submit but continue flow even if it fails
        try {
          console.log('🔄 Attempting to submit hybrid form to API...');
          apiResponse = await unifiedDynamicMatchingService.submitDynamicForm(
            form, 
            finalData, 
            userData, 
            form._id, 
            conversation._id
          );
          
          if (apiResponse.success) {
            completionMessage = 'Your hybrid form has been submitted successfully!';
            console.log('✅ Hybrid form submitted successfully');
          } else {
            completionMessage = 'Your hybrid form data has been collected. There was an issue with the final submission, but your information has been saved.';
            console.log('⚠️ Hybrid form submission failed, but continuing flow:', apiResponse.error);
          }
        } catch (error) {
          console.error('❌ Error submitting hybrid form to API:', error);
          completionMessage = 'Your hybrid form data has been collected. There was an issue with the final submission, but your information has been saved.';
          apiResponse = {
            success: false,
            error: error.message
          };
        }
      }
      
      await conversation.save();
      
      return {
        success: true,
        message: completionMessage,
        isHybridFormComplete: true,
        formData: finalData,
        formId: form._id,
        needsSubmission: !isGetRequest,
        formConfig: form,
        apiResponse: apiResponse,
        conversationalData: conversationalData,
        isGetRequest: isGetRequest
      };
      
    } catch (error) {
      console.error('Error completing hybrid form flow:', error);
      return {
        success: false,
        message: 'Sorry, I encountered an error completing your hybrid form. Please try again.'
      };
    }
  }

  /**
   * Get the next conversational question
   * @param {Object} form - The form configuration
   * @param {number} fieldIndex - Current field index
   * @param {Object} collectedData - Already collected data
   * @param {string} phase - 'pre-form' or 'post-form'
   * @returns {Object} - Question object
   */
  getNextConversationalQuestion(form, fieldIndex, collectedData, phase = 'pre-form') {
    const conversationalFields = phase === 'pre-form' 
      ? this.getPreFormConversationalFields(form) 
      : this.getPostFormConversationalFields(form);
    const field = conversationalFields[fieldIndex];
    
    // Debug: Log field information
    console.log('🔍 Processing conversational field:', {
      fieldIndex,
      totalConversationalFields: conversationalFields.length,
      fieldName: field?.name,
      fieldType: field?.type,
      hasOptions: !!(field?.options && field?.options?.length > 0),
      optionsCount: field?.options?.length || 0
    });
    
    if (!field) {
      return {
        message: 'No more conversational questions.',
        fieldName: null,
        fieldType: null,
        options: null
      };
    }

    // Use custom conversational prompt if available
    let message = field.conversationalPrompt || this.generateDefaultPrompt(field);
    
    // For select fields with options, just add a simple instruction
    if (field.type === 'select' && field.options && field.options.length > 0) {
      message += '\n\nPlease select one of the available options:';
    }
    
    const result = {
      message: message,
      fieldName: field.name,
      fieldType: field.type,
      options: field.options || null
    };
    
    console.log('🎯 Returning conversational question with options:', {
      fieldName: result.fieldName,
      fieldType: result.fieldType,
      hasOptions: !!(result.options && result.options.length > 0),
      optionsCount: result.options?.length || 0
    });
    
    return result;
  }

  /**
   * Generate default prompt for a field
   * @param {Object} field - Field configuration
   * @returns {string} - Generated prompt
   */
  generateDefaultPrompt(field) {
    const fieldLabel = field.label || field.name;
    const requiredText = field.required ? ' (required)' : '';
    
    switch (field.type) {
      case 'email':
        return `Please provide your ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'date':
        return `Please enter the ${fieldLabel.toLowerCase()} (YYYY-MM-DD format)${requiredText}:`;
      case 'time':
        return `Please enter the ${fieldLabel.toLowerCase()} (HH:MM format)${requiredText}:`;
      case 'number':
        return `Please enter a number for ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'textarea':
        return `Please provide details for ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'select':
      case 'radio':
        return `Please select an option for ${fieldLabel.toLowerCase()}${requiredText}:`;
      case 'checkbox':
        return `Please confirm ${fieldLabel.toLowerCase()} (yes/no)${requiredText}:`;
      default:
        return `Please provide ${fieldLabel.toLowerCase()}${requiredText}:`;
    }
  }

  /**
   * Validate user response for a field
   * @param {Object} field - Field configuration
   * @param {string} response - User response
   * @returns {Object} - Validation result
   */
  validateFieldResponse(field, response) {
    const trimmedResponse = response.trim();
    
    // Check required fields
    if (field.required && !trimmedResponse) {
      return {
        isValid: false,
        message: `${field.label || field.name} is required. Please provide a value.`
      };
    }
    
    // If field is not required and response is empty, allow it
    if (!trimmedResponse && !field.required) {
      return {
        isValid: true,
        value: ''
      };
    }
    
    // Type-specific validation
    switch (field.type) {
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide a valid email address.'
          };
        }
        break;
        
      case 'number':
        if (isNaN(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide a valid number.'
          };
        }
        break;
        
      case 'date':
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide date in YYYY-MM-DD format (e.g., 2024-01-15).'
          };
        }
        break;
        
      case 'time':
        const timeRegex = /^\d{2}:\d{2}$/;
        if (!timeRegex.test(trimmedResponse)) {
          return {
            isValid: false,
            message: 'Please provide time in HH:MM format (e.g., 14:30).'
          };
        }
        break;
        
      case 'select':
      case 'radio':
        if (field.options && field.options.length > 0) {
          // Check if response is a number (option index)
          const optionIndex = parseInt(trimmedResponse) - 1;
          if (optionIndex >= 0 && optionIndex < field.options.length) {
            return {
              isValid: true,
              value: field.options[optionIndex]
            };
          }
          
          // Check if response matches an option directly
          const matchingOption = field.options.find(opt => 
            opt.toLowerCase() === trimmedResponse.toLowerCase()
          );
          
          if (matchingOption) {
            return {
              isValid: true,
              value: matchingOption
            };
          }
          
          return {
            isValid: false,
            message: `Please select a valid option by number (1-${field.options.length}) or by name.`
          };
        }
        break;
        
      case 'checkbox':
        // Handle checkbox fields with multiple options (checklist)
        if (field.options && field.options.length > 0) {
          // Parse comma-separated or multiple selections
          const selections = trimmedResponse.split(',').map(s => s.trim());
          const validSelections = [];
          
          for (const selection of selections) {
            // Check if selection is a number (option index)
            const optionIndex = parseInt(selection) - 1;
            if (optionIndex >= 0 && optionIndex < field.options.length) {
              validSelections.push(field.options[optionIndex]);
            } else {
              // Check if selection matches an option directly
              const matchingOption = field.options.find(opt => 
                opt.toLowerCase() === selection.toLowerCase()
              );
              if (matchingOption) {
                validSelections.push(matchingOption);
              }
            }
          }
          
          if (validSelections.length === 0) {
            return {
              isValid: false,
              message: `Please select valid options. You can select multiple options by separating them with commas (e.g., "1,3" or "option1,option2"). Available options: ${field.options.map((opt, idx) => `${idx + 1}. ${opt}`).join(', ')}`
            };
          }
          
          return {
            isValid: true,
            value: validSelections
          };
        }
    }
    
    // If we get here, validation passed
    return {
      isValid: true,
      value: field.type === 'number' ? parseFloat(trimmedResponse) : trimmedResponse
    };
  }

  /**
   * Get fields that should be asked conversationally BEFORE the form
   * @param {Object} form - Form configuration
   * @returns {Array} - Array of pre-form conversational fields
   */
  getPreFormConversationalFields(form) {
    if (!form.formConfig || !form.formConfig.fields) {
      return [];
    }
    
    const preFormFields = form.formConfig.fields.filter(field => {
      // Skip fields that are marked to skip in conversation
      if (field.skipInConversation) {
        return false;
      }
      
      // Include conversational fields that are NOT reason field
      return field.displayMode === 'conversational' && field.name !== 'reason';
    });
    
    console.log('🔍 Pre-form conversational fields found:', preFormFields.map(f => ({
      name: f.name,
      type: f.type,
      displayMode: f.displayMode,
      hasOptions: !!(f.options && f.options.length > 0),
      optionsCount: f.options?.length || 0
    })));
    
    return preFormFields;
  }

  /**
   * Get fields that should be asked conversationally AFTER the form
   * @param {Object} form - Form configuration
   * @returns {Array} - Array of post-form conversational fields
   */
  getPostFormConversationalFields(form) {
    if (!form.formConfig || !form.formConfig.fields) {
      return [];
    }
    
    const postFormFields = form.formConfig.fields.filter(field => {
      // Skip fields that are marked to skip in conversation
      if (field.skipInConversation) {
        return false;
      }
      
      // Include only the reason field for post-form conversation
      return field.displayMode === 'conversational' && field.name === 'reason';
    });
    
    console.log('🔍 Post-form conversational fields found:', postFormFields.map(f => ({
      name: f.name,
      type: f.type,
      displayMode: f.displayMode,
      hasOptions: !!(f.options && f.options.length > 0),
      optionsCount: f.options?.length || 0
    })));
    
    return postFormFields;
  }

  /**
   * Get fields that should be asked conversationally in hybrid flow (legacy method)
   * @param {Object} form - Form configuration
   * @returns {Array} - Array of conversational fields
   */
  getConversationalFields(form) {
    // For backward compatibility, return pre-form conversational fields
    return this.getPreFormConversationalFields(form);
  }

  /**
   * Get fields that should be displayed as form fields in hybrid flow
   * @param {Object} form - Form configuration
   * @returns {Array} - Array of form fields
   */
  getFormFields(form) {
    if (!form.formConfig || !form.formConfig.fields) {
      return [];
    }
    
    return form.formConfig.fields.filter(field => {
      // Skip fields that are marked to skip in conversation
      if (field.skipInConversation) {
        return false;
      }
      
      // In hybrid flow, include fields that are:
      // 1. Explicitly set to 'form' mode
      // 2. Set to 'auto' mode but come after conversational fields
      return field.displayMode === 'form' ||
             (field.displayMode === 'auto' && !this.isConversationalField(field)) ||
             (!field.displayMode && !this.isConversationalField(field));
    });
  }

  /**
   * Check if a field should be conversational in auto mode
   * @param {Object} field - Field configuration
   * @returns {boolean} - True if should be conversational
   */
  isConversationalField(field) {
    // In auto mode, text-based fields are typically conversational
    // while complex fields (select, checkbox with many options) are form fields
    const conversationalTypes = ['text', 'email', 'number', 'date', 'time', 'textarea'];
    
    if (conversationalTypes.includes(field.type)) {
      return true;
    }
    
    // For select/radio/checkbox, consider them conversational if they have few options
    if ((field.type === 'select' || field.type === 'radio') && field.options && field.options.length <= 5) {
      return true;
    }
    
    return false;
  }

  /**
   * Check if a conversation has an active hybrid form flow
   * @param {Object} conversation - Conversation object
   * @returns {boolean} - True if has active hybrid flow
   */
  hasActiveHybridFlow(conversation) {
    return conversation.activeFormFlow && 
           conversation.activeFormFlow.formId && 
           conversation.activeFormFlow.flowType === 'hybrid' &&
           !conversation.activeFormFlow.isComplete;
  }

  /**
   * Cancel active hybrid form flow
   * @param {Object} conversation - Conversation object
   * @returns {Object} - Cancellation response
   */
  async cancelHybridFlow(conversation) {
    try {
      if (conversation.activeFormFlow) {
        conversation.activeFormFlow.isComplete = true;
        conversation.activeFormFlow.formId = null;
        await conversation.save();
      }
      
      return {
        success: true,
        message: 'Hybrid form cancelled. How else can I help you?'
      };
    } catch (error) {
      console.error('Error cancelling hybrid form flow:', error);
      return {
        success: false,
        message: 'Error cancelling hybrid form.'
      };
    }
  }
}

module.exports = new HybridFormService();