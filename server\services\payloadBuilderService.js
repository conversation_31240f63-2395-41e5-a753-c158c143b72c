const moment = require('moment');

/**
 * Service for building custom payload structures
 */
class PayloadBuilderService {
  
  /**
   * Build complete API configuration including endpoint and payload
   * @param {Object} apiConfig - API configuration with custom payload settings
   * @param {Object} userData - User data for injection
   * @param {Object} additionalData - Additional data to include
   * @returns {Object} - Built configuration with endpoint and payload
   */
  buildApiConfiguration(apiConfig, userData = {}, additionalData = {}) {
    try {
      console.log(`🔧 Building complete API configuration for: ${apiConfig.name}`);
      
      // Build endpoint URL if it exists
      let processedEndpoint = apiConfig.endpoint;
      if (processedEndpoint) {
        processedEndpoint = this.buildApiEndpoint(processedEndpoint, userData, additionalData);
      }

      // Build payload
      const payload = this.buildApiPayload(apiConfig, userData, additionalData);

      const result = {
        endpoint: processedEndpoint,
        payload: payload
      };

      console.log(`✅ Complete API configuration built:`, JSON.stringify(result, null, 2));
      return result;

    } catch (error) {
      console.error('❌ Error building complete API configuration:', error);
      return {
        endpoint: apiConfig.endpoint,
        payload: this.buildDefaultApiPayload(apiConfig, userData, additionalData)
      };
    }
  }

  /**
   * Build custom payload for API calls
   * @param {Object} apiConfig - API configuration with custom payload settings
   * @param {Object} userData - User data for injection
   * @param {Object} additionalData - Additional data to include
   * @returns {Object} - Built payload
   */
  buildApiPayload(apiConfig, userData = {}, additionalData = {}) {
    try {
      // If custom payload is not enabled, use default structure
      if (!apiConfig.customPayload?.enabled) {
        return this.buildDefaultApiPayload(apiConfig, userData, additionalData);
      }

      console.log(`🔧 Building custom API payload for: ${apiConfig.name}`);
      
      // Get the custom payload structure
      const customStructure = apiConfig.customPayload.structure || {};
      const mergeStrategy = apiConfig.customPayload.mergeStrategy || 'replace';
      const transformations = apiConfig.customPayload.transformations || [];

      // Start with base payload
      let payload = {};

      // Apply merge strategy
      switch (mergeStrategy) {
        case 'replace':
          // Use only custom structure
          payload = this.processPayloadStructure(customStructure, userData, additionalData);
          break;
        case 'merge':
          // Merge custom structure with default
          const defaultPayload = this.buildDefaultApiPayload(apiConfig, userData, additionalData);
          payload = { ...defaultPayload, ...this.processPayloadStructure(customStructure, userData, additionalData) };
          break;
        case 'append':
          // Add custom structure to default
          const basePayload = this.buildDefaultApiPayload(apiConfig, userData, additionalData);
          const customPayload = this.processPayloadStructure(customStructure, userData, additionalData);
          payload = { ...basePayload, custom: customPayload };
          break;
      }

      // Apply transformations
      payload = this.applyTransformations(payload, transformations, userData, additionalData);

      console.log(`✅ Custom API payload built:`, JSON.stringify(payload, null, 2));
      return payload;

    } catch (error) {
      console.error('❌ Error building custom API payload:', error);
      // Fallback to default payload
      return this.buildDefaultApiPayload(apiConfig, userData, additionalData);
    }
  }

  /**
   * Build default API payload structure
   * @param {Object} apiConfig - API configuration
   * @param {Object} userData - User data
   * @param {Object} additionalData - Additional data
   * @returns {Object} - Default payload
   */
  buildDefaultApiPayload(apiConfig, userData = {}, additionalData = {}) {
    let payload = { ...additionalData };
    
    if (apiConfig.dataInjection?.injectUserData) {
      if (apiConfig.dataInjection.autoFields?.empId && userData.empId) {
        payload.empId = userData.empId;
      }
      if (apiConfig.dataInjection.autoFields?.roleType && userData.roleType) {
        payload.roleType = userData.roleType;
      }
    }

    return payload;
  }

  /**
   * Build default form payload structure
   * @param {Object} formConfig - Form configuration
   * @param {Object} formData - Form data
   * @param {Object} userData - User data
   * @returns {Object} - Default payload
   */
  buildDefaultFormPayload(formConfig, formData = {}, userData = {}) {
    let payload = { ...formData };
    
    const submitConfig = formConfig.formConfig.submitApiConfig;
    
    // Apply data mapping if configured
    if (submitConfig.dataMapping && submitConfig.dataMapping.size > 0) {
      const mappedData = {};
      submitConfig.dataMapping.forEach((apiField, formField) => {
        if (formData[formField] !== undefined) {
          mappedData[apiField] = formData[formField];
        }
      });
      payload = { ...payload, ...mappedData };
    }

    return payload;
  }

  /**
   * Process payload structure by replacing placeholders
   * @param {Object} structure - Payload structure template
   * @param {Object} userData - User data
   * @param {Object} additionalData - Additional data (form data or API data)
   * @returns {Object} - Processed payload
   */
  processPayloadStructure(structure, userData = {}, additionalData = {}) {
    if (!structure || typeof structure !== 'object') {
      return {};
    }

    console.log(`🔧 Processing payload structure:`, JSON.stringify(structure, null, 2));
    console.log(`📊 Available form data:`, JSON.stringify(additionalData, null, 2));

    const processValue = (value) => {
      if (typeof value === 'string') {
        // Check if this is a direct placeholder replacement
        const directPlaceholderMatch = value.match(/^{{data\.(\w+)}}$/);
        if (directPlaceholderMatch) {
          const fieldName = directPlaceholderMatch[1];
          if (additionalData.hasOwnProperty(fieldName)) {
            console.log(`🔄 Direct replacement: ${value} -> ${JSON.stringify(additionalData[fieldName])}`);
            return additionalData[fieldName]; // Return the actual value (array, object, etc.)
          }
        }
        
        const userPlaceholderMatch = value.match(/^{{user\.(\w+)}}$/);
        if (userPlaceholderMatch) {
          const fieldName = userPlaceholderMatch[1];
          if (userData.hasOwnProperty(fieldName)) {
            console.log(`🔄 Direct user replacement: ${value} -> ${JSON.stringify(userData[fieldName])}`);
            return userData[fieldName];
          }
        }
        
        // Fall back to string replacement for complex templates
        return this.replacePlaceholders(value, userData, additionalData);
      } else if (Array.isArray(value)) {
        return value.map(processValue);
      } else if (typeof value === 'object' && value !== null) {
        const processed = {};
        Object.keys(value).forEach(key => {
          processed[key] = processValue(value[key]);
        });
        return processed;
      }
      return value;
    };

    const result = processValue(structure);
    console.log(`✅ Processed payload structure result:`, JSON.stringify(result, null, 2));
    return result;
  }

  /**
   * Replace placeholders in string values
   * @param {string} template - Template string with placeholders
   * @param {Object} userData - User data
   * @param {Object} additionalData - Additional data
   * @returns {string} - Processed string
   */
  replacePlaceholders(template, userData = {}, additionalData = {}) {
    if (typeof template !== 'string') {
      return template;
    }

    let result = template;

    // Replace user data placeholders
    Object.keys(userData).forEach(key => {
      const placeholder = `{{user.${key}}}`;
      const value = userData[key];
      if (value !== undefined) {
        result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
      }
    });

    // Replace additional data placeholders (form data)
    Object.keys(additionalData).forEach(key => {
      const placeholder = `{{data.${key}}}`;
      const value = additionalData[key];
      if (value !== undefined) {
        // For string templates, convert objects/arrays to strings
        const replacementValue = typeof value === 'object' ? JSON.stringify(value) : value;
        result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacementValue);
      }
    });

    // Replace system placeholders
    result = result.replace(/{{system\.timestamp}}/g, Date.now());
    result = result.replace(/{{system\.date}}/g, moment().format('YYYY-MM-DD'));
    result = result.replace(/{{system\.datetime}}/g, moment().format('YYYY-MM-DD HH:mm:ss'));
    result = result.replace(/{{system\.year}}/g, moment().format('YYYY'));
    result = result.replace(/{{system\.month}}/g, moment().format('MM'));
    result = result.replace(/{{system\.day}}/g, moment().format('DD'));
    result = result.replace(/{{system\.uuid}}/g, this.generateUUID());

    return result;
  }

  /**
   * Apply transformations to payload
   * @param {Object} payload - Current payload
   * @param {Array} transformations - List of transformations
   * @param {Object} userData - User data
   * @param {Object} additionalData - Additional data
   * @returns {Object} - Transformed payload
   */
  applyTransformations(payload, transformations, userData = {}, additionalData = {}) {
    if (!transformations || !Array.isArray(transformations)) {
      return payload;
    }

    let result = { ...payload };

    transformations.forEach(transformation => {
      try {
        const { field, operation, value } = transformation;
        
        switch (operation) {
          case 'format':
            if (result[field] !== undefined) {
              result[field] = this.formatValue(result[field], value);
            }
            break;
          case 'calculate':
            result[field] = this.calculateValue(value, result, userData, additionalData);
            break;
          case 'transform':
            result[field] = this.transformValue(result[field], value);
            break;
          case 'condition':
            result = this.applyCondition(result, field, value, userData, additionalData);
            break;
        }
      } catch (error) {
        console.error('❌ Error applying transformation:', error);
      }
    });

    return result;
  }

  /**
   * Format value according to format specification
   * @param {*} value - Value to format
   * @param {string} format - Format specification
   * @returns {*} - Formatted value
   */
  formatValue(value, format) {
    if (typeof format !== 'string') {
      return value;
    }

    switch (format.toLowerCase()) {
      case 'uppercase':
        return typeof value === 'string' ? value.toUpperCase() : value;
      case 'lowercase':
        return typeof value === 'string' ? value.toLowerCase() : value;
      case 'capitalize':
        return typeof value === 'string' ? value.charAt(0).toUpperCase() + value.slice(1).toLowerCase() : value;
      case 'date':
        return moment(value).format('YYYY-MM-DD');
      case 'datetime':
        return moment(value).format('YYYY-MM-DD HH:mm:ss');
      case 'timestamp':
        return moment(value).unix();
      default:
        return value;
    }
  }

  /**
   * Calculate value based on expression
   * @param {string} expression - Calculation expression
   * @param {Object} payload - Current payload
   * @param {Object} userData - User data
   * @param {Object} additionalData - Additional data
   * @returns {*} - Calculated value
   */
  calculateValue(expression, payload, userData, additionalData) {
    try {
      // Simple expression evaluation - can be extended
      if (expression === 'count') {
        return Object.keys(payload).length;
      }
      
      // If expression is a string template, process it
      if (typeof expression === 'string') {
        return this.replacePlaceholders(expression, userData, additionalData);
      }

      return expression;
    } catch (error) {
      console.error('❌ Error calculating value:', error);
      return expression;
    }
  }

  /**
   * Transform value based on transformation rule
   * @param {*} value - Value to transform
   * @param {*} rule - Transformation rule
   * @returns {*} - Transformed value
   */
  transformValue(value, rule) {
    // Simple transformation rules - can be extended
    if (typeof rule === 'object' && rule.map) {
      return rule.map[value] || value;
    }

    return rule;
  }

  /**
   * Apply conditional logic
   * @param {Object} payload - Current payload
   * @param {string} field - Target field
   * @param {Object} condition - Condition specification
   * @param {Object} userData - User data
   * @param {Object} additionalData - Additional data
   * @returns {Object} - Updated payload
   */
  applyCondition(payload, field, condition, userData, additionalData) {
    try {
      const { if: conditionCheck, then: thenValue, else: elseValue } = condition;
      
      // Simple condition evaluation - can be extended
      let conditionResult = false;
      
      if (conditionCheck && conditionCheck.field && conditionCheck.operator && conditionCheck.value) {
        const checkValue = payload[conditionCheck.field] || userData[conditionCheck.field] || additionalData[conditionCheck.field];
        
        switch (conditionCheck.operator) {
          case 'equals':
            conditionResult = checkValue === conditionCheck.value;
            break;
          case 'not_equals':
            conditionResult = checkValue !== conditionCheck.value;
            break;
          case 'contains':
            conditionResult = typeof checkValue === 'string' && checkValue.includes(conditionCheck.value);
            break;
          case 'exists':
            conditionResult = checkValue !== undefined && checkValue !== null;
            break;
        }
      }

      payload[field] = conditionResult ? thenValue : elseValue;
      
    } catch (error) {
      console.error('❌ Error applying condition:', error);
    }

    return payload;
  }

  /**
   * Generate UUID
   * @returns {string} - Generated UUID
   */
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * Validate payload structure
   * @param {Object} structure - Payload structure to validate
   * @returns {Object} - Validation result
   */
  validatePayloadStructure(structure) {
    try {
      // Basic validation
      if (!structure || typeof structure !== 'object') {
        return { valid: false, error: 'Payload structure must be an object' };
      }

      // Check for circular references
      JSON.stringify(structure);

      return { valid: true };
    } catch (error) {
      return { valid: false, error: error.message };
    }
  }

  /**
   * Build dynamic API endpoint URL by replacing placeholders
   * @param {string} endpoint - Endpoint template with placeholders
   * @param {Object} userData - User data for injection
   * @param {Object} additionalData - Additional data to include
   * @returns {string} - Built endpoint URL
   */
  buildApiEndpoint(endpoint, userData = {}, additionalData = {}) {
    try {
      if (!endpoint || typeof endpoint !== 'string') {
        return endpoint;
      }

      console.log(`🔧 Building API endpoint: ${endpoint}`);
      console.log(`📊 Available data for URL replacement:`, JSON.stringify(additionalData, null, 2));

      let processedEndpoint = endpoint;

      // Replace URL placeholders in angle brackets like <monthYear>
      processedEndpoint = processedEndpoint.replace(/<(\w+)>/g, (match, paramName) => {
        if (additionalData.hasOwnProperty(paramName)) {
          console.log(`🔄 URL parameter replacement: ${match} -> ${additionalData[paramName]}`);
          return additionalData[paramName];
        }
        
        // Try to find in userData
        if (userData.hasOwnProperty(paramName)) {
          console.log(`🔄 URL parameter replacement from user data: ${match} -> ${userData[paramName]}`);
          return userData[paramName];
        }
        
        // Keep original if no replacement found
        console.log(`⚠️ No replacement found for URL parameter: ${match}`);
        return match;
      });

      // Also support standard placeholder format {{data.fieldName}}
      processedEndpoint = this.replacePlaceholders(processedEndpoint, userData, additionalData);

      console.log(`✅ Final endpoint URL: ${processedEndpoint}`);
      return processedEndpoint;

    } catch (error) {
      console.error('❌ Error building API endpoint:', error);
      return endpoint; // Return original endpoint on error
    }
  }

  /**
   * Get available placeholders documentation
   * @returns {Object} - Available placeholders
   */
  getAvailablePlaceholders() {
    return {
      user: {
        description: 'User-specific data',
        examples: ['{{user.empId}}', '{{user.roleType}}', '{{user.token}}']
      },
      data: {
        description: 'Form data or additional API data',
        examples: ['{{data.fieldName}}', '{{data.value}}']
      },
      system: {
        description: 'System-generated values',
        examples: [
          '{{system.timestamp}}',
          '{{system.date}}',
          '{{system.datetime}}',
          '{{system.year}}',
          '{{system.month}}',
          '{{system.day}}',
          '{{system.uuid}}'
        ]
      },
      url: {
        description: 'URL parameter placeholders',
        examples: ['<monthYear>', '<userId>', '<categoryId>']
      }
    };
  }
}

module.exports = new PayloadBuilderService();