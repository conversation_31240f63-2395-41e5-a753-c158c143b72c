const UnifiedConfig = require('../models/UnifiedConfig');
const axios = require('axios');
const querystring = require('querystring');
const payloadBuilderService = require('./payloadBuilderService');

class UnifiedDynamicMatchingService {
  
  /**
   * Find matching forms and APIs for a given query using unified UnifiedConfig collection
   * @param {string} query - User query
   * @returns {Object} - Match results with forms, apis, and best match
   */
  async findMatches(query) {
    try {
      console.log('🔍 Finding matches for query:', query);
      
      // Get all active configurations
      const allConfigs = await UnifiedConfig.findActiveConfigs();
      
      console.log(`📊 Available configurations: ${allConfigs.length} total (${allConfigs.filter(c => c.type === 'form').length} forms, ${allConfigs.filter(c => c.type === 'api').length} APIs)`);
      
      // Calculate confidence scores for all configurations
      const matches = allConfigs.map(config => ({
        ...config.toObject(),
        confidence: this.calculateMatchConfidence(query, config)
      }));
      
      // Filter and sort by confidence (minimum threshold of 0.1 to avoid noise)
      const validMatches = matches
        .filter(match => match.confidence > 0.1)
        .sort((a, b) => {
          // Sort by confidence first, then by priority, then by creation date
          if (b.confidence !== a.confidence) return b.confidence - a.confidence;
          if (b.priority !== a.priority) return (b.priority || 0) - (a.priority || 0);
          return new Date(b.createdAt) - new Date(a.createdAt);
        });
      
      // Separate forms and APIs
      const formMatches = validMatches.filter(m => m.type === 'form');
      const apiMatches = validMatches.filter(m => m.type === 'api');
      
      console.log('🎯 Top matches:', validMatches.slice(0, 3).map(m => ({ 
        name: m.name, 
        type: m.type, 
        confidence: m.confidence.toFixed(3),
        priority: m.priority || 0
      })));
      
      // Only return matches that meet a minimum meaningful threshold
      const meaningfulMatches = validMatches.filter(match => match.confidence >= 0.2);
      
      return {
        forms: formMatches.filter(match => match.confidence >= 0.2),
        apis: apiMatches.filter(match => match.confidence >= 0.2),
        allMatches: meaningfulMatches,
        bestMatch: meaningfulMatches[0] || null,
        confidence: meaningfulMatches[0]?.confidence || 0,
        matchType: meaningfulMatches[0]?.type || null,
        totalConfigs: allConfigs.length
      };
      
    } catch (error) {
      console.error('❌ Error finding matches:', error);
      return {
        forms: [],
        apis: [],
        allMatches: [],
        bestMatch: null,
        confidence: 0,
        matchType: null,
        totalConfigs: 0
      };
    }
  }

  /**
   * Calculate match confidence between query and configuration
   * @param {string} query - User query
   * @param {Object} config - Configuration object (API or Form)
   * @returns {number} - Confidence score (0-1)
   */
  calculateMatchConfidence(query, config) {
    let score = 0;
    const queryLower = query.toLowerCase();
    const queryWords = queryLower.split(/\s+/);
    let matchDetails = [];

    // 1. Exact trigger phrase matches (highest priority)
    if (config.triggerPhrases && config.triggerPhrases.length > 0) {
      for (const phrase of config.triggerPhrases) {
        if (queryLower.includes(phrase.toLowerCase())) {
          score += 0.8; // High score for trigger phrases
          matchDetails.push(`trigger:"${phrase}"`);
        }
      }
    }

    // 2. Keyword matches (more strict matching)
    if (config.keywords && config.keywords.length > 0) {
      const keywordMatches = config.keywords.filter(keyword => {
        const keywordLower = keyword.toLowerCase();
        // Be more strict about matching to avoid false positives
        return queryWords.some(word => {
          // Exact word match
          if (word === keywordLower) return true;
          // Exact phrase match in query
          if (queryLower.includes(keywordLower) && keywordLower.length > 3) return true;
          // Partial match only for longer keywords and if they're meaningful
          if (keywordLower.length > 5 && word.length > 3 && 
              (word.includes(keywordLower) || keywordLower.includes(word))) return true;
          return false;
        });
      });
      if (keywordMatches.length > 0) {
        const keywordScore = (keywordMatches.length / config.keywords.length) * 0.6;
        score += keywordScore;
        matchDetails.push(`keywords:[${keywordMatches.join(', ')}]`);
      }
    }

    // 3. Name similarity
    if (config.name) {
      const nameSimilarity = this.calculateStringSimilarity(queryLower, config.name.toLowerCase());
      if (nameSimilarity > 0.1) {
        const nameScore = nameSimilarity * 0.5;
        score += nameScore;
        matchDetails.push(`name:${nameSimilarity.toFixed(2)}`);
      }
    }

    // 4. Prompt/description content matching (more conservative)
    if (config.prompt) {
      const promptWords = config.prompt.toLowerCase().split(/\s+/);
      const matchingWords = queryWords.filter(word => 
        word.length > 2 && promptWords.some(pWord => pWord.includes(word) || word.includes(pWord))
      );
      if (matchingWords.length > 0) {
        const promptScore = (matchingWords.length / queryWords.length) * 0.3; // Reduced from 0.4
        score += promptScore;
        matchDetails.push(`prompt:[${matchingWords.join(', ')}]`);
      }
    }

    // 5. Type-specific scoring adjustments
    if (config.type === 'api') {
      // Boost API matches for action-oriented queries
      const actionWords = ['check', 'get', 'fetch', 'show', 'display', 'retrieve'];
      const actionMatch = queryWords.filter(word => actionWords.includes(word));
      if (actionMatch.length > 0) {
        score += 0.1;
        matchDetails.push(`api-action:[${actionMatch.join(', ')}]`);
      }
    } else if (config.type === 'form') {
      // Boost form matches for application-oriented queries
      const formWords = ['apply', 'submit', 'request', 'create', 'add', 'register'];
      const formMatch = queryWords.filter(word => formWords.includes(word));
      if (formMatch.length > 0) {
        score += 0.1;
        matchDetails.push(`form-action:[${formMatch.join(', ')}]`);
      }
    }

    // 6. Priority boost
    if (config.priority && config.priority > 0) {
      score += (config.priority / 10) * 0.1; // Small boost based on priority
      matchDetails.push(`priority:${config.priority}`);
    }

    // Log matching details for debugging (only if there's some score)
    if (score > 0.1) {
      console.log(`🔍 Match "${config.name}" (${config.type}): ${score.toFixed(3)} - ${matchDetails.join(', ')}`);
    }

    // Normalize score to be between 0 and 1
    return Math.min(score, 1);
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  calculateStringSimilarity(str1, str2) {
    const matrix = [];
    const len1 = str1.length;
    const len2 = str2.length;

    if (len1 === 0) return len2 === 0 ? 1 : 0;
    if (len2 === 0) return 0;

    // Initialize matrix
    for (let i = 0; i <= len1; i++) {
      matrix[i] = [i];
    }
    for (let j = 0; j <= len2; j++) {
      matrix[0][j] = j;
    }

    // Fill matrix
    for (let i = 1; i <= len1; i++) {
      for (let j = 1; j <= len2; j++) {
        const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[i][j] = Math.min(
          matrix[i - 1][j] + 1,     // deletion
          matrix[i][j - 1] + 1,     // insertion
          matrix[i - 1][j - 1] + cost // substitution
        );
      }
    }

    const maxLen = Math.max(len1, len2);
    const distance = matrix[len1][len2];
    return 1 - (distance / maxLen);
  }

  /**
   * Execute dynamic API configuration
   * @param {Object} apiConfig - API configuration from UnifiedConfig
   * @param {Object} userData - User data for injection
   * @param {Object} additionalData - Additional data to send
   * @returns {Object} - API execution result
   */
  async executeDynamicApi(apiConfig, userData = {}, additionalData = {}, formConfig = null) {
    try {
      console.log(`🚀 Executing dynamic API: ${apiConfig.name}`);
      console.log(`📡 Endpoint: ${apiConfig.apiConfig.method} ${apiConfig.apiConfig.endpoint}`);

      // Prepare endpoint URL with data injection
      let endpoint = apiConfig.apiConfig.endpoint;
      
      // Inject user data into URL parameters
      if (apiConfig.apiConfig.dataInjection?.injectUserData) {
        if (apiConfig.apiConfig.dataInjection.autoFields?.empId && userData.empId) {
          endpoint = endpoint.replace('{empId}', userData.empId);
        }
        
        // Add current year if {year} placeholder exists
        if (endpoint.includes('{year}')) {
          endpoint = endpoint.replace('{year}', new Date().getFullYear());
        }
        
        // Add current month-year if needed
        if (endpoint.includes('{monthYear}')) {
          const now = new Date();
          const monthYear = `${String(now.getMonth() + 1).padStart(2, '0')}-${now.getFullYear()}`;
          endpoint = endpoint.replace('{monthYear}', monthYear);
        }
      }

      // Prepare headers
      const headers = {
        'Content-Type': 'application/json'
      };
      
      // Add additional headers if they exist
      if (apiConfig.apiConfig.headers) {
        if (apiConfig.apiConfig.headers instanceof Map) {
          // If it's a Map, convert to object
          Object.assign(headers, Object.fromEntries(apiConfig.apiConfig.headers));
        } else if (Array.isArray(apiConfig.apiConfig.headers)) {
          // If it's an array of key-value pairs, convert to object
          Object.assign(headers, Object.fromEntries(apiConfig.apiConfig.headers));
        } else if (typeof apiConfig.apiConfig.headers === 'object') {
          // If it's already a plain object, merge it directly
          Object.assign(headers, apiConfig.apiConfig.headers);
        }
      }

      // Inject authentication
      // If this API call is triggered by a form, use the form's auth config first
      if (formConfig && formConfig.formConfig?.submitApiConfig?.authType !== 'none') {
        const formAuthConfig = formConfig.formConfig.submitApiConfig;
        
        if (formAuthConfig.authType === 'bearer') {
          const useCustomToken = formAuthConfig.authConfig?.useCustomToken;
          const customToken = formAuthConfig.authConfig?.token;
          const userToken = userData.token;
          
          let tokenToUse = null;
          
          if (useCustomToken && customToken) {
            tokenToUse = customToken;
            console.log('🔐 Using custom token from form config for API call');
          } else if (userToken) {
            tokenToUse = userToken;
            console.log('🔐 Using user login token for API call');
          }
          
          if (tokenToUse) {
            headers['Authorization'] = `Bearer ${tokenToUse}`;
          }
        } else if (formAuthConfig.authType === 'apikey') {
          const useCustomToken = formAuthConfig.authConfig?.useCustomToken;
          
          if (useCustomToken) {
            const apiKey = formAuthConfig.authConfig?.apiKey;
            const headerName = formAuthConfig.authConfig?.headerName || 'X-API-Key';
            if (apiKey) {
              headers[headerName] = apiKey;
              console.log(`🔐 Using custom API key for API call: ${headerName}`);
            }
          } else if (userData.token) {
            headers['Authorization'] = `Bearer ${userData.token}`;
            console.log('🔐 Using user login token for API call');
          }
        }
      } else if (apiConfig.apiConfig.dataInjection?.autoFields?.token && userData.token) {
        // Fall back to standard API config authentication
        headers['Authorization'] = `Bearer ${userData.token}`;
        console.log('🔐 Using user token for standalone API call');
      }

      // Prepare request body using payload builder
       let requestBody = payloadBuilderService.buildApiPayload(apiConfig.apiConfig, userData, additionalData);

      // Make the API request
      const startTime = Date.now();
      const response = await axios({
        method: apiConfig.apiConfig.method,
        url: endpoint,
        headers: headers,
        data: apiConfig.apiConfig.method !== 'GET' ? requestBody : undefined,
        params: apiConfig.apiConfig.method === 'GET' ? requestBody : undefined,
        timeout: apiConfig.apiConfig.timeout || 30000
      });

      const responseTime = Date.now() - startTime;

      console.log(`✅ API call successful: ${response.status} ${response.statusText}`);
      console.log(`⏱️ Response time: ${responseTime}ms`);

      return {
        success: true,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        responseTime: responseTime,
        headers: response.headers
      };

    } catch (error) {
      console.error(`❌ API call failed for ${apiConfig.name}:`, error.message);
      
      const errorDetails = {
        success: false,
        error: error.message,
        status: error.response?.status || 0,
        statusText: error.response?.statusText || 'Unknown Error',
        data: error.response?.data || null
      };

      // Update API configuration with test result
      try {
        await UnifiedConfig.findByIdAndUpdate(apiConfig._id, {
          'apiConfig.lastTestedAt': new Date(),
          'apiConfig.lastTestResult': {
            success: false,
            statusCode: errorDetails.status,
            message: error.message,
            error: error.message
          }
        });
      } catch (updateError) {
        console.error('Failed to update test result:', updateError.message);
      }

      return errorDetails;
    }
  }

  /**
   * Format API response using template
   * @param {Object} apiConfig - API configuration
   * @param {Object} apiResult - API execution result
   * @returns {string} - Formatted response
   */
  formatApiResponse(apiConfig, apiResult) {
    try {
      if (!apiResult.success) {
        return `❌ **API Error**\n\nFailed to execute ${apiConfig.name}:\n${apiResult.error}`;
      }

      if (apiConfig.apiConfig.responseTemplate) {
        return this.processResponseTemplate(apiConfig.apiConfig.responseTemplate, apiResult);
      } else {
        // Default formatting
        return `✅ **${apiConfig.name} - Success**\n\nStatus: ${apiResult.status} ${apiResult.statusText}\n\nData: ${JSON.stringify(apiResult.data, null, 2)}`;
      }
    } catch (error) {
      console.error('Error formatting API response:', error);
      return `✅ **${apiConfig.name} - Success**\n\nRaw response: ${JSON.stringify(apiResult.data)}`;
    }
  }

  /**
   * Flatten nested object for template replacement
   */
  flattenObject(obj, prefix = '') {
    const flattened = {};
    
    // Check if obj is null or undefined
    if (obj === null || obj === undefined) {
      return flattened;
    }
    
    // Handle arrays
    if (Array.isArray(obj)) {
      // Store the full array as JSON string for direct access
      if (prefix) {
        flattened[prefix] = JSON.stringify(obj, null, 2);
      }
      
      // Also flatten individual array elements
      obj.forEach((item, index) => {
        const newKey = prefix ? `${prefix}[${index}]` : `[${index}]`;
        if (item !== null && typeof item === 'object') {
          Object.assign(flattened, this.flattenObject(item, newKey));
        } else {
          flattened[newKey] = item;
        }
      });
      
      // Also add first element properties without index for common access patterns
      if (obj.length > 0) {
        const firstItem = obj[0];
        if (firstItem !== null && typeof firstItem === 'object') {
          Object.assign(flattened, this.flattenObject(firstItem, prefix));
        } else if (prefix) {
          flattened[prefix] = firstItem;
        }
      }
      
      return flattened;
    }
    
    // Handle non-objects
    if (typeof obj !== 'object') {
      if (prefix) {
        flattened[prefix] = obj;
      }
      return flattened;
    }
    
    Object.keys(obj).forEach(key => {
      const value = obj[key];
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (value !== null && typeof value === 'object' && Array.isArray(value)) {
        // Handle arrays - store full array and flatten elements
        flattened[newKey] = JSON.stringify(value, null, 2);
        Object.assign(flattened, this.flattenObject(value, newKey));
      } else if (value !== null && typeof value === 'object') {
        // Handle nested objects - store the full object and then flatten its properties
        flattened[newKey] = JSON.stringify(value, null, 2);
        Object.assign(flattened, this.flattenObject(value, newKey));
      } else {
        flattened[newKey] = value;
      }
    });
    
    return flattened;
  }

  /**
   * Format form API response using template (for GET requests)
   */
  formatFormApiResponse(submitConfig, apiResult) {
    try {
      if (!apiResult.success) {
        return `❌ **Form API Error**\n\nFailed to retrieve data:\n${apiResult.error}`;
      }

      if (submitConfig.responseTemplate) {
        return this.processResponseTemplate(submitConfig.responseTemplate, apiResult);
      } else {
        // Default formatting for GET requests
        return `✅ **Data Retrieved Successfully**\n\nStatus: ${apiResult.status} ${apiResult.statusText}\n\nData: ${JSON.stringify(apiResult.data, null, 2)}`;
      }
    } catch (error) {
      console.error('Error formatting form API response:', error);
      return `✅ **Data Retrieved Successfully**\n\nRaw response: ${JSON.stringify(apiResult.data)}`;
    }
  }

  /**
   * Process response template with advanced formatting options
   */
  processResponseTemplate(template, apiResult) {
    try {
      let formatted = template;
      
      console.log('🎨 Processing response template:', template);
      console.log('🔍 API Result data:', JSON.stringify(apiResult.data, null, 2));
      
      // Replace basic placeholders
      formatted = formatted.replace(/\{status\}/g, apiResult.status);
      formatted = formatted.replace(/\{statusText\}/g, apiResult.statusText);
      
      // Replace data placeholders
      if (apiResult.data && typeof apiResult.data === 'object') {
        const flatData = this.flattenObject(apiResult.data);
        
        // First, handle specific field filtering patterns like {data.data.empId} or {data.arrayName.fieldName}
        const fieldFilterMatches = template.match(/\{data\.([^}]+)\}/g);
        const processedMatches = new Set(); // Track which matches we've processed
        
        if (fieldFilterMatches) {
          fieldFilterMatches.forEach(match => {
            const fullPath = match.match(/\{data\.([^}]+)\}/)[1];
            
            console.log(`🎯 Processing template: ${match} (fullPath: ${fullPath})`);
            
            // First, try to get the value directly at the full path
            const directValue = this.getNestedValue(apiResult.data, fullPath);
            console.log(`🔍 Direct value type at ${fullPath}:`, Array.isArray(directValue) ? 'array' : typeof directValue);
            
            // If the direct path points to an array, format it directly
            if (Array.isArray(directValue)) {
              console.log(`✅ Found array directly at path: ${fullPath}, formatting array with ${directValue.length} items`);
              const arrayDisplay = this.formatArrayForDisplay(directValue);
              const regex = new RegExp(match.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
              formatted = formatted.replace(regex, arrayDisplay);
              processedMatches.add(match); // Mark as processed
              return; // Exit early, we've handled this match
            }
            
            // Check if this contains comma-separated fields (multiple field selection)
            if (fullPath.includes(',')) {
              const pathParts = fullPath.split('.');
              if (pathParts.length >= 2) {
                const arrayPath = pathParts.slice(0, -1).join('.');
                const fieldsString = pathParts[pathParts.length - 1];
                const fieldNames = fieldsString.split(',').map(f => f.trim());
                
                console.log(`🎯 Processing multi-field template: ${match}`);
                console.log(`🔍 Array path: ${arrayPath}, Field names: ${fieldNames.join(', ')}`);
                
                // Navigate to the data array using the path
                let dataArray = this.getNestedValue(apiResult.data, arrayPath);
                
                if (Array.isArray(dataArray)) {
                  const filteredDisplay = this.formatArrayForDisplayWithMultipleFields(dataArray, fieldNames);
                  const regex = new RegExp(match.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
                  formatted = formatted.replace(regex, filteredDisplay);
                  processedMatches.add(match); // Mark as processed
                } else {
                  console.log(`⚠️ Could not find array at path: ${arrayPath}`);
                }
              }
            } else {
              // Single field filtering - only if direct value is not an array or primitive
              const pathParts = fullPath.split('.');
              
              // Check if this is a field-specific pattern (has more than one part after 'data')
              if (pathParts.length >= 2) {
                const arrayPath = pathParts.slice(0, -1).join('.');
                const fieldName = pathParts[pathParts.length - 1];
                
                console.log(`🎯 Processing single-field template: ${match}`);
                console.log(`🔍 Array path: ${arrayPath}, Field name: ${fieldName}`);
                
                // Navigate to the data array using the path
                let dataArray = this.getNestedValue(apiResult.data, arrayPath);
                
                if (Array.isArray(dataArray)) {
                  const filteredDisplay = this.formatArrayForDisplayWithFieldFilter(dataArray, fieldName);
                  const regex = new RegExp(match.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
                  formatted = formatted.replace(regex, filteredDisplay);
                  processedMatches.add(match); // Mark as processed
                } else {
                  console.log(`⚠️ Could not find array at path: ${arrayPath}`);
                }
              }
            }
          });
        }
        
        // Sort keys by length (descending) to replace longer keys first
        const sortedKeys = Object.keys(flatData).sort((a, b) => b.length - a.length);
        
        sortedKeys.forEach(key => {
          const placeholder = `{data.${key}}`;
          
          // Skip if this placeholder was already handled by field filtering
          if (processedMatches.has(placeholder)) {
            console.log(`⏭️ Skipping already processed match: ${placeholder}`);
            return;
          }
          
          const value = flatData[key];
          
          // Handle different value types
          let replacementValue;
          if (value === null || value === undefined) {
            replacementValue = 'N/A';
          } else if (typeof value === 'string') {
            // Check if it's already a JSON string (from object/array flattening)
            try {
              const parsedValue = JSON.parse(value);
              if (Array.isArray(parsedValue)) {
                // Format array nicely
                replacementValue = this.formatArrayForDisplay(parsedValue);
              } else if (typeof parsedValue === 'object' && parsedValue !== null) {
                // Format object nicely
                replacementValue = this.formatObjectForDisplay(parsedValue);
              } else {
                replacementValue = value;
              }
            } catch (e) {
              replacementValue = value;
            }
          } else if (typeof value === 'object') {
            if (Array.isArray(value)) {
              replacementValue = this.formatArrayForDisplay(value);
            } else {
              replacementValue = this.formatObjectForDisplay(value);
            }
          } else {
            replacementValue = String(value);
          }
          
          const regex = new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g');
          formatted = formatted.replace(regex, replacementValue);
          
        });
      }
      
      console.log('✅ Final formatted response:', formatted);
      return formatted;
    } catch (error) {
      console.error('Error processing response template:', error);
      return `Template processing error: ${error.message}`;
    }
  }

  /**
   * Format array data for better display
   */
  formatArrayForDisplay(arr) {
    if (!Array.isArray(arr) || arr.length === 0) {
      return 'No data available';
    }

    if (arr.length === 1 && typeof arr[0] === 'object') {
      // Single object - format as key-value pairs
      const obj = arr[0];
      
      // Check if this is leave data by looking for leaveTypeName and balance fields
      const isLeaveData = obj.hasOwnProperty('leaveTypeName') && obj.hasOwnProperty('balance');
      
      let lines;
      if (isLeaveData) {
        // For leave data, only show leaveTypeName and balance
        lines = [
          `leaveTypeName: ${obj.leaveTypeName}`,
          `balance: ${obj.balance}`
        ];
      } else {
        // For other data, show all fields as before
        lines = Object.keys(obj).map(key => `${key}: ${obj[key]}`);
      }
      
      return lines.join('\n');
    } else if (arr.length > 1 && typeof arr[0] === 'object') {
      // Multiple objects - format as a table-like structure
      const lines = arr.map((item, index) => {
        // Check if this is leave data by looking for leaveTypeName and balance fields
        const isLeaveData = item.hasOwnProperty('leaveTypeName') && item.hasOwnProperty('balance');
        
        let itemLines;
        if (isLeaveData) {
          // For leave data, only show leaveTypeName and balance
          itemLines = [
            `  leaveTypeName: ${item.leaveTypeName}`,
            `  balance: ${item.balance}`
          ];
        } else {
          // For other data, show all fields as before
          itemLines = Object.keys(item).map(key => `  ${key}: ${item[key]}`);
        }
        
        return `Record ${index + 1}:\n${itemLines.join('\n')}`;
      });
      return lines.join('\n\n');
    } else {
      // Simple array - join with commas
      return arr.join(', ');
    }
  }

  /**
   * Get nested value from object using dot notation path
   */
  getNestedValue(obj, path) {
    if (!obj || !path) return undefined;
    
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current === null || current === undefined || typeof current !== 'object') {
        return undefined;
      }
      current = current[key];
    }
    
    return current;
  }

  /**
   * Format array data for display with specific field filtering
   * This method displays only the specified field from each record
   */
  formatArrayForDisplayWithFieldFilter(arr, fieldName) {
    if (!Array.isArray(arr) || arr.length === 0) {
      return 'No data available';
    }

    console.log(`🎯 Formatting array with field filter for: ${fieldName}`);
    console.log(`🔍 Array length: ${arr.length}`);
    console.log(`🔍 Sample record:`, arr[0]);

    if (arr.length === 1 && typeof arr[0] === 'object') {
      // Single object - show only the specified field
      const obj = arr[0];
      const fieldValue = this.getNestedValue(obj, fieldName);
      
      if (fieldValue !== undefined && fieldValue !== null) {
        return `${fieldName}: ${fieldValue}`;
      } else {
        return `${fieldName}: N/A`;
      }
    } else if (arr.length > 1 && typeof arr[0] === 'object') {
      // Multiple objects - show only the specified field from each record
      const lines = arr.map((item, index) => {
        const fieldValue = this.getNestedValue(item, fieldName);
        const displayValue = (fieldValue !== undefined && fieldValue !== null) ? fieldValue : 'N/A';
        return `Record ${index + 1}:\n  ${fieldName}: ${displayValue}`;
      });
      return lines.join('\n\n');
    } else {
      // Simple array - not applicable for field filtering
      return arr.join(', ');
    }
  }

  /**
   * Format array data for display with multiple specific fields filtering
   * This method displays only the specified fields from each record
   */
  formatArrayForDisplayWithMultipleFields(arr, fieldNames) {
    if (!Array.isArray(arr) || arr.length === 0) {
      return 'No data available';
    }

    console.log(`🎯 Formatting array with multiple field filters for: ${fieldNames.join(', ')}`);
    console.log(`🔍 Array length: ${arr.length}`);
    console.log(`🔍 Sample record:`, arr[0]);

    if (arr.length === 1 && typeof arr[0] === 'object') {
      // Single object - show only the specified fields
      const obj = arr[0];
      const lines = fieldNames.map(fieldName => {
        const fieldValue = this.getNestedValue(obj, fieldName);
        const displayValue = (fieldValue !== undefined && fieldValue !== null) ? fieldValue : 'N/A';
        return `${fieldName}: ${displayValue}`;
      });
      return lines.join('\n');
    } else if (arr.length > 1 && typeof arr[0] === 'object') {
      // Multiple objects - show only the specified fields from each record
      const lines = arr.map((item, index) => {
        const itemLines = fieldNames.map(fieldName => {
          const fieldValue = this.getNestedValue(item, fieldName);
          const displayValue = (fieldValue !== undefined && fieldValue !== null) ? fieldValue : 'N/A';
          return `  ${fieldName}: ${displayValue}`;
        });
        return `Record ${index + 1}:\n${itemLines.join('\n')}`;
      });
      return lines.join('\n\n');
    } else {
      // Simple array - not applicable for field filtering
      return arr.join(', ');
    }
  }

  /**
   * Format object data for better display
   */
  formatObjectForDisplay(obj) {
    if (!obj || typeof obj !== 'object') {
      return String(obj);
    }

    const lines = Object.keys(obj).map(key => {
      const value = obj[key];
      if (Array.isArray(value)) {
        return `${key}: [\n${this.formatArrayForDisplay(value).split('\n').map(line => '  ' + line).join('\n')}\n]`;
      } else if (typeof value === 'object' && value !== null) {
        return `${key}: ${JSON.stringify(value, null, 2)}`;
      } else {
        return `${key}: ${value}`;
      }
    });
    
    return lines.join('\n');
  }

  /**
   * Submit form data using configuration
   * @param {Object} formConfig - Form configuration from UnifiedConfig
   * @param {Object} formData - Form submission data
   * @param {Object} userData - User data for injection
   * @param {string} formId - Form ID (optional)
   * @param {string} conversationId - Conversation ID (optional)
   * @returns {Object} - Submission result
   */
  async submitDynamicForm(formConfig, formData, userData = {}, formId = null, conversationId = null) {
    try {
       
      if (!formConfig.formConfig.submitApiConfig || !formConfig.formConfig.submitApiConfig.endpoint) {
        throw new Error('Form submission API configuration not found');
      }

      const submitConfig = formConfig.formConfig.submitApiConfig;      
      // Check access control if enabled
      if (submitConfig.accessControl?.enabled) {
      
        const allowedRoles = submitConfig.accessControl.allowedRoles || [];
        const userRole = userData.roleType;
        
        if (
          !allowedRoles.map(role => role.toLowerCase()).includes(userRole.toLowerCase())
        ) {
          return {
            success: false,
            error: `Access denied for role '${userRole}'.`,
            status: 403,
            statusText: 'forbidden'
          };
        }
        
        console.log(`✅ Access granted: User role '${userRole}' is authorized`);
      }
      
      // Prepare endpoint URL with path parameter replacement using PayloadBuilderService
      let endpoint = submitConfig.endpoint;
      console.log('🔍 Original endpoint:', endpoint);
      
      // Use PayloadBuilderService to build the endpoint with placeholder replacement
      endpoint = payloadBuilderService.buildApiEndpoint(endpoint, userData, formData);
      console.log('🔍 Processed endpoint:', endpoint);
      
      // Prepare headers
      console.log('🔍 Preparing headers...');
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (submitConfig.headers) {
        if (submitConfig.headers instanceof Map) {
          // Convert Map to object
          console.log('🔍 Converting Map headers to object');
          submitConfig.headers.forEach((value, key) => {
            headers[key] = value;
          });
        } else if (typeof submitConfig.headers === 'object') {
          // Handle plain object
          console.log('🔍 Merging object headers');
          Object.keys(submitConfig.headers).forEach(key => {
            headers[key] = submitConfig.headers[key];
          });
        }
      }
      
      console.log('🔍 Final headers:', headers);

      // Inject authentication with toggle support
      if (submitConfig.authType === 'bearer') {
        // Check if custom token is enabled and available
        const useCustomToken = submitConfig.authConfig?.useCustomToken;
        const customToken = submitConfig.authConfig?.token;
        const userToken = userData.token;
        
        let tokenToUse = null;
        
        if (useCustomToken && customToken) {
          tokenToUse = customToken;
          console.log('🔐 Using custom token from form configuration');
        } else if (userToken) {
          tokenToUse = userToken;
          console.log('🔐 Using user login token');
        }
        
        if (!tokenToUse) {
          console.warn('⚠️ Bearer authentication required but no token available');
          throw new Error('Authentication token is required for this form');
        }
        
        headers['Authorization'] = `Bearer ${tokenToUse}`;
      } else if (submitConfig.authType === 'apikey') {
        const useCustomToken = submitConfig.authConfig?.useCustomToken;
        
        if (useCustomToken) {
          const apiKey = submitConfig.authConfig?.apiKey;
          const headerName = submitConfig.authConfig?.headerName || 'X-API-Key';
          
          if (!apiKey) {
            console.warn('⚠️ API Key authentication required but no API key provided');
            throw new Error('API Key is required for this form');
          }
          
          headers[headerName] = apiKey;
          console.log(`🔐 Using custom API key: ${headerName}`);
        } else if (userData.token) {
          headers['Authorization'] = `Bearer ${userData.token}`;
          console.log('🔐 Using user login token as Bearer token');
        }
      } else if (submitConfig.authType === 'basic') {
        const useCustomToken = submitConfig.authConfig?.useCustomToken;
        
        if (useCustomToken) {
          const username = submitConfig.authConfig?.username;
          const password = submitConfig.authConfig?.password;
          
          if (!username || !password) {
            console.warn('⚠️ Basic authentication required but username/password not provided');
            throw new Error('Username and password are required for this form');
          }
          
          const credentials = Buffer.from(`${username}:${password}`).toString('base64');
          headers['Authorization'] = `Basic ${credentials}`;
          console.log('🔐 Using custom basic authentication');
        } else if (userData.token) {
          headers['Authorization'] = `Bearer ${userData.token}`;
          console.log('🔐 Using user login token as Bearer token');
        }
      }

       // Prepare request body with data mapping
      let requestBody = { ...formData };
      
      // Apply data mapping if configured
      if (submitConfig.dataMapping && submitConfig.dataMapping.size > 0) {
        const mappedData = {};
        submitConfig.dataMapping.forEach((apiField, formField) => {
          if (formData[formField] !== undefined) {
            mappedData[apiField] = formData[formField];
          }
        });
        requestBody = { ...requestBody, ...mappedData };
      }

      // Make the API request - handle GET requests differently
      let response;
      const method = submitConfig.method || 'POST';
      
      if (method === 'GET') {
        // For GET requests, don't send body data but append form data as query parameters if needed
        const config = {
          method: 'GET',
          url: endpoint,
          headers: headers,
          timeout: 30000
        };
        
        // For GET requests, append form data as query parameters only if form data exists
        // and the field wasn't already used in the URL path
        if (formData && typeof formData === 'object' && !Array.isArray(formData)) {
          const formDataKeys = Object.keys(formData);
          if (formDataKeys.length > 0) {
            const urlObj = new URL(endpoint);
            const originalEndpoint = submitConfig.endpoint;
            
            formDataKeys.forEach(key => {
              // Only add as query parameter if it wasn't used in URL path replacement
              const wasUsedInPath = originalEndpoint.includes(`{${key}}`);
              if (!wasUsedInPath && formData[key] !== undefined && formData[key] !== null && formData[key] !== '') {
                urlObj.searchParams.append(key, formData[key]);
              }
            });
            config.url = urlObj.toString();
            console.log('🔍 GET request URL:', config.url);
          } else {
            console.log('🔍 GET request without form data (data retrieval only)');
          }
        } else {
          console.log('🔍 GET request without form data (data retrieval only)');
        }
        
        try {
          response = await axios(config);
        } catch (axiosError) {
          console.error('❌ GET request failed:', axiosError.message);
          throw new Error(`GET request failed: ${axiosError.message}`);
        }
      } else {
        // For POST/PUT/PATCH requests, send data in body
        try {
          response = await axios({
            method: method,
            url: endpoint,
            headers: headers,
            data: requestBody,
            timeout: 30000
          });
        } catch (axiosError) {
          console.error(`❌ ${method} request failed:`, axiosError.message);
          throw new Error(`${method} request failed: ${axiosError.message}`);
        }
      }

      // Format response message
      let responseMessage;
      const hasFormLinking = formConfig && formConfig.formConfig && formConfig.formConfig.formLinking && formConfig.formConfig.formLinking.enabled && formConfig.formConfig.formLinking.recordActions && formConfig.formConfig.formLinking.recordActions.length > 0;
       
      
      if (method === 'GET' && submitConfig.responseTemplate) {
       
        try {
          responseMessage = this.formatFormApiResponse(submitConfig, {
            success: true,
            status: response.status,
            statusText: response.statusText,
            data: response.data
          });
          console.log('✅ Template formatting successful:', responseMessage);
        } catch (templateError) {
          console.error('❌ Template formatting failed:', templateError.message);
          responseMessage = `✅ **Data Retrieved Successfully**\n\nStatus: ${response.status} ${response.statusText}\n\nData: ${JSON.stringify(response.data, null, 2)}`;
        }
      } else if (hasFormLinking) {
        // For form linking without response template, use simple message
        console.log('🔗 Form linking enabled, preserving structured data');
        responseMessage = 'Data retrieved successfully. Use the Apply buttons below to perform actions.';
      } else {
        // Use regular success message for POST/PUT/PATCH
        responseMessage = submitConfig.successMessage || 'Form submitted successfully!';
      }

      return {
        success: true,
        status: response.status,
        statusText: response.statusText,
        data: response.data,
        message: responseMessage,
        isGetRequest: method === 'GET',
        formattedResponse: method === 'GET' ? responseMessage : null
      };

    } catch (error) {
      console.error(`❌ Form submission failed for ${formConfig.name}:`, error.message);
      
      // Handle specific authentication errors
      let errorMessage = formConfig.formConfig.submitApiConfig?.errorMessage || 'Failed to submit form. Please try again.';
      
      if (error.response?.status === 401) {
        errorMessage = 'Authentication failed. Please check your login credentials or contact support.';
        console.error('🔐 Authentication error details:', {
          status: error.response.status,
          statusText: error.response.statusText,
          data: error.response.data,
          headers: error.response.config?.headers?.Authorization ? 'Authorization header present' : 'No Authorization header'
        });
      }
      
      return {
        success: false,
        error: error.message,
        status: error.response?.status || 0,
        statusText: error.response?.statusText || 'Unknown Error',
        data: error.response?.data || null,
        message: errorMessage
      };
    }
  }
}

module.exports = new UnifiedDynamicMatchingService();
