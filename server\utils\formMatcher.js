/**
 * Utility functions for matching user queries to forms and detecting intent
 */

/**
 * Calculates the similarity score between a query and a form
 * @param {string} query - The user's query
 * @param {object} form - The form object to match against
 * @returns {number} A similarity score (0-1)
 */
const calculateSimilarity = (query, form) => {
  if (!query || !form) return 0;
  
  // Convert query to lowercase for case-insensitive matching
  const normalizedQuery = query.toLowerCase();
  
  // Create a set of keywords from the form
  const formKeywords = new Set([
    ...(form.name ? form.name.toLowerCase().split(/\s+/) : []),
    ...(form.description ? form.description.toLowerCase().split(/\s+/) : []),
    // Add field names and labels
    ...(form.fields ? form.fields.flatMap(field => [
      field.name.toLowerCase(),
      ...field.label.toLowerCase().split(/\s+/)
    ]) : [])
  ]);
  
  // Filter out common words that don't add much meaning
  const filteredFormKeywords = Array.from(formKeywords).filter(
    word => word.length > 2 && !['the', 'and', 'for', 'with', 'this', 'that', 'from', 'your', 'form'].includes(word)
  );
  
  // Count how many words from the query appear in the form keywords
  const queryWords = normalizedQuery.split(/\s+/).filter(
    word => word.length > 2 && !['the', 'and', 'for', 'with', 'this', 'that', 'from', 'your', 'form'].includes(word)
  );
  
  if (queryWords.length === 0) return 0;
  
  // Give higher weight to exact matches of significant words
  let score = 0;
  let exactMatches = 0;
  let partialMatches = 0;
  
  // Check for special keywords related to common form types
  const formTypeKeywords = {
    'leave': ['leave', 'vacation', 'time off', 'day off', 'absence', 'apply leave', 'leave apply', 'leave application', 'take leave', 'request leave', 'leave request'],
    'balance': ['balance', 'remaining', 'available'],
    'request': ['request', 'apply', 'application'],
    'feedback': ['feedback', 'review', 'opinion', 'survey'],
    'contact': ['contact', 'reach out', 'message', 'inquiry'],
    'registration': ['register', 'sign up', 'join', 'enroll']
  };
  
  // Check if the form name contains any of these form types
  const formNameLower = form.name.toLowerCase();
  for (const [formType, keywords] of Object.entries(formTypeKeywords)) {
    if (formNameLower.includes(formType)) {
      // Check if any of the related keywords are in the query
      const keywordMatch = keywords.some(keyword => normalizedQuery.includes(keyword));
      if (keywordMatch) {
        // Significant boost for matching form type keywords
        score += 0.5;
      }
    }
  }
  
  // Check for exact and partial matches
  for (const word of queryWords) {
    if (filteredFormKeywords.includes(word)) {
      exactMatches++;
    } else if (filteredFormKeywords.some(keyword => keyword.includes(word) || word.includes(keyword))) {
      partialMatches++;
    }
  }
  
  // Calculate weighted score
  score += (exactMatches * 1.0 + partialMatches * 0.5) / queryWords.length;
  
  // Special case for "leave balance" form
  if (formNameLower.includes('leave') && normalizedQuery.includes('leave')) {
    score += 0.3;
  }
  
  // Special case for "balance" in the query
  if (formNameLower.includes('balance') && normalizedQuery.includes('balance')) {
    score += 0.3;
  }
  
  // Cap the score at 1.0
  return Math.min(score, 1.0);
};

/**
 * Detects whether a query is asking for information or requesting a form
 * @param {string} query - The user's query
 * @returns {string} The detected intent: 'form' or 'information'
 */
const detectQueryIntent = (query) => {
  if (!query) return 'information';
  
  const normalizedQuery = query.toLowerCase();
  
  // Check for direct form-related terms first
  const formTerms = ['form', 'application', 'template', 'balance', 'request', 'submission', 'leave', 'apply'];
  const formVerbs = ['show', 'display', 'view', 'see', 'check', 'get', 'access', 'open', 'create', 'submit', 'fill', 'complete', 'apply', 'take', 'request'];
  
  // If the query contains both a form term and a form verb, it's likely a form request
  const hasFormTerm = formTerms.some(term => normalizedQuery.includes(term));
  const hasFormVerb = formVerbs.some(verb => normalizedQuery.includes(verb));
  
  if (hasFormTerm && hasFormVerb) {
    return 'form';
  }
  
  // Check for possessive phrases that often indicate form requests
  if (normalizedQuery.includes('my ') && 
      formTerms.some(term => normalizedQuery.includes(`my ${term}`))) {
    return 'form';
  }
  
  // Check for question patterns that indicate informational queries
  const informationPatterns = [
    /^what (is|are)/i,
    /^how (do|does|can|to)/i,
    /^tell me about/i,
    /^explain/i,
    /^describe/i,
    /^define/i,
    /^who/i,
    /^when/i,
    /^where/i,
    /^why/i,
    /^can you (tell|explain|describe|provide information)/i,
    /^i (want|need|would like) (to know|information|details) about/i,
    /^give me (information|details|an explanation) (about|on)/i,
    /^provide (information|details) (about|on)/i,
    /^i'm curious about/i,
    /^i'd like to learn about/i,
    /^i am interested in learning about/i
  ];
  
  // Check for action patterns that indicate form requests
  const formPatterns = [
    // Direct action requests
    /^(i (want|need|would like) to|can (i|you)|let('s| me| us)|please) (create|submit|fill|complete)/i,
    /^(create|submit|fill out|complete|make|add|generate)/i,
    /^(show|display|open|give|view|see|check|get|access) (me |my |the |a |an )/i,
    /^(help me|assist me) (with|to) (create|submit|fill)/i,
    
    // More specific action intents
    /^i need to (create|submit|fill|complete)/i,
    /^i want to (create|submit|fill|complete)/i,
    /^i would like to (create|submit|fill|complete)/i,
    /^i'd like to (create|submit|fill|complete)/i,
    /^i have to (create|submit|fill|complete)/i,
    /^i must (create|submit|fill|complete)/i,
    /^i should (create|submit|fill|complete)/i,
    /^how (can|do) i (create|submit|fill|complete)/i,
    /^start (creating|submitting|filling)/i,
    /^begin (creating|submitting|filling)/i,
    
    // Specific to viewing/checking forms
    /^(show|display|view|see|check|get|access) my/i,
    /^i (want|need|would like) to (see|view|check|access)/i,
    /^can i (see|view|check|access)/i,
    
    // Leave-specific patterns
    /^(i (want|need|would like) to|can i|let me|please) (apply|take|request) (for |a )?leave/i,
    /^(apply|take|request) (for |a )?leave/i,
    /^leave (apply|application|request)/i,
    /^(apply|request) (for )?leave/i,
    /^i (want|need) (to take|some) leave/i,
    /^(help me|i need to|i want to) (apply for|request|take) leave/i
  ];
  
  // If the query ends with a question mark, check if it's a form-related question
  // before classifying it as an informational query
  if (normalizedQuery.endsWith('?')) {
    // If it contains form-related terms, it might be a form request despite the question mark
    if (hasFormTerm) {
      return 'form';
    }
    return 'information';
  }
  
  // If the query matches any information pattern, classify as information
  if (informationPatterns.some(pattern => pattern.test(normalizedQuery))) {
    return 'information';
  }
  
  // If the query matches any form pattern, classify as form
  if (formPatterns.some(pattern => pattern.test(normalizedQuery))) {
    return 'form';
  }
  
  // Check for action verbs that strongly indicate form intent
  const actionVerbs = ['create', 'submit', 'fill', 'complete', 'make', 'add', 'generate', 'show', 'display', 'view', 'see', 'check'];
  if (actionVerbs.some(verb => normalizedQuery.includes(verb))) {
    // If the query contains action verbs but doesn't match information patterns,
    // it's likely a form request
    return 'form';
  }
  
  // Default to information if no clear patterns are matched
  return 'information';
};

module.exports = {
  calculateSimilarity,
  detectQueryIntent
};